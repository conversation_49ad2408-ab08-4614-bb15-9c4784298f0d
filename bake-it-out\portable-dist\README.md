# Bake It Out - Portable Version

## Quick Start

### Windows
- Double-click `start.bat` to launch the game (normal startup)
- If you get port errors, double-click `kill-port-and-start.bat` instead

### macOS/Linux
Run `./start.sh` in terminal to launch the game.

## Manual Start

1. Install dependencies:
   ```
   npm install --production
   ```

2. Start the multiplayer server:
   ```
   npm run server
   ```

3. Start the game:
   ```
   npm start
   ```

## Features

- 🎮 Single-player and multiplayer modes
- 🌍 English and Czech language support
- 👥 Real-time multiplayer collaboration
- 🏪 Progressive bakery management
- 🤖 Automation and upgrades

## System Requirements

- Node.js 18+ (will be installed automatically if missing)
- 2GB RAM minimum
- 500MB disk space
- Internet connection for multiplayer

## Troubleshooting

If the game doesn't start:
1. **Port conflicts**: Use `kill-port-and-start.bat` (Windows) to clear ports
2. Make sure Node.js is installed
3. Run `npm install` manually
4. Check that ports 3000-3010 are available
5. Try running `npm start` directly

**Common Error Messages:**
- "EADDRINUSE: address already in use" → Use the kill-port-and-start script
- "JavaScript error occurred" → Try restarting your computer
- Server won't start → Check antivirus software isn't blocking the app

## Support

For issues and support, please visit:
https://github.com/your-repo/bake-it-out

Enjoy baking! 🥖✨
