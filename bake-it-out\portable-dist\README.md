# 🧁 Bake It Out - Portable Distribution v1.1

**Complete standalone version of the bakery management game with full localization!**

## 📦 What's Included

This portable distribution contains everything you need to run "Bake It Out" without any installation:

- **🎮 Complete Game** - Full bakery management experience
- **🌐 Browser Version** - Play directly in your web browser  
- **🖥️ Desktop Version** - Native Electron app
- **🔗 Multiplayer Server** - Local server for multiplayer games
- **🌍 Full Localization** - English and Czech language support
- **📚 Documentation** - Complete setup and usage guides

## ✨ **NEW in v1.1**
- **🌍 Complete Localization** - Professional English and Czech translations
- **🧁 Recipe Localization** - All recipes, ingredients, and equipment properly translated
- **🔧 Localization Fixes** - Resolved all translation issues and duplicate keys
- **📖 Enhanced Documentation** - Updated guides and instructions
- **🌐 Improved Browser Version** - Better browser compatibility and instructions

## 🚀 Quick Start

### **🎯 Super Easy (Recommended)**
```bash
# Just run this!
start.bat
```

### **🌐 Browser Only**
```bash
# Start browser version
cd app
python -m http.server 3000
# Open: http://localhost:3000
```

### **🖥️ Desktop Only**  
```bash
# Start desktop version
npm start
```

### **👥 Multiplayer**
```bash
# Start server first
start-server.bat

# Then start game
start.bat
```

---

## 🎮 **Game Features**

### **🏪 Bakery Management**
- Build and upgrade your bakery
- Manage ingredients and inventory
- Handle customer orders
- Unlock new recipes and equipment

### **🎯 Gameplay**
- Progressive difficulty
- Achievement system
- Skill development
- Multiple game modes

### **👥 Multiplayer**
- Cooperative gameplay
- Shared kitchens and resources
- Real-time collaboration
- Team achievements

### **🌍 Languages**
- **English** - Complete professional translations
- **Czech** - Native-quality Czech translations
- **Real-time switching** - Change language anytime

---

## 📁 **Directory Structure**

```
portable-dist/
├── app/                    # Browser version (built game)
├── src/                    # Source code with localization
├── electron/               # Desktop app files
├── server/                 # Multiplayer server
├── node_modules/           # Dependencies
├── start.bat              # Main launcher
├── start-server.bat       # Server launcher
├── serve-browser.js       # Browser server
└── Documentation files
```

---

## 🌐 **Browser Version**

### **Quick Start:**
1. **Navigate to app folder**: `cd app`
2. **Start server**: `python -m http.server 3000`
3. **Open browser**: http://localhost:3000
4. **Start playing**: Choose your language and enjoy!

### **Alternative Methods:**
- **Node.js**: `node ../serve-browser.js`
- **Serve**: `npx serve . -p 3000`
- **Any static server** that can serve the `app/` folder

**See `BROWSER-INSTRUCTIONS.md` for detailed browser setup guide.**

---

## 🖥️ **Desktop Version**

### **Requirements:**
- **Node.js** 18+ installed
- **Windows, macOS, or Linux**

### **Quick Start:**
1. **Run**: `start.bat` (Windows) or `npm start`
2. **Choose language**: English or Czech
3. **Start playing**: Single-player or multiplayer

---

## 👥 **Multiplayer Setup**

### **Local Multiplayer:**
1. **Start server**: `start-server.bat`
2. **Start game**: `start.bat`
3. **Create/Join room**: Use multiplayer menu
4. **Play together**: Cooperative bakery management

### **Network Multiplayer:**
1. **Configure server**: Edit `server/src/config.js`
2. **Start server**: `cd server && npm start`
3. **Connect clients**: Use server IP address
4. **Enjoy**: Multi-player bakery empire!

---

## 🌍 **Localization Features**

### **Complete Translation Coverage:**
- ✅ **550+ Translation Keys** - Every text element localized
- ✅ **Recipe Names** - All recipes in both languages
- ✅ **Ingredient Names** - All ingredients properly translated
- ✅ **Equipment Names** - All equipment localized
- ✅ **UI Elements** - Complete interface translation
- ✅ **Error Messages** - All errors and status messages
- ✅ **Game Features** - Achievements, skills, automation

### **Language Quality:**
- **English**: Professional gaming terminology
- **Czech**: Native-speaker quality translations
- **Real-time switching**: Change language without restart
- **Fallback support**: Graceful handling of missing translations

---

## 🆘 **Troubleshooting**

### **"Game won't start"**
- Check if Node.js is installed: `node --version`
- Try: `npm install` in the main directory
- Use: `start.bat` instead of manual commands

### **"Browser version won't load"**
- Check if port 3000 is available
- Try different port: `python -m http.server 8080`
- Clear browser cache and reload

### **"Multiplayer not working"**
- Start server first: `start-server.bat`
- Check firewall settings
- Verify port 3001 is available

### **"Language not switching"**
- Clear browser cache/localStorage
- Restart the application
- Check browser console for errors

---

## 📚 **Documentation**

- **`LOCALIZATION-FIXES.md`** - Complete localization update details
- **`BROWSER-VERSION.md`** - Comprehensive browser version guide
- **`BROWSER-INSTRUCTIONS.md`** - Step-by-step browser setup
- **`server/README.md`** - Server setup and configuration

---

## 🎉 **Ready to Play!**

### **Quick Start Checklist:**
1. ✅ **Extract** portable distribution
2. ✅ **Run** `start.bat`
3. ✅ **Choose** your language (English/Czech)
4. ✅ **Start** your bakery empire!

### **For Multiplayer:**
1. ✅ **Start server** with `start-server.bat`
2. ✅ **Start game** with `start.bat`
3. ✅ **Create room** in multiplayer menu
4. ✅ **Invite friends** to join your bakery!

## 📞 **Contact & Support**

- **Discord**: `.avariss` - For questions, feedback, or multiplayer coordination
- **Facebook**: [https://www.facebook.com/tazzisthebset](https://www.facebook.com/tazzisthebset) - Follow for updates and community
- **Issues**: Use GitHub issues for bug reports and feature requests
- **Development**: Open source project - contributions welcome!
- **Localization**: Help translate to additional languages!

**🧁 Happy baking in your language of choice! 🌍**
