const express = require('express');
const { body, validationResult } = require('express-validator');
const User = require('../models/User');
const CloudSave = require('../models/CloudSave');
const { authenticateToken } = require('../middleware/auth');

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// Get user profile
router.get('/profile', async (req, res) => {
  try {
    const user = await User.findById(req.userId);
    
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    // Get additional stats
    const saveStats = await CloudSave.aggregate([
      { $match: { userId: user._id, isActive: true } },
      {
        $group: {
          _id: null,
          totalSaves: { $sum: 1 },
          totalSize: { $sum: '$size' },
          lastSaveDate: { $max: '$updatedAt' },
          saveTypes: {
            $push: '$saveType'
          }
        }
      }
    ]);

    const stats = saveStats[0] || {
      totalSaves: 0,
      totalSize: 0,
      lastSaveDate: null,
      saveTypes: []
    };

    res.json({
      user: user.getPublicProfile(),
      cloudStats: {
        totalSaves: stats.totalSaves,
        totalSize: stats.totalSize,
        lastSaveDate: stats.lastSaveDate,
        autoSaves: stats.saveTypes.filter(type => type === 'auto').length,
        manualSaves: stats.saveTypes.filter(type => type === 'manual').length
      }
    });

  } catch (error) {
    console.error('Get profile error:', error);
    res.status(500).json({
      error: 'Failed to get user profile',
      code: 'PROFILE_ERROR'
    });
  }
});

// Update user profile
router.put('/profile', [
  body('profile.displayName')
    .optional()
    .isLength({ min: 1, max: 50 })
    .trim()
    .withMessage('Display name must be 1-50 characters'),
  body('profile.avatar')
    .optional()
    .isLength({ min: 1, max: 10 })
    .withMessage('Avatar must be 1-10 characters'),
  body('profile.preferredLanguage')
    .optional()
    .isIn(['en', 'cs'])
    .withMessage('Language must be en or cs')
], async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { profile } = req.body;

    const user = await User.findById(req.userId);
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    // Update profile fields
    if (profile.displayName !== undefined) {
      user.profile.displayName = profile.displayName;
    }
    if (profile.avatar !== undefined) {
      user.profile.avatar = profile.avatar;
    }
    if (profile.preferredLanguage !== undefined) {
      user.profile.preferredLanguage = profile.preferredLanguage;
    }

    await user.save();

    res.json({
      message: 'Profile updated successfully',
      user: user.getPublicProfile()
    });

  } catch (error) {
    console.error('Update profile error:', error);
    res.status(500).json({
      error: 'Failed to update profile',
      code: 'UPDATE_PROFILE_ERROR'
    });
  }
});

// Update user settings
router.put('/settings', [
  body('autoSave')
    .optional()
    .isBoolean()
    .withMessage('Auto save must be boolean'),
  body('syncFrequency')
    .optional()
    .isInt({ min: 60, max: 3600 })
    .withMessage('Sync frequency must be between 60 and 3600 seconds'),
  body('maxSaves')
    .optional()
    .isInt({ min: 5, max: 50 })
    .withMessage('Max saves must be between 5 and 50')
], async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { autoSave, syncFrequency, maxSaves } = req.body;

    const user = await User.findById(req.userId);
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    // Update settings
    if (autoSave !== undefined) {
      user.settings.autoSave = autoSave;
    }
    if (syncFrequency !== undefined) {
      user.settings.syncFrequency = syncFrequency;
    }
    if (maxSaves !== undefined) {
      user.settings.maxSaves = maxSaves;
    }

    await user.save();

    res.json({
      message: 'Settings updated successfully',
      settings: user.settings
    });

  } catch (error) {
    console.error('Update settings error:', error);
    res.status(500).json({
      error: 'Failed to update settings',
      code: 'UPDATE_SETTINGS_ERROR'
    });
  }
});

// Get user statistics
router.get('/stats', async (req, res) => {
  try {
    const user = await User.findById(req.userId);
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    // Get detailed save statistics
    const saveStats = await CloudSave.aggregate([
      { $match: { userId: user._id, isActive: true } },
      {
        $group: {
          _id: '$saveType',
          count: { $sum: 1 },
          totalSize: { $sum: '$size' },
          avgLevel: { $avg: '$metadata.level' },
          maxLevel: { $max: '$metadata.level' },
          totalMoney: { $sum: '$metadata.money' }
        }
      }
    ]);

    // Get recent activity
    const recentSaves = await CloudSave.find({
      userId: user._id,
      isActive: true
    })
    .sort({ updatedAt: -1 })
    .limit(5)
    .select('saveName saveType metadata.level updatedAt');

    res.json({
      gameStats: user.gameStats,
      saveStats: saveStats.reduce((acc, stat) => {
        acc[stat._id] = {
          count: stat.count,
          totalSize: stat.totalSize,
          avgLevel: Math.round(stat.avgLevel || 0),
          maxLevel: stat.maxLevel || 0,
          totalMoney: stat.totalMoney || 0
        };
        return acc;
      }, {}),
      recentActivity: recentSaves,
      accountInfo: {
        memberSince: user.createdAt,
        lastLogin: user.lastLoginAt,
        totalSaves: saveStats.reduce((sum, stat) => sum + stat.count, 0)
      }
    });

  } catch (error) {
    console.error('Get stats error:', error);
    res.status(500).json({
      error: 'Failed to get user statistics',
      code: 'STATS_ERROR'
    });
  }
});

// Delete user account (soft delete)
router.delete('/account', [
  body('confirmPassword')
    .notEmpty()
    .withMessage('Password confirmation required')
], async (req, res) => {
  try {
    // Check validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { confirmPassword } = req.body;

    const user = await User.findById(req.userId);
    if (!user) {
      return res.status(404).json({
        error: 'User not found',
        code: 'USER_NOT_FOUND'
      });
    }

    // Verify password
    const isPasswordValid = await user.comparePassword(confirmPassword);
    if (!isPasswordValid) {
      return res.status(401).json({
        error: 'Invalid password',
        code: 'INVALID_PASSWORD'
      });
    }

    // Soft delete user and all saves
    user.isActive = false;
    await user.save();

    await CloudSave.updateMany(
      { userId: user._id },
      { isActive: false }
    );

    res.json({
      message: 'Account deleted successfully'
    });

  } catch (error) {
    console.error('Delete account error:', error);
    res.status(500).json({
      error: 'Failed to delete account',
      code: 'DELETE_ACCOUNT_ERROR'
    });
  }
});

module.exports = router;
