"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[978],{637:(e,t,i)=>{i.d(t,{MultiplayerProvider:()=>u,K:()=>m});var a=i(5155),o=i(2115),n=i(4298),r=i(9509);class s{connect(){var e;if(null==(e=this.socket)||!e.connected)try{this.socket=(0,n.io)(r.env.NEXT_PUBLIC_SOCKET_URL||"http://localhost:3001",{transports:["websocket","polling"],timeout:1e4,forceNew:!0,autoConnect:!1}),this.setupEventListeners(),this.socket.connect()}catch(e){console.warn("Failed to initialize socket connection:",e)}}setupEventListeners(){this.socket&&(this.socket.on("connect",()=>{console.log("Connected to multiplayer server"),this.isConnected=!0,this.reconnectAttempts=0}),this.socket.on("disconnect",e=>{console.log("Disconnected from multiplayer server:",e),this.isConnected=!1,"io server disconnect"===e&&this.handleReconnect()}),this.socket.on("connect_error",e=>{console.warn("Multiplayer server not available:",e.message),this.isConnected=!1}),this.socket.on("error",e=>{console.warn("Socket error:",e.message)}))}handleReconnect(){this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,console.log("Attempting to reconnect (".concat(this.reconnectAttempts,"/").concat(this.maxReconnectAttempts,")...")),setTimeout(()=>{this.connect()},1e3*Math.pow(2,this.reconnectAttempts))):console.error("Max reconnection attempts reached")}getConnectionStatus(){return{isConnected:this.isConnected,socket:this.socket}}createRoom(e){return new Promise((t,i)=>{var a;if(!(null==(a=this.socket)?void 0:a.connected))return void i(Error("Not connected to server"));this.socket.emit("create_room",e),this.socket.once("room_created",e=>{this.currentRoom=e,t(e)}),this.socket.once("error",e=>{i(Error(e.message))})})}joinRoom(e,t){return new Promise((i,a)=>{var o;if(!(null==(o=this.socket)?void 0:o.connected))return void a(Error("Not connected to server"));this.socket.emit("join_room",e,t),this.socket.once("room_joined",(e,t)=>{this.currentRoom=e,this.currentPlayer=t,i({room:e,player:t})}),this.socket.once("error",e=>{a(Error(e.message))})})}leaveRoom(){var e;(null==(e=this.socket)?void 0:e.connected)&&this.currentRoom&&(this.socket.emit("leave_room",this.currentRoom.id),this.currentRoom=null,this.currentPlayer=null)}sendPlayerAction(e){var t;(null==(t=this.socket)?void 0:t.connected)&&this.currentPlayer&&this.socket.emit("player_action",{...e,playerId:this.currentPlayer.id,timestamp:Date.now()})}sendMessage(e){var t;(null==(t=this.socket)?void 0:t.connected)&&this.currentPlayer&&this.socket.emit("send_message",{playerId:this.currentPlayer.id,playerName:this.currentPlayer.name,content:e,timestamp:Date.now()})}on(e,t){var i;null==(i=this.socket)||i.on(e,t)}off(e,t){var i;null==(i=this.socket)||i.off(e,t)}once(e,t){var i;null==(i=this.socket)||i.once(e,t)}isSocketConnected(){var e;return this.isConnected&&(null==(e=this.socket)?void 0:e.connected)===!0}getCurrentRoom(){return this.currentRoom}getCurrentPlayer(){return this.currentPlayer}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null,this.isConnected=!1,this.currentRoom=null,this.currentPlayer=null)}constructor(){this.socket=null,this.isConnected=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.currentRoom=null,this.currentPlayer=null}}let l=new s,c=(0,o.createContext)(void 0);function u(e){let{children:t}=e,[i,n]=(0,o.useState)(!1),[r,s]=(0,o.useState)(!1),[u,m]=(0,o.useState)(null),[d,p]=(0,o.useState)(null),[y,v]=(0,o.useState)(null),[g,k]=(0,o.useState)([]),[h,f]=(0,o.useState)("waiting"),[b,S]=(0,o.useState)(null),[P,w]=(0,o.useState)([]);(0,o.useEffect)(()=>{let e=()=>{n(!0),m(null)},t=()=>{n(!1),s(!1),p(null),v(null),k([])},i=e=>{m(e.message||"Connection error"),console.error("Multiplayer error:",e)},a=e=>{p(e),k(e.players),s(!0),f(e.gameState);let t=e.players.find(e=>e.isHost);t&&v(t)},o=(e,t)=>{p(e),v(t),k(e.players),s(!0),f(e.gameState)},r=()=>{p(null),v(null),k([]),s(!1),f("waiting"),S(null),w([])},c=e=>{p(e),k(e.players),f(e.gameState)},u=e=>{k(t=>[...t,e]),C("".concat(e.name," joined the room"))},d=e=>{k(t=>{let i=t.find(t=>t.id===e);return i&&C("".concat(i.name," left the room")),t.filter(t=>t.id!==e)})},y=e=>{f("playing"),S(e),C("Game started!")},g=e=>{S(t=>t?{...t,...e}:null)},h=e=>{console.log("Player action received:",e)},b=e=>{let t={id:Date.now().toString()+Math.random().toString(36).substring(2,11),playerId:e.playerId,playerName:e.playerName,content:e.content,timestamp:e.timestamp};w(e=>[...e,t])};return l.on("connect",e),l.on("disconnect",t),l.on("error",i),l.on("room_created",a),l.on("room_joined",o),l.on("room_left",r),l.on("room_updated",c),l.on("player_joined",u),l.on("player_left",d),l.on("game_started",y),l.on("game_state_update",g),l.on("player_action",h),l.on("message_received",b),n(l.isSocketConnected()),()=>{l.off("connect",e),l.off("disconnect",t),l.off("error",i),l.off("room_created",a),l.off("room_joined",o),l.off("room_left",r),l.off("room_updated",c),l.off("player_joined",u),l.off("player_left",d),l.off("game_started",y),l.off("game_state_update",g),l.off("player_action",h),l.off("message_received",b)}},[]);let C=e=>{let t={id:Date.now().toString()+Math.random().toString(36).substring(2,11),playerId:"system",playerName:"System",content:e,timestamp:Date.now()};w(e=>[...e,t])},_=(0,o.useCallback)(async(e,t)=>{try{m(null);let{isConnected:i}=l.getConnectionStatus();i||(l.connect(),await new Promise(e=>setTimeout(e,1e3))),await l.createRoom({...e,hostName:t.name,hostAvatar:t.avatar,hostLevel:t.level})}catch(e){throw m(e.message),e}},[]),I=(0,o.useCallback)(async(e,t)=>{try{m(null);let{isConnected:i}=l.getConnectionStatus();i||(l.connect(),await new Promise(e=>setTimeout(e,1e3))),await l.joinRoom(e,t)}catch(e){throw m(e.message),e}},[]),M=(0,o.useCallback)(()=>{l.leaveRoom()},[]),R=(0,o.useCallback)(()=>{d&&(null==y?void 0:y.isHost)&&l.sendPlayerAction({type:"start_game",data:{roomId:d.id}})},[d,y]),D=(0,o.useCallback)(e=>{l.sendMessage(e)},[]),q=(0,o.useCallback)(e=>{l.sendPlayerAction(e)},[]),A=(0,o.useCallback)(e=>{y&&q({type:"player_ready",data:{ready:e}})},[y,q]),E=(0,o.useCallback)(e=>{(null==y?void 0:y.isHost)&&q({type:"kick_player",data:{playerId:e}})},[y,q]),z=(0,o.useCallback)(e=>{(null==y?void 0:y.isHost)&&q({type:"update_room_settings",data:{settings:e}})},[y,q]);return(0,a.jsx)(c.Provider,{value:{isConnected:i,isInRoom:r,connectionError:u,currentRoom:d,currentPlayer:y,players:g,gameState:h,sharedGameState:b,messages:P,createRoom:_,joinRoom:I,leaveRoom:M,startGame:R,sendMessage:D,sendPlayerAction:q,setPlayerReady:A,kickPlayer:E,updateRoomSettings:z},children:t})}function m(){let e=(0,o.useContext)(c);return void 0===e?(console.warn("useMultiplayer called outside of MultiplayerProvider, using fallback"),{isConnected:!1,connectionStatus:"disconnected",currentRoom:null,gameState:null,createRoom:async()=>{},joinRoom:async()=>{},leaveRoom:()=>{},sendChatMessage:()=>{},updateGameState:()=>{},setPlayerReady:()=>{}}):e}},2148:(e,t,i)=>{i.d(t,{DiscordRPCProvider:()=>m,l:()=>u});var a=i(5155),o=i(2115);class n{async initializeRPC(){if(this.isEnabled&&this.isElectron)try{window.electronAPI&&window.electronAPI.initDiscordRPC?await window.electronAPI.initDiscordRPC(this.CLIENT_ID)?(console.log("Discord RPC initialized successfully"),this.isConnected=!0,setTimeout(async()=>{await this.updateActivity({state:"In Main Menu",details:"Starting the bakery adventure",largeImageKey:"bake_it_out_logo",largeImageText:"Bake It Out - Bakery Management Game",startTimestamp:this.startTime})},1e3)):(console.log("Discord RPC initialization failed"),this.isConnected=!1):(console.log("Discord RPC not available - missing Electron API"),this.isConnected=!1)}catch(e){console.log("Discord RPC initialization error:",e.message||e),this.isConnected=!1}}async updatePlayerStatus(e){if(!this.isConnected||!this.client)return;let t=this.createActivityFromPlayerStatus(e);await this.updateActivity(t)}createActivityFromPlayerStatus(e){let t={state:"",details:"",largeImageKey:"bake_it_out_logo",largeImageText:"Bake It Out - Bakery Management Game",startTimestamp:this.startTime};switch(e.currentActivity){case"menu":return{...t,state:"In Main Menu",details:"Choosing game mode",smallImageKey:"menu_icon",smallImageText:"Main Menu"};case"baking":return{...t,state:"Level ".concat(e.level," Baker"),details:e.currentOrder?"Baking: ".concat(e.currentOrder):"Managing the bakery",smallImageKey:"baking_icon",smallImageText:"Baking",buttons:[{label:"Play Bake It Out",url:"https://bakeitout.game"}]};case"managing":return{...t,state:"Level ".concat(e.level," - $").concat(e.money),details:e.bakeryName?"Managing ".concat(e.bakeryName):"Managing bakery",smallImageKey:"management_icon",smallImageText:"Bakery Management"};case"multiplayer":let i={...t,state:"Level ".concat(e.level," Baker"),details:"Playing with friends",smallImageKey:"multiplayer_icon",smallImageText:"Multiplayer",buttons:[{label:"Join Game",url:"https://bakeitout.game/join"}]};return e.multiplayerRoom&&(i.partyId=e.multiplayerRoom.id,i.partySize=e.multiplayerRoom.playerCount,i.partyMax=e.multiplayerRoom.maxPlayers,i.details="Multiplayer Bakery (".concat(e.multiplayerRoom.playerCount,"/").concat(e.multiplayerRoom.maxPlayers,")")),i;case"idle":return{...t,state:"Level ".concat(e.level," Baker"),details:"Taking a break",smallImageKey:"idle_icon",smallImageText:"Idle"};default:return{...t,state:"Playing Bake It Out",details:"Bakery Management Game"}}}async updateActivity(e){if(this.isConnected&&this.isElectron)try{window.electronAPI&&window.electronAPI.updateDiscordRPC&&(await window.electronAPI.updateDiscordRPC(e),this.currentActivity=e,console.log("Discord RPC activity updated:",e.details))}catch(e){console.error("Failed to update Discord RPC activity:",e)}}async setMenuActivity(){await this.updateActivity({state:"In Main Menu",details:"Choosing game mode",largeImageKey:"bake_it_out_logo",largeImageText:"Bake It Out - Bakery Management Game",smallImageKey:"menu_icon",smallImageText:"Main Menu",startTimestamp:this.startTime})}async setGameActivity(e,t,i){await this.updateActivity({state:"Level ".concat(e," - $").concat(t),details:i||"Managing bakery",largeImageKey:"bake_it_out_logo",largeImageText:"Bake It Out - Bakery Management Game",smallImageKey:"baking_icon",smallImageText:"In Game",startTimestamp:this.startTime,buttons:[{label:"Play Bake It Out",url:"https://bakeitout.game"}]})}async setMultiplayerActivity(e,t,i){await this.updateActivity({state:"Multiplayer Bakery",details:"Playing with friends (".concat(t,"/").concat(i,")"),largeImageKey:"bake_it_out_logo",largeImageText:"Bake It Out - Bakery Management Game",smallImageKey:"multiplayer_icon",smallImageText:"Multiplayer",startTimestamp:this.startTime,partyId:e,partySize:t,partyMax:i,buttons:[{label:"Join Game",url:"https://bakeitout.game/join/".concat(e)}]})}async setBakingActivity(e,t){await this.updateActivity({state:"Level ".concat(e," Baker"),details:"Baking: ".concat(t),largeImageKey:"bake_it_out_logo",largeImageText:"Bake It Out - Bakery Management Game",smallImageKey:"baking_icon",smallImageText:"Baking",startTimestamp:this.startTime})}async clearActivity(){if(this.isConnected&&this.isElectron)try{window.electronAPI&&window.electronAPI.clearDiscordRPC&&(await window.electronAPI.clearDiscordRPC(),this.currentActivity=null,console.log("Discord RPC activity cleared"))}catch(e){console.error("Failed to clear Discord RPC activity:",e)}}setEnabled(e){this.isEnabled=e,!e&&this.isConnected?(this.clearActivity(),this.disconnect()):e&&!this.isConnected&&this.initializeRPC()}isRPCEnabled(){return this.isEnabled}isRPCConnected(){return this.isConnected}getCurrentActivity(){return this.currentActivity}async disconnect(){if(this.isConnected&&this.isElectron)try{window.electronAPI&&window.electronAPI.disconnectDiscordRPC&&(await window.electronAPI.disconnectDiscordRPC(),console.log("Discord RPC disconnected"))}catch(e){console.error("Error disconnecting Discord RPC:",e)}this.isConnected=!1,this.currentActivity=null}async cleanup(){await this.clearActivity(),await this.disconnect()}constructor(){this.isConnected=!1,this.isEnabled=!0,this.startTime=Date.now(),this.currentActivity=null,this.isElectron=!1,this.CLIENT_ID="1234567890123456789",this.isElectron=void 0!==window.electronAPI,this.isElectron?this.initializeRPC():console.log("Discord RPC only available in desktop version")}}let r=new n;var s=i(2517),l=i(637);let c=(0,o.createContext)(void 0);function u(){let e=(0,o.useContext)(c);return void 0===e?{isEnabled:!1,isConnected:!1,currentActivity:null,setEnabled:()=>{},updateActivity:async()=>{},setMenuActivity:async()=>{},setGameActivity:async()=>{},setMultiplayerActivity:async()=>{},setBakingActivity:async()=>{},clearActivity:async()=>{}}:e}function m(e){let{children:t}=e,[i,n]=(0,o.useState)(!1),[u,m]=(0,o.useState)(!1),[d,p]=(0,o.useState)(null),[y,v]=(0,o.useState)(!1),g=(0,s.I)(),k=(0,l.K)();(0,o.useEffect)(()=>{v(!0);let e=localStorage.getItem("discordRPCEnabled");null!==e?n("true"===e):n(void 0!==window.electronAPI)},[]),(0,o.useEffect)(()=>{if(y){if(void 0!==window.electronAPI&&i){r.setEnabled(!0);let e=()=>{m(r.isRPCConnected()),p(r.getCurrentActivity())},t=setInterval(e,2e3);return e(),()=>{clearInterval(t)}}r.setEnabled(!1)}},[y,i]),(0,o.useEffect)(()=>{if(!y||!i||!u||!g)return;let e=async()=>{try{let t,{player:i,currentOrders:a}=g,o="managing";if(a&&a.length>0){var e;o="baking",t=(null==(e=a[0].items[0])?void 0:e.name)||"Unknown item"}(null==k?void 0:k.gameState)==="playing"&&(o="multiplayer");let n={level:i.level,money:i.money,currentActivity:o,currentOrder:t,playTime:i.playTime};await r.updatePlayerStatus(n)}catch(e){console.error("Failed to update Discord RPC from game state:",e)}};e();let t=setInterval(e,3e4);return()=>clearInterval(t)},[null==g?void 0:g.player,null==g?void 0:g.currentOrders,null==k?void 0:k.gameState,y,i,u]),(0,o.useEffect)(()=>{y&&i&&u&&k&&(async()=>{try{let{gameState:e,currentRoom:t,players:i}=k;"playing"===e&&t&&await r.setMultiplayerActivity(t.id,i.length,t.maxPlayers||4)}catch(e){console.error("Failed to update multiplayer Discord RPC:",e)}})()},[null==k?void 0:k.gameState,null==k?void 0:k.currentRoom,null==k?void 0:k.players,y,i,u]);let h=async e=>{await r.updateActivity(e),p(e)},f=async()=>{await r.setMenuActivity(),p(r.getCurrentActivity())},b=async(e,t,i)=>{await r.setGameActivity(e,t,i),p(r.getCurrentActivity())},S=async(e,t,i)=>{await r.setMultiplayerActivity(e,t,i),p(r.getCurrentActivity())},P=async(e,t)=>{await r.setBakingActivity(e,t),p(r.getCurrentActivity())},w=async()=>{await r.clearActivity(),p(null)};return(0,o.useEffect)(()=>()=>{r.cleanup()},[]),(0,a.jsx)(c.Provider,{value:{isEnabled:i,isConnected:u,currentActivity:d,setEnabled:e=>{n(e),r.setEnabled(e),y&&localStorage.setItem("discordRPCEnabled",e.toString())},updateActivity:h,setMenuActivity:f,setGameActivity:b,setMultiplayerActivity:S,setBakingActivity:P,clearActivity:w},children:t})}},2517:(e,t,i)=>{i.d(t,{S:()=>d,I:()=>p});var a=i(5155),o=i(2115),n=i(4983);function r(e){return e<=1?0:Math.floor(100*Math.pow(1.15,e-1))}let s=[{id:"first_order",name:"First Customer",description:"Complete your first order",icon:"\uD83C\uDFAF",category:"baking",requirements:[{type:"orders_completed",target:1}],reward:{type:"money",id:"first_order_bonus",name:"First Order Bonus",description:"Bonus for first order",value:50},unlocked:!0,completed:!1},{id:"baker_apprentice",name:"Baker Apprentice",description:"Complete 10 orders",icon:"\uD83D\uDC68‍\uD83C\uDF73",category:"baking",requirements:[{type:"orders_completed",target:10}],reward:{type:"skill_point",id:"apprentice_skill",name:"Skill Point",description:"Gain 1 skill point",value:1},unlocked:!0,completed:!1},{id:"baker_journeyman",name:"Baker Journeyman",description:"Complete 50 orders",icon:"\uD83D\uDC68‍\uD83C\uDF73",category:"baking",requirements:[{type:"orders_completed",target:50}],reward:{type:"money",id:"journeyman_bonus",name:"Journeyman Bonus",description:"Large money bonus",value:500},unlocked:!0,completed:!1},{id:"master_baker",name:"Master Baker",description:"Complete 100 orders",icon:"\uD83C\uDFC6",category:"baking",requirements:[{type:"orders_completed",target:100}],reward:{type:"skill_point",id:"master_skill",name:"Master Skill Points",description:"Gain 3 skill points",value:3},unlocked:!0,completed:!1},{id:"speed_baker",name:"Speed Baker",description:"Bake 100 items",icon:"⚡",category:"efficiency",requirements:[{type:"items_baked",target:100}],reward:{type:"skill_point",id:"speed_skill",name:"Speed Skill Point",description:"Gain 1 skill point",value:1},unlocked:!0,completed:!1},{id:"money_maker",name:"Money Maker",description:"Earn $1000 total",icon:"\uD83D\uDCB0",category:"business",requirements:[{type:"money_earned",target:1e3}],reward:{type:"skill_point",id:"money_maker_skill",name:"Business Skill Point",description:"Extra skill point for business success",value:1},unlocked:!0,completed:!1},{id:"recipe_collector",name:"Recipe Collector",description:"Unlock 5 different recipes",icon:"\uD83D\uDCDA",category:"collection",requirements:[{type:"recipes_unlocked",target:5}],reward:{type:"money",id:"recipe_bonus",name:"Recipe Collection Bonus",description:"Bonus for collecting recipes",value:200},unlocked:!0,completed:!1},{id:"level_master",name:"Level Master",description:"Reach level 10",icon:"⭐",category:"special",requirements:[{type:"level_reached",target:10}],reward:{type:"skill_point",id:"level_master_skill",name:"Master Level Bonus",description:"Gain 2 skill points",value:2},unlocked:!0,completed:!1},{id:"first_hundred",name:"First Hundred",description:"Earn $100 total",icon:"\uD83D\uDCB5",category:"business",requirements:[{type:"money_earned",target:100}],reward:{type:"money",id:"first_hundred_bonus",name:"Business Bonus",description:"Small business milestone bonus",value:25},unlocked:!0,completed:!1},{id:"entrepreneur",name:"Entrepreneur",description:"Earn $5000 total",icon:"\uD83C\uDFE2",category:"business",requirements:[{type:"money_earned",target:5e3}],reward:{type:"skill_point",id:"entrepreneur_skill",name:"Business Skill Points",description:"Gain 2 skill points",value:2},unlocked:!0,completed:!1},{id:"equipment_enthusiast",name:"Equipment Enthusiast",description:"Own 3 pieces of equipment",icon:"⚙️",category:"collection",requirements:[{type:"equipment_owned",target:3}],reward:{type:"money",id:"equipment_bonus",name:"Equipment Bonus",description:"Equipment investment bonus",value:300},unlocked:!0,completed:!1},{id:"rising_star",name:"Rising Star",description:"Reach level 5",icon:"\uD83C\uDF1F",category:"special",requirements:[{type:"level_reached",target:5}],reward:{type:"skill_point",id:"rising_star_skill",name:"Rising Star Bonus",description:"Gain 1 skill point",value:1},unlocked:!0,completed:!1},{id:"legendary_baker",name:"Legendary Baker",description:"Reach level 20",icon:"\uD83D\uDC51",category:"special",requirements:[{type:"level_reached",target:20}],reward:{type:"skill_point",id:"legendary_skill",name:"Legendary Bonus",description:"Gain 5 skill points",value:5},unlocked:!0,completed:!1}],l=[{id:"baking_speed_1",name:"Quick Hands",description:"Increase baking speed by 10%",icon:"⚡",category:"efficiency",level:0,maxLevel:3,cost:1,requirements:{playerLevel:2},effects:[{type:"baking_speed",value:.1}]},{id:"money_bonus_1",name:"Business Sense",description:"Increase money earned by 15%",icon:"\uD83D\uDCBC",category:"business",level:0,maxLevel:3,cost:1,requirements:{playerLevel:3},effects:[{type:"money_multiplier",value:.15}]},{id:"xp_bonus_1",name:"Fast Learner",description:"Increase experience gained by 20%",icon:"\uD83D\uDCC8",category:"efficiency",level:0,maxLevel:2,cost:2,requirements:{playerLevel:4},effects:[{type:"xp_multiplier",value:.2}]},{id:"ingredient_efficiency_1",name:"Efficient Baker",description:"Use 10% fewer ingredients",icon:"\uD83C\uDF3E",category:"efficiency",level:0,maxLevel:2,cost:2,requirements:{playerLevel:5,skills:["baking_speed_1"]},effects:[{type:"ingredient_efficiency",value:.1}]},{id:"automation_unlock_1",name:"Automation Expert",description:"Unlock advanced automation features",icon:"\uD83E\uDD16",category:"automation",level:0,maxLevel:1,cost:3,requirements:{playerLevel:8,achievements:["baker_apprentice"]},effects:[{type:"automation_unlock",value:1}]}];var c=i(7871),u=i(5877);let m=(0,o.createContext)(void 0);function d(e){let{children:t}=e,[i,d]=(0,o.useState)({level:1,experience:0,money:100,maxExperience:100,skillPoints:0,totalMoneyEarned:0,totalOrdersCompleted:0,totalItemsBaked:0,unlockedRecipes:["chocolate_chip_cookies","vanilla_muffins"],automationUpgrades:[]}),[p,y]=(0,o.useState)([{id:"oven1",name:"Basic Oven",type:"oven",isActive:!1,level:1,efficiency:1,automationLevel:0},{id:"mixer1",name:"Hand Mixer",type:"mixer",isActive:!1,level:1,efficiency:1,automationLevel:0},{id:"counter1",name:"Work Counter",type:"counter",isActive:!1,level:1,efficiency:1,automationLevel:0}]),[v,g]=(0,o.useState)([{name:"Flour",quantity:15,cost:5,icon:"\uD83C\uDF3E"},{name:"Sugar",quantity:12,cost:8,icon:"\uD83C\uDF6F"},{name:"Eggs",quantity:10,cost:12,icon:"\uD83E\uDD5A"},{name:"Butter",quantity:8,cost:15,icon:"\uD83E\uDDC8"},{name:"Chocolate Chips",quantity:6,cost:20,icon:"\uD83C\uDF6B"},{name:"Vanilla",quantity:5,cost:25,icon:"\uD83C\uDF3F"},{name:"Salt",quantity:10,cost:3,icon:"\uD83E\uDDC2"}]),[k,h]=(0,o.useState)([{id:"1",customerName:"Alice Johnson",items:["Chocolate Chip Cookies"],timeLimit:300,reward:25,status:"pending",difficulty:1}]),[f,b]=(0,o.useState)(s),[S,P]=(0,o.useState)(l),[w,C]=(0,o.useState)([]),[_,I]=(0,o.useState)(!1),[M,R]=(0,o.useState)({enabled:!1,autoStart:!1,preferredRecipes:[],maxConcurrentJobs:2,priorityMode:"efficiency",ingredientThreshold:5}),[D,q]=(0,o.useState)([]),[A,E]=(0,o.useState)([]);(0,o.useEffect)(()=>{let e=setInterval(()=>{y(e=>e.map(e=>e.isActive&&e.timeRemaining&&e.timeRemaining>0?{...e,timeRemaining:e.timeRemaining-1}:e.isActive&&0===e.timeRemaining?{...e,isActive:!1,timeRemaining:void 0,currentRecipe:void 0}:e))},1e3);return()=>clearInterval(e)},[]),(0,o.useEffect)(()=>{let e=setInterval(()=>{h(e=>e.map(e=>{if(("accepted"===e.status||"in_progress"===e.status)&&e.timeLimit>0){let t=e.timeLimit-1;return 0===t?{...e,status:"failed",timeLimit:0}:{...e,timeLimit:t}}return e}))},1e3);return()=>clearInterval(e)},[]);let z=(e,t)=>{y(i=>i.map(i=>i.id===e?{...i,...t}:i))},j=e=>{d(t=>{let i=t.experience+e,a=function(e){let t=1,i=0;for(;;){let a=r(t+1);if(i+a>e)break;i+=a,t++}let a=r(t+1);return{level:t,experience:e-i,experienceRequired:a,totalExperience:e,rewards:function(e){let t=[];t.push({type:"money",id:"money_".concat(e),name:"Level Bonus",description:"Bonus money for reaching level ".concat(e),value:25*e});let i={2:["cinnamon_rolls"],3:["chocolate_brownies","sourdough_bread"],4:["croissants"],5:["cheesecake"],6:["macarons"],7:["honey_glazed_donuts"],8:["sourdough_bread"],9:["chocolate_souffle"],10:["croquembouche"],12:["opera_cake"],15:["artisan_pizza_dough"]};i[e]&&i[e].forEach(e=>{t.push({type:"recipe",id:e,name:"New Recipe Unlocked",description:"You can now bake ".concat(e.replace(/_/g," "))})});let a={3:["professional_oven"],4:["auto_mixer"],5:["stand_mixer"],6:["auto_oven"],7:["conveyor_belt"],8:["advanced_auto_mixer"],10:["industrial_oven"],12:["smart_conveyor_system"]};return a[e]&&a[e].forEach(e=>{t.push({type:"equipment",id:e,name:"New Equipment Available",description:"".concat(e.replace(/_/g," ")," is now available for purchase")})}),e%2==0&&t.push({type:"skill_point",id:"skill_point_".concat(e),name:"Skill Point",description:"Use this to upgrade your skills in the technology tree",value:1}),t}(t)}}(i);if(a.level>t.level){C(a.rewards),I(!0);let e=+(a.level%2==0);return setTimeout(()=>T(),100),{...t,level:a.level,experience:i,maxExperience:a.experienceRequired,skillPoints:t.skillPoints+e}}return{...t,experience:i,maxExperience:a.experienceRequired}})},L=e=>{d(t=>({...t,money:t.money+e,totalMoneyEarned:t.totalMoneyEarned+e})),setTimeout(()=>T(),100)},x=e=>i.money>=e&&(d(t=>({...t,money:t.money-e})),!0),B=(e,t)=>{let i=v.find(t=>t.name===e);return!!i&&i.quantity>=t&&(g(i=>i.map(i=>i.name===e?{...i,quantity:i.quantity-t}:i)),!0)},T=()=>{b(e=>e.map(e=>{if(e.completed)return e;let t=e.requirements.map(e=>{let t=0;switch(e.type){case"orders_completed":t=i.totalOrdersCompleted;break;case"money_earned":t=i.totalMoneyEarned;break;case"recipes_unlocked":t=i.unlockedRecipes.length;break;case"level_reached":t=i.level;break;case"items_baked":t=i.totalItemsBaked;break;case"equipment_owned":t=p.length}return{...e,current:t}}),a=t.every(e=>e.current>=e.target);return a&&!e.completed&&(showSuccess("Achievement Unlocked!","\uD83C\uDFC6 ".concat(e.name)),"money"===e.reward.type&&e.reward.value?L(e.reward.value):"skill_point"===e.reward.type&&e.reward.value&&d(t=>({...t,skillPoints:t.skillPoints+e.reward.value}))),{...e,requirements:t,completed:a}}))},O=async(e,t)=>{try{let a={version:"1.0.0",timestamp:Date.now(),player:{level:i.level,experience:i.experience,money:i.money,skillPoints:i.skillPoints,totalMoneyEarned:i.totalMoneyEarned||0,totalOrdersCompleted:i.totalOrdersCompleted||0,totalItemsBaked:i.totalItemsBaked||0,unlockedRecipes:i.unlockedRecipes||[],automationUpgrades:i.automationUpgrades||[],name:t||"Save ".concat(e||1),playTime:i.playTime||0},equipment:p,inventory:v,achievements:f,skills:S,automationSettings:M,gameSettings:{language:"en",soundEnabled:!0,musicEnabled:!0,notificationsEnabled:!0,autoSaveEnabled:!0},bakeries:[],currentBakeryId:"main"};if(e){let t="bakeItOut_save_slot_".concat(e);localStorage.setItem(t,JSON.stringify(a))}else u.B.saveToLocal(a);return!0}catch(e){return console.error("Failed to save game:",e),!1}},N=async e=>{try{let t=localStorage.getItem("bakeItOut_save_slot_".concat(e));if(!t)return!1;let i=JSON.parse(t);return d(e=>({...e,level:i.player.level,experience:i.player.experience,money:i.player.money,skillPoints:i.player.skillPoints,totalMoneyEarned:i.player.totalMoneyEarned||0,totalOrdersCompleted:i.player.totalOrdersCompleted||0,totalItemsBaked:i.player.totalItemsBaked||0,unlockedRecipes:i.player.unlockedRecipes||[],automationUpgrades:i.player.automationUpgrades||[],playTime:i.player.playTime||0})),y(i.equipment||[]),g(i.inventory||[]),b(i.achievements||[]),P(i.skills||[]),R(i.automationSettings||{enabled:!1,efficiency:1,speed:1,qualityBonus:0}),!0}catch(e){return console.error("Failed to load game:",e),!1}},U=async()=>await O(0,"Quick Save"),F=async()=>await N(0),G=async()=>await O(-1,"Auto Save");return(0,o.useEffect)(()=>{let e=setInterval(()=>{G()},12e4);return()=>clearInterval(e)},[]),(0,a.jsx)(m.Provider,{value:{player:i,equipment:p,inventory:v,orders:k,achievements:f,skills:S,levelUpRewards:w,showLevelUp:_,automationSettings:M,automationJobs:D,conveyorBelts:A,updatePlayer:e=>{d(t=>({...t,...e}))},updateEquipment:z,addEquipment:e=>{let t={...e,id:Date.now().toString()+Math.random().toString(36).substring(2,11)};y(e=>[...e,t]),setTimeout(()=>T(),100)},addExperience:j,addMoney:L,spendMoney:x,useIngredient:B,addIngredient:(e,t)=>{g(i=>i.map(i=>i.name===e?{...i,quantity:i.quantity+t}:i))},acceptOrder:e=>{h(t=>t.map(t=>t.id===e?{...t,status:"accepted"}:t))},completeOrder:e=>{let t=k.find(t=>t.id===e);if(t&&t.items.every(e=>{let t=(0,n.dU)(e.toLowerCase().replace(/\s+/g,"_"));return!!t&&(0,n.hF)(t,v)})){t.items.forEach(e=>{let t=(0,n.dU)(e.toLowerCase().replace(/\s+/g,"_"));t&&t.ingredients.forEach(e=>{B(e.name,e.quantity)})}),h(t=>t.map(t=>t.id===e?{...t,status:"completed"}:t)),d(e=>({...e,totalOrdersCompleted:e.totalOrdersCompleted+1,totalItemsBaked:e.totalItemsBaked+t.items.length}));let i=t.timeLimit>60,a=(0,n.Qb)(t.difficulty,i);L(t.reward),j(a),setTimeout(()=>T(),100)}},declineOrder:e=>{h(t=>t.filter(t=>t.id!==e))},generateNewOrder:()=>{let e=(0,n.jg)(i.level);h(t=>[...t,e])},upgradeSkill:e=>{let t=S.find(t=>t.id===e);!t||t.level>=t.maxLevel||i.skillPoints<t.cost||(P(t=>t.map(t=>t.id===e?{...t,level:t.level+1}:t)),d(e=>({...e,skillPoints:e.skillPoints-t.cost})))},checkAchievements:T,dismissLevelUp:()=>{I(!1),C([])},updateAutomationSettings:e=>{R(t=>({...t,...e}))},purchaseAutomationUpgrade:e=>{let t=c.sA.find(t=>t.id===e);t&&!(i.money<t.cost)&&x(t.cost)&&d(t=>({...t,automationUpgrades:[...t.automationUpgrades,e]}))},startAutomationJob:e=>{if(!M.enabled)return;let t=p.find(t=>t.id===e);if(!t||t.isActive||0===t.automationLevel)return;let a=(0,n.x0)(i.level),o=(0,c.tX)(a,v,M.priorityMode,k);if(!o)return;let r=(0,n.dU)(o);if(!r||!(0,n.hF)(r,v))return;let s=(0,c.XN)(t.efficiency,t.automationLevel,i.automationUpgrades),l=(0,c.Ws)(e,o,r,s);q(e=>[...e,{...l,status:"running"}]),z(e,{isActive:!0,timeRemaining:l.duration,currentRecipe:r.name}),l.ingredients.forEach(e=>{B(e.name,e.quantity)})},saveGameState:O,loadGameState:N,quickSave:U,quickLoad:F,autoSave:G},children:t})}function p(){let e=(0,o.useContext)(m);return void 0===e?{player:{level:1,experience:0,money:100,maxExperience:100,skillPoints:0,totalMoneyEarned:0,totalOrdersCompleted:0,totalItemsBaked:0,unlockedRecipes:[],automationUpgrades:[]},equipment:[],inventory:[],orders:[],achievements:[],skills:[],levelUpRewards:[],showLevelUp:!1,updateEquipment:()=>{},acceptOrder:()=>{},completeOrder:()=>{},declineOrder:()=>{},generateNewOrder:()=>{},upgradeSkill:()=>{},checkAchievements:()=>{},dismissLevelUp:()=>{},spendMoney:()=>!1,addMoney:()=>{},addExperience:()=>{},addEquipment:()=>{},removeEquipment:()=>{},addInventoryItem:()=>{},removeInventoryItem:()=>{},updateAutomationSettings:()=>{},purchaseAutomationUpgrade:()=>{},startAutomationJob:()=>{},saveGameState:async()=>!1,loadGameState:async()=>!1,quickSave:async()=>!1,quickLoad:async()=>!1,autoSave:async()=>!1}:e}},4983:(e,t,i)=>{i.d(t,{Qb:()=>r,dU:()=>l,hF:()=>s,jg:()=>n,x0:()=>c});let a=[{id:"chocolate_chip_cookies",name:"Chocolate Chip Cookies",ingredients:[{name:"Flour",quantity:2},{name:"Sugar",quantity:1},{name:"Butter",quantity:1},{name:"Chocolate Chips",quantity:1}],bakingTime:45,difficulty:1,unlockLevel:1,basePrice:25,category:"cookies"},{id:"vanilla_muffins",name:"Vanilla Muffins",ingredients:[{name:"Flour",quantity:2},{name:"Sugar",quantity:1},{name:"Eggs",quantity:1},{name:"Vanilla",quantity:1}],bakingTime:60,difficulty:1,unlockLevel:1,basePrice:20,category:"cakes"},{id:"cinnamon_rolls",name:"Cinnamon Rolls",ingredients:[{name:"Flour",quantity:3},{name:"Sugar",quantity:2},{name:"Butter",quantity:2},{name:"Eggs",quantity:1}],bakingTime:90,difficulty:2,unlockLevel:2,basePrice:35,category:"pastries"},{id:"chocolate_brownies",name:"Chocolate Brownies",ingredients:[{name:"Flour",quantity:2},{name:"Sugar",quantity:2},{name:"Butter",quantity:1},{name:"Chocolate Chips",quantity:2}],bakingTime:75,difficulty:2,unlockLevel:2,basePrice:30,category:"cakes"},{id:"sourdough_bread",name:"Sourdough Bread",ingredients:[{name:"Flour",quantity:4},{name:"Salt",quantity:1}],bakingTime:180,difficulty:3,unlockLevel:3,basePrice:45,category:"bread"}],o=["Alice Johnson","Bob Smith","Carol Davis","David Wilson","Emma Brown","Frank Miller","Grace Taylor","Henry Anderson","Ivy Thomas","Jack Martinez","Kate Garcia","Liam Rodriguez","Mia Lopez","Noah Gonzalez","Olivia Hernandez","Paul Perez","Quinn Turner","Ruby Phillips","Sam Campbell","Tina Parker"];function n(e){let t=a.filter(t=>t.unlockLevel<=e);0===t.length&&t.push(a[0]);let i=.7>Math.random()?1:.9>Math.random()?2:3,n=[];for(let e=0;e<i;e++){let e=t[Math.floor(Math.random()*t.length)];n.push(e)}let r=Math.ceil(n.reduce((e,t)=>e+t.difficulty,0)/n.length),s=n.reduce((e,t)=>e+t.basePrice,0),l=1.5+ +Math.random(),c=Math.floor(s*(.8+.4*Math.random())),u=Math.floor(n.reduce((e,t)=>e+t.bakingTime,0)*l);return{id:Date.now().toString()+Math.random().toString(36).substr(2,9),customerName:o[Math.floor(Math.random()*o.length)],items:n.map(e=>e.name),timeLimit:u,reward:c,status:"pending",difficulty:Math.min(5,r)}}function r(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],i=10*e,a=t?Math.floor(.5*i):0;return i+a}function s(e,t){return e.ingredients.every(e=>{let i=t.find(t=>t.name===e.name);return i&&i.quantity>=e.quantity})}function l(e){return a.find(t=>t.id===e)}function c(e){return a.filter(t=>t.unlockLevel<=e)}},5877:(e,t,i)=>{i.d(t,{B:()=>r});let a="1.0.0",o="bakeItOut_gameSave";class n{saveToLocal(e){try{let t={version:a,timestamp:Date.now(),...e},i=JSON.stringify(t);return localStorage.setItem(o,i),console.log("Game saved to local storage"),!0}catch(e){return console.error("Failed to save game to local storage:",e),!1}}loadFromLocal(){try{let e=localStorage.getItem(o);if(!e)return null;let t=JSON.parse(e);if(t.version!==a)return console.warn("Save version mismatch, attempting migration"),this.migrateSave(t);return console.log("Game loaded from local storage"),t}catch(e){return console.error("Failed to load game from local storage:",e),null}}deleteLocalSave(){try{return localStorage.removeItem(o),console.log("Local save deleted"),!0}catch(e){return console.error("Failed to delete local save:",e),!1}}initializeAutoSave(){this.autoSaveInterval=setInterval(()=>{this.triggerAutoSave()},3e4)}triggerAutoSave(){let e=new CustomEvent("autoSave");window.dispatchEvent(e)}stopAutoSave(){this.autoSaveInterval&&(clearInterval(this.autoSaveInterval),this.autoSaveInterval=null)}async saveToCloud(e,t){try{var i;let o={id:"".concat(t,"_").concat(Date.now()),userId:t,deviceId:this.getDeviceId(),lastModified:Date.now(),gameVersion:a,bakeryCount:(null==(i=e.bakeries)?void 0:i.length)||1,playerLevel:e.player.level};return console.log("Cloud save would be implemented here",{gameData:e,metadata:o}),!0}catch(e){return console.error("Failed to save to cloud:",e),!1}}async loadFromCloud(e){try{return console.log("Cloud load would be implemented here",{userId:e}),null}catch(e){return console.error("Failed to load from cloud:",e),null}}async syncWithCloud(e,t){try{let i=await this.loadFromCloud(t);if(!i)return await this.saveToCloud(e,t),e;if(i.timestamp>e.timestamp)return console.log("Cloud save is newer, using cloud data"),i;return console.log("Local save is newer, uploading to cloud"),await this.saveToCloud(e,t),e}catch(t){return console.error("Failed to sync with cloud:",t),e}}migrateSave(e){try{var t,i,o,n,r,s,l,c,u,m,d,p,y,v,g,k,h,f;let b={version:a,timestamp:e.timestamp||Date.now(),player:{level:(null==(t=e.player)?void 0:t.level)||1,experience:(null==(i=e.player)?void 0:i.experience)||0,money:(null==(o=e.player)?void 0:o.money)||100,skillPoints:(null==(n=e.player)?void 0:n.skillPoints)||0,totalMoneyEarned:(null==(r=e.player)?void 0:r.totalMoneyEarned)||0,totalOrdersCompleted:(null==(s=e.player)?void 0:s.totalOrdersCompleted)||0,totalItemsBaked:(null==(l=e.player)?void 0:l.totalItemsBaked)||0,unlockedRecipes:(null==(c=e.player)?void 0:c.unlockedRecipes)||["chocolate_chip_cookies","vanilla_muffins"],automationUpgrades:(null==(u=e.player)?void 0:u.automationUpgrades)||[]},equipment:e.equipment||[],inventory:e.inventory||[],achievements:e.achievements||[],skills:e.skills||[],automationSettings:e.automationSettings||{},gameSettings:{language:(null==(m=e.gameSettings)?void 0:m.language)||"en",soundEnabled:null==(g=null==(d=e.gameSettings)?void 0:d.soundEnabled)||g,musicEnabled:null==(k=null==(p=e.gameSettings)?void 0:p.musicEnabled)||k,notificationsEnabled:null==(h=null==(y=e.gameSettings)?void 0:y.notificationsEnabled)||h,autoSaveEnabled:null==(f=null==(v=e.gameSettings)?void 0:v.autoSaveEnabled)||f},bakeries:e.bakeries||[],currentBakeryId:e.currentBakeryId||"main"};return console.log("Save migrated successfully"),b}catch(e){return console.error("Failed to migrate save:",e),null}}getDeviceId(){let e=localStorage.getItem("deviceId");return e||(e="device_"+Date.now()+"_"+Math.random().toString(36).substr(2,9),localStorage.setItem("deviceId",e)),e}exportSave(e){return JSON.stringify(e,null,2)}importSave(e){try{let t=JSON.parse(e);return this.migrateSave(t)}catch(e){return console.error("Failed to import save:",e),null}}createBackup(e){try{let t="".concat(o,"_backup_").concat(Date.now());return localStorage.setItem(t,JSON.stringify(e)),this.cleanupOldBackups(),!0}catch(e){return console.error("Failed to create backup:",e),!1}}cleanupOldBackups(){let e=Object.keys(localStorage).filter(e=>e.startsWith("".concat(o,"_backup_"))).sort();for(;e.length>5;){let t=e.shift();t&&localStorage.removeItem(t)}}getBackups(){let e=[];return Object.keys(localStorage).forEach(t=>{if(t.startsWith("".concat(o,"_backup_")))try{let i=JSON.parse(localStorage.getItem(t)||"{}"),a=parseInt(t.split("_").pop()||"0");e.push({key:t,timestamp:a,data:i})}catch(e){console.error("Failed to parse backup:",e)}}),e.sort((e,t)=>t.timestamp-e.timestamp)}constructor(){this.autoSaveInterval=null,this.cloudSyncEnabled=!1,this.initializeAutoSave()}}let r=new n},7871:(e,t,i)=>{i.d(t,{Ws:()=>n,XN:()=>o,sA:()=>a,tX:()=>r});let a=[{id:"auto_queue_basic",name:"Basic Auto-Queue",description:"Equipment automatically starts the next recipe when finished",type:"intelligence",cost:500,unlockLevel:4,effects:{autoQueueing:!0}},{id:"efficiency_boost_1",name:"Efficiency Boost I",description:"Automated equipment uses 10% fewer ingredients",type:"efficiency",cost:750,unlockLevel:5,effects:{efficiencyBonus:.1}},{id:"speed_boost_1",name:"Speed Boost I",description:"Automated equipment works 15% faster",type:"speed",cost:1e3,unlockLevel:6,effects:{speedMultiplier:1.15}},{id:"smart_prioritization",name:"Smart Prioritization",description:"Automation prioritizes orders based on profit and urgency",type:"intelligence",cost:1500,unlockLevel:8,effects:{smartPrioritization:!0}},{id:"efficiency_boost_2",name:"Efficiency Boost II",description:"Automated equipment uses 20% fewer ingredients",type:"efficiency",cost:2e3,unlockLevel:10,effects:{efficiencyBonus:.2}},{id:"speed_boost_2",name:"Speed Boost II",description:"Automated equipment works 30% faster",type:"speed",cost:2500,unlockLevel:12,effects:{speedMultiplier:1.3}}];function o(e,t,i){let o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,n=e;return n*=1+.1*t,i.forEach(e=>{let t=a.find(t=>t.id===e);(null==t?void 0:t.effects.efficiencyBonus)&&(n*=1+t.effects.efficiencyBonus)}),Math.min(n*=1+o,2)}function n(e,t,i,a){let o=Math.floor(i.bakingTime/a),n=i.ingredients.map(e=>({...e,quantity:Math.ceil(e.quantity*(1-(a-1)*.1))}));return{id:Date.now().toString()+Math.random().toString(36).substr(2,9),equipmentId:e,recipeId:t,startTime:Date.now(),duration:o,status:"queued",ingredients:n,efficiency:a}}function r(e,t,i,a){let o=e.filter(e=>e.ingredients.every(e=>{let i=t.find(t=>t.name===e.name);return i&&i.quantity>=e.quantity}));if(0===o.length)return null;switch(i){case"speed":return o.reduce((e,t)=>t.bakingTime<e.bakingTime?t:e).id;case"profit":return o.reduce((e,t)=>t.basePrice>e.basePrice?t:e).id;case"efficiency":let n=a.flatMap(e=>e.items),r=o.filter(e=>n.includes(e.name));if(r.length>0)return r[0].id;return o[0].id;default:return o[0].id}}},9283:(e,t,i)=>{i.d(t,{LanguageProvider:()=>s,o:()=>l});var a=i(5155),o=i(2115);let n=(0,o.createContext)(void 0),r={en:{"game.title":"Bake It Out","game.subtitle":"Master the art of bakery management in this engaging multiplayer game. Complete orders, unlock recipes, automate your processes, and compete with friends!","game.play":"\uD83C\uDFAE Start Playing","game.singlePlayer":"\uD83C\uDFAE Single Player","game.singlePlayerDesc":"Play solo and master your bakery skills","game.multiplayer":"\uD83D\uDC65 Multiplayer","game.multiplayerDesc":"Play with friends in cooperative or competitive modes","game.english":"\uD83C\uDDFA\uD83C\uDDF8 English","game.czech":"\uD83C\uDDE8\uD83C\uDDFF Čeština","game.home":"\uD83C\uDFE0 Home","game.close":"✕ Close","game.continue":"\uD83D\uDE80 Continue Playing","menu.singlePlayer":"Single Player","menu.multiplayer":"Multiplayer","menu.settings":"Settings","menu.credits":"Credits","menu.exit":"Exit","credits.title":"About Bake It Out","credits.subtitle":"Game Information & Credits","credits.description":"A multiplayer bakery management game with real-time collaboration and localization support.","credits.version":"Version","credits.release":"Release Date","credits.releaseDate":"January 2025","credits.platform":"Platform","credits.platforms":"Windows, macOS, Linux","credits.close":"Close","credits.features":"Features","credits.multiplayer":"Multiplayer Support","credits.multiplayerDesc":"Real-time collaboration with friends in cooperative or competitive modes.","credits.localization":"Localization","credits.localizationDesc":"Full support for English and Czech languages with easy switching.","credits.progression":"Progression System","credits.progressionDesc":"Level up your bakery, unlock recipes, and master automation.","credits.automation":"Automation","credits.automationDesc":"Advanced automation systems to optimize your bakery operations.","credits.technology":"Technology Stack","credits.team":"Development Team","credits.developedBy":"Developed by","credits.teamDesc":"Created with passion by the Bake It Out development team.","credits.thanks":"Special Thanks","credits.thanksPlayers":"All our amazing players and beta testers","credits.thanksTranslators":"Community translators for localization support","credits.thanksOpenSource":"Open source community for incredible tools and libraries","credits.thanksBakers":"Real bakers who inspired this game","credits.contact":"Contact & Support","credits.website":"Website","credits.support":"Support","credits.github":"GitHub","credits.discord":"Discord","menu.newGame":"New Game","menu.continueGame":"Continue Game","menu.loadGame":"Load Game","menu.selectLanguage":"Select Language","menu.about":"About","menu.help":"Help","menu.quit":"Quit","features.manage.title":"Manage Your Bakery","features.manage.description":"Take orders, bake delicious goods, and serve happy customers","features.levelup.title":"Level Up & Automate","features.levelup.description":"Unlock new recipes, buy equipment, and automate your processes","features.multiplayer.title":"Play Together","features.multiplayer.description":"Cooperative and competitive multiplayer modes with friends","status.development":"\uD83D\uDEA7 Game in Development - Phase 5: Multilayer Support! \uD83D\uDEA7","ui.level":"Level {{level}}","ui.money":"${{amount}}","ui.experience":"XP: {{current}}/{{max}}","ui.skillPoints":"SP: {{points}}","ui.achievements":"\uD83C\uDFC6 Achievements","ui.skills":"\uD83C\uDF1F Skills","ui.automation":"\uD83E\uDD16 Automation","kitchen.title":"\uD83C\uDFEA Kitchen","kitchen.clickToUse":"Click to use","kitchen.making":"Making: {{recipe}}","kitchen.timeRemaining":"Time: {{time}}","inventory.title":"\uD83D\uDCE6 Inventory","inventory.quantity":"Qty: {{qty}}","inventory.cost":"${{cost}} each","orders.title":"\uD83D\uDCCB Orders","orders.newOrder":"+ New Order","orders.accept":"Accept","orders.decline":"Decline","orders.complete":"Complete","orders.inProgress":"In Progress","orders.timeLimit":"Time: {{time}}","orders.reward":"${{amount}}","orders.customer":"Customer: {{name}}","actions.title":"⚡ Quick Actions","actions.buyIngredients":"\uD83D\uDED2 Buy Ingredients","actions.viewRecipes":"\uD83D\uDCD6 View Recipes","actions.equipmentShop":"\uD83D\uDD27 Equipment Shop","modal.recipes.title":"\uD83D\uDCD6 Recipe Book","modal.shop.title":"\uD83D\uDED2 Ingredient Shop","modal.baking.title":"\uD83D\uDD25 {{equipment}} - Select Recipe","modal.achievements.title":"\uD83C\uDFC6 Achievements","modal.skills.title":"\uD83C\uDF1F Skill Tree","modal.automation.title":"\uD83E\uDD16 Automation Control","modal.equipmentShop.title":"\uD83C\uDFEA Equipment Shop","modal.settings.title":"⚙️ Settings","modal.bakeries.title":"\uD83C\uDFEA Bakery Manager","modal.levelUp.title":"Level Up!","modal.levelUp.subtitle":"You reached Level {{level}}!","recipes.all":"All","recipes.cookies":"Cookies","recipes.cakes":"Cakes","recipes.bread":"Bread","recipes.pastries":"Pastries","recipes.ingredients":"Ingredients:","recipes.difficulty":"Difficulty:","recipes.time":"Time:","recipes.canCraft":"✅ Can Craft","recipes.unlockLevel":"Unlocked at Level {{level}}","recipes.noRecipes":"No recipes available in this category.","recipes.levelUpToUnlock":"Level up to unlock more recipes!","shop.currentStock":"Current stock: {{quantity}}","shop.buy":"Buy","shop.tooExpensive":"Too Expensive","shop.tips.title":"\uD83D\uDCA1 Shopping Tips","shop.tips.bulk":"• Buy ingredients in bulk to save time","shop.tips.stock":"• Keep an eye on your stock levels","shop.tips.rare":"• Some recipes require rare ingredients","shop.tips.prices":"• Prices may vary based on availability","baking.selectRecipe":"Select Recipe","baking.noRecipes":"No recipes available","baking.noIngredients":"You don't have enough ingredients to craft any recipes.","baking.buyIngredients":"Buy Ingredients","baking.startBaking":"\uD83D\uDD25 Start Baking","baking.instructions":"\uD83D\uDCCB Baking Instructions for {{recipe}}","baking.expectedReward":"Expected reward: ${{amount}}","baking.makesSure":"Make sure you have all ingredients before starting!","baking.inProgress":"Baking in progress...","baking.completed":"Baking completed!","baking.cancelled":"Baking cancelled","baking.timeRemaining":"Time remaining: {{time}}","baking.clickToCollect":"Click to collect","achievements.completed":"{{completed}} of {{total}} achievements completed","achievements.overallProgress":"Overall Progress","achievements.progress":"Progress","achievements.reward":"Reward:","achievements.noAchievements":"No achievements in this category.","skills.availablePoints":"Available Skill Points: {{points}}","skills.efficiency":"Efficiency","skills.automation":"Automation","skills.quality":"Quality","skills.business":"Business","skills.effects":"Effects:","skills.requires":"Requires: {{requirements}}","skills.requiresLevel":"Requires Level {{level}}","skills.maxed":"✅ Maxed","skills.upgrade":"⬆️ Upgrade ({{cost}} SP)","skills.locked":"\uD83D\uDD12 Locked","skills.noSkills":"No skills in this category.","skills.tips.title":"\uD83D\uDCA1 Skill Tips","skills.tips.earnPoints":"• Earn skill points by leveling up (1 point every 2 levels)","skills.tips.prerequisites":"• Some skills require other skills to be unlocked first","skills.tips.playstyle":"• Focus on skills that match your playstyle","skills.tips.efficiency":"• Efficiency skills help with resource management","automation.masterControl":"\uD83C\uDF9B️ Master Control","automation.enableAutomation":"Enable Automation","automation.autoStart":"Auto-start Equipment","automation.priorityMode":"\uD83C\uDFAF Priority Mode","automation.efficiency":"Efficiency (Orders First)","automation.profit":"Profit (Highest Value)","automation.speed":"Speed (Fastest Recipes)","automation.priorityDescription":"How automation chooses what to bake","automation.performance":"⚡ Performance","automation.maxJobs":"Max Concurrent Jobs: {{jobs}}","automation.safety":"\uD83D\uDEE1️ Safety","automation.stopWhenLow":"Stop when ingredients below: {{threshold}}","automation.upgrades":"\uD83D\uDCA1 Automation Upgrades","automation.upgradesDescription":"Improve your automation efficiency, speed, and intelligence with these upgrades.","automation.purchase":"Purchase","automation.noUpgrades":"No upgrades available at your current level.","automation.levelUpForUpgrades":"Level up to unlock more automation upgrades!","automation.automatedEquipment":"Automated Equipment","automation.activeUpgrades":"Active Upgrades","automation.automationStatus":"Automation Status","automation.equipmentStatus":"\uD83C\uDFED Equipment Status","automation.running":"Running","automation.idle":"Idle","automation.noAutomatedEquipment":"No automated equipment available.","automation.purchaseAutoEquipment":"Purchase auto-equipment from the shop to get started!","equipmentShop.upgradeYourBakery":"Upgrade your bakery with professional equipment","equipmentShop.basic":"Basic","equipmentShop.automated":"Automated","equipmentShop.advanced":"Advanced","equipmentShop.efficiency":"Efficiency: {{efficiency}}x","equipmentShop.automation":"Automation:","equipmentShop.unlockLevel":"Unlock Level: {{level}}","equipmentShop.purchase":"\uD83D\uDCB0 Purchase","equipmentShop.noEquipment":"No equipment available in this category.","equipmentShop.levelUpForEquipment":"Level up to unlock more equipment!","equipmentShop.tips.title":"\uD83D\uDCA1 Equipment Tips","equipmentShop.tips.automated":"• Automated equipment can run without your supervision","equipmentShop.tips.efficiency":"• Higher efficiency means faster production and better quality","equipmentShop.tips.conveyor":"• Conveyor belts connect equipment for seamless workflow","equipmentShop.tips.advanced":"• Advanced equipment unlocks at higher levels","levelUp.levelRewards":"\uD83C\uDF81 Level Rewards","levelUp.whatsNext":"\uD83D\uDCA1 What's Next?","levelUp.checkRecipes":"• Check out new recipes in your recipe book","levelUp.visitShop":"• Visit the shop for new equipment","levelUp.challengingOrders":"• Take on more challenging orders","levelUp.investSkills":"• Invest in skill upgrades","settings.title":"⚙️ Settings","settings.general":"General","settings.audio":"Audio","settings.graphics":"Graphics","settings.save":"Save & Data","settings.language":"\uD83C\uDF0D Language","settings.gameplay":"\uD83C\uDFAE Gameplay","settings.notifications":"Enable Notifications","settings.tutorials":"Show Tutorials","settings.animationSpeed":"Animation Speed","settings.sound":"Sound Effects","settings.music":"Background Music","settings.quality":"\uD83C\uDFA8 Graphics Quality","settings.autoSave":"\uD83D\uDCBE Auto-Save","settings.enableAutoSave":"Enable Auto-Save","settings.dataManagement":"\uD83D\uDCC1 Data Management","settings.exportSave":"\uD83D\uDCE4 Export Save","settings.importSave":"\uD83D\uDCE5 Import Save","settings.cloudSync":"☁️ Cloud Sync","settings.cloudSyncDescription":"Cloud sync allows you to save your progress online and play across multiple devices.","settings.comingSoon":"Coming Soon","bakeries.title":"\uD83C\uDFEA Bakery Manager","bakeries.subtitle":"Manage your bakery empire","bakeries.owned":"My Bakeries","bakeries.available":"Available","bakeries.current":"Current","bakeries.level":"Level","bakeries.specialization":"Specialization","bakeries.equipment":"Equipment","bakeries.orders":"Active Orders","bakeries.switchTo":"Switch To","bakeries.noOwned":"You don't own any bakeries yet.","bakeries.purchase":"\uD83D\uDCB0 Purchase","bakeries.tooExpensive":"\uD83D\uDCB8 Too Expensive","bakeries.allOwned":"You own all available bakeries!","bakeries.tips":"\uD83D\uDCA1 Bakery Tips","bakeries.tip1":"Each bakery specializes in different products for bonus efficiency","bakeries.tip2":"Switch between bakeries to manage multiple locations","bakeries.tip3":"Specialized bakeries attract customers looking for specific items","bakeries.tip4":"Upgrade each bakery independently for maximum profit","notifications.orderAccepted":"Order Accepted","notifications.orderAcceptedMessage":"You have accepted a new order!","notifications.orderCompleted":"Order Completed!","notifications.orderCompletedMessage":"You earned ${{reward}} and gained experience!","notifications.orderDeclined":"Order Declined","notifications.orderDeclinedMessage":"Order has been removed from your queue.","notifications.bakeryPurchased":"Bakery Purchased!","notifications.bakeryPurchasedMessage":"You now own {{name}}!","notifications.bakerySwitched":"Bakery Switched","notifications.bakerySwitchedMessage":"Switched to {{name}}","common.accept":"Accept","common.decline":"Decline","common.complete":"Complete","common.purchase":"Purchase","common.upgrade":"Upgrade","common.cancel":"Cancel","common.confirm":"Confirm","common.save":"Save","common.load":"Load","common.delete":"Delete","common.edit":"Edit","common.back":"Back","common.next":"Next","common.previous":"Previous","common.yes":"Yes","common.no":"No","common.create":"Create","common.join":"Join","common.leave":"Leave","common.start":"Start","common.ready":"Ready","common.notReady":"Not Ready","common.send":"Send","common.refresh":"Refresh","common.retry":"Retry","common.reset":"Reset","common.clear":"Clear","common.apply":"Apply","common.warning":"Warning","common.info":"Information","common.success":"Success","common.error":"Error","saveLoad.saveDesc":"Choose a slot to save your progress","saveLoad.loadDesc":"Select a save file to load","saveLoad.saveName":"Save Name","saveLoad.emptySlot":"Empty Slot","saveLoad.selectedSaveSlot":"Selected: Slot {{slot}}","saveLoad.selectedLoadSlot":"Selected: Slot {{slot}}","saveLoad.confirmOverwrite":"Overwrite Save?","saveLoad.overwriteWarning":"This will overwrite the existing save. This action cannot be undone.","saveLoad.overwrite":"Overwrite","saveLoad.fileSlots":"File Slots","saveLoad.gameSlots":"Game Slots","saveLoad.exportSave":"Export Save","saveLoad.importSave":"Import Save","saveLoad.deleteConfirm":"Delete Save?","saveLoad.deleteWarning":"This will permanently delete this save file. This action cannot be undone.","saveLoad.delete":"Delete","gameMenu.title":"Game Menu","gameMenu.subtitle":"Manage your game","gameMenu.resume":"Resume Game","gameMenu.resumeDesc":"Continue playing","gameMenu.save":"Save Game","gameMenu.saveDesc":"Save your progress","gameMenu.load":"Load Game","gameMenu.loadDesc":"Load saved progress","gameMenu.settings":"Settings","gameMenu.settingsDesc":"Game preferences","gameMenu.mainMenu":"Main Menu","gameMenu.mainMenuDesc":"Return to main menu","gameMenu.exit":"Exit Game","gameMenu.exitDesc":"Close the application","gameMenu.tip":"Press ESC to open this menu anytime","settings.discord":"Discord","settings.discordRichPresence":"Discord Rich Presence","settings.discordDescription":"Show your current game status and activity in Discord.","settings.enableDiscordRPC":"Enable Discord Rich Presence","settings.discordConnected":"✅ Connected to Discord","settings.discordDisconnected":"❌ Not connected to Discord","settings.discordInfo":"What is Discord Rich Presence?","settings.discordInfoDesc1":"Discord Rich Presence shows your friends what you're doing in Bake It Out:","settings.discordFeature1":"Your current level and money","settings.discordFeature2":"What you're currently baking","settings.discordFeature3":"Multiplayer room information","settings.discordFeature4":"How long you've been playing","settings.discordInfoDesc2":"Your friends can even join your multiplayer games directly from Discord!","settings.discordTroubleshooting":"Discord Not Connected","settings.discordTrouble1":"Make sure Discord is running on your computer.","settings.discordTrouble2":"Discord Rich Presence only works in the desktop version of the game.","settings.discordTrouble3":"Try restarting both Discord and the game if the connection fails.","settings.discordPrivacy":"Privacy Information","settings.discordPrivacyDesc1":"Discord Rich Presence only shares:","settings.discordPrivacy1":"Your current game activity (public)","settings.discordPrivacy2":"Your player level and progress (public)","settings.discordPrivacy3":"Multiplayer room codes (for joining)","settings.discordPrivacyDesc2":"No personal information or save data is shared with Discord.","settings.discordStatus":"Discord Status","settings.discordInitializing":"\uD83D\uDD04 Initializing Discord RPC...","settings.discordRetrying":"\uD83D\uDD04 Retrying connection...","settings.discordUnavailable":"❌ Discord not available","settings.discordDesktopOnly":"ℹ️ Discord RPC only available in desktop version","error.general":"An error occurred","error.saveLoad":"Failed to save/load game","error.connection":"Connection error","error.fileNotFound":"File not found","error.invalidData":"Invalid data format","error.permissionDenied":"Permission denied","status.loading":"Loading...","status.saving":"Saving...","status.connecting":"Connecting...","status.ready":"Ready","status.success":"Success!","status.failed":"Failed","status.offline":"Offline","status.online":"Online","ui.placeholder":"Enter text...","ui.search":"Search","ui.filter":"Filter","ui.sort":"Sort","ui.ascending":"Ascending","ui.descending":"Descending","ui.selectAll":"Select All","ui.deselectAll":"Deselect All","ui.noResults":"No results found","ui.noData":"No data available","ui.loading":"Loading...","ui.saving":"Saving...","ui.saved":"Saved!","ui.failed":"Failed","ui.retry":"Retry","ui.back":"Back","ui.forward":"Forward","ui.home":"Home","ui.menu":"Menu","ui.options":"Options","ui.preferences":"Preferences","multiplayer.lobby":"\uD83D\uDC65 Multiplayer Lobby","multiplayer.connected":"\uD83D\uDFE2 Connected","multiplayer.disconnected":"\uD83D\uDD34 Disconnected","multiplayer.createRoom":"Create Room","multiplayer.joinRoom":"Join Room","multiplayer.room":"Room","multiplayer.yourName":"Your Name","multiplayer.enterName":"Enter your name","multiplayer.roomName":"Room Name","multiplayer.enterRoomName":"Enter room name","multiplayer.gameMode":"Game Mode","multiplayer.cooperative":"\uD83E\uDD1D Cooperative","multiplayer.competitive":"⚔️ Competitive","multiplayer.maxPlayers":"Max Players: {{count}}","multiplayer.roomId":"Room ID","multiplayer.enterRoomId":"Enter room ID","multiplayer.players":"Players ({{count}})","multiplayer.host":"HOST","multiplayer.level":"Level {{level}}","multiplayer.chat":"Chat","multiplayer.typeMessage":"Type a message...","multiplayer.gameTime":"Game Time: {{time}}","multiplayer.teamStats":"\uD83D\uDCCA Team Stats","multiplayer.ordersCompleted":"Orders Completed:","multiplayer.totalRevenue":"Total Revenue:","multiplayer.teamExperience":"Team Experience:","multiplayer.sharedKitchen":"\uD83C\uDFEA Shared Kitchen","multiplayer.sharedOrders":"\uD83D\uDCCB Shared Orders","multiplayer.sharedInventory":"\uD83D\uDCE6 Shared Inventory","multiplayer.contribution":"Contribution:","multiplayer.online":"\uD83D\uDFE2 Online","multiplayer.status":"Status:","multiplayer.you":"(You)","multiplayer.teamChat":"\uD83D\uDCAC Team Chat","multiplayer.chatPlaceholder":"Chat messages will appear here...","multiplayer.mode.cooperative.description":"\uD83E\uDD1D Cooperative Mode: Work together to complete orders and grow your shared bakery!","multiplayer.mode.competitive.description":"⚔️ Competitive Mode: Compete against other players to complete the most orders!","multiplayer.game.title":"\uD83C\uDFAE Multiplayer Game - {{roomName}}","multiplayer.game.mode":"Mode: {{mode}}","multiplayer.game.playersCount":"Players: {{count}}","multiplayer.game.playing":"\uD83D\uDFE2 Playing","multiplayer.game.leaveGame":"\uD83D\uDEAA Leave Game","multiplayer.game.tabs.game":"Game","multiplayer.game.tabs.players":"Players","multiplayer.game.tabs.chat":"Chat","multiplayer.create.title":"\uD83C\uDFD7️ Create Room","multiplayer.join.title":"\uD83D\uDEAA Join Room","multiplayer.room.info":"Mode: {{mode}} • Players: {{current}}/{{max}}","multiplayer.room.readyUp":"✅ Ready","multiplayer.room.notReady":"⏳ Not Ready","multiplayer.room.startGame":"\uD83D\uDE80 Start Game","multiplayer.room.leaveRoom":"\uD83D\uDEAA Leave","multiplayer.connection.connecting":"Connecting...","multiplayer.connection.reconnecting":"Reconnecting...","multiplayer.connection.failed":"Connection failed","multiplayer.connection.error":"⚠️ {{error}}","multiplayer.system.playerJoined":"{{name}} joined the room","multiplayer.system.playerLeft":"{{name}} left the room","multiplayer.system.gameStarted":"Game started!","multiplayer.system.gameEnded":"Game ended!","multiplayer.system.roomCreated":"Room created successfully","multiplayer.system.roomJoined":"Joined room successfully"},cs:{"game.title":"Bake It Out","game.subtitle":"Ovl\xe1dněte uměn\xed ř\xedzen\xed pek\xe1rny v t\xe9to poutav\xe9 multiplayerov\xe9 hře. Plňte objedn\xe1vky, odemykejte recepty, automatizujte procesy a soutěžte s př\xe1teli!","game.play":"\uD83C\uDFAE Zač\xedt hr\xe1t","game.singlePlayer":"\uD83C\uDFAE Jeden hr\xe1č","game.singlePlayerDesc":"Hrajte s\xf3lo a zdokonalte sv\xe9 pekařsk\xe9 dovednosti","game.multiplayer":"\uD83D\uDC65 Multiplayer","game.multiplayerDesc":"Hrajte s př\xe1teli v kooperativn\xedch nebo soutěžn\xedch režimech","game.english":"\uD83C\uDDFA\uD83C\uDDF8 English","game.czech":"\uD83C\uDDE8\uD83C\uDDFF Čeština","game.home":"\uD83C\uDFE0 Domů","game.close":"✕ Zavř\xedt","game.continue":"\uD83D\uDE80 Pokračovat ve hře","menu.singlePlayer":"Jeden hr\xe1č","menu.multiplayer":"Multiplayer","menu.settings":"Nastaven\xed","menu.credits":"Titulky","menu.exit":"Ukončit","credits.title":"O hře Bake It Out","credits.subtitle":"Informace o hře a titulky","credits.description":"Multiplayerov\xe1 hra na spr\xe1vu pek\xe1rny s real-time spoluprac\xed a podporou lokalizace.","credits.version":"Verze","credits.release":"Datum vyd\xe1n\xed","credits.releaseDate":"Leden 2025","credits.platform":"Platforma","credits.platforms":"Windows, macOS, Linux","credits.close":"Zavř\xedt","credits.features":"Funkce","credits.multiplayer":"Podpora multiplayeru","credits.multiplayerDesc":"Real-time spolupr\xe1ce s př\xe1teli v kooperativn\xedch nebo soutěžn\xedch režimech.","credits.localization":"Lokalizace","credits.localizationDesc":"Pln\xe1 podpora angličtiny a češtiny s jednoduch\xfdm přep\xedn\xe1n\xedm.","credits.progression":"Syst\xe9m postupu","credits.progressionDesc":"Vylepšujte svou pek\xe1rnu, odemykejte recepty a ovl\xe1dněte automatizaci.","credits.automation":"Automatizace","credits.automationDesc":"Pokročil\xe9 automatizačn\xed syst\xe9my pro optimalizaci provozu pek\xe1rny.","credits.technology":"Technologick\xfd stack","credits.team":"V\xfdvojov\xfd t\xfdm","credits.developedBy":"Vyvinuto t\xfdmem","credits.teamDesc":"Vytvořeno s l\xe1skou v\xfdvojov\xfdm t\xfdmem Bake It Out.","credits.thanks":"Speci\xe1ln\xed poděkov\xe1n\xed","credits.thanksPlayers":"Všem našim \xfažasn\xfdm hr\xe1čům a beta testerům","credits.thanksTranslators":"Komunitn\xedm překladatelům za podporu lokalizace","credits.thanksOpenSource":"Open source komunitě za neuvěřiteln\xe9 n\xe1stroje a knihovny","credits.thanksBakers":"Skutečn\xfdm pekařům, kteř\xed inspirovali tuto hru","credits.contact":"Kontakt a podpora","credits.website":"Webov\xe9 str\xe1nky","credits.support":"Podpora","credits.github":"GitHub","credits.discord":"Discord","menu.newGame":"Nov\xe1 hra","menu.continueGame":"Pokračovat ve hře","menu.loadGame":"Nač\xedst hru","menu.selectLanguage":"Vybrat jazyk","menu.about":"O hře","menu.help":"N\xe1pověda","menu.quit":"Ukončit","features.manage.title":"Spravujte svou pek\xe1rnu","features.manage.description":"Přij\xedmejte objedn\xe1vky, pečte lahodn\xe9 v\xfdrobky a obsluhujte spokojen\xe9 z\xe1kazn\xedky","features.levelup.title":"Postupujte a automatizujte","features.levelup.description":"Odemykejte nov\xe9 recepty, kupujte vybaven\xed a automatizujte sv\xe9 procesy","features.multiplayer.title":"Hrajte společně","features.multiplayer.description":"Kooperativn\xed a soutěžn\xed multiplayerov\xe9 režimy s př\xe1teli","status.development":"\uD83D\uDEA7 Hra ve v\xfdvoji - F\xe1ze 5: V\xedcevrstv\xe1 podpora! \uD83D\uDEA7","ui.level":"\xdaroveň {{level}}","ui.money":"{{amount}} Kč","ui.experience":"XP: {{current}}/{{max}}","ui.skillPoints":"SP: {{points}}","ui.achievements":"\uD83C\uDFC6 \xdaspěchy","ui.skills":"\uD83C\uDF1F Dovednosti","ui.automation":"\uD83E\uDD16 Automatizace","kitchen.title":"\uD83C\uDFEA Kuchyně","kitchen.clickToUse":"Klikněte pro použit\xed","kitchen.making":"Připravuje: {{recipe}}","kitchen.timeRemaining":"Čas: {{time}}","inventory.title":"\uD83D\uDCE6 Sklad","inventory.quantity":"Množstv\xed: {{qty}}","inventory.cost":"{{cost}} Kč za kus","orders.title":"\uD83D\uDCCB Objedn\xe1vky","orders.newOrder":"+ Nov\xe1 objedn\xe1vka","orders.accept":"Přijmout","orders.decline":"Odm\xedtnout","orders.complete":"Dokončit","orders.inProgress":"Prob\xedh\xe1","orders.timeLimit":"Čas: {{time}}","orders.reward":"{{amount}} Kč","orders.customer":"Z\xe1kazn\xedk: {{name}}","actions.title":"⚡ Rychl\xe9 akce","actions.buyIngredients":"\uD83D\uDED2 Koupit suroviny","actions.viewRecipes":"\uD83D\uDCD6 Zobrazit recepty","actions.equipmentShop":"\uD83D\uDD27 Obchod s vybaven\xedm","modal.recipes.title":"\uD83D\uDCD6 Kniha receptů","modal.shop.title":"\uD83D\uDED2 Obchod se surovinami","modal.baking.title":"\uD83D\uDD25 {{equipment}} - Vyberte recept","modal.achievements.title":"\uD83C\uDFC6 \xdaspěchy","modal.skills.title":"\uD83C\uDF1F Strom dovednost\xed","modal.automation.title":"\uD83E\uDD16 Ovl\xe1d\xe1n\xed automatizace","modal.equipmentShop.title":"\uD83C\uDFEA Obchod s vybaven\xedm","modal.settings.title":"⚙️ Nastaven\xed","modal.bakeries.title":"\uD83C\uDFEA Spr\xe1vce pek\xe1ren","modal.levelUp.title":"Postup na vyšš\xed \xfaroveň!","modal.levelUp.subtitle":"Dos\xe1hli jste \xfarovně {{level}}!","recipes.all":"Vše","recipes.cookies":"Sušenky","recipes.cakes":"Dorty","recipes.bread":"Chl\xe9b","recipes.pastries":"Pečivo","recipes.ingredients":"Suroviny:","recipes.difficulty":"Obt\xedžnost:","recipes.time":"Čas:","recipes.canCraft":"✅ Lze vyrobit","recipes.unlockLevel":"Odemčeno na \xfarovni {{level}}","recipes.noRecipes":"V t\xe9to kategorii nejsou k dispozici ž\xe1dn\xe9 recepty.","recipes.levelUpToUnlock":"Postupte na vyšš\xed \xfaroveň pro odemčen\xed dalš\xedch receptů!","shop.currentStock":"Aktu\xe1ln\xed z\xe1soba: {{quantity}}","shop.buy":"Koupit","shop.tooExpensive":"Př\xedliš drah\xe9","shop.tips.title":"\uD83D\uDCA1 Tipy pro nakupov\xe1n\xed","shop.tips.bulk":"• Kupujte suroviny ve velk\xe9m množstv\xed pro \xfasporu času","shop.tips.stock":"• Sledujte \xfaroveň sv\xfdch z\xe1sob","shop.tips.rare":"• Někter\xe9 recepty vyžaduj\xed vz\xe1cn\xe9 suroviny","shop.tips.prices":"• Ceny se mohou lišit podle dostupnosti","baking.selectRecipe":"Vyberte recept","baking.noRecipes":"Ž\xe1dn\xe9 recepty k dispozici","baking.noIngredients":"Nem\xe1te dostatek surovin pro v\xfdrobu jak\xe9hokoli receptu.","baking.buyIngredients":"Koupit suroviny","baking.startBaking":"\uD83D\uDD25 Zač\xedt p\xe9ct","baking.instructions":"\uD83D\uDCCB Pokyny pro pečen\xed {{recipe}}","baking.expectedReward":"Oček\xe1van\xe1 odměna: {{amount}} Kč","baking.makesSure":"Ujistěte se, že m\xe1te všechny suroviny před zač\xe1tkem!","baking.inProgress":"Pečen\xed prob\xedh\xe1...","baking.completed":"Pečen\xed dokončeno!","baking.cancelled":"Pečen\xed zrušeno","baking.timeRemaining":"Zb\xfdvaj\xedc\xed čas: {{time}}","baking.clickToCollect":"Klikněte pro vyzvednut\xed","achievements.completed":"{{completed}} z {{total}} \xfaspěchů dokončeno","achievements.overallProgress":"Celkov\xfd pokrok","achievements.progress":"Pokrok","achievements.reward":"Odměna:","achievements.noAchievements":"V t\xe9to kategorii nejsou ž\xe1dn\xe9 \xfaspěchy.","skills.availablePoints":"Dostupn\xe9 body dovednost\xed: {{points}}","skills.efficiency":"Efektivita","skills.automation":"Automatizace","skills.quality":"Kvalita","skills.business":"Podnik\xe1n\xed","skills.effects":"Efekty:","skills.requires":"Vyžaduje: {{requirements}}","skills.requiresLevel":"Vyžaduje \xfaroveň {{level}}","skills.maxed":"✅ Maxim\xe1ln\xed","skills.upgrade":"⬆️ Vylepšit ({{cost}} SP)","skills.locked":"\uD83D\uDD12 Uzamčeno","skills.noSkills":"V t\xe9to kategorii nejsou ž\xe1dn\xe9 dovednosti.","skills.tips.title":"\uD83D\uDCA1 Tipy pro dovednosti","skills.tips.earnPoints":"• Z\xedsk\xe1vejte body dovednost\xed postupem na vyšš\xed \xfaroveň (1 bod každ\xe9 2 \xfarovně)","skills.tips.prerequisites":"• Někter\xe9 dovednosti vyžaduj\xed nejprve odemčen\xed jin\xfdch dovednost\xed","skills.tips.playstyle":"• Zaměřte se na dovednosti, kter\xe9 odpov\xeddaj\xed vašemu stylu hry","skills.tips.efficiency":"• Dovednosti efektivity pom\xe1haj\xed se spr\xe1vou zdrojů","automation.masterControl":"\uD83C\uDF9B️ Hlavn\xed ovl\xe1d\xe1n\xed","automation.enableAutomation":"Povolit automatizaci","automation.autoStart":"Automatick\xe9 spuštěn\xed vybaven\xed","automation.priorityMode":"\uD83C\uDFAF Režim priority","automation.efficiency":"Efektivita (objedn\xe1vky prvn\xed)","automation.profit":"Zisk (nejvyšš\xed hodnota)","automation.speed":"Rychlost (nejrychlejš\xed recepty)","automation.priorityDescription":"Jak automatizace vyb\xedr\xe1, co p\xe9ct","automation.performance":"⚡ V\xfdkon","automation.maxJobs":"Max současn\xfdch \xfaloh: {{jobs}}","automation.safety":"\uD83D\uDEE1️ Bezpečnost","automation.stopWhenLow":"Zastavit, když suroviny klesnou pod: {{threshold}}","automation.upgrades":"\uD83D\uDCA1 Vylepšen\xed automatizace","automation.upgradesDescription":"Vylepšete efektivitu, rychlost a inteligenci vaš\xed automatizace.","automation.purchase":"Koupit","automation.noUpgrades":"Na vaš\xed současn\xe9 \xfarovni nejsou k dispozici ž\xe1dn\xe1 vylepšen\xed.","automation.levelUpForUpgrades":"Postupte na vyšš\xed \xfaroveň pro odemčen\xed dalš\xedch vylepšen\xed automatizace!","automation.automatedEquipment":"Automatizovan\xe9 vybaven\xed","automation.activeUpgrades":"Aktivn\xed vylepšen\xed","automation.automationStatus":"Stav automatizace","automation.equipmentStatus":"\uD83C\uDFED Stav vybaven\xed","automation.running":"Běž\xed","automation.idle":"Nečinn\xe9","automation.noAutomatedEquipment":"Ž\xe1dn\xe9 automatizovan\xe9 vybaven\xed k dispozici.","automation.purchaseAutoEquipment":"Kupte si auto-vybaven\xed z obchodu pro zač\xe1tek!","equipmentShop.upgradeYourBakery":"Vylepšete svou pek\xe1rnu profesion\xe1ln\xedm vybaven\xedm","equipmentShop.basic":"Z\xe1kladn\xed","equipmentShop.automated":"Automatizovan\xe9","equipmentShop.advanced":"Pokročil\xe9","equipmentShop.efficiency":"Efektivita: {{efficiency}}x","equipmentShop.automation":"Automatizace:","equipmentShop.unlockLevel":"\xdaroveň odemčen\xed: {{level}}","equipmentShop.purchase":"\uD83D\uDCB0 Koupit","equipmentShop.noEquipment":"V t\xe9to kategorii nen\xed k dispozici ž\xe1dn\xe9 vybaven\xed.","equipmentShop.levelUpForEquipment":"Postupte na vyšš\xed \xfaroveň pro odemčen\xed dalš\xedho vybaven\xed!","equipmentShop.tips.title":"\uD83D\uDCA1 Tipy pro vybaven\xed","equipmentShop.tips.automated":"• Automatizovan\xe9 vybaven\xed může běžet bez vašeho dohledu","equipmentShop.tips.efficiency":"• Vyšš\xed efektivita znamen\xe1 rychlejš\xed v\xfdrobu a lepš\xed kvalitu","equipmentShop.tips.conveyor":"• Dopravn\xed p\xe1sy spojuj\xed vybaven\xed pro bezprobl\xe9mov\xfd pracovn\xed tok","equipmentShop.tips.advanced":"• Pokročil\xe9 vybaven\xed se odemyk\xe1 na vyšš\xedch \xfarovn\xedch","levelUp.levelRewards":"\uD83C\uDF81 Odměny za \xfaroveň","levelUp.whatsNext":"\uD83D\uDCA1 Co d\xe1l?","levelUp.checkRecipes":"• Pod\xedvejte se na nov\xe9 recepty ve sv\xe9 knize receptů","levelUp.visitShop":"• Navštivte obchod pro nov\xe9 vybaven\xed","levelUp.challengingOrders":"• Přijměte n\xe1ročnějš\xed objedn\xe1vky","levelUp.investSkills":"• Investujte do vylepšen\xed dovednost\xed","settings.title":"⚙️ Nastaven\xed","settings.general":"Obecn\xe9","settings.audio":"Zvuk","settings.graphics":"Grafika","settings.save":"Uložen\xed a data","settings.language":"\uD83C\uDF0D Jazyk","settings.gameplay":"\uD83C\uDFAE Hratelnost","settings.notifications":"Povolit ozn\xe1men\xed","settings.tutorials":"Zobrazit n\xe1vody","settings.animationSpeed":"Rychlost animace","settings.sound":"Zvukov\xe9 efekty","settings.music":"Hudba na pozad\xed","settings.quality":"\uD83C\uDFA8 Kvalita grafiky","settings.autoSave":"\uD83D\uDCBE Automatick\xe9 ukl\xe1d\xe1n\xed","settings.enableAutoSave":"Povolit automatick\xe9 ukl\xe1d\xe1n\xed","settings.dataManagement":"\uD83D\uDCC1 Spr\xe1va dat","settings.exportSave":"\uD83D\uDCE4 Exportovat uložen\xed","settings.importSave":"\uD83D\uDCE5 Importovat uložen\xed","settings.cloudSync":"☁️ Cloudov\xe1 synchronizace","settings.cloudSyncDescription":"Cloudov\xe1 synchronizace v\xe1m umožňuje uložit pokrok online a hr\xe1t na v\xedce zař\xedzen\xedch.","settings.comingSoon":"Již brzy","bakeries.title":"\uD83C\uDFEA Spr\xe1vce pek\xe1ren","bakeries.subtitle":"Spravujte sv\xe9 pek\xe1rensk\xe9 imp\xe9rium","bakeries.owned":"Moje pek\xe1rny","bakeries.available":"Dostupn\xe9","bakeries.current":"Aktu\xe1ln\xed","bakeries.level":"\xdaroveň","bakeries.specialization":"Specializace","bakeries.equipment":"Vybaven\xed","bakeries.orders":"Aktivn\xed objedn\xe1vky","bakeries.switchTo":"Přepnout na","bakeries.noOwned":"Ještě nevlastn\xedte ž\xe1dn\xe9 pek\xe1rny.","bakeries.purchase":"\uD83D\uDCB0 Koupit","bakeries.tooExpensive":"\uD83D\uDCB8 Př\xedliš drah\xe9","bakeries.allOwned":"Vlastn\xedte všechny dostupn\xe9 pek\xe1rny!","bakeries.tips":"\uD83D\uDCA1 Tipy pro pek\xe1rny","bakeries.tip1":"Každ\xe1 pek\xe1rna se specializuje na různ\xe9 produkty pro bonusovou efektivitu","bakeries.tip2":"Přep\xednejte mezi pek\xe1rnami pro spr\xe1vu v\xedce lokalit","bakeries.tip3":"Specializovan\xe9 pek\xe1rny přitahuj\xed z\xe1kazn\xedky hledaj\xedc\xed konkr\xe9tn\xed položky","bakeries.tip4":"Vylepšujte každou pek\xe1rnu nez\xe1visle pro maxim\xe1ln\xed zisk","notifications.orderAccepted":"Objedn\xe1vka přijata","notifications.orderAcceptedMessage":"Přijali jste novou objedn\xe1vku!","notifications.orderCompleted":"Objedn\xe1vka dokončena!","notifications.orderCompletedMessage":"Z\xedskali jste {{reward}} Kč a zkušenosti!","notifications.orderDeclined":"Objedn\xe1vka odm\xedtnuta","notifications.orderDeclinedMessage":"Objedn\xe1vka byla odstraněna z vaš\xed fronty.","notifications.bakeryPurchased":"Pek\xe1rna zakoupena!","notifications.bakeryPurchasedMessage":"Nyn\xed vlastn\xedte {{name}}!","notifications.bakerySwitched":"Pek\xe1rna přepnuta","notifications.bakerySwitchedMessage":"Přepnuto na {{name}}","common.accept":"Přijmout","common.decline":"Odm\xedtnout","common.complete":"Dokončit","common.purchase":"Koupit","common.upgrade":"Vylepšit","common.cancel":"Zrušit","common.confirm":"Potvrdit","common.save":"Uložit","common.load":"Nač\xedst","common.delete":"Smazat","common.edit":"Upravit","common.back":"Zpět","common.next":"Dalš\xed","common.previous":"Předchoz\xed","common.yes":"Ano","common.no":"Ne","common.create":"Vytvořit","common.join":"Připojit se","common.leave":"Odej\xedt","common.start":"Zač\xedt","common.ready":"Připraven","common.notReady":"Nepřipraven","common.send":"Odeslat","common.refresh":"Obnovit","common.retry":"Zkusit znovu","common.reset":"Resetovat","common.clear":"Vymazat","common.apply":"Použ\xedt","common.warning":"Varov\xe1n\xed","common.info":"Informace","common.success":"\xdaspěch","common.error":"Chyba","saveLoad.saveDesc":"Vyberte slot pro uložen\xed vašeho postupu","saveLoad.loadDesc":"Vyberte soubor uložen\xed k načten\xed","saveLoad.saveName":"N\xe1zev uložen\xed","saveLoad.emptySlot":"Pr\xe1zdn\xfd slot","saveLoad.selectedSaveSlot":"Vybr\xe1n: Slot {{slot}}","saveLoad.selectedLoadSlot":"Vybr\xe1n: Slot {{slot}}","saveLoad.confirmOverwrite":"Přepsat uložen\xed?","saveLoad.overwriteWarning":"Toto přep\xedše existuj\xedc\xed uložen\xed. Tuto akci nelze vr\xe1tit zpět.","saveLoad.overwrite":"Přepsat","saveLoad.fileSlots":"Souborov\xe9 sloty","saveLoad.gameSlots":"Hern\xed sloty","saveLoad.exportSave":"Exportovat uložen\xed","saveLoad.importSave":"Importovat uložen\xed","saveLoad.deleteConfirm":"Smazat uložen\xed?","saveLoad.deleteWarning":"Toto trvale smaže tento soubor uložen\xed. Tuto akci nelze vr\xe1tit zpět.","saveLoad.delete":"Smazat","gameMenu.title":"Hern\xed menu","gameMenu.subtitle":"Spravujte svou hru","gameMenu.resume":"Pokračovat ve hře","gameMenu.resumeDesc":"Pokračovat v hran\xed","gameMenu.save":"Uložit hru","gameMenu.saveDesc":"Uložit v\xe1š postup","gameMenu.load":"Nač\xedst hru","gameMenu.loadDesc":"Nač\xedst uložen\xfd postup","gameMenu.settings":"Nastaven\xed","gameMenu.settingsDesc":"Hern\xed předvolby","gameMenu.mainMenu":"Hlavn\xed menu","gameMenu.mainMenuDesc":"N\xe1vrat do hlavn\xedho menu","gameMenu.exit":"Ukončit hru","gameMenu.exitDesc":"Zavř\xedt aplikaci","gameMenu.tip":"Stiskněte ESC pro otevřen\xed tohoto menu kdykoli","settings.discord":"Discord","settings.discordRichPresence":"Discord Rich Presence","settings.discordDescription":"Zobrazit v\xe1š aktu\xe1ln\xed hern\xed stav a aktivitu v Discordu.","settings.enableDiscordRPC":"Povolit Discord Rich Presence","settings.discordConnected":"✅ Připojeno k Discordu","settings.discordDisconnected":"❌ Nepřipojeno k Discordu","settings.discordInfo":"Co je Discord Rich Presence?","settings.discordInfoDesc1":"Discord Rich Presence ukazuje vašim př\xe1telům, co děl\xe1te v Bake It Out:","settings.discordFeature1":"Vaši aktu\xe1ln\xed \xfaroveň a pen\xedze","settings.discordFeature2":"Co pr\xe1vě pečete","settings.discordFeature3":"Informace o multiplayer m\xedstnosti","settings.discordFeature4":"Jak dlouho hrajete","settings.discordInfoDesc2":"Vaši př\xe1tel\xe9 se mohou připojit k vašim multiplayer hr\xe1m př\xedmo z Discordu!","settings.discordTroubleshooting":"Discord nen\xed připojen","settings.discordTrouble1":"Ujistěte se, že Discord běž\xed na vašem poč\xedtači.","settings.discordTrouble2":"Discord Rich Presence funguje pouze v desktopov\xe9 verzi hry.","settings.discordTrouble3":"Zkuste restartovat Discord i hru, pokud se připojen\xed nezdař\xed.","settings.discordPrivacy":"Informace o soukrom\xed","settings.discordPrivacyDesc1":"Discord Rich Presence sd\xedl\xed pouze:","settings.discordPrivacy1":"Vaši aktu\xe1ln\xed hern\xed aktivitu (veřejn\xe9)","settings.discordPrivacy2":"Vaši \xfaroveň hr\xe1če a postup (veřejn\xe9)","settings.discordPrivacy3":"K\xf3dy multiplayer m\xedstnost\xed (pro připojen\xed)","settings.discordPrivacyDesc2":"Ž\xe1dn\xe9 osobn\xed informace nebo uložen\xe1 data nejsou sd\xedlena s Discordem.","settings.discordStatus":"Stav Discordu","settings.discordInitializing":"\uD83D\uDD04 Inicializace Discord RPC...","settings.discordRetrying":"\uD83D\uDD04 Opakov\xe1n\xed připojen\xed...","settings.discordUnavailable":"❌ Discord nen\xed dostupn\xfd","settings.discordDesktopOnly":"ℹ️ Discord RPC dostupn\xfd pouze v desktopov\xe9 verzi","error.general":"Došlo k chybě","error.saveLoad":"Nepodařilo se uložit/nač\xedst hru","error.connection":"Chyba připojen\xed","error.fileNotFound":"Soubor nenalezen","error.invalidData":"Neplatn\xfd form\xe1t dat","error.permissionDenied":"Př\xedstup odepřen","status.loading":"Nač\xedt\xe1n\xed...","status.saving":"Ukl\xe1d\xe1n\xed...","status.connecting":"Připojov\xe1n\xed...","status.ready":"Připraven","status.success":"\xdaspěch!","status.failed":"Ne\xfaspěšn\xe9","status.offline":"Offline","status.online":"Online","ui.placeholder":"Zadejte text...","ui.search":"Hledat","ui.filter":"Filtrovat","ui.sort":"Seřadit","ui.ascending":"Vzestupně","ui.descending":"Sestupně","ui.selectAll":"Vybrat vše","ui.deselectAll":"Zrušit v\xfdběr","ui.noResults":"Ž\xe1dn\xe9 v\xfdsledky","ui.noData":"Ž\xe1dn\xe1 data k dispozici","ui.loading":"Nač\xedt\xe1n\xed...","ui.saving":"Ukl\xe1d\xe1n\xed...","ui.saved":"Uloženo!","ui.failed":"Ne\xfaspěšn\xe9","ui.retry":"Zkusit znovu","ui.back":"Zpět","ui.forward":"Vpřed","ui.home":"Domů","ui.menu":"Menu","ui.options":"Možnosti","ui.preferences":"Předvolby","multiplayer.lobby":"\uD83D\uDC65 Multiplayerov\xe1 lobby","multiplayer.connected":"\uD83D\uDFE2 Připojeno","multiplayer.disconnected":"\uD83D\uDD34 Odpojeno","multiplayer.createRoom":"Vytvořit m\xedstnost","multiplayer.joinRoom":"Připojit se k m\xedstnosti","multiplayer.room":"M\xedstnost","multiplayer.yourName":"Vaše jm\xe9no","multiplayer.enterName":"Zadejte sv\xe9 jm\xe9no","multiplayer.roomName":"N\xe1zev m\xedstnosti","multiplayer.enterRoomName":"Zadejte n\xe1zev m\xedstnosti","multiplayer.gameMode":"Hern\xed režim","multiplayer.cooperative":"\uD83E\uDD1D Kooperativn\xed","multiplayer.competitive":"⚔️ Soutěžn\xed","multiplayer.maxPlayers":"Max hr\xe1čů: {{count}}","multiplayer.roomId":"ID m\xedstnosti","multiplayer.enterRoomId":"Zadejte ID m\xedstnosti","multiplayer.players":"Hr\xe1či ({{count}})","multiplayer.host":"HOSTITEL","multiplayer.level":"\xdaroveň {{level}}","multiplayer.chat":"Chat","multiplayer.typeMessage":"Napište zpr\xe1vu...","multiplayer.gameTime":"Hern\xed čas: {{time}}","multiplayer.teamStats":"\uD83D\uDCCA T\xfdmov\xe9 statistiky","multiplayer.ordersCompleted":"Dokončen\xe9 objedn\xe1vky:","multiplayer.totalRevenue":"Celkov\xfd př\xedjem:","multiplayer.teamExperience":"T\xfdmov\xe9 zkušenosti:","multiplayer.sharedKitchen":"\uD83C\uDFEA Sd\xedlen\xe1 kuchyně","multiplayer.sharedOrders":"\uD83D\uDCCB Sd\xedlen\xe9 objedn\xe1vky","multiplayer.sharedInventory":"\uD83D\uDCE6 Sd\xedlen\xfd sklad","multiplayer.contribution":"Př\xedspěvek:","multiplayer.online":"\uD83D\uDFE2 Online","multiplayer.status":"Stav:","multiplayer.you":"(Vy)","multiplayer.teamChat":"\uD83D\uDCAC T\xfdmov\xfd chat","multiplayer.chatPlaceholder":"Zde se zobraz\xed zpr\xe1vy chatu...","multiplayer.mode.cooperative.description":"\uD83E\uDD1D Kooperativn\xed režim: Spolupracujte na dokončov\xe1n\xed objedn\xe1vek a rozvoji sd\xedlen\xe9 pek\xe1rny!","multiplayer.mode.competitive.description":"⚔️ Soutěžn\xed režim: Soutěžte s ostatn\xedmi hr\xe1či o dokončen\xed nejv\xedce objedn\xe1vek!","multiplayer.game.title":"\uD83C\uDFAE Multiplayerov\xe1 hra - {{roomName}}","multiplayer.game.mode":"Režim: {{mode}}","multiplayer.game.playersCount":"Hr\xe1či: {{count}}","multiplayer.game.playing":"\uD83D\uDFE2 Hraje se","multiplayer.game.leaveGame":"\uD83D\uDEAA Opustit hru","multiplayer.game.tabs.game":"Hra","multiplayer.game.tabs.players":"Hr\xe1či","multiplayer.game.tabs.chat":"Chat","multiplayer.create.title":"\uD83C\uDFD7️ Vytvořit m\xedstnost","multiplayer.join.title":"\uD83D\uDEAA Připojit se k m\xedstnosti","multiplayer.room.info":"Režim: {{mode}} • Hr\xe1či: {{current}}/{{max}}","multiplayer.room.readyUp":"✅ Připraven","multiplayer.room.notReady":"⏳ Nepřipraven","multiplayer.room.startGame":"\uD83D\uDE80 Zač\xedt hru","multiplayer.room.leaveRoom":"\uD83D\uDEAA Opustit","multiplayer.connection.connecting":"Připojov\xe1n\xed...","multiplayer.connection.reconnecting":"Znovu se připojuje...","multiplayer.connection.failed":"Připojen\xed selhalo","multiplayer.connection.error":"⚠️ {{error}}","multiplayer.system.playerJoined":"{{name}} se připojil do m\xedstnosti","multiplayer.system.playerLeft":"{{name}} opustil m\xedstnost","multiplayer.system.gameStarted":"Hra začala!","multiplayer.system.gameEnded":"Hra skončila!","multiplayer.system.roomCreated":"M\xedstnost byla \xfaspěšně vytvořena","multiplayer.system.roomJoined":"\xdaspěšně jste se připojili do m\xedstnosti"}};function s(e){let{children:t}=e,[i,s]=(0,o.useState)("en"),[l,c]=(0,o.useState)(!1);return(0,o.useEffect)(()=>{c(!0)},[]),(0,o.useEffect)(()=>{if(!l)return;let e=localStorage.getItem("language");e&&("en"===e||"cs"===e)&&s(e)},[l]),(0,a.jsx)(n.Provider,{value:{language:i,setLanguage:e=>{s(e),l&&localStorage.setItem("language",e)},t:(e,t)=>{let a=r[i][e]||e;return t&&Object.entries(t).forEach(e=>{let[t,i]=e;a=a.replace("{{".concat(t,"}}"),i)}),a}},children:t})}function l(){let e=(0,o.useContext)(n);return void 0===e?(console.warn("useLanguage called outside of LanguageProvider, using fallback"),{language:"en",setLanguage:()=>{},t:e=>e}):e}}}]);