-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Players table
CREATE TABLE players (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
    level INTEGER DEFAULT 1,
    experience INTEGER DEFAULT 0,
    money INTEGER DEFAULT 100,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Recipes table
CREATE TABLE recipes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    ingredients JSONB NOT NULL, -- Array of ingredient names
    baking_time INTEGER NOT NULL, -- in seconds
    difficulty INTEGER CHECK (difficulty >= 1 AND difficulty <= 5),
    unlock_level INTEGER DEFAULT 1,
    base_price INTEGER NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Equipment table
CREATE TABLE equipment (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) CHECK (type IN ('oven', 'mixer', 'conveyor', 'auto_mixer', 'auto_oven')),
    level INTEGER DEFAULT 1,
    cost INTEGER NOT NULL,
    unlock_level INTEGER DEFAULT 1,
    automation_level INTEGER DEFAULT 0 CHECK (automation_level >= 0 AND automation_level <= 3),
    efficiency_multiplier DECIMAL(3,2) DEFAULT 1.0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Player recipes (unlocked recipes)
CREATE TABLE player_recipes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    player_id UUID REFERENCES players(id) ON DELETE CASCADE,
    recipe_id UUID REFERENCES recipes(id) ON DELETE CASCADE,
    unlocked_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(player_id, recipe_id)
);

-- Player equipment (owned equipment)
CREATE TABLE player_equipment (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    player_id UUID REFERENCES players(id) ON DELETE CASCADE,
    equipment_id UUID REFERENCES equipment(id) ON DELETE CASCADE,
    quantity INTEGER DEFAULT 1,
    purchased_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Game sessions (for multiplayer)
CREATE TABLE game_sessions (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    host_player_id UUID REFERENCES players(id) ON DELETE CASCADE,
    session_code VARCHAR(10) UNIQUE NOT NULL,
    max_players INTEGER DEFAULT 4,
    current_players INTEGER DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    game_mode VARCHAR(20) CHECK (game_mode IN ('cooperative', 'competitive')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Orders table
CREATE TABLE orders (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    player_id UUID REFERENCES players(id) ON DELETE CASCADE,
    session_id UUID REFERENCES game_sessions(id) ON DELETE SET NULL,
    customer_name VARCHAR(255) NOT NULL,
    items JSONB NOT NULL, -- Array of order items
    total_price INTEGER NOT NULL,
    time_limit INTEGER NOT NULL, -- in seconds
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Ingredients table
CREATE TABLE ingredients (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    name VARCHAR(255) UNIQUE NOT NULL,
    cost INTEGER NOT NULL,
    availability DECIMAL(3,2) DEFAULT 1.0 CHECK (availability >= 0 AND availability <= 1),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for better performance
CREATE INDEX idx_players_user_id ON players(user_id);
CREATE INDEX idx_player_recipes_player_id ON player_recipes(player_id);
CREATE INDEX idx_player_equipment_player_id ON player_equipment(player_id);
CREATE INDEX idx_orders_player_id ON orders(player_id);
CREATE INDEX idx_orders_session_id ON orders(session_id);
CREATE INDEX idx_game_sessions_active ON game_sessions(is_active);

-- Row Level Security (RLS) policies
ALTER TABLE players ENABLE ROW LEVEL SECURITY;
ALTER TABLE player_recipes ENABLE ROW LEVEL SECURITY;
ALTER TABLE player_equipment ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE game_sessions ENABLE ROW LEVEL SECURITY;

-- Players can only access their own data
CREATE POLICY "Players can view own data" ON players
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Players can update own data" ON players
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Players can insert own data" ON players
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Player recipes policies
CREATE POLICY "Players can view own recipes" ON player_recipes
    FOR SELECT USING (
        player_id IN (SELECT id FROM players WHERE user_id = auth.uid())
    );

CREATE POLICY "Players can insert own recipes" ON player_recipes
    FOR INSERT WITH CHECK (
        player_id IN (SELECT id FROM players WHERE user_id = auth.uid())
    );

-- Player equipment policies
CREATE POLICY "Players can view own equipment" ON player_equipment
    FOR SELECT USING (
        player_id IN (SELECT id FROM players WHERE user_id = auth.uid())
    );

CREATE POLICY "Players can insert own equipment" ON player_equipment
    FOR INSERT WITH CHECK (
        player_id IN (SELECT id FROM players WHERE user_id = auth.uid())
    );

CREATE POLICY "Players can update own equipment" ON player_equipment
    FOR UPDATE USING (
        player_id IN (SELECT id FROM players WHERE user_id = auth.uid())
    );

-- Orders policies
CREATE POLICY "Players can view own orders" ON orders
    FOR SELECT USING (
        player_id IN (SELECT id FROM players WHERE user_id = auth.uid())
    );

CREATE POLICY "Players can insert own orders" ON orders
    FOR INSERT WITH CHECK (
        player_id IN (SELECT id FROM players WHERE user_id = auth.uid())
    );

CREATE POLICY "Players can update own orders" ON orders
    FOR UPDATE USING (
        player_id IN (SELECT id FROM players WHERE user_id = auth.uid())
    );

-- Game sessions policies
CREATE POLICY "Players can view active sessions" ON game_sessions
    FOR SELECT USING (is_active = true);

CREATE POLICY "Players can create sessions" ON game_sessions
    FOR INSERT WITH CHECK (
        host_player_id IN (SELECT id FROM players WHERE user_id = auth.uid())
    );

CREATE POLICY "Host can update own sessions" ON game_sessions
    FOR UPDATE USING (
        host_player_id IN (SELECT id FROM players WHERE user_id = auth.uid())
    );

-- Public read access for recipes, equipment, and ingredients
CREATE POLICY "Anyone can view recipes" ON recipes FOR SELECT USING (true);
CREATE POLICY "Anyone can view equipment" ON equipment FOR SELECT USING (true);
CREATE POLICY "Anyone can view ingredients" ON ingredients FOR SELECT USING (true);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at
CREATE TRIGGER update_players_updated_at BEFORE UPDATE ON players
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
