@echo off
title Bake It Out - Easy Server Setup
color 0A

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                    🧁 BAKE IT OUT 🧁                         ║
echo  ║                   Easy Server Setup                          ║
echo  ║                                                              ║
echo  ║  This will set up everything you need in under 30 seconds!  ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

REM Progress tracking
set STEP=1
set TOTAL_STEPS=8

echo [%STEP%/%TOTAL_STEPS%] 🔍 Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found!
    echo.
    echo 📥 Please install Node.js first:
    echo    1. Go to https://nodejs.org/
    echo    2. Download and install Node.js 18+
    echo    3. Restart this script
    echo.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js %NODE_VERSION% found!
set /a STEP+=1

echo.
echo [%STEP%/%TOTAL_STEPS%] 📦 Installing dependencies...
npm install --silent
if %errorlevel% neq 0 (
    echo ❌ Failed to install dependencies
    echo 💡 Try running: npm install
    pause
    exit /b 1
)
echo ✅ Dependencies installed!
set /a STEP+=1

echo.
echo [%STEP%/%TOTAL_STEPS%] ⚙️ Setting up configuration...
if not exist ".env" (
    copy ".env.example" ".env" >nul
    echo ✅ Configuration file created!
) else (
    echo ✅ Configuration file already exists!
)
set /a STEP+=1

echo.
echo [%STEP%/%TOTAL_STEPS%] 🗂️ Creating directories...
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads
if not exist "backups" mkdir backups
echo ✅ Directories created!
set /a STEP+=1

echo.
echo [%STEP%/%TOTAL_STEPS%] 🐳 Setting up MongoDB...

REM Check if Docker is available
docker --version >nul 2>&1
if %errorlevel% equ 0 (
    echo 🐳 Docker found! Setting up MongoDB with Docker...
    
    REM Check if MongoDB container already exists
    docker ps -a | findstr "bake-it-out-mongo" >nul
    if %errorlevel% equ 0 (
        echo 🔄 Starting existing MongoDB container...
        docker start bake-it-out-mongo >nul 2>&1
    ) else (
        echo 📥 Creating new MongoDB container...
        docker run -d --name bake-it-out-mongo -p 27017:27017 mongo:6.0 >nul 2>&1
    )
    
    REM Wait for MongoDB to start
    echo ⏳ Waiting for MongoDB to start...
    timeout /t 5 >nul
    echo ✅ MongoDB is running!
) else (
    echo ⚠️ Docker not found. Checking for local MongoDB...
    
    REM Check if MongoDB is running locally
    netstat -an | findstr "27017" >nul
    if %errorlevel% equ 0 (
        echo ✅ MongoDB is already running locally!
    ) else (
        echo 📝 MongoDB setup options:
        echo    1. Install Docker and restart this script (Recommended)
        echo    2. Install MongoDB locally
        echo    3. Use MongoDB Atlas (cloud)
        echo    4. Continue anyway (will use default connection)
        echo.
        echo 💡 For now, continuing with default settings...
        echo    You can change the database URL in .env file later
    )
)
set /a STEP+=1

echo.
echo [%STEP%/%TOTAL_STEPS%] 🔐 Generating secure keys...

REM Generate a random JWT secret if not exists
findstr "your-super-secret-jwt-key-change-in-production" .env >nul
if %errorlevel% equ 0 (
    echo 🔑 Generating secure JWT secret...
    
    REM Generate random string (simple method for Windows)
    set "chars=ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789"
    set "secret="
    for /l %%i in (1,1,32) do (
        set /a "rand=!random! %% 62"
        for %%j in (!rand!) do set "secret=!secret!!chars:~%%j,1!"
    )
    
    REM Replace the default secret (simplified approach)
    powershell -Command "(Get-Content .env) -replace 'your-super-secret-jwt-key-change-in-production', '%secret%' | Set-Content .env" >nul 2>&1
    echo ✅ Secure JWT secret generated!
) else (
    echo ✅ JWT secret already configured!
)
set /a STEP+=1

echo.
echo [%STEP%/%TOTAL_STEPS%] 🧪 Testing server startup...
echo ⏳ Starting server for health check...

REM Start server in background for testing
start /B node src/index.js >test.log 2>&1

REM Wait for server to start
timeout /t 8 >nul

REM Test health endpoint
curl -s http://localhost:3001/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Server health check passed!
) else (
    echo ⚠️ Health check failed, but server might still be starting...
)

REM Stop test server
taskkill /F /IM node.exe >nul 2>&1
timeout /t 2 >nul

set /a STEP+=1

echo.
echo [%STEP%/%TOTAL_STEPS%] 🚀 Starting your server...

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                        🎉 SUCCESS! 🎉                       ║
echo  ║                                                              ║
echo  ║  Your Bake It Out server is ready to launch!                ║
echo  ║                                                              ║
echo  ║  🌐 Server: http://localhost:3001                           ║
echo  ║  📊 Dashboard: http://localhost:3001/dashboard              ║
echo  ║  🔍 Health: http://localhost:3001/health                    ║
echo  ║                                                              ║
echo  ║  Press any key to start the server...                       ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

pause >nul

echo 🚀 Starting server...
echo.
echo 💡 Tips:
echo    • Press Ctrl+C to stop the server
echo    • Dashboard login: Use any game account
echo    • Need help? Check the README.md file
echo.

REM Start the server
node src/index.js

echo.
echo 👋 Server stopped. Thanks for using Bake It Out!
pause
