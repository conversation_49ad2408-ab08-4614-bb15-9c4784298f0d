{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/RecipeModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Recipe, getAvailableRecipes } from '@/lib/gameLogic'\nimport { useGame } from '@/contexts/GameContext'\nimport { useLanguage } from '@/contexts/LanguageContext'\n\ninterface RecipeModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport function RecipeModal({ isOpen, onClose }: RecipeModalProps) {\n  const { player, inventory } = useGame()\n  const { t } = useLanguage()\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n  \n  if (!isOpen) return null\n\n  const availableRecipes = getAvailableRecipes(player.level)\n  const filteredRecipes = selectedCategory === 'all' \n    ? availableRecipes \n    : availableRecipes.filter(recipe => recipe.category === selectedCategory)\n\n  const canCraft = (recipe: Recipe) => {\n    return recipe.ingredients.every(ingredient => {\n      const inventoryItem = inventory.find(item => item.name === ingredient.name)\n      return inventoryItem && inventoryItem.quantity >= ingredient.quantity\n    })\n  }\n\n  const getDifficultyStars = (difficulty: number) => {\n    return '⭐'.repeat(difficulty) + '☆'.repeat(5 - difficulty)\n  }\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const categories = [\n    { id: 'all', name: t('recipes.all'), icon: '🍽️' },\n    { id: 'cookies', name: t('recipes.cookies'), icon: '🍪' },\n    { id: 'cakes', name: t('recipes.cakes'), icon: '🧁' },\n    { id: 'bread', name: t('recipes.bread'), icon: '🍞' },\n    { id: 'pastries', name: t('recipes.pastries'), icon: '🥐' }\n  ]\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-2xl font-bold text-orange-800\">{t('modal.recipes.title')}</h2>\n            <Button variant=\"secondary\" onClick={onClose}>\n              {t('game.close')}\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Category Filter */}\n          <div className=\"flex flex-wrap gap-2 mb-6\">\n            {categories.map(category => (\n              <Button\n                key={category.id}\n                variant={selectedCategory === category.id ? 'primary' : 'secondary'}\n                size=\"sm\"\n                onClick={() => setSelectedCategory(category.id)}\n              >\n                {category.icon} {category.name}\n              </Button>\n            ))}\n          </div>\n\n          {/* Recipes Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto\">\n            {filteredRecipes.map(recipe => (\n              <div\n                key={recipe.id}\n                className={`p-4 rounded-lg border-2 ${\n                  canCraft(recipe)\n                    ? 'border-green-300 bg-green-50'\n                    : 'border-gray-300 bg-gray-50'\n                }`}\n              >\n                <div className=\"flex justify-between items-start mb-2\">\n                  <h3 className=\"font-semibold text-gray-800\">{recipe.name}</h3>\n                  <span className=\"text-sm text-green-600\">${recipe.basePrice}</span>\n                </div>\n\n                <div className=\"text-xs text-gray-500 mb-2\">\n                  {getDifficultyStars(recipe.difficulty)} • ⏱️ {formatTime(recipe.bakingTime)}\n                </div>\n\n                <div className=\"space-y-1 mb-3\">\n                  <div className=\"text-sm font-medium text-gray-700\">{t('recipes.ingredients')}</div>\n                  {recipe.ingredients.map((ingredient, index) => {\n                    const inventoryItem = inventory.find(item => item.name === ingredient.name)\n                    const hasEnough = inventoryItem && inventoryItem.quantity >= ingredient.quantity\n\n                    return (\n                      <div\n                        key={index}\n                        className={`text-xs flex justify-between ${\n                          hasEnough ? 'text-green-600' : 'text-red-600'\n                        }`}\n                      >\n                        <span>{ingredient.name}</span>\n                        <span>\n                          {ingredient.quantity}\n                          {inventoryItem && (\n                            <span className=\"ml-1\">\n                              ({inventoryItem.quantity} available)\n                            </span>\n                          )}\n                        </span>\n                      </div>\n                    )\n                  })}\n                </div>\n\n                <div className=\"text-xs text-gray-500\">\n                  {t('recipes.unlockLevel', { level: recipe.unlockLevel.toString() })}\n                </div>\n\n                {canCraft(recipe) && (\n                  <div className=\"mt-2\">\n                    <Button size=\"sm\" variant=\"success\" className=\"w-full\">\n                      {t('recipes.canCraft')}\n                    </Button>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n\n          {filteredRecipes.length === 0 && (\n            <div className=\"text-center py-8 text-gray-500\">\n              <div className=\"text-4xl mb-2\">📝</div>\n              <p>{t('recipes.noRecipes')}</p>\n              <p className=\"text-sm\">{t('recipes.levelUpToUnlock')}</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAaO,SAAS,YAAY,KAAqC;QAArC,EAAE,MAAM,EAAE,OAAO,EAAoB,GAArC;;IAC1B,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACpC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IACxB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,mBAAmB,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,KAAK;IACzD,MAAM,kBAAkB,qBAAqB,QACzC,mBACA,iBAAiB,MAAM,CAAC,CAAA,SAAU,OAAO,QAAQ,KAAK;IAE1D,MAAM,WAAW,CAAC;QAChB,OAAO,OAAO,WAAW,CAAC,KAAK,CAAC,CAAA;YAC9B,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,IAAI;YAC1E,OAAO,iBAAiB,cAAc,QAAQ,IAAI,WAAW,QAAQ;QACvE;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAO,IAAI,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI;IACjD;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,AAAC,GAAU,OAAR,MAAK,KAAoC,OAAjC,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAChD;IAEA,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM,EAAE;YAAgB,MAAM;QAAM;QACjD;YAAE,IAAI;YAAW,MAAM,EAAE;YAAoB,MAAM;QAAK;QACxD;YAAE,IAAI;YAAS,MAAM,EAAE;YAAkB,MAAM;QAAK;QACpD;YAAE,IAAI;YAAS,MAAM,EAAE;YAAkB,MAAM;QAAK;QACpD;YAAE,IAAI;YAAY,MAAM,EAAE;YAAqB,MAAM;QAAK;KAC3D;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC,EAAE;;;;;;0CACtD,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAY,SAAS;0CAClC,EAAE;;;;;;;;;;;;;;;;;8BAKT,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC,qIAAA,CAAA,SAAM;oCAEL,SAAS,qBAAqB,SAAS,EAAE,GAAG,YAAY;oCACxD,MAAK;oCACL,SAAS,IAAM,oBAAoB,SAAS,EAAE;;wCAE7C,SAAS,IAAI;wCAAC;wCAAE,SAAS,IAAI;;mCALzB,SAAS,EAAE;;;;;;;;;;sCAWtB,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAA,uBACnB,6LAAC;oCAEC,WAAW,AAAC,2BAIX,OAHC,SAAS,UACL,iCACA;;sDAGN,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA+B,OAAO,IAAI;;;;;;8DACxD,6LAAC;oDAAK,WAAU;;wDAAyB;wDAAE,OAAO,SAAS;;;;;;;;;;;;;sDAG7D,6LAAC;4CAAI,WAAU;;gDACZ,mBAAmB,OAAO,UAAU;gDAAE;gDAAO,WAAW,OAAO,UAAU;;;;;;;sDAG5E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAqC,EAAE;;;;;;gDACrD,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY;oDACnC,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,IAAI;oDAC1E,MAAM,YAAY,iBAAiB,cAAc,QAAQ,IAAI,WAAW,QAAQ;oDAEhF,qBACE,6LAAC;wDAEC,WAAW,AAAC,gCAEX,OADC,YAAY,mBAAmB;;0EAGjC,6LAAC;0EAAM,WAAW,IAAI;;;;;;0EACtB,6LAAC;;oEACE,WAAW,QAAQ;oEACnB,+BACC,6LAAC;wEAAK,WAAU;;4EAAO;4EACnB,cAAc,QAAQ;4EAAC;;;;;;;;;;;;;;uDAV1B;;;;;gDAgBX;;;;;;;sDAGF,6LAAC;4CAAI,WAAU;sDACZ,EAAE,uBAAuB;gDAAE,OAAO,OAAO,WAAW,CAAC,QAAQ;4CAAG;;;;;;wCAGlE,SAAS,yBACR,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gDAAC,MAAK;gDAAK,SAAQ;gDAAU,WAAU;0DAC3C,EAAE;;;;;;;;;;;;mCAlDJ,OAAO,EAAE;;;;;;;;;;wBA0DnB,gBAAgB,MAAM,KAAK,mBAC1B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;8CAAG,EAAE;;;;;;8CACN,6LAAC;oCAAE,WAAU;8CAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOxC;GAzIgB;;QACgB,kIAAA,CAAA,UAAO;QACvB,sIAAA,CAAA,cAAW;;;KAFX", "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/ShopModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { useGame } from '@/contexts/GameContext'\nimport { useLanguage } from '@/contexts/LanguageContext'\n\ninterface ShopModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport function ShopModal({ isOpen, onClose }: ShopModalProps) {\n  const { player, inventory, spendMoney, addIngredient } = useGame()\n  const { t } = useLanguage()\n  const [quantities, setQuantities] = useState<Record<string, number>>({})\n\n  if (!isOpen) return null\n\n  const handleQuantityChange = (ingredientName: string, quantity: number) => {\n    setQuantities(prev => ({\n      ...prev,\n      [ingredientName]: Math.max(0, quantity)\n    }))\n  }\n\n  const buyIngredient = (ingredientName: string, cost: number) => {\n    const quantity = quantities[ingredientName] || 1\n    const totalCost = cost * quantity\n    \n    if (spendMoney(totalCost)) {\n      addIngredient(ingredientName, quantity)\n      setQuantities(prev => ({ ...prev, [ingredientName]: 0 }))\n    }\n  }\n\n  const getTotalCost = (ingredientName: string, cost: number) => {\n    const quantity = quantities[ingredientName] || 1\n    return cost * quantity\n  }\n\n  const canAfford = (ingredientName: string, cost: number) => {\n    return player.money >= getTotalCost(ingredientName, cost)\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-2xl font-bold text-orange-800\">{t('modal.shop.title')}</h2>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"bg-green-100 px-3 py-1 rounded-full\">\n                <span className=\"text-green-800 font-medium\">{t('ui.money', { amount: player.money.toString() })}</span>\n              </div>\n              <Button variant=\"secondary\" onClick={onClose}>\n                {t('game.close')}\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          <div className=\"space-y-4 max-h-[60vh] overflow-y-auto\">\n            {inventory.map(ingredient => {\n              const quantity = quantities[ingredient.name] || 1\n              const totalCost = getTotalCost(ingredient.name, ingredient.cost)\n              const affordable = canAfford(ingredient.name, ingredient.cost)\n\n              return (\n                <div\n                  key={ingredient.name}\n                  className=\"flex items-center justify-between p-4 bg-gray-50 rounded-lg\"\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"text-2xl\">{ingredient.icon}</div>\n                    <div>\n                      <h3 className=\"font-medium text-gray-800\">{ingredient.name}</h3>\n                      <p className=\"text-sm text-gray-600\">\n                        {t('shop.currentStock', { quantity: ingredient.quantity.toString() })}\n                      </p>\n                      <p className=\"text-sm text-green-600\">\n                        {t('inventory.cost', { cost: ingredient.cost.toString() })}\n                      </p>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"flex items-center space-x-2\">\n                      <Button\n                        size=\"sm\"\n                        variant=\"secondary\"\n                        onClick={() => handleQuantityChange(ingredient.name, quantity - 1)}\n                        disabled={quantity <= 1}\n                      >\n                        -\n                      </Button>\n                      <span className=\"w-12 text-center font-mono\">{quantity}</span>\n                      <Button\n                        size=\"sm\"\n                        variant=\"secondary\"\n                        onClick={() => handleQuantityChange(ingredient.name, quantity + 1)}\n                        disabled={!canAfford(ingredient.name, ingredient.cost) && quantity >= 1}\n                      >\n                        +\n                      </Button>\n                    </div>\n\n                    <div className=\"text-right min-w-[80px]\">\n                      <div className={`font-medium ${affordable ? 'text-green-600' : 'text-red-600'}`}>\n                        ${totalCost}\n                      </div>\n                      <Button\n                        size=\"sm\"\n                        variant={affordable ? 'success' : 'secondary'}\n                        onClick={() => buyIngredient(ingredient.name, ingredient.cost)}\n                        disabled={!affordable}\n                        className=\"mt-1\"\n                      >\n                        {affordable ? t('shop.buy') : t('shop.tooExpensive')}\n                      </Button>\n                    </div>\n                  </div>\n                </div>\n              )\n            })}\n          </div>\n\n          <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n            <h3 className=\"font-medium text-blue-800 mb-2\">{t('shop.tips.title')}</h3>\n            <ul className=\"text-sm text-blue-700 space-y-1\">\n              <li>{t('shop.tips.bulk')}</li>\n              <li>{t('shop.tips.stock')}</li>\n              <li>{t('shop.tips.rare')}</li>\n              <li>{t('shop.tips.prices')}</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAYO,SAAS,UAAU,KAAmC;QAAnC,EAAE,MAAM,EAAE,OAAO,EAAkB,GAAnC;;IACxB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/D,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IACxB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAEtE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,uBAAuB,CAAC,gBAAwB;QACpD,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,eAAe,EAAE,KAAK,GAAG,CAAC,GAAG;YAChC,CAAC;IACH;IAEA,MAAM,gBAAgB,CAAC,gBAAwB;QAC7C,MAAM,WAAW,UAAU,CAAC,eAAe,IAAI;QAC/C,MAAM,YAAY,OAAO;QAEzB,IAAI,WAAW,YAAY;YACzB,cAAc,gBAAgB;YAC9B,cAAc,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,eAAe,EAAE;gBAAE,CAAC;QACzD;IACF;IAEA,MAAM,eAAe,CAAC,gBAAwB;QAC5C,MAAM,WAAW,UAAU,CAAC,eAAe,IAAI;QAC/C,OAAO,OAAO;IAChB;IAEA,MAAM,YAAY,CAAC,gBAAwB;QACzC,OAAO,OAAO,KAAK,IAAI,aAAa,gBAAgB;IACtD;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAsC,EAAE;;;;;;0CACtD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA8B,EAAE,YAAY;gDAAE,QAAQ,OAAO,KAAK,CAAC,QAAQ;4CAAG;;;;;;;;;;;kDAEhG,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAY,SAAS;kDAClC,EAAE;;;;;;;;;;;;;;;;;;;;;;;8BAMX,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAA;gCACb,MAAM,WAAW,UAAU,CAAC,WAAW,IAAI,CAAC,IAAI;gCAChD,MAAM,YAAY,aAAa,WAAW,IAAI,EAAE,WAAW,IAAI;gCAC/D,MAAM,aAAa,UAAU,WAAW,IAAI,EAAE,WAAW,IAAI;gCAE7D,qBACE,6LAAC;oCAEC,WAAU;;sDAEV,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAY,WAAW,IAAI;;;;;;8DAC1C,6LAAC;;sEACC,6LAAC;4DAAG,WAAU;sEAA6B,WAAW,IAAI;;;;;;sEAC1D,6LAAC;4DAAE,WAAU;sEACV,EAAE,qBAAqB;gEAAE,UAAU,WAAW,QAAQ,CAAC,QAAQ;4DAAG;;;;;;sEAErE,6LAAC;4DAAE,WAAU;sEACV,EAAE,kBAAkB;gEAAE,MAAM,WAAW,IAAI,CAAC,QAAQ;4DAAG;;;;;;;;;;;;;;;;;;sDAK9D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,qBAAqB,WAAW,IAAI,EAAE,WAAW;4DAChE,UAAU,YAAY;sEACvB;;;;;;sEAGD,6LAAC;4DAAK,WAAU;sEAA8B;;;;;;sEAC9C,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,SAAS,IAAM,qBAAqB,WAAW,IAAI,EAAE,WAAW;4DAChE,UAAU,CAAC,UAAU,WAAW,IAAI,EAAE,WAAW,IAAI,KAAK,YAAY;sEACvE;;;;;;;;;;;;8DAKH,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAW,AAAC,eAA6D,OAA/C,aAAa,mBAAmB;;gEAAkB;gEAC7E;;;;;;;sEAEJ,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS,aAAa,YAAY;4DAClC,SAAS,IAAM,cAAc,WAAW,IAAI,EAAE,WAAW,IAAI;4DAC7D,UAAU,CAAC;4DACX,WAAU;sEAET,aAAa,EAAE,cAAc,EAAE;;;;;;;;;;;;;;;;;;;mCAhDjC,WAAW,IAAI;;;;;4BAsD1B;;;;;;sCAGF,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAkC,EAAE;;;;;;8CAClD,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAI,EAAE;;;;;;sDACP,6LAAC;sDAAI,EAAE;;;;;;sDACP,6LAAC;sDAAI,EAAE;;;;;;sDACP,6LAAC;sDAAI,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOrB;GAjIgB;;QAC2C,kIAAA,CAAA,UAAO;QAClD,sIAAA,CAAA,cAAW;;;KAFX", "debugId": null}}, {"offset": {"line": 702, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/BakingModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Recipe, getAvailableRecipes, canCraftRecipe } from '@/lib/gameLogic'\nimport { useGame } from '@/contexts/GameContext'\n\ninterface BakingModalProps {\n  isOpen: boolean\n  onClose: () => void\n  equipmentId: string\n  equipmentName: string\n}\n\nexport function BakingModal({ isOpen, onClose, equipmentId, equipmentName }: BakingModalProps) {\n  const { player, inventory, updateEquipment, useIngredient } = useGame()\n  const [selectedRecipe, setSelectedRecipe] = useState<Recipe | null>(null)\n  \n  if (!isOpen) return null\n\n  const availableRecipes = getAvailableRecipes(player.level)\n  const craftableRecipes = availableRecipes.filter(recipe => \n    canCraftRecipe(recipe, inventory)\n  )\n\n  const startBaking = (recipe: Recipe) => {\n    // Check if we can craft the recipe\n    if (!canCraftRecipe(recipe, inventory)) {\n      return\n    }\n\n    // Consume ingredients - check if we have enough first\n    const hasEnoughIngredients = recipe.ingredients.every(ingredient => {\n      const inventoryItem = inventory.find(item => item.name === ingredient.name)\n      return inventoryItem && inventoryItem.quantity >= ingredient.quantity\n    })\n\n    if (!hasEnoughIngredients) {\n      return\n    }\n\n    // Consume the ingredients\n    recipe.ingredients.forEach(ingredient => {\n      useIngredient(ingredient.name, ingredient.quantity)\n    })\n\n    // Start the equipment\n    updateEquipment(equipmentId, {\n      isActive: true,\n      timeRemaining: recipe.bakingTime,\n      currentRecipe: recipe.name\n    })\n\n    onClose()\n  }\n\n  const formatTime = (seconds: number) => {\n    const mins = Math.floor(seconds / 60)\n    const secs = seconds % 60\n    return `${mins}:${secs.toString().padStart(2, '0')}`\n  }\n\n  const getDifficultyStars = (difficulty: number) => {\n    return '⭐'.repeat(difficulty) + '☆'.repeat(5 - difficulty)\n  }\n\n  const getRecipeIcon = (category: string) => {\n    switch (category) {\n      case 'cookies': return '🍪'\n      case 'cakes': return '🧁'\n      case 'bread': return '🍞'\n      case 'pastries': return '🥐'\n      default: return '🍽️'\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-2xl font-bold text-orange-800\">\n              🔥 {equipmentName} - Select Recipe\n            </h2>\n            <Button variant=\"secondary\" onClick={onClose}>\n              ✕ Close\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          {craftableRecipes.length === 0 ? (\n            <div className=\"text-center py-8\">\n              <div className=\"text-4xl mb-4\">😔</div>\n              <h3 className=\"text-lg font-medium text-gray-800 mb-2\">\n                No recipes available\n              </h3>\n              <p className=\"text-gray-600 mb-4\">\n                You don&apos;t have enough ingredients to craft any recipes.\n              </p>\n              <Button variant=\"primary\" onClick={onClose}>\n                Buy Ingredients\n              </Button>\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[60vh] overflow-y-auto\">\n              {craftableRecipes.map(recipe => (\n                <div\n                  key={recipe.id}\n                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${\n                    selectedRecipe?.id === recipe.id\n                      ? 'border-orange-400 bg-orange-50'\n                      : 'border-gray-300 bg-gray-50 hover:border-orange-300'\n                  }`}\n                  onClick={() => setSelectedRecipe(recipe)}\n                >\n                  <div className=\"flex items-start justify-between mb-2\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-2xl\">{getRecipeIcon(recipe.category)}</span>\n                      <h3 className=\"font-semibold text-gray-800\">{recipe.name}</h3>\n                    </div>\n                    <span className=\"text-sm text-green-600\">${recipe.basePrice}</span>\n                  </div>\n\n                  <div className=\"text-xs text-gray-500 mb-2\">\n                    {getDifficultyStars(recipe.difficulty)} • ⏱️ {formatTime(recipe.bakingTime)}\n                  </div>\n\n                  <div className=\"space-y-1 mb-3\">\n                    <div className=\"text-sm font-medium text-gray-700\">Ingredients:</div>\n                    {recipe.ingredients.map((ingredient, index) => {\n                      const inventoryItem = inventory.find(item => item.name === ingredient.name)\n                      \n                      return (\n                        <div\n                          key={index}\n                          className=\"text-xs flex justify-between text-green-600\"\n                        >\n                          <span>{ingredient.name}</span>\n                          <span>\n                            {ingredient.quantity} \n                            <span className=\"ml-1\">\n                              ({inventoryItem?.quantity || 0} available)\n                            </span>\n                          </span>\n                        </div>\n                      )\n                    })}\n                  </div>\n\n                  {selectedRecipe?.id === recipe.id && (\n                    <Button\n                      variant=\"success\"\n                      size=\"sm\"\n                      className=\"w-full\"\n                      onClick={() => startBaking(recipe)}\n                    >\n                      🔥 Start Baking\n                    </Button>\n                  )}\n                </div>\n              ))}\n            </div>\n          )}\n\n          {selectedRecipe && craftableRecipes.length > 0 && (\n            <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n              <h3 className=\"font-medium text-blue-800 mb-2\">\n                📋 Baking Instructions for {selectedRecipe.name}\n              </h3>\n              <div className=\"text-sm text-blue-700 space-y-1\">\n                <p>• Baking time: {formatTime(selectedRecipe.bakingTime)}</p>\n                <p>• Difficulty: {getDifficultyStars(selectedRecipe.difficulty)}</p>\n                <p>• Expected reward: ${selectedRecipe.basePrice}</p>\n                <p>• Make sure you have all ingredients before starting!</p>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAcO,SAAS,YAAY,KAAiE;QAAjE,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,aAAa,EAAoB,GAAjE;;IAC1B,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,eAAe,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACpE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,mBAAmB,CAAA,GAAA,0HAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,KAAK;IACzD,MAAM,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,SAC/C,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ;IAGzB,MAAM,cAAc,CAAC;;QACnB,mCAAmC;QACnC,IAAI,CAAC,CAAA,GAAA,0HAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,YAAY;YACtC;QACF;QAEA,sDAAsD;QACtD,MAAM,uBAAuB,OAAO,WAAW,CAAC,KAAK,CAAC,CAAA;YACpD,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,IAAI;YAC1E,OAAO,iBAAiB,cAAc,QAAQ,IAAI,WAAW,QAAQ;QACvE;QAEA,IAAI,CAAC,sBAAsB;YACzB;QACF;QAEA,0BAA0B;QAC1B,OAAO,WAAW,CAAC,OAAO,IAAC,CAAA;;YACzB,cAAc,WAAW,IAAI,EAAE,WAAW,QAAQ;QACpD;;gBADE;;;QAGF,sBAAsB;QACtB,gBAAgB,aAAa;YAC3B,UAAU;YACV,eAAe,OAAO,UAAU;YAChC,eAAe,OAAO,IAAI;QAC5B;QAEA;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,KAAK,KAAK,CAAC,UAAU;QAClC,MAAM,OAAO,UAAU;QACvB,OAAO,AAAC,GAAU,OAAR,MAAK,KAAoC,OAAjC,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG;IAChD;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAO,IAAI,MAAM,CAAC,cAAc,IAAI,MAAM,CAAC,IAAI;IACjD;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAAqC;oCAC7C;oCAAc;;;;;;;0CAEpB,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAY,SAAS;0CAAS;;;;;;;;;;;;;;;;;8BAMlD,6LAAC;oBAAI,WAAU;;wBACZ,iBAAiB,MAAM,KAAK,kBAC3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;oCAAG,WAAU;8CAAyC;;;;;;8CAGvD,6LAAC;oCAAE,WAAU;8CAAqB;;;;;;8CAGlC,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS;8CAAS;;;;;;;;;;;iDAK9C,6LAAC;4BAAI,WAAU;sCACZ,iBAAiB,GAAG,CAAC,CAAA,uBACpB,6LAAC;oCAEC,WAAW,AAAC,yDAIX,OAHC,CAAA,2BAAA,qCAAA,eAAgB,EAAE,MAAK,OAAO,EAAE,GAC5B,mCACA;oCAEN,SAAS,IAAM,kBAAkB;;sDAEjC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAY,cAAc,OAAO,QAAQ;;;;;;sEACzD,6LAAC;4DAAG,WAAU;sEAA+B,OAAO,IAAI;;;;;;;;;;;;8DAE1D,6LAAC;oDAAK,WAAU;;wDAAyB;wDAAE,OAAO,SAAS;;;;;;;;;;;;;sDAG7D,6LAAC;4CAAI,WAAU;;gDACZ,mBAAmB,OAAO,UAAU;gDAAE;gDAAO,WAAW,OAAO,UAAU;;;;;;;sDAG5E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAoC;;;;;;gDAClD,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,YAAY;oDACnC,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,IAAI;oDAE1E,qBACE,6LAAC;wDAEC,WAAU;;0EAEV,6LAAC;0EAAM,WAAW,IAAI;;;;;;0EACtB,6LAAC;;oEACE,WAAW,QAAQ;kFACpB,6LAAC;wEAAK,WAAU;;4EAAO;4EACnB,CAAA,0BAAA,oCAAA,cAAe,QAAQ,KAAI;4EAAE;;;;;;;;;;;;;;uDAP9B;;;;;gDAYX;;;;;;;wCAGD,CAAA,2BAAA,qCAAA,eAAgB,EAAE,MAAK,OAAO,EAAE,kBAC/B,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,YAAY;sDAC5B;;;;;;;mCAhDE,OAAO,EAAE;;;;;;;;;;wBAyDrB,kBAAkB,iBAAiB,MAAM,GAAG,mBAC3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAAiC;wCACjB,eAAe,IAAI;;;;;;;8CAEjD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;gDAAE;gDAAgB,WAAW,eAAe,UAAU;;;;;;;sDACvD,6LAAC;;gDAAE;gDAAe,mBAAmB,eAAe,UAAU;;;;;;;sDAC9D,6LAAC;;gDAAE;gDAAqB,eAAe,SAAS;;;;;;;sDAChD,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnB;GAxKgB;;QACgD,kIAAA,CAAA,UAAO;;;KADvD", "debugId": null}}, {"offset": {"line": 1107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/NotificationSystem.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\n\nexport interface Notification {\n  id: string\n  type: 'success' | 'error' | 'warning' | 'info'\n  title: string\n  message: string\n  duration?: number\n}\n\ninterface NotificationSystemProps {\n  notifications: Notification[]\n  onRemove: (id: string) => void\n}\n\nexport function NotificationSystem({ notifications, onRemove }: NotificationSystemProps) {\n  useEffect(() => {\n    notifications.forEach(notification => {\n      if (notification.duration) {\n        const timer = setTimeout(() => {\n          onRemove(notification.id)\n        }, notification.duration)\n        \n        return () => clearTimeout(timer)\n      }\n    })\n  }, [notifications, onRemove])\n\n  const getNotificationStyle = (type: string) => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-100 border-green-400 text-green-800'\n      case 'error':\n        return 'bg-red-100 border-red-400 text-red-800'\n      case 'warning':\n        return 'bg-yellow-100 border-yellow-400 text-yellow-800'\n      case 'info':\n        return 'bg-blue-100 border-blue-400 text-blue-800'\n      default:\n        return 'bg-gray-100 border-gray-400 text-gray-800'\n    }\n  }\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'success':\n        return '✅'\n      case 'error':\n        return '❌'\n      case 'warning':\n        return '⚠️'\n      case 'info':\n        return 'ℹ️'\n      default:\n        return '📢'\n    }\n  }\n\n  if (notifications.length === 0) return null\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 space-y-2 max-w-sm\">\n      {notifications.map(notification => (\n        <div\n          key={notification.id}\n          className={`p-4 rounded-lg border-l-4 shadow-lg transition-all duration-300 ${getNotificationStyle(notification.type)}`}\n        >\n          <div className=\"flex items-start justify-between\">\n            <div className=\"flex items-start space-x-2\">\n              <span className=\"text-lg\">{getNotificationIcon(notification.type)}</span>\n              <div>\n                <h4 className=\"font-medium\">{notification.title}</h4>\n                <p className=\"text-sm opacity-90\">{notification.message}</p>\n              </div>\n            </div>\n            <button\n              onClick={() => onRemove(notification.id)}\n              className=\"text-lg opacity-60 hover:opacity-100 transition-opacity\"\n            >\n              ×\n            </button>\n          </div>\n        </div>\n      ))}\n    </div>\n  )\n}\n\n// Hook for managing notifications\nexport function useNotifications() {\n  const [notifications, setNotifications] = useState<Notification[]>([])\n\n  const addNotification = (notification: Omit<Notification, 'id'>) => {\n    const id = Date.now().toString() + Math.random().toString(36).substr(2, 9)\n    const newNotification: Notification = {\n      ...notification,\n      id,\n      duration: notification.duration || 5000\n    }\n    setNotifications(prev => [...prev, newNotification])\n  }\n\n  const removeNotification = (id: string) => {\n    setNotifications(prev => prev.filter(n => n.id !== id))\n  }\n\n  const showSuccess = (title: string, message: string) => {\n    addNotification({ type: 'success', title, message })\n  }\n\n  const showError = (title: string, message: string) => {\n    addNotification({ type: 'error', title, message })\n  }\n\n  const showWarning = (title: string, message: string) => {\n    addNotification({ type: 'warning', title, message })\n  }\n\n  const showInfo = (title: string, message: string) => {\n    addNotification({ type: 'info', title, message })\n  }\n\n  return {\n    notifications,\n    addNotification,\n    removeNotification,\n    showSuccess,\n    showError,\n    showWarning,\n    showInfo\n  }\n}\n"], "names": [], "mappings": ";;;;;AAEA;;;AAFA;;AAiBO,SAAS,mBAAmB,KAAoD;QAApD,EAAE,aAAa,EAAE,QAAQ,EAA2B,GAApD;;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR,cAAc,OAAO;gDAAC,CAAA;oBACpB,IAAI,aAAa,QAAQ,EAAE;wBACzB,MAAM,QAAQ;kEAAW;gCACvB,SAAS,aAAa,EAAE;4BAC1B;iEAAG,aAAa,QAAQ;wBAExB;4DAAO,IAAM,aAAa;;oBAC5B;gBACF;;QACF;uCAAG;QAAC;QAAe;KAAS;IAE5B,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBA<PERSON>,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,cAAc,MAAM,KAAK,GAAG,OAAO;IAEvC,qBACE,6LAAC;QAAI,WAAU;kBACZ,cAAc,GAAG,CAAC,CAAA,6BACjB,6LAAC;gBAEC,WAAW,AAAC,mEAA0G,OAAxC,qBAAqB,aAAa,IAAI;0BAEpH,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;8CAAW,oBAAoB,aAAa,IAAI;;;;;;8CAChE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAe,aAAa,KAAK;;;;;;sDAC/C,6LAAC;4CAAE,WAAU;sDAAsB,aAAa,OAAO;;;;;;;;;;;;;;;;;;sCAG3D,6LAAC;4BACC,SAAS,IAAM,SAAS,aAAa,EAAE;4BACvC,WAAU;sCACX;;;;;;;;;;;;eAdE,aAAa,EAAE;;;;;;;;;;AAsB9B;GAvEgB;KAAA;AA0ET,SAAS;;IACd,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,MAAM,kBAAkB,CAAC;QACvB,MAAM,KAAK,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QACxE,MAAM,kBAAgC;YACpC,GAAG,YAAY;YACf;YACA,UAAU,aAAa,QAAQ,IAAI;QACrC;QACA,iBAAiB,CAAA,OAAQ;mBAAI;gBAAM;aAAgB;IACrD;IAEA,MAAM,qBAAqB,CAAC;QAC1B,iBAAiB,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACrD;IAEA,MAAM,cAAc,CAAC,OAAe;QAClC,gBAAgB;YAAE,MAAM;YAAW;YAAO;QAAQ;IACpD;IAEA,MAAM,YAAY,CAAC,OAAe;QAChC,gBAAgB;YAAE,MAAM;YAAS;YAAO;QAAQ;IAClD;IAEA,MAAM,cAAc,CAAC,OAAe;QAClC,gBAAgB;YAAE,MAAM;YAAW;YAAO;QAAQ;IACpD;IAEA,MAAM,WAAW,CAAC,OAAe;QAC/B,gBAAgB;YAAE,MAAM;YAAQ;YAAO;QAAQ;IACjD;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;IA1CgB", "debugId": null}}, {"offset": {"line": 1314, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/LevelUpModal.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui/Button'\nimport { LevelReward } from '@/lib/progressionSystem'\n\ninterface LevelUpModalProps {\n  isOpen: boolean\n  onClose: () => void\n  newLevel: number\n  rewards: LevelReward[]\n}\n\nexport function LevelUpModal({ isOpen, onClose, newLevel, rewards }: LevelUpModalProps) {\n  if (!isOpen) return null\n\n  const getRewardIcon = (type: string) => {\n    switch (type) {\n      case 'recipe': return '📖'\n      case 'equipment': return '⚙️'\n      case 'money': return '💰'\n      case 'skill_point': return '⭐'\n      case 'achievement': return '🏆'\n      default: return '🎁'\n    }\n  }\n\n  const getRewardColor = (type: string) => {\n    switch (type) {\n      case 'recipe': return 'bg-blue-50 border-blue-300 text-blue-800'\n      case 'equipment': return 'bg-purple-50 border-purple-300 text-purple-800'\n      case 'money': return 'bg-green-50 border-green-300 text-green-800'\n      case 'skill_point': return 'bg-yellow-50 border-yellow-300 text-yellow-800'\n      case 'achievement': return 'bg-orange-50 border-orange-300 text-orange-800'\n      default: return 'bg-gray-50 border-gray-300 text-gray-800'\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-md w-full overflow-hidden\">\n        {/* Header with celebration */}\n        <div className=\"bg-gradient-to-r from-yellow-400 to-orange-500 p-6 text-center\">\n          <div className=\"text-6xl mb-2\">🎉</div>\n          <h2 className=\"text-3xl font-bold text-white mb-2\">Level Up!</h2>\n          <p className=\"text-xl text-yellow-100\">\n            You reached Level {newLevel}!\n          </p>\n        </div>\n\n        <div className=\"p-6\">\n          <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">\n            🎁 Level Rewards\n          </h3>\n\n          <div className=\"space-y-3 mb-6\">\n            {rewards.map((reward, index) => (\n              <div\n                key={index}\n                className={`p-3 rounded-lg border ${getRewardColor(reward.type)}`}\n              >\n                <div className=\"flex items-center space-x-3\">\n                  <span className=\"text-2xl\">{getRewardIcon(reward.type)}</span>\n                  <div className=\"flex-1\">\n                    <h4 className=\"font-medium\">{reward.name}</h4>\n                    <p className=\"text-sm opacity-80\">{reward.description}</p>\n                    {reward.value && (\n                      <p className=\"text-sm font-semibold\">\n                        {reward.type === 'money' ? `$${reward.value}` : `+${reward.value}`}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n\n          <div className=\"bg-blue-50 p-4 rounded-lg mb-6\">\n            <h4 className=\"font-medium text-blue-800 mb-2\">💡 What&apos;s Next?</h4>\n            <ul className=\"text-sm text-blue-700 space-y-1\">\n              <li>• Check out new recipes in your recipe book</li>\n              <li>• Visit the shop for new equipment</li>\n              <li>• Take on more challenging orders</li>\n              <li>• Invest in skill upgrades</li>\n            </ul>\n          </div>\n\n          <Button\n            variant=\"primary\"\n            size=\"lg\"\n            className=\"w-full\"\n            onClick={onClose}\n          >\n            🚀 Continue Playing\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYO,SAAS,aAAa,KAAyD;QAAzD,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAqB,GAAzD;IAC3B,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAe,OAAO;YAC3B,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,6LAAC;4BAAG,WAAU;sCAAqC;;;;;;sCACnD,6LAAC;4BAAE,WAAU;;gCAA0B;gCAClB;gCAAS;;;;;;;;;;;;;8BAIhC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCAIzD,6LAAC;4BAAI,WAAU;sCACZ,QAAQ,GAAG,CAAC,CAAC,QAAQ,sBACpB,6LAAC;oCAEC,WAAW,AAAC,yBAAoD,OAA5B,eAAe,OAAO,IAAI;8CAE9D,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAAY,cAAc,OAAO,IAAI;;;;;;0DACrD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAe,OAAO,IAAI;;;;;;kEACxC,6LAAC;wDAAE,WAAU;kEAAsB,OAAO,WAAW;;;;;;oDACpD,OAAO,KAAK,kBACX,6LAAC;wDAAE,WAAU;kEACV,OAAO,IAAI,KAAK,UAAU,AAAC,IAAgB,OAAb,OAAO,KAAK,IAAK,AAAC,IAAgB,OAAb,OAAO,KAAK;;;;;;;;;;;;;;;;;;mCAVnE;;;;;;;;;;sCAmBX,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAIR,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;sCACV;;;;;;;;;;;;;;;;;;;;;;;AAOX;KAtFgB", "debugId": null}}, {"offset": {"line": 1569, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/AchievementsModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { Achievement } from '@/lib/progressionSystem'\n\ninterface AchievementsModalProps {\n  isOpen: boolean\n  onClose: () => void\n  achievements: Achievement[]\n}\n\nexport function AchievementsModal({ isOpen, onClose, achievements }: AchievementsModalProps) {\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n\n  if (!isOpen) return null\n\n  const categories = [\n    { id: 'all', name: 'All', icon: '🏆' },\n    { id: 'baking', name: 'Baking', icon: '👨‍🍳' },\n    { id: 'business', name: 'Business', icon: '💼' },\n    { id: 'efficiency', name: 'Efficiency', icon: '⚡' },\n    { id: 'collection', name: 'Collection', icon: '📚' },\n    { id: 'special', name: 'Special', icon: '⭐' }\n  ]\n\n  const filteredAchievements = selectedCategory === 'all' \n    ? achievements \n    : achievements.filter(achievement => achievement.category === selectedCategory)\n\n  const completedCount = achievements.filter(a => a.completed).length\n  const totalCount = achievements.length\n\n  const getProgressPercentage = (achievement: Achievement) => {\n    if (achievement.completed) return 100\n\n    // Calculate progress for all requirements (use the minimum progress)\n    const progressValues = achievement.requirements.map(req => {\n      if (!req.current) return 0\n      return Math.min(100, (req.current / req.target) * 100)\n    })\n\n    return Math.min(...progressValues)\n  }\n\n  const getProgressText = (achievement: Achievement) => {\n    if (achievement.completed) return 'Completed!'\n\n    // Show progress for the first requirement (most achievements have one)\n    const req = achievement.requirements[0]\n    return `${req.current || 0} / ${req.target}`\n  }\n\n  const getRewardIcon = (type: string) => {\n    switch (type) {\n      case 'recipe': return '📖'\n      case 'equipment': return '⚙️'\n      case 'money': return '💰'\n      case 'skill_point': return '⭐'\n      default: return '🎁'\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-orange-800\">🏆 Achievements</h2>\n              <p className=\"text-gray-600\">\n                {completedCount} of {totalCount} achievements completed\n              </p>\n            </div>\n            <Button variant=\"secondary\" onClick={onClose}>\n              ✕ Close\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Progress Bar */}\n          <div className=\"mb-6\">\n            <div className=\"flex justify-between items-center mb-2\">\n              <span className=\"text-sm font-medium text-gray-700\">Overall Progress</span>\n              <span className=\"text-sm text-gray-500\">\n                {Math.round((completedCount / totalCount) * 100)}%\n              </span>\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-3\">\n              <div \n                className=\"bg-gradient-to-r from-yellow-400 to-orange-500 h-3 rounded-full transition-all duration-500\"\n                style={{ width: `${(completedCount / totalCount) * 100}%` }}\n              ></div>\n            </div>\n          </div>\n\n          {/* Category Filter */}\n          <div className=\"flex flex-wrap gap-2 mb-6\">\n            {categories.map(category => (\n              <Button\n                key={category.id}\n                variant={selectedCategory === category.id ? 'primary' : 'secondary'}\n                size=\"sm\"\n                onClick={() => setSelectedCategory(category.id)}\n              >\n                {category.icon} {category.name}\n              </Button>\n            ))}\n          </div>\n\n          {/* Achievements Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[50vh] overflow-y-auto\">\n            {filteredAchievements.map(achievement => {\n              const progress = getProgressPercentage(achievement)\n              const isCompleted = achievement.completed\n              const isUnlocked = achievement.unlocked\n\n              return (\n                <div\n                  key={achievement.id}\n                  className={`p-4 rounded-lg border-2 ${\n                    isCompleted\n                      ? 'border-green-400 bg-green-50'\n                      : isUnlocked\n                      ? 'border-gray-300 bg-white'\n                      : 'border-gray-200 bg-gray-50 opacity-60'\n                  }`}\n                >\n                  <div className=\"flex items-start space-x-3\">\n                    <div className={`text-3xl ${isCompleted ? 'grayscale-0' : 'grayscale'}`}>\n                      {achievement.icon}\n                    </div>\n                    <div className=\"flex-1\">\n                      <h3 className={`font-semibold ${isCompleted ? 'text-green-800' : 'text-gray-800'}`}>\n                        {achievement.name}\n                        {isCompleted && <span className=\"ml-2\">✅</span>}\n                      </h3>\n                      <p className=\"text-sm text-gray-600 mb-2\">\n                        {achievement.description}\n                      </p>\n\n                      {/* Progress */}\n                      {isUnlocked && !isCompleted && (\n                        <div className=\"mb-2\">\n                          <div className=\"flex justify-between items-center mb-1\">\n                            <span className=\"text-xs text-gray-500\">Progress</span>\n                            <span className=\"text-xs text-gray-500\">\n                              {getProgressText(achievement)}\n                            </span>\n                          </div>\n                          <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                            <div \n                              className=\"bg-blue-500 h-2 rounded-full transition-all duration-300\"\n                              style={{ width: `${progress}%` }}\n                            ></div>\n                          </div>\n                        </div>\n                      )}\n\n                      {/* Reward */}\n                      <div className=\"flex items-center space-x-2 text-sm\">\n                        <span className=\"text-gray-500\">Reward:</span>\n                        <span className=\"text-lg\">{getRewardIcon(achievement.reward.type)}</span>\n                        <span className=\"text-gray-700\">{achievement.reward.name}</span>\n                        {achievement.reward.value && (\n                          <span className=\"text-green-600 font-medium\">\n                            {achievement.reward.type === 'money' ? `$${achievement.reward.value}` : `+${achievement.reward.value}`}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )\n            })}\n          </div>\n\n          {filteredAchievements.length === 0 && (\n            <div className=\"text-center py-8 text-gray-500\">\n              <div className=\"text-4xl mb-2\">🏆</div>\n              <p>No achievements in this category.</p>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAYO,SAAS,kBAAkB,KAAyD;QAAzD,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAA0B,GAAzD;;IAChC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM;YAAO,MAAM;QAAK;QACrC;YAAE,IAAI;YAAU,MAAM;YAAU,MAAM;QAAQ;QAC9C;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAK;QAC/C;YAAE,IAAI;YAAc,MAAM;YAAc,MAAM;QAAI;QAClD;YAAE,IAAI;YAAc,MAAM;YAAc,MAAM;QAAK;QACnD;YAAE,IAAI;YAAW,MAAM;YAAW,MAAM;QAAI;KAC7C;IAED,MAAM,uBAAuB,qBAAqB,QAC9C,eACA,aAAa,MAAM,CAAC,CAAA,cAAe,YAAY,QAAQ,KAAK;IAEhE,MAAM,iBAAiB,aAAa,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,MAAM;IACnE,MAAM,aAAa,aAAa,MAAM;IAEtC,MAAM,wBAAwB,CAAC;QAC7B,IAAI,YAAY,SAAS,EAAE,OAAO;QAElC,qEAAqE;QACrE,MAAM,iBAAiB,YAAY,YAAY,CAAC,GAAG,CAAC,CAAA;YAClD,IAAI,CAAC,IAAI,OAAO,EAAE,OAAO;YACzB,OAAO,KAAK,GAAG,CAAC,KAAK,AAAC,IAAI,OAAO,GAAG,IAAI,MAAM,GAAI;QACpD;QAEA,OAAO,KAAK,GAAG,IAAI;IACrB;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,YAAY,SAAS,EAAE,OAAO;QAElC,uEAAuE;QACvE,MAAM,MAAM,YAAY,YAAY,CAAC,EAAE;QACvC,OAAO,AAAC,GAAwB,OAAtB,IAAI,OAAO,IAAI,GAAE,OAAgB,OAAX,IAAI,MAAM;IAC5C;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAe,OAAO;YAC3B;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC;wCAAE,WAAU;;4CACV;4CAAe;4CAAK;4CAAW;;;;;;;;;;;;;0CAGpC,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAY,SAAS;0CAAS;;;;;;;;;;;;;;;;;8BAMlD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAoC;;;;;;sDACpD,6LAAC;4CAAK,WAAU;;gDACb,KAAK,KAAK,CAAC,AAAC,iBAAiB,aAAc;gDAAK;;;;;;;;;;;;;8CAGrD,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,AAAC,GAAsC,OAApC,AAAC,iBAAiB,aAAc,KAAI;wCAAG;;;;;;;;;;;;;;;;;sCAMhE,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC,qIAAA,CAAA,SAAM;oCAEL,SAAS,qBAAqB,SAAS,EAAE,GAAG,YAAY;oCACxD,MAAK;oCACL,SAAS,IAAM,oBAAoB,SAAS,EAAE;;wCAE7C,SAAS,IAAI;wCAAC;wCAAE,SAAS,IAAI;;mCALzB,SAAS,EAAE;;;;;;;;;;sCAWtB,6LAAC;4BAAI,WAAU;sCACZ,qBAAqB,GAAG,CAAC,CAAA;gCACxB,MAAM,WAAW,sBAAsB;gCACvC,MAAM,cAAc,YAAY,SAAS;gCACzC,MAAM,aAAa,YAAY,QAAQ;gCAEvC,qBACE,6LAAC;oCAEC,WAAW,AAAC,2BAMX,OALC,cACI,iCACA,aACA,6BACA;8CAGN,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAW,AAAC,YAAqD,OAA1C,cAAc,gBAAgB;0DACvD,YAAY,IAAI;;;;;;0DAEnB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAW,AAAC,iBAAiE,OAAjD,cAAc,mBAAmB;;4DAC9D,YAAY,IAAI;4DAChB,6BAAe,6LAAC;gEAAK,WAAU;0EAAO;;;;;;;;;;;;kEAEzC,6LAAC;wDAAE,WAAU;kEACV,YAAY,WAAW;;;;;;oDAIzB,cAAc,CAAC,6BACd,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAwB;;;;;;kFACxC,6LAAC;wEAAK,WAAU;kFACb,gBAAgB;;;;;;;;;;;;0EAGrB,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,OAAO,AAAC,GAAW,OAAT,UAAS;oEAAG;;;;;;;;;;;;;;;;;kEAOvC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAK,WAAU;0EAAW,cAAc,YAAY,MAAM,CAAC,IAAI;;;;;;0EAChE,6LAAC;gEAAK,WAAU;0EAAiB,YAAY,MAAM,CAAC,IAAI;;;;;;4DACvD,YAAY,MAAM,CAAC,KAAK,kBACvB,6LAAC;gEAAK,WAAU;0EACb,YAAY,MAAM,CAAC,IAAI,KAAK,UAAU,AAAC,IAA4B,OAAzB,YAAY,MAAM,CAAC,KAAK,IAAK,AAAC,IAA4B,OAAzB,YAAY,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;mCA/CzG,YAAY,EAAE;;;;;4BAuDzB;;;;;;wBAGD,qBAAqB,MAAM,KAAK,mBAC/B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;8CAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB;GAhLgB;KAAA", "debugId": null}}, {"offset": {"line": 2004, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/SkillTreeModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { SkillTree } from '@/lib/progressionSystem'\n\ninterface SkillTreeModalProps {\n  isOpen: boolean\n  onClose: () => void\n  skills: SkillTree[]\n  skillPoints: number\n  playerLevel: number\n  onUpgradeSkill: (skillId: string) => void\n}\n\nexport function SkillTreeModal({ \n  isOpen, \n  onClose, \n  skills, \n  skillPoints, \n  playerLevel,\n  onUpgradeSkill \n}: SkillTreeModalProps) {\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n\n  if (!isOpen) return null\n\n  const categories = [\n    { id: 'all', name: 'All', icon: '🌟' },\n    { id: 'efficiency', name: 'Efficiency', icon: '⚡' },\n    { id: 'automation', name: 'Automation', icon: '🤖' },\n    { id: 'quality', name: 'Quality', icon: '💎' },\n    { id: 'business', name: 'Business', icon: '💼' }\n  ]\n\n  const filteredSkills = selectedCategory === 'all' \n    ? skills \n    : skills.filter(skill => skill.category === selectedCategory)\n\n  const canUpgradeSkill = (skill: SkillTree) => {\n    if (skill.level >= skill.maxLevel) return false\n    if (skillPoints < skill.cost) return false\n    if (skill.requirements.playerLevel && playerLevel < skill.requirements.playerLevel) return false\n    if (skill.requirements.skills) {\n      return skill.requirements.skills.every(requiredSkillId => {\n        const requiredSkill = skills.find(s => s.id === requiredSkillId)\n        return requiredSkill && requiredSkill.level > 0\n      })\n    }\n    return true\n  }\n\n  const getSkillStatus = (skill: SkillTree) => {\n    if (skill.level >= skill.maxLevel) return 'maxed'\n    if (!canUpgradeSkill(skill)) return 'locked'\n    return 'available'\n  }\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'maxed': return 'border-green-400 bg-green-50'\n      case 'available': return 'border-blue-400 bg-blue-50'\n      case 'locked': return 'border-gray-300 bg-gray-50'\n      default: return 'border-gray-300 bg-gray-50'\n    }\n  }\n\n  const getEffectDescription = (effect: any) => {\n    const percentage = Math.round(effect.value * 100)\n    switch (effect.type) {\n      case 'baking_speed': return `+${percentage}% baking speed`\n      case 'money_multiplier': return `+${percentage}% money earned`\n      case 'xp_multiplier': return `+${percentage}% experience gained`\n      case 'ingredient_efficiency': return `${percentage}% less ingredients used`\n      case 'automation_unlock': return 'Unlock automation features'\n      default: return `+${percentage}% bonus`\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-orange-800\">🌟 Skill Tree</h2>\n              <p className=\"text-gray-600\">\n                Available Skill Points: <span className=\"font-semibold text-blue-600\">{skillPoints}</span>\n              </p>\n            </div>\n            <Button variant=\"secondary\" onClick={onClose}>\n              ✕ Close\n            </Button>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Category Filter */}\n          <div className=\"flex flex-wrap gap-2 mb-6\">\n            {categories.map(category => (\n              <Button\n                key={category.id}\n                variant={selectedCategory === category.id ? 'primary' : 'secondary'}\n                size=\"sm\"\n                onClick={() => setSelectedCategory(category.id)}\n              >\n                {category.icon} {category.name}\n              </Button>\n            ))}\n          </div>\n\n          {/* Skills Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto\">\n            {filteredSkills.map(skill => {\n              const status = getSkillStatus(skill)\n              const canUpgrade = canUpgradeSkill(skill)\n\n              return (\n                <div\n                  key={skill.id}\n                  className={`p-4 rounded-lg border-2 ${getStatusColor(status)}`}\n                >\n                  <div className=\"flex items-start justify-between mb-3\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-2xl\">{skill.icon}</span>\n                      <div>\n                        <h3 className=\"font-semibold text-gray-800\">{skill.name}</h3>\n                        <p className=\"text-xs text-gray-500 uppercase tracking-wide\">\n                          {skill.category}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className=\"text-sm font-medium text-gray-700\">\n                        Level {skill.level}/{skill.maxLevel}\n                      </div>\n                      {status !== 'maxed' && (\n                        <div className=\"text-xs text-blue-600\">\n                          Cost: {skill.cost} SP\n                        </div>\n                      )}\n                    </div>\n                  </div>\n\n                  <p className=\"text-sm text-gray-600 mb-3\">\n                    {skill.description}\n                  </p>\n\n                  {/* Effects */}\n                  <div className=\"mb-3\">\n                    <h4 className=\"text-xs font-medium text-gray-700 mb-1\">Effects:</h4>\n                    {skill.effects.map((effect, index) => (\n                      <div key={index} className=\"text-xs text-green-600\">\n                        • {getEffectDescription(effect)}\n                      </div>\n                    ))}\n                  </div>\n\n                  {/* Requirements */}\n                  {skill.requirements.playerLevel && playerLevel < skill.requirements.playerLevel && (\n                    <div className=\"mb-3\">\n                      <div className=\"text-xs text-red-600\">\n                        Requires Level {skill.requirements.playerLevel}\n                      </div>\n                    </div>\n                  )}\n\n                  {skill.requirements.skills && (\n                    <div className=\"mb-3\">\n                      <div className=\"text-xs text-gray-600\">\n                        Requires: {skill.requirements.skills.map(skillId => {\n                          const reqSkill = skills.find(s => s.id === skillId)\n                          return reqSkill?.name\n                        }).join(', ')}\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Progress Bar */}\n                  <div className=\"mb-3\">\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div \n                        className=\"bg-blue-500 h-2 rounded-full transition-all duration-300\"\n                        style={{ width: `${(skill.level / skill.maxLevel) * 100}%` }}\n                      ></div>\n                    </div>\n                  </div>\n\n                  {/* Upgrade Button */}\n                  {status === 'maxed' ? (\n                    <Button variant=\"success\" size=\"sm\" className=\"w-full\" disabled>\n                      ✅ Maxed\n                    </Button>\n                  ) : canUpgrade ? (\n                    <Button \n                      variant=\"primary\" \n                      size=\"sm\" \n                      className=\"w-full\"\n                      onClick={() => onUpgradeSkill(skill.id)}\n                    >\n                      ⬆️ Upgrade ({skill.cost} SP)\n                    </Button>\n                  ) : (\n                    <Button variant=\"secondary\" size=\"sm\" className=\"w-full\" disabled>\n                      🔒 Locked\n                    </Button>\n                  )}\n                </div>\n              )\n            })}\n          </div>\n\n          {filteredSkills.length === 0 && (\n            <div className=\"text-center py-8 text-gray-500\">\n              <div className=\"text-4xl mb-2\">🌟</div>\n              <p>No skills in this category.</p>\n            </div>\n          )}\n\n          <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n            <h3 className=\"font-medium text-blue-800 mb-2\">💡 Skill Tips</h3>\n            <ul className=\"text-sm text-blue-700 space-y-1\">\n              <li>• Earn skill points by leveling up (1 point every 2 levels)</li>\n              <li>• Some skills require other skills to be unlocked first</li>\n              <li>• Focus on skills that match your playstyle</li>\n              <li>• Efficiency skills help with resource management</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAeO,SAAS,eAAe,KAOT;QAPS,EAC7B,MAAM,EACN,OAAO,EACP,MAAM,EACN,WAAW,EACX,WAAW,EACX,cAAc,EACM,GAPS;;IAQ7B,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM;YAAO,MAAM;QAAK;QACrC;YAAE,IAAI;YAAc,MAAM;YAAc,MAAM;QAAI;QAClD;YAAE,IAAI;YAAc,MAAM;YAAc,MAAM;QAAK;QACnD;YAAE,IAAI;YAAW,MAAM;YAAW,MAAM;QAAK;QAC7C;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAK;KAChD;IAED,MAAM,iBAAiB,qBAAqB,QACxC,SACA,OAAO,MAAM,CAAC,CAAA,QAAS,MAAM,QAAQ,KAAK;IAE9C,MAAM,kBAAkB,CAAC;QACvB,IAAI,MAAM,KAAK,IAAI,MAAM,QAAQ,EAAE,OAAO;QAC1C,IAAI,cAAc,MAAM,IAAI,EAAE,OAAO;QACrC,IAAI,MAAM,YAAY,CAAC,WAAW,IAAI,cAAc,MAAM,YAAY,CAAC,WAAW,EAAE,OAAO;QAC3F,IAAI,MAAM,YAAY,CAAC,MAAM,EAAE;YAC7B,OAAO,MAAM,YAAY,CAAC,MAAM,CAAC,KAAK,CAAC,CAAA;gBACrC,MAAM,gBAAgB,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAChD,OAAO,iBAAiB,cAAc,KAAK,GAAG;YAChD;QACF;QACA,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,MAAM,KAAK,IAAI,MAAM,QAAQ,EAAE,OAAO;QAC1C,IAAI,CAAC,gBAAgB,QAAQ,OAAO;QACpC,OAAO;IACT;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,MAAM,aAAa,KAAK,KAAK,CAAC,OAAO,KAAK,GAAG;QAC7C,OAAQ,OAAO,IAAI;YACjB,KAAK;gBAAgB,OAAO,AAAC,IAAc,OAAX,YAAW;YAC3C,KAAK;gBAAoB,OAAO,AAAC,IAAc,OAAX,YAAW;YAC/C,KAAK;gBAAiB,OAAO,AAAC,IAAc,OAAX,YAAW;YAC5C,KAAK;gBAAyB,OAAO,AAAC,GAAa,OAAX,YAAW;YACnD,KAAK;gBAAqB,OAAO;YACjC;gBAAS,OAAO,AAAC,IAAc,OAAX,YAAW;QACjC;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC;wCAAE,WAAU;;4CAAgB;0DACH,6LAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;;;;;;;;0CAG3E,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAY,SAAS;0CAAS;;;;;;;;;;;;;;;;;8BAMlD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC,qIAAA,CAAA,SAAM;oCAEL,SAAS,qBAAqB,SAAS,EAAE,GAAG,YAAY;oCACxD,MAAK;oCACL,SAAS,IAAM,oBAAoB,SAAS,EAAE;;wCAE7C,SAAS,IAAI;wCAAC;wCAAE,SAAS,IAAI;;mCALzB,SAAS,EAAE;;;;;;;;;;sCAWtB,6LAAC;4BAAI,WAAU;sCACZ,eAAe,GAAG,CAAC,CAAA;gCAClB,MAAM,SAAS,eAAe;gCAC9B,MAAM,aAAa,gBAAgB;gCAEnC,qBACE,6LAAC;oCAEC,WAAW,AAAC,2BAAiD,OAAvB,eAAe;;sDAErD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAY,MAAM,IAAI;;;;;;sEACtC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA+B,MAAM,IAAI;;;;;;8EACvD,6LAAC;oEAAE,WAAU;8EACV,MAAM,QAAQ;;;;;;;;;;;;;;;;;;8DAIrB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEAAoC;gEAC1C,MAAM,KAAK;gEAAC;gEAAE,MAAM,QAAQ;;;;;;;wDAEpC,WAAW,yBACV,6LAAC;4DAAI,WAAU;;gEAAwB;gEAC9B,MAAM,IAAI;gEAAC;;;;;;;;;;;;;;;;;;;sDAM1B,6LAAC;4CAAE,WAAU;sDACV,MAAM,WAAW;;;;;;sDAIpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAyC;;;;;;gDACtD,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,sBAC1B,6LAAC;wDAAgB,WAAU;;4DAAyB;4DAC/C,qBAAqB;;uDADhB;;;;;;;;;;;wCAOb,MAAM,YAAY,CAAC,WAAW,IAAI,cAAc,MAAM,YAAY,CAAC,WAAW,kBAC7E,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;oDAAuB;oDACpB,MAAM,YAAY,CAAC,WAAW;;;;;;;;;;;;wCAKnD,MAAM,YAAY,CAAC,MAAM,kBACxB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;oDAAwB;oDAC1B,MAAM,YAAY,CAAC,MAAM,CAAC,GAAG,CAAC,CAAA;wDACvC,MAAM,WAAW,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;wDAC3C,OAAO,qBAAA,+BAAA,SAAU,IAAI;oDACvB,GAAG,IAAI,CAAC;;;;;;;;;;;;sDAMd,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,AAAC,GAAuC,OAArC,AAAC,MAAM,KAAK,GAAG,MAAM,QAAQ,GAAI,KAAI;oDAAG;;;;;;;;;;;;;;;;wCAMhE,WAAW,wBACV,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;4CAAS,QAAQ;sDAAC;;;;;mDAG9D,2BACF,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,eAAe,MAAM,EAAE;;gDACvC;gDACc,MAAM,IAAI;gDAAC;;;;;;iEAG1B,6LAAC,qIAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAY,MAAK;4CAAK,WAAU;4CAAS,QAAQ;sDAAC;;;;;;;mCApF/D,MAAM,EAAE;;;;;4BA0FnB;;;;;;wBAGD,eAAe,MAAM,KAAK,mBACzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;8CAAE;;;;;;;;;;;;sCAIP,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GAzNgB;KAAA", "debugId": null}}, {"offset": {"line": 2529, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/AutomationModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { AutomationSettings, AUTOMATION_UPGRADES } from '@/lib/automationSystem'\nimport { useGame } from '@/contexts/GameContext'\n\ninterface AutomationModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport function AutomationModal({ isOpen, onClose }: AutomationModalProps) {\n  const { player, equipment, automationSettings, updateAutomationSettings, purchaseAutomationUpgrade } = useGame()\n  const [selectedTab, setSelectedTab] = useState<'settings' | 'upgrades' | 'status'>('settings')\n\n  if (!isOpen) return null\n\n  const automatedEquipment = equipment.filter(eq => eq.automationLevel > 0)\n  const availableUpgrades = AUTOMATION_UPGRADES.filter(upgrade => \n    player.level >= upgrade.unlockLevel && !player.automationUpgrades?.includes(upgrade.id)\n  )\n\n  const handleSettingChange = (key: keyof AutomationSettings, value: boolean | number) => {\n    updateAutomationSettings({ [key]: value })\n  }\n\n  const tabs = [\n    { id: 'settings', name: '<PERSON><PERSON><PERSON>', icon: '⚙️' },\n    { id: 'upgrades', name: 'Upgrades', icon: '🔧' },\n    { id: 'status', name: 'Status', icon: '📊' }\n  ]\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <h2 className=\"text-2xl font-bold text-orange-800\">🤖 Automation Control</h2>\n            <Button variant=\"secondary\" onClick={onClose}>\n              ✕ Close\n            </Button>\n          </div>\n        </div>\n\n        {/* Tab Navigation */}\n        <div className=\"border-b border-gray-200\">\n          <div className=\"flex space-x-0\">\n            {tabs.map(tab => (\n              <button\n                key={tab.id}\n                onClick={() => setSelectedTab(tab.id as 'settings' | 'upgrades' | 'jobs')}\n                className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${\n                  selectedTab === tab.id\n                    ? 'border-orange-500 text-orange-600 bg-orange-50'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'\n                }`}\n              >\n                {tab.icon} {tab.name}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"p-6 max-h-[60vh] overflow-y-auto\">\n          {/* Settings Tab */}\n          {selectedTab === 'settings' && (\n            <div className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {/* Master Control */}\n                <div className=\"bg-blue-50 p-4 rounded-lg\">\n                  <h3 className=\"font-semibold text-blue-800 mb-3\">🎛️ Master Control</h3>\n                  <div className=\"space-y-3\">\n                    <label className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"checkbox\"\n                        checked={automationSettings?.enabled || false}\n                        onChange={(e) => handleSettingChange('enabled', e.target.checked)}\n                        className=\"rounded\"\n                      />\n                      <span className=\"text-sm\">Enable Automation</span>\n                    </label>\n                    <label className=\"flex items-center space-x-2\">\n                      <input\n                        type=\"checkbox\"\n                        checked={automationSettings?.autoStart || false}\n                        onChange={(e) => handleSettingChange('autoStart', e.target.checked)}\n                        className=\"rounded\"\n                      />\n                      <span className=\"text-sm\">Auto-start Equipment</span>\n                    </label>\n                  </div>\n                </div>\n\n                {/* Priority Settings */}\n                <div className=\"bg-green-50 p-4 rounded-lg\">\n                  <h3 className=\"font-semibold text-green-800 mb-3\">🎯 Priority Mode</h3>\n                  <select\n                    value={automationSettings?.priorityMode || 'efficiency'}\n                    onChange={(e) => handleSettingChange('priorityMode', e.target.value)}\n                    className=\"w-full p-2 border rounded-lg\"\n                  >\n                    <option value=\"efficiency\">Efficiency (Orders First)</option>\n                    <option value=\"profit\">Profit (Highest Value)</option>\n                    <option value=\"speed\">Speed (Fastest Recipes)</option>\n                  </select>\n                  <p className=\"text-xs text-green-600 mt-1\">\n                    How automation chooses what to bake\n                  </p>\n                </div>\n\n                {/* Capacity Settings */}\n                <div className=\"bg-purple-50 p-4 rounded-lg\">\n                  <h3 className=\"font-semibold text-purple-800 mb-3\">⚡ Performance</h3>\n                  <div className=\"space-y-2\">\n                    <label className=\"block text-sm\">\n                      Max Concurrent Jobs: {automationSettings?.maxConcurrentJobs || 2}\n                    </label>\n                    <input\n                      type=\"range\"\n                      min=\"1\"\n                      max=\"5\"\n                      value={automationSettings?.maxConcurrentJobs || 2}\n                      onChange={(e) => handleSettingChange('maxConcurrentJobs', parseInt(e.target.value))}\n                      className=\"w-full\"\n                    />\n                  </div>\n                </div>\n\n                {/* Safety Settings */}\n                <div className=\"bg-yellow-50 p-4 rounded-lg\">\n                  <h3 className=\"font-semibold text-yellow-800 mb-3\">🛡️ Safety</h3>\n                  <div className=\"space-y-2\">\n                    <label className=\"block text-sm\">\n                      Stop when ingredients below: {automationSettings?.ingredientThreshold || 5}\n                    </label>\n                    <input\n                      type=\"range\"\n                      min=\"0\"\n                      max=\"20\"\n                      value={automationSettings?.ingredientThreshold || 5}\n                      onChange={(e) => handleSettingChange('ingredientThreshold', parseInt(e.target.value))}\n                      className=\"w-full\"\n                    />\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Upgrades Tab */}\n          {selectedTab === 'upgrades' && (\n            <div className=\"space-y-4\">\n              <div className=\"bg-blue-50 p-4 rounded-lg mb-4\">\n                <h3 className=\"font-medium text-blue-800 mb-2\">💡 Automation Upgrades</h3>\n                <p className=\"text-sm text-blue-700\">\n                  Improve your automation efficiency, speed, and intelligence with these upgrades.\n                </p>\n              </div>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                {availableUpgrades.map(upgrade => (\n                  <div\n                    key={upgrade.id}\n                    className=\"p-4 border rounded-lg bg-white hover:bg-gray-50 transition-colors\"\n                  >\n                    <div className=\"flex justify-between items-start mb-2\">\n                      <h4 className=\"font-semibold text-gray-800\">{upgrade.name}</h4>\n                      <span className=\"text-sm text-green-600 font-medium\">${upgrade.cost}</span>\n                    </div>\n                    <p className=\"text-sm text-gray-600 mb-3\">{upgrade.description}</p>\n                    \n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-xs text-gray-500 uppercase tracking-wide\">\n                        {upgrade.type}\n                      </span>\n                      <Button\n                        size=\"sm\"\n                        variant={player.money >= upgrade.cost ? 'primary' : 'secondary'}\n                        disabled={player.money < upgrade.cost}\n                        onClick={() => purchaseAutomationUpgrade(upgrade.id)}\n                      >\n                        {player.money >= upgrade.cost ? 'Purchase' : 'Too Expensive'}\n                      </Button>\n                    </div>\n                  </div>\n                ))}\n              </div>\n\n              {availableUpgrades.length === 0 && (\n                <div className=\"text-center py-8 text-gray-500\">\n                  <div className=\"text-4xl mb-2\">🔧</div>\n                  <p>No upgrades available at your current level.</p>\n                  <p className=\"text-sm\">Level up to unlock more automation upgrades!</p>\n                </div>\n              )}\n            </div>\n          )}\n\n          {/* Status Tab */}\n          {selectedTab === 'status' && (\n            <div className=\"space-y-6\">\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                <div className=\"bg-green-50 p-4 rounded-lg text-center\">\n                  <div className=\"text-2xl text-green-600 mb-1\">{automatedEquipment.length}</div>\n                  <div className=\"text-sm text-green-800\">Automated Equipment</div>\n                </div>\n                <div className=\"bg-blue-50 p-4 rounded-lg text-center\">\n                  <div className=\"text-2xl text-blue-600 mb-1\">\n                    {automationSettings?.enabled ? '✅' : '❌'}\n                  </div>\n                  <div className=\"text-sm text-blue-800\">Automation Status</div>\n                </div>\n                <div className=\"bg-purple-50 p-4 rounded-lg text-center\">\n                  <div className=\"text-2xl text-purple-600 mb-1\">\n                    {player.automationUpgrades?.length || 0}\n                  </div>\n                  <div className=\"text-sm text-purple-800\">Active Upgrades</div>\n                </div>\n              </div>\n\n              <div className=\"space-y-4\">\n                <h3 className=\"font-semibold text-gray-800\">🏭 Equipment Status</h3>\n                {automatedEquipment.length > 0 ? (\n                  <div className=\"space-y-2\">\n                    {automatedEquipment.map(eq => (\n                      <div key={eq.id} className=\"flex justify-between items-center p-3 bg-gray-50 rounded-lg\">\n                        <div>\n                          <span className=\"font-medium\">{eq.name}</span>\n                          <span className=\"text-sm text-gray-500 ml-2\">\n                            Level {eq.automationLevel} • {eq.efficiency}x efficiency\n                          </span>\n                        </div>\n                        <div className={`px-2 py-1 rounded text-xs ${\n                          eq.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'\n                        }`}>\n                          {eq.isActive ? 'Running' : 'Idle'}\n                        </div>\n                      </div>\n                    ))}\n                  </div>\n                ) : (\n                  <div className=\"text-center py-4 text-gray-500\">\n                    <p>No automated equipment available.</p>\n                    <p className=\"text-sm\">Purchase auto-equipment from the shop to get started!</p>\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAYO,SAAS,gBAAgB,KAAyC;QAAzC,EAAE,MAAM,EAAE,OAAO,EAAwB,GAAzC;QA2MX;;IA1MnB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,kBAAkB,EAAE,wBAAwB,EAAE,yBAAyB,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC7G,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsC;IAEnF,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,qBAAqB,UAAU,MAAM,CAAC,CAAA,KAAM,GAAG,eAAe,GAAG;IACvE,MAAM,oBAAoB,iIAAA,CAAA,sBAAmB,CAAC,MAAM,CAAC,CAAA;YACX;eAAxC,OAAO,KAAK,IAAI,QAAQ,WAAW,IAAI,GAAC,6BAAA,OAAO,kBAAkB,cAAzB,iDAAA,2BAA2B,QAAQ,CAAC,QAAQ,EAAE;;IAGxF,MAAM,sBAAsB,CAAC,KAA+B;QAC1D,yBAAyB;YAAE,CAAC,IAAI,EAAE;QAAM;IAC1C;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAK;QAC/C;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAK;QAC/C;YAAE,IAAI;YAAU,MAAM;YAAU,MAAM;QAAK;KAC5C;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAqC;;;;;;0CACnD,6LAAC,qIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAY,SAAS;0CAAS;;;;;;;;;;;;;;;;;8BAOlD,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAA,oBACR,6LAAC;gCAEC,SAAS,IAAM,eAAe,IAAI,EAAE;gCACpC,WAAW,AAAC,8DAIX,OAHC,gBAAgB,IAAI,EAAE,GAClB,mDACA;;oCAGL,IAAI,IAAI;oCAAC;oCAAE,IAAI,IAAI;;+BARf,IAAI,EAAE;;;;;;;;;;;;;;;8BAcnB,6LAAC;oBAAI,WAAU;;wBAEZ,gBAAgB,4BACf,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEACC,MAAK;gEACL,SAAS,CAAA,+BAAA,yCAAA,mBAAoB,OAAO,KAAI;gEACxC,UAAU,CAAC,IAAM,oBAAoB,WAAW,EAAE,MAAM,CAAC,OAAO;gEAChE,WAAU;;;;;;0EAEZ,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;kEAE5B,6LAAC;wDAAM,WAAU;;0EACf,6LAAC;gEACC,MAAK;gEACL,SAAS,CAAA,+BAAA,yCAAA,mBAAoB,SAAS,KAAI;gEAC1C,UAAU,CAAC,IAAM,oBAAoB,aAAa,EAAE,MAAM,CAAC,OAAO;gEAClE,WAAU;;;;;;0EAEZ,6LAAC;gEAAK,WAAU;0EAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAMhC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAoC;;;;;;0DAClD,6LAAC;gDACC,OAAO,CAAA,+BAAA,yCAAA,mBAAoB,YAAY,KAAI;gDAC3C,UAAU,CAAC,IAAM,oBAAoB,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDACnE,WAAU;;kEAEV,6LAAC;wDAAO,OAAM;kEAAa;;;;;;kEAC3B,6LAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,6LAAC;wDAAO,OAAM;kEAAQ;;;;;;;;;;;;0DAExB,6LAAC;gDAAE,WAAU;0DAA8B;;;;;;;;;;;;kDAM7C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;;4DAAgB;4DACT,CAAA,+BAAA,yCAAA,mBAAoB,iBAAiB,KAAI;;;;;;;kEAEjE,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,OAAO,CAAA,+BAAA,yCAAA,mBAAoB,iBAAiB,KAAI;wDAChD,UAAU,CAAC,IAAM,oBAAoB,qBAAqB,SAAS,EAAE,MAAM,CAAC,KAAK;wDACjF,WAAU;;;;;;;;;;;;;;;;;;kDAMhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAqC;;;;;;0DACnD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAM,WAAU;;4DAAgB;4DACD,CAAA,+BAAA,yCAAA,mBAAoB,mBAAmB,KAAI;;;;;;;kEAE3E,6LAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,KAAI;wDACJ,OAAO,CAAA,+BAAA,yCAAA,mBAAoB,mBAAmB,KAAI;wDAClD,UAAU,CAAC,IAAM,oBAAoB,uBAAuB,SAAS,EAAE,MAAM,CAAC,KAAK;wDACnF,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;wBASrB,gBAAgB,4BACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAiC;;;;;;sDAC/C,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAKvC,6LAAC;oCAAI,WAAU;8CACZ,kBAAkB,GAAG,CAAC,CAAA,wBACrB,6LAAC;4CAEC,WAAU;;8DAEV,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA+B,QAAQ,IAAI;;;;;;sEACzD,6LAAC;4DAAK,WAAU;;gEAAqC;gEAAE,QAAQ,IAAI;;;;;;;;;;;;;8DAErE,6LAAC;oDAAE,WAAU;8DAA8B,QAAQ,WAAW;;;;;;8DAE9D,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEACb,QAAQ,IAAI;;;;;;sEAEf,6LAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAS,OAAO,KAAK,IAAI,QAAQ,IAAI,GAAG,YAAY;4DACpD,UAAU,OAAO,KAAK,GAAG,QAAQ,IAAI;4DACrC,SAAS,IAAM,0BAA0B,QAAQ,EAAE;sEAElD,OAAO,KAAK,IAAI,QAAQ,IAAI,GAAG,aAAa;;;;;;;;;;;;;2CAnB5C,QAAQ,EAAE;;;;;;;;;;gCA0BpB,kBAAkB,MAAM,KAAK,mBAC5B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,6LAAC;sDAAE;;;;;;sDACH,6LAAC;4CAAE,WAAU;sDAAU;;;;;;;;;;;;;;;;;;wBAO9B,gBAAgB,0BACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgC,mBAAmB,MAAM;;;;;;8DACxE,6LAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,CAAA,+BAAA,yCAAA,mBAAoB,OAAO,IAAG,MAAM;;;;;;8DAEvC,6LAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACZ,EAAA,6BAAA,OAAO,kBAAkB,cAAzB,iDAAA,2BAA2B,MAAM,KAAI;;;;;;8DAExC,6LAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;8CAI7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA8B;;;;;;wCAC3C,mBAAmB,MAAM,GAAG,kBAC3B,6LAAC;4CAAI,WAAU;sDACZ,mBAAmB,GAAG,CAAC,CAAA,mBACtB,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAe,GAAG,IAAI;;;;;;8EACtC,6LAAC;oEAAK,WAAU;;wEAA6B;wEACpC,GAAG,eAAe;wEAAC;wEAAI,GAAG,UAAU;wEAAC;;;;;;;;;;;;;sEAGhD,6LAAC;4DAAI,WAAW,AAAC,6BAEhB,OADC,GAAG,QAAQ,GAAG,gCAAgC;sEAE7C,GAAG,QAAQ,GAAG,YAAY;;;;;;;mDAVrB,GAAG,EAAE;;;;;;;;;iEAgBnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;8DAAE;;;;;;8DACH,6LAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU3C;GAlPgB;;QACyF,kIAAA,CAAA,UAAO;;;KADhG", "debugId": null}}, {"offset": {"line": 3268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/EquipmentShopModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { useGame } from '@/contexts/GameContext'\n\ninterface EquipmentItem {\n  id: string\n  name: string\n  type: string\n  description: string\n  cost: number\n  unlockLevel: number\n  automationLevel: number\n  efficiency: number\n  icon: string\n  category: 'basic' | 'automated' | 'advanced'\n}\n\nconst EQUIPMENT_SHOP: EquipmentItem[] = [\n  {\n    id: 'professional_oven',\n    name: 'Professional Oven',\n    type: 'oven',\n    description: 'Faster and more efficient than basic oven',\n    cost: 500,\n    unlockLevel: 3,\n    automationLevel: 0,\n    efficiency: 1.3,\n    icon: '🔥',\n    category: 'basic'\n  },\n  {\n    id: 'auto_oven',\n    name: 'Automated Oven',\n    type: 'auto_oven',\n    description: 'Fully automated oven that can run without supervision',\n    cost: 1500,\n    unlockLevel: 5,\n    automationLevel: 2,\n    efficiency: 1.5,\n    icon: '🤖',\n    category: 'automated'\n  },\n  {\n    id: 'industrial_mixer',\n    name: 'Industrial Mixer',\n    type: 'mixer',\n    description: 'High-capacity mixer for large batches',\n    cost: 750,\n    unlockLevel: 4,\n    automationLevel: 0,\n    efficiency: 1.4,\n    icon: '🥄',\n    category: 'basic'\n  },\n  {\n    id: 'auto_mixer',\n    name: 'Automated Mixer',\n    type: 'auto_mixer',\n    description: 'Self-operating mixer with ingredient dispensing',\n    cost: 2000,\n    unlockLevel: 6,\n    automationLevel: 2,\n    efficiency: 1.6,\n    icon: '🤖',\n    category: 'automated'\n  },\n  {\n    id: 'conveyor_belt_basic',\n    name: 'Basic Conveyor Belt',\n    type: 'conveyor',\n    description: 'Moves items between equipment automatically',\n    cost: 1000,\n    unlockLevel: 7,\n    automationLevel: 1,\n    efficiency: 1.2,\n    icon: '🔄',\n    category: 'automated'\n  },\n  {\n    id: 'smart_conveyor',\n    name: 'Smart Conveyor System',\n    type: 'conveyor',\n    description: 'Intelligent conveyor with sorting and routing',\n    cost: 3000,\n    unlockLevel: 10,\n    automationLevel: 3,\n    efficiency: 1.8,\n    icon: '🧠',\n    category: 'advanced'\n  },\n  {\n    id: 'master_oven',\n    name: 'Master Oven',\n    type: 'oven',\n    description: 'The ultimate baking machine with AI assistance',\n    cost: 5000,\n    unlockLevel: 12,\n    automationLevel: 3,\n    efficiency: 2.0,\n    icon: '👑',\n    category: 'advanced'\n  }\n]\n\ninterface EquipmentShopModalProps {\n  isOpen: boolean\n  onClose: () => void\n  onShowSuccess?: (title: string, message: string) => void\n}\n\nexport function EquipmentShopModal({ isOpen, onClose, onShowSuccess }: EquipmentShopModalProps) {\n  const { player, equipment, spendMoney, addEquipment } = useGame()\n  const [selectedCategory, setSelectedCategory] = useState<string>('all')\n\n  if (!isOpen) return null\n\n  const categories = [\n    { id: 'all', name: 'All', icon: '🏪' },\n    { id: 'basic', name: 'Basic', icon: '🔧' },\n    { id: 'automated', name: 'Automated', icon: '🤖' },\n    { id: 'advanced', name: 'Advanced', icon: '⚡' }\n  ]\n\n  const availableEquipment = EQUIPMENT_SHOP.filter(item => \n    player.level >= item.unlockLevel &&\n    (selectedCategory === 'all' || item.category === selectedCategory)\n  )\n\n  const purchaseEquipment = (item: EquipmentItem) => {\n    if (player.money < item.cost) return\n\n    if (spendMoney(item.cost)) {\n      const newEquipment = {\n        name: item.name,\n        type: item.type as 'oven' | 'mixer' | 'counter' | 'display' | 'storage',\n        isActive: false,\n        level: 1,\n        efficiency: item.efficiency,\n        automationLevel: item.automationLevel\n      }\n\n      addEquipment(newEquipment)\n      if (onShowSuccess) {\n        onShowSuccess('Equipment Purchased!', `You bought ${item.name}!`)\n      }\n    }\n  }\n\n  const getAutomationBadge = (level: number) => {\n    if (level === 0) return null\n    return (\n      <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800\">\n        🤖 Auto Level {level}\n      </span>\n    )\n  }\n\n  const getCategoryColor = (category: string) => {\n    switch (category) {\n      case 'basic': return 'border-gray-300 bg-gray-50'\n      case 'automated': return 'border-blue-300 bg-blue-50'\n      case 'advanced': return 'border-purple-300 bg-purple-50'\n      default: return 'border-gray-300 bg-white'\n    }\n  }\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-orange-800\">🏪 Equipment Shop</h2>\n              <p className=\"text-gray-600\">\n                Upgrade your bakery with professional equipment\n              </p>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"bg-green-100 px-3 py-1 rounded-full\">\n                <span className=\"text-green-800 font-medium\">${player.money}</span>\n              </div>\n              <Button variant=\"secondary\" onClick={onClose}>\n                ✕ Close\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        <div className=\"p-6\">\n          {/* Category Filter */}\n          <div className=\"flex flex-wrap gap-2 mb-6\">\n            {categories.map(category => (\n              <Button\n                key={category.id}\n                variant={selectedCategory === category.id ? 'primary' : 'secondary'}\n                size=\"sm\"\n                onClick={() => setSelectedCategory(category.id)}\n              >\n                {category.icon} {category.name}\n              </Button>\n            ))}\n          </div>\n\n          {/* Equipment Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto\">\n            {availableEquipment.map(item => (\n              <div\n                key={item.id}\n                className={`p-4 rounded-lg border-2 ${getCategoryColor(item.category)}`}\n              >\n                <div className=\"flex items-start justify-between mb-3\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"text-2xl\">{item.icon}</span>\n                    <div>\n                      <h3 className=\"font-semibold text-gray-800\">{item.name}</h3>\n                      <p className=\"text-xs text-gray-500 uppercase tracking-wide\">\n                        {item.category}\n                      </p>\n                    </div>\n                  </div>\n                  <span className=\"text-lg font-bold text-green-600\">${item.cost}</span>\n                </div>\n\n                <p className=\"text-sm text-gray-600 mb-3\">{item.description}</p>\n\n                <div className=\"space-y-2 mb-4\">\n                  <div className=\"flex justify-between text-sm\">\n                    <span className=\"text-gray-600\">Efficiency:</span>\n                    <span className=\"font-medium\">{item.efficiency}x</span>\n                  </div>\n                  {item.automationLevel > 0 && (\n                    <div className=\"flex justify-between text-sm\">\n                      <span className=\"text-gray-600\">Automation:</span>\n                      {getAutomationBadge(item.automationLevel)}\n                    </div>\n                  )}\n                  <div className=\"flex justify-between text-sm\">\n                    <span className=\"text-gray-600\">Unlock Level:</span>\n                    <span className=\"font-medium\">{item.unlockLevel}</span>\n                  </div>\n                </div>\n\n                <Button\n                  variant={player.money >= item.cost ? 'success' : 'secondary'}\n                  size=\"sm\"\n                  className=\"w-full\"\n                  disabled={player.money < item.cost}\n                  onClick={() => purchaseEquipment(item)}\n                >\n                  {player.money >= item.cost ? '💰 Purchase' : '💸 Too Expensive'}\n                </Button>\n              </div>\n            ))}\n          </div>\n\n          {availableEquipment.length === 0 && (\n            <div className=\"text-center py-8 text-gray-500\">\n              <div className=\"text-4xl mb-2\">🏪</div>\n              <p>No equipment available in this category.</p>\n              <p className=\"text-sm\">Level up to unlock more equipment!</p>\n            </div>\n          )}\n\n          <div className=\"mt-6 p-4 bg-blue-50 rounded-lg\">\n            <h3 className=\"font-medium text-blue-800 mb-2\">💡 Equipment Tips</h3>\n            <ul className=\"text-sm text-blue-700 space-y-1\">\n              <li>• Automated equipment can run without your supervision</li>\n              <li>• Higher efficiency means faster production and better quality</li>\n              <li>• Conveyor belts connect equipment for seamless workflow</li>\n              <li>• Advanced equipment unlocks at higher levels</li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAmBA,MAAM,iBAAkC;IACtC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,aAAa;QACb,MAAM;QACN,aAAa;QACb,iBAAiB;QACjB,YAAY;QACZ,MAAM;QACN,UAAU;IACZ;CACD;AAQM,SAAS,mBAAmB,KAA2D;QAA3D,EAAE,MAAM,EAAE,OAAO,EAAE,aAAa,EAA2B,GAA3D;;IACjC,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC9D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM;YAAO,MAAM;QAAK;QACrC;YAAE,IAAI;YAAS,MAAM;YAAS,MAAM;QAAK;QACzC;YAAE,IAAI;YAAa,MAAM;YAAa,MAAM;QAAK;QACjD;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAI;KAC/C;IAED,MAAM,qBAAqB,eAAe,MAAM,CAAC,CAAA,OAC/C,OAAO,KAAK,IAAI,KAAK,WAAW,IAChC,CAAC,qBAAqB,SAAS,KAAK,QAAQ,KAAK,gBAAgB;IAGnE,MAAM,oBAAoB,CAAC;QACzB,IAAI,OAAO,KAAK,GAAG,KAAK,IAAI,EAAE;QAE9B,IAAI,WAAW,KAAK,IAAI,GAAG;YACzB,MAAM,eAAe;gBACnB,MAAM,KAAK,IAAI;gBACf,MAAM,KAAK,IAAI;gBACf,UAAU;gBACV,OAAO;gBACP,YAAY,KAAK,UAAU;gBAC3B,iBAAiB,KAAK,eAAe;YACvC;YAEA,aAAa;YACb,IAAI,eAAe;gBACjB,cAAc,wBAAwB,AAAC,cAAuB,OAAV,KAAK,IAAI,EAAC;YAChE;QACF;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,UAAU,GAAG,OAAO;QACxB,qBACE,6LAAC;YAAK,WAAU;;gBAAgG;gBAC/F;;;;;;;IAGrB;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAqC;;;;;;kDACnD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAI/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;;gDAA6B;gDAAE,OAAO,KAAK;;;;;;;;;;;;kDAE7D,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAY,SAAS;kDAAS;;;;;;;;;;;;;;;;;;;;;;;8BAOpD,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACZ,WAAW,GAAG,CAAC,CAAA,yBACd,6LAAC,qIAAA,CAAA,SAAM;oCAEL,SAAS,qBAAqB,SAAS,EAAE,GAAG,YAAY;oCACxD,MAAK;oCACL,SAAS,IAAM,oBAAoB,SAAS,EAAE;;wCAE7C,SAAS,IAAI;wCAAC;wCAAE,SAAS,IAAI;;mCALzB,SAAS,EAAE;;;;;;;;;;sCAWtB,6LAAC;4BAAI,WAAU;sCACZ,mBAAmB,GAAG,CAAC,CAAA,qBACtB,6LAAC;oCAEC,WAAW,AAAC,2BAA0D,OAAhC,iBAAiB,KAAK,QAAQ;;sDAEpE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAY,KAAK,IAAI;;;;;;sEACrC,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EAA+B,KAAK,IAAI;;;;;;8EACtD,6LAAC;oEAAE,WAAU;8EACV,KAAK,QAAQ;;;;;;;;;;;;;;;;;;8DAIpB,6LAAC;oDAAK,WAAU;;wDAAmC;wDAAE,KAAK,IAAI;;;;;;;;;;;;;sDAGhE,6LAAC;4CAAE,WAAU;sDAA8B,KAAK,WAAW;;;;;;sDAE3D,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;;gEAAe,KAAK,UAAU;gEAAC;;;;;;;;;;;;;gDAEhD,KAAK,eAAe,GAAG,mBACtB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;wDAC/B,mBAAmB,KAAK,eAAe;;;;;;;8DAG5C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,6LAAC;4DAAK,WAAU;sEAAe,KAAK,WAAW;;;;;;;;;;;;;;;;;;sDAInD,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,YAAY;4CACjD,MAAK;4CACL,WAAU;4CACV,UAAU,OAAO,KAAK,GAAG,KAAK,IAAI;4CAClC,SAAS,IAAM,kBAAkB;sDAEhC,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,gBAAgB;;;;;;;mCA1C1C,KAAK,EAAE;;;;;;;;;;wBAgDjB,mBAAmB,MAAM,KAAK,mBAC7B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,6LAAC;8CAAE;;;;;;8CACH,6LAAC;oCAAE,WAAU;8CAAU;;;;;;;;;;;;sCAI3B,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GAtKgB;;QAC0C,kIAAA,CAAA,UAAO;;;KADjD", "debugId": null}}, {"offset": {"line": 3842, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/BakeryManagerModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { useLanguage } from '@/contexts/LanguageContext'\nimport { BakeryLocation } from '@/lib/saveSystem'\n\ninterface BakeryManagerModalProps {\n  isOpen: boolean\n  onClose: () => void\n  bakeries: BakeryLocation[]\n  currentBakeryId: string\n  onSwitchBakery: (bakeryId: string) => void\n  onPurchaseBakery: (bakery: BakeryLocation) => void\n  playerMoney: number\n}\n\nconst AVAILABLE_BAKERIES: Omit<BakeryLocation, 'id' | 'unlocked'>[] = [\n  {\n    name: 'Downtown Delights',\n    location: 'City Center',\n    specialization: 'general',\n    level: 1,\n    equipment: [],\n    inventory: [],\n    orders: [],\n    automationJobs: [],\n    conveyorBelts: [],\n    purchaseCost: 0 // Main bakery is free\n  },\n  {\n    name: 'Cookie Corner',\n    location: 'Shopping Mall',\n    specialization: 'cookies',\n    level: 1,\n    equipment: [],\n    inventory: [],\n    orders: [],\n    automationJobs: [],\n    conveyorBelts: [],\n    purchaseCost: 2500\n  },\n  {\n    name: 'Cake Castle',\n    location: 'Wedding District',\n    specialization: 'cakes',\n    level: 1,\n    equipment: [],\n    inventory: [],\n    orders: [],\n    automationJobs: [],\n    conveyorBelts: [],\n    purchaseCost: 3500\n  },\n  {\n    name: 'Bread Basket',\n    location: 'Farmers Market',\n    specialization: 'bread',\n    level: 1,\n    equipment: [],\n    inventory: [],\n    orders: [],\n    automationJobs: [],\n    conveyorBelts: [],\n    purchaseCost: 3000\n  },\n  {\n    name: 'Pastry Palace',\n    location: 'French Quarter',\n    specialization: 'pastries',\n    level: 1,\n    equipment: [],\n    inventory: [],\n    orders: [],\n    automationJobs: [],\n    conveyorBelts: [],\n    purchaseCost: 4000\n  }\n]\n\nexport function BakeryManagerModal({\n  isOpen,\n  onClose,\n  bakeries,\n  currentBakeryId,\n  onSwitchBakery,\n  onPurchaseBakery,\n  playerMoney\n}: BakeryManagerModalProps) {\n  const { t } = useLanguage()\n  const [selectedTab, setSelectedTab] = useState<'owned' | 'available'>('owned')\n\n  if (!isOpen) return null\n\n  const ownedBakeries = bakeries.filter(b => b.unlocked)\n  const availableBakeries = AVAILABLE_BAKERIES.filter(ab => \n    !bakeries.some(b => b.name === ab.name && b.unlocked)\n  )\n\n  const getSpecializationIcon = (specialization: string) => {\n    switch (specialization) {\n      case 'cookies': return '🍪'\n      case 'cakes': return '🧁'\n      case 'bread': return '🍞'\n      case 'pastries': return '🥐'\n      default: return '🏪'\n    }\n  }\n\n  const getSpecializationBonus = (specialization: string) => {\n    switch (specialization) {\n      case 'cookies': return '+20% Cookie Production Speed'\n      case 'cakes': return '+25% Cake Profit Margin'\n      case 'bread': return '+15% Bread Ingredient Efficiency'\n      case 'pastries': return '+30% Pastry Experience Gain'\n      default: return 'Balanced Production'\n    }\n  }\n\n  const handlePurchaseBakery = (bakery: Omit<BakeryLocation, 'id' | 'unlocked'>) => {\n    if (playerMoney >= bakery.purchaseCost) {\n      const newBakery: BakeryLocation = {\n        ...bakery,\n        id: `bakery_${Date.now()}`,\n        unlocked: true\n      }\n      onPurchaseBakery(newBakery)\n    }\n  }\n\n  const tabs = [\n    { id: 'owned', name: t('bakeries.owned') || 'My Bakeries', icon: '🏪' },\n    { id: 'available', name: t('bakeries.available') || 'Available', icon: '🛒' }\n  ]\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n        <div className=\"p-6 border-b border-gray-200\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h2 className=\"text-2xl font-bold text-orange-800\">\n                {t('bakeries.title') || '🏪 Bakery Manager'}\n              </h2>\n              <p className=\"text-gray-600\">\n                {t('bakeries.subtitle') || 'Manage your bakery empire'}\n              </p>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"bg-green-100 px-3 py-1 rounded-full\">\n                <span className=\"text-green-800 font-medium\">${playerMoney}</span>\n              </div>\n              <Button variant=\"secondary\" onClick={onClose}>\n                {t('game.close') || '✕ Close'}\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Tab Navigation */}\n        <div className=\"border-b border-gray-200\">\n          <div className=\"flex space-x-0\">\n            {tabs.map(tab => (\n              <button\n                key={tab.id}\n                onClick={() => setSelectedTab(tab.id as 'overview' | 'locations' | 'expansion')}\n                className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${\n                  selectedTab === tab.id\n                    ? 'border-orange-500 text-orange-600 bg-orange-50'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'\n                }`}\n              >\n                {tab.icon} {tab.name}\n              </button>\n            ))}\n          </div>\n        </div>\n\n        <div className=\"p-6 max-h-[60vh] overflow-y-auto\">\n          {/* Owned Bakeries */}\n          {selectedTab === 'owned' && (\n            <div className=\"space-y-4\">\n              {ownedBakeries.length > 0 ? (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  {ownedBakeries.map(bakery => (\n                    <div\n                      key={bakery.id}\n                      className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${\n                        bakery.id === currentBakeryId\n                          ? 'border-orange-400 bg-orange-50'\n                          : 'border-gray-300 bg-white hover:border-orange-300'\n                      }`}\n                      onClick={() => onSwitchBakery(bakery.id)}\n                    >\n                      <div className=\"flex items-start justify-between mb-3\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"text-2xl\">{getSpecializationIcon(bakery.specialization)}</span>\n                          <div>\n                            <h3 className=\"font-semibold text-gray-800\">{bakery.name}</h3>\n                            <p className=\"text-sm text-gray-600\">{bakery.location}</p>\n                          </div>\n                        </div>\n                        {bakery.id === currentBakeryId && (\n                          <span className=\"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full\">\n                            {t('bakeries.current') || 'Current'}\n                          </span>\n                        )}\n                      </div>\n\n                      <div className=\"space-y-2 text-sm\">\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">{t('bakeries.level') || 'Level'}:</span>\n                          <span className=\"font-medium\">{bakery.level}</span>\n                        </div>\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">{t('bakeries.specialization') || 'Specialization'}:</span>\n                          <span className=\"font-medium capitalize\">{bakery.specialization}</span>\n                        </div>\n                        <div className=\"text-xs text-blue-600\">\n                          {getSpecializationBonus(bakery.specialization)}\n                        </div>\n                      </div>\n\n                      <div className=\"mt-3 pt-3 border-t border-gray-200\">\n                        <div className=\"flex justify-between text-sm\">\n                          <span className=\"text-gray-600\">{t('bakeries.equipment') || 'Equipment'}:</span>\n                          <span>{bakery.equipment.length}</span>\n                        </div>\n                        <div className=\"flex justify-between text-sm\">\n                          <span className=\"text-gray-600\">{t('bakeries.orders') || 'Active Orders'}:</span>\n                          <span>{bakery.orders.length}</span>\n                        </div>\n                      </div>\n\n                      {bakery.id !== currentBakeryId && (\n                        <Button\n                          variant=\"primary\"\n                          size=\"sm\"\n                          className=\"w-full mt-3\"\n                          onClick={(e) => {\n                            e.stopPropagation()\n                            onSwitchBakery(bakery.id)\n                          }}\n                        >\n                          {t('bakeries.switchTo') || 'Switch To'}\n                        </Button>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"text-center py-8 text-gray-500\">\n                  <div className=\"text-4xl mb-2\">🏪</div>\n                  <p>{t('bakeries.noOwned') || 'You don\\'t own any bakeries yet.'}</p>\n                </div>\n              )}\n            </div>\n          )}\n\n          {/* Available Bakeries */}\n          {selectedTab === 'available' && (\n            <div className=\"space-y-4\">\n              {availableBakeries.length > 0 ? (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  {availableBakeries.map((bakery, index) => (\n                    <div\n                      key={index}\n                      className=\"p-4 rounded-lg border-2 border-gray-300 bg-white\"\n                    >\n                      <div className=\"flex items-start justify-between mb-3\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"text-2xl\">{getSpecializationIcon(bakery.specialization)}</span>\n                          <div>\n                            <h3 className=\"font-semibold text-gray-800\">{bakery.name}</h3>\n                            <p className=\"text-sm text-gray-600\">{bakery.location}</p>\n                          </div>\n                        </div>\n                        <span className=\"text-lg font-bold text-green-600\">${bakery.purchaseCost}</span>\n                      </div>\n\n                      <div className=\"space-y-2 text-sm mb-4\">\n                        <div className=\"flex justify-between\">\n                          <span className=\"text-gray-600\">{t('bakeries.specialization') || 'Specialization'}:</span>\n                          <span className=\"font-medium capitalize\">{bakery.specialization}</span>\n                        </div>\n                        <div className=\"text-xs text-blue-600\">\n                          {getSpecializationBonus(bakery.specialization)}\n                        </div>\n                      </div>\n\n                      <Button\n                        variant={playerMoney >= bakery.purchaseCost ? 'success' : 'secondary'}\n                        size=\"sm\"\n                        className=\"w-full\"\n                        disabled={playerMoney < bakery.purchaseCost}\n                        onClick={() => handlePurchaseBakery(bakery)}\n                      >\n                        {playerMoney >= bakery.purchaseCost \n                          ? (t('bakeries.purchase') || '💰 Purchase')\n                          : (t('bakeries.tooExpensive') || '💸 Too Expensive')\n                        }\n                      </Button>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <div className=\"text-center py-8 text-gray-500\">\n                  <div className=\"text-4xl mb-2\">🎉</div>\n                  <p>{t('bakeries.allOwned') || 'You own all available bakeries!'}</p>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n\n        <div className=\"p-4 bg-blue-50 border-t border-gray-200\">\n          <h3 className=\"font-medium text-blue-800 mb-2\">\n            {t('bakeries.tips') || '💡 Bakery Tips'}\n          </h3>\n          <ul className=\"text-sm text-blue-700 space-y-1\">\n            <li>• {t('bakeries.tip1') || 'Each bakery specializes in different products for bonus efficiency'}</li>\n            <li>• {t('bakeries.tip2') || 'Switch between bakeries to manage multiple locations'}</li>\n            <li>• {t('bakeries.tip3') || 'Specialized bakeries attract customers looking for specific items'}</li>\n            <li>• {t('bakeries.tip4') || 'Upgrade each bakery independently for maximum profit'}</li>\n          </ul>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAiBA,MAAM,qBAAgE;IACpE;QACE,MAAM;QACN,UAAU;QACV,gBAAgB;QAChB,OAAO;QACP,WAAW,EAAE;QACb,WAAW,EAAE;QACb,QAAQ,EAAE;QACV,gBAAgB,EAAE;QAClB,eAAe,EAAE;QACjB,cAAc,EAAE,sBAAsB;IACxC;IACA;QACE,MAAM;QACN,UAAU;QACV,gBAAgB;QAChB,OAAO;QACP,WAAW,EAAE;QACb,WAAW,EAAE;QACb,QAAQ,EAAE;QACV,gBAAgB,EAAE;QAClB,eAAe,EAAE;QACjB,cAAc;IAChB;IACA;QACE,MAAM;QACN,UAAU;QACV,gBAAgB;QAChB,OAAO;QACP,WAAW,EAAE;QACb,WAAW,EAAE;QACb,QAAQ,EAAE;QACV,gBAAgB,EAAE;QAClB,eAAe,EAAE;QACjB,cAAc;IAChB;IACA;QACE,MAAM;QACN,UAAU;QACV,gBAAgB;QAChB,OAAO;QACP,WAAW,EAAE;QACb,WAAW,EAAE;QACb,QAAQ,EAAE;QACV,gBAAgB,EAAE;QAClB,eAAe,EAAE;QACjB,cAAc;IAChB;IACA;QACE,MAAM;QACN,UAAU;QACV,gBAAgB;QAChB,OAAO;QACP,WAAW,EAAE;QACb,WAAW,EAAE;QACb,QAAQ,EAAE;QACV,gBAAgB,EAAE;QAClB,eAAe,EAAE;QACjB,cAAc;IAChB;CACD;AAEM,SAAS,mBAAmB,KAQT;QARS,EACjC,MAAM,EACN,OAAO,EACP,QAAQ,EACR,eAAe,EACf,cAAc,EACd,gBAAgB,EAChB,WAAW,EACa,GARS;;IASjC,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IACxB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAEtE,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,gBAAgB,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ;IACrD,MAAM,oBAAoB,mBAAmB,MAAM,CAAC,CAAA,KAClD,CAAC,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,GAAG,IAAI,IAAI,EAAE,QAAQ;IAGtD,MAAM,wBAAwB,CAAC;QAC7B,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAQ;YACN,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,eAAe,OAAO,YAAY,EAAE;YACtC,MAAM,YAA4B;gBAChC,GAAG,MAAM;gBACT,IAAI,AAAC,UAAoB,OAAX,KAAK,GAAG;gBACtB,UAAU;YACZ;YACA,iBAAiB;QACnB;IACF;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAS,MAAM,EAAE,qBAAqB;YAAe,MAAM;QAAK;QACtE;YAAE,IAAI;YAAa,MAAM,EAAE,yBAAyB;YAAa,MAAM;QAAK;KAC7E;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDACX,EAAE,qBAAqB;;;;;;kDAE1B,6LAAC;wCAAE,WAAU;kDACV,EAAE,wBAAwB;;;;;;;;;;;;0CAG/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;;gDAA6B;gDAAE;;;;;;;;;;;;kDAEjD,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAY,SAAS;kDAClC,EAAE,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;8BAO5B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,KAAK,GAAG,CAAC,CAAA,oBACR,6LAAC;gCAEC,SAAS,IAAM,eAAe,IAAI,EAAE;gCACpC,WAAW,AAAC,8DAIX,OAHC,gBAAgB,IAAI,EAAE,GAClB,mDACA;;oCAGL,IAAI,IAAI;oCAAC;oCAAE,IAAI,IAAI;;+BARf,IAAI,EAAE;;;;;;;;;;;;;;;8BAcnB,6LAAC;oBAAI,WAAU;;wBAEZ,gBAAgB,yBACf,6LAAC;4BAAI,WAAU;sCACZ,cAAc,MAAM,GAAG,kBACtB,6LAAC;gCAAI,WAAU;0CACZ,cAAc,GAAG,CAAC,CAAA,uBACjB,6LAAC;wCAEC,WAAW,AAAC,yDAIX,OAHC,OAAO,EAAE,KAAK,kBACV,mCACA;wCAEN,SAAS,IAAM,eAAe,OAAO,EAAE;;0DAEvC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAY,sBAAsB,OAAO,cAAc;;;;;;0EACvE,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAA+B,OAAO,IAAI;;;;;;kFACxD,6LAAC;wEAAE,WAAU;kFAAyB,OAAO,QAAQ;;;;;;;;;;;;;;;;;;oDAGxD,OAAO,EAAE,KAAK,iCACb,6LAAC;wDAAK,WAAU;kEACb,EAAE,uBAAuB;;;;;;;;;;;;0DAKhC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEAAiB,EAAE,qBAAqB;oEAAQ;;;;;;;0EAChE,6LAAC;gEAAK,WAAU;0EAAe,OAAO,KAAK;;;;;;;;;;;;kEAE7C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEAAiB,EAAE,8BAA8B;oEAAiB;;;;;;;0EAClF,6LAAC;gEAAK,WAAU;0EAA0B,OAAO,cAAc;;;;;;;;;;;;kEAEjE,6LAAC;wDAAI,WAAU;kEACZ,uBAAuB,OAAO,cAAc;;;;;;;;;;;;0DAIjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEAAiB,EAAE,yBAAyB;oEAAY;;;;;;;0EACxE,6LAAC;0EAAM,OAAO,SAAS,CAAC,MAAM;;;;;;;;;;;;kEAEhC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEAAiB,EAAE,sBAAsB;oEAAgB;;;;;;;0EACzE,6LAAC;0EAAM,OAAO,MAAM,CAAC,MAAM;;;;;;;;;;;;;;;;;;4CAI9B,OAAO,EAAE,KAAK,iCACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,eAAe;oDACjB,eAAe,OAAO,EAAE;gDAC1B;0DAEC,EAAE,wBAAwB;;;;;;;uCA1D1B,OAAO,EAAE;;;;;;;;;qDAiEpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;kDAAG,EAAE,uBAAuB;;;;;;;;;;;;;;;;;wBAOpC,gBAAgB,6BACf,6LAAC;4BAAI,WAAU;sCACZ,kBAAkB,MAAM,GAAG,kBAC1B,6LAAC;gCAAI,WAAU;0CACZ,kBAAkB,GAAG,CAAC,CAAC,QAAQ,sBAC9B,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAAY,sBAAsB,OAAO,cAAc;;;;;;0EACvE,6LAAC;;kFACC,6LAAC;wEAAG,WAAU;kFAA+B,OAAO,IAAI;;;;;;kFACxD,6LAAC;wEAAE,WAAU;kFAAyB,OAAO,QAAQ;;;;;;;;;;;;;;;;;;kEAGzD,6LAAC;wDAAK,WAAU;;4DAAmC;4DAAE,OAAO,YAAY;;;;;;;;;;;;;0DAG1E,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;;oEAAiB,EAAE,8BAA8B;oEAAiB;;;;;;;0EAClF,6LAAC;gEAAK,WAAU;0EAA0B,OAAO,cAAc;;;;;;;;;;;;kEAEjE,6LAAC;wDAAI,WAAU;kEACZ,uBAAuB,OAAO,cAAc;;;;;;;;;;;;0DAIjD,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAS,eAAe,OAAO,YAAY,GAAG,YAAY;gDAC1D,MAAK;gDACL,WAAU;gDACV,UAAU,cAAc,OAAO,YAAY;gDAC3C,SAAS,IAAM,qBAAqB;0DAEnC,eAAe,OAAO,YAAY,GAC9B,EAAE,wBAAwB,gBAC1B,EAAE,4BAA4B;;;;;;;uCAjChC;;;;;;;;;qDAwCX,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;kDAAG,EAAE,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;8BAOxC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCACX,EAAE,oBAAoB;;;;;;sCAEzB,6LAAC;4BAAG,WAAU;;8CACZ,6LAAC;;wCAAG;wCAAG,EAAE,oBAAoB;;;;;;;8CAC7B,6LAAC;;wCAAG;wCAAG,EAAE,oBAAoB;;;;;;;8CAC7B,6LAAC;;wCAAG;wCAAG,EAAE,oBAAoB;;;;;;;8CAC7B,6LAAC;;wCAAG;wCAAG,EAAE,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC;GAzPgB;;QASA,sIAAA,CAAA,cAAW;;;KATX", "debugId": null}}, {"offset": {"line": 4606, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/lib/fileSaveSystem.ts"], "sourcesContent": ["// File-based save system for desktop app\n// Provides better save management for Electron applications\n\nimport { GameSave } from './saveSystem'\n\nexport interface FileSaveMetadata {\n  fileName: string\n  displayName: string\n  timestamp: number\n  playerLevel: number\n  money: number\n  playTime: number\n  version: string\n  fileSize: number\n}\n\nexport class FileSaveSystem {\n  private saveDirectory: string = ''\n  private isElectron: boolean = false\n\n  constructor() {\n    this.isElectron = typeof window !== 'undefined' && window.electronAPI !== undefined\n    this.initializeSaveDirectory()\n  }\n\n  private async initializeSaveDirectory() {\n    if (this.isElectron && window.electronAPI) {\n      try {\n        // Get the user's documents folder + game saves\n        this.saveDirectory = await window.electronAPI.getSaveDirectory()\n        await this.ensureSaveDirectoryExists()\n      } catch (error) {\n        console.error('Failed to initialize save directory:', error)\n        // Fallback to localStorage\n        this.isElectron = false\n      }\n    }\n  }\n\n  private async ensureSaveDirectoryExists() {\n    if (this.isElectron && window.electronAPI) {\n      try {\n        await window.electronAPI.ensureDirectory(this.saveDirectory)\n      } catch (error) {\n        console.error('Failed to create save directory:', error)\n      }\n    }\n  }\n\n  // Save game to file\n  async saveToFile(gameData: GameSave, fileName?: string): Promise<boolean> {\n    if (!this.isElectron || !window.electronAPI) {\n      // Fallback to localStorage\n      return this.saveToLocalStorage(gameData, fileName)\n    }\n\n    try {\n      const saveFileName = fileName || `save_${Date.now()}.json`\n      const filePath = `${this.saveDirectory}/${saveFileName}`\n      \n      const saveString = JSON.stringify(gameData, null, 2)\n      await window.electronAPI.writeFile(filePath, saveString)\n      \n      console.log(`Game saved to file: ${filePath}`)\n      return true\n    } catch (error) {\n      console.error('Failed to save to file:', error)\n      return false\n    }\n  }\n\n  // Load game from file\n  async loadFromFile(fileName: string): Promise<GameSave | null> {\n    if (!this.isElectron || !window.electronAPI) {\n      // Fallback to localStorage\n      return this.loadFromLocalStorage(fileName)\n    }\n\n    try {\n      const filePath = `${this.saveDirectory}/${fileName}`\n      const saveString = await window.electronAPI.readFile(filePath)\n      \n      if (!saveString) return null\n      \n      const gameData: GameSave = JSON.parse(saveString)\n      console.log(`Game loaded from file: ${filePath}`)\n      return gameData\n    } catch (error) {\n      console.error('Failed to load from file:', error)\n      return null\n    }\n  }\n\n  // Get list of save files\n  async getSaveFiles(): Promise<FileSaveMetadata[]> {\n    if (!this.isElectron || !window.electronAPI) {\n      // Fallback to localStorage\n      return this.getLocalStorageSaves()\n    }\n\n    try {\n      const files = await window.electronAPI.listFiles(this.saveDirectory, '.json')\n      const saveFiles: FileSaveMetadata[] = []\n\n      for (const file of files) {\n        try {\n          const filePath = `${this.saveDirectory}/${file.name}`\n          const saveString = await window.electronAPI.readFile(filePath)\n          const gameData: GameSave = JSON.parse(saveString)\n\n          saveFiles.push({\n            fileName: file.name,\n            displayName: gameData.player.name || file.name.replace('.json', ''),\n            timestamp: gameData.timestamp,\n            playerLevel: gameData.player.level,\n            money: gameData.player.money,\n            playTime: gameData.player.playTime || 0,\n            version: gameData.version,\n            fileSize: file.size || 0\n          })\n        } catch (error) {\n          console.error(`Failed to read save file ${file.name}:`, error)\n        }\n      }\n\n      return saveFiles.sort((a, b) => b.timestamp - a.timestamp)\n    } catch (error) {\n      console.error('Failed to get save files:', error)\n      return []\n    }\n  }\n\n  // Delete save file\n  async deleteSaveFile(fileName: string): Promise<boolean> {\n    if (!this.isElectron || !window.electronAPI) {\n      // Fallback to localStorage\n      return this.deleteFromLocalStorage(fileName)\n    }\n\n    try {\n      const filePath = `${this.saveDirectory}/${fileName}`\n      await window.electronAPI.deleteFile(filePath)\n      console.log(`Save file deleted: ${filePath}`)\n      return true\n    } catch (error) {\n      console.error('Failed to delete save file:', error)\n      return false\n    }\n  }\n\n  // Export save file to user-chosen location\n  async exportSave(gameData: GameSave, suggestedName?: string): Promise<boolean> {\n    if (!this.isElectron || !window.electronAPI) {\n      // Fallback to browser download\n      return this.exportToBrowser(gameData, suggestedName)\n    }\n\n    try {\n      const defaultName = suggestedName || `bake-it-out-save-${Date.now()}.json`\n      const filePath = await window.electronAPI.showSaveDialog(defaultName)\n      \n      if (!filePath) return false // User cancelled\n      \n      const saveString = JSON.stringify(gameData, null, 2)\n      await window.electronAPI.writeFile(filePath, saveString)\n      \n      console.log(`Save exported to: ${filePath}`)\n      return true\n    } catch (error) {\n      console.error('Failed to export save:', error)\n      return false\n    }\n  }\n\n  // Import save file from user-chosen location\n  async importSave(): Promise<GameSave | null> {\n    if (!this.isElectron || !window.electronAPI) {\n      // Fallback to browser file input\n      return this.importFromBrowser()\n    }\n\n    try {\n      const filePath = await window.electronAPI.showOpenDialog(['.json'])\n      \n      if (!filePath) return null // User cancelled\n      \n      const saveString = await window.electronAPI.readFile(filePath)\n      const gameData: GameSave = JSON.parse(saveString)\n      \n      console.log(`Save imported from: ${filePath}`)\n      return gameData\n    } catch (error) {\n      console.error('Failed to import save:', error)\n      return null\n    }\n  }\n\n  // Create backup of save file\n  async createBackup(fileName: string): Promise<boolean> {\n    if (!this.isElectron || !window.electronAPI) {\n      return false // Not supported in browser\n    }\n\n    try {\n      const sourceFile = `${this.saveDirectory}/${fileName}`\n      const backupFile = `${this.saveDirectory}/backups/${fileName}.backup.${Date.now()}`\n      \n      await window.electronAPI.ensureDirectory(`${this.saveDirectory}/backups`)\n      await window.electronAPI.copyFile(sourceFile, backupFile)\n      \n      console.log(`Backup created: ${backupFile}`)\n      return true\n    } catch (error) {\n      console.error('Failed to create backup:', error)\n      return false\n    }\n  }\n\n  // Fallback methods for browser environment\n  private saveToLocalStorage(gameData: GameSave, fileName?: string): boolean {\n    try {\n      const key = fileName || `save_${Date.now()}`\n      localStorage.setItem(`bakeItOut_file_${key}`, JSON.stringify(gameData))\n      return true\n    } catch (error) {\n      console.error('Failed to save to localStorage:', error)\n      return false\n    }\n  }\n\n  private loadFromLocalStorage(fileName: string): GameSave | null {\n    try {\n      const saveString = localStorage.getItem(`bakeItOut_file_${fileName}`)\n      return saveString ? JSON.parse(saveString) : null\n    } catch (error) {\n      console.error('Failed to load from localStorage:', error)\n      return null\n    }\n  }\n\n  private getLocalStorageSaves(): FileSaveMetadata[] {\n    const saves: FileSaveMetadata[] = []\n    \n    for (let i = 0; i < localStorage.length; i++) {\n      const key = localStorage.key(i)\n      if (key?.startsWith('bakeItOut_file_')) {\n        try {\n          const saveString = localStorage.getItem(key)\n          if (saveString) {\n            const gameData: GameSave = JSON.parse(saveString)\n            const fileName = key.replace('bakeItOut_file_', '')\n            \n            saves.push({\n              fileName,\n              displayName: gameData.player.name || fileName,\n              timestamp: gameData.timestamp,\n              playerLevel: gameData.player.level,\n              money: gameData.player.money,\n              playTime: gameData.player.playTime || 0,\n              version: gameData.version,\n              fileSize: saveString.length\n            })\n          }\n        } catch (error) {\n          console.error(`Failed to parse save ${key}:`, error)\n        }\n      }\n    }\n    \n    return saves.sort((a, b) => b.timestamp - a.timestamp)\n  }\n\n  private deleteFromLocalStorage(fileName: string): boolean {\n    try {\n      localStorage.removeItem(`bakeItOut_file_${fileName}`)\n      return true\n    } catch (error) {\n      console.error('Failed to delete from localStorage:', error)\n      return false\n    }\n  }\n\n  private exportToBrowser(gameData: GameSave, suggestedName?: string): boolean {\n    try {\n      const saveString = JSON.stringify(gameData, null, 2)\n      const blob = new Blob([saveString], { type: 'application/json' })\n      const url = URL.createObjectURL(blob)\n      \n      const a = document.createElement('a')\n      a.href = url\n      a.download = suggestedName || `bake-it-out-save-${Date.now()}.json`\n      a.click()\n      \n      URL.revokeObjectURL(url)\n      return true\n    } catch (error) {\n      console.error('Failed to export to browser:', error)\n      return false\n    }\n  }\n\n  private importFromBrowser(): Promise<GameSave | null> {\n    return new Promise((resolve) => {\n      const input = document.createElement('input')\n      input.type = 'file'\n      input.accept = '.json'\n      \n      input.onchange = (e) => {\n        const file = (e.target as HTMLInputElement).files?.[0]\n        if (!file) {\n          resolve(null)\n          return\n        }\n        \n        const reader = new FileReader()\n        reader.onload = (e) => {\n          try {\n            const saveString = e.target?.result as string\n            const gameData: GameSave = JSON.parse(saveString)\n            resolve(gameData)\n          } catch (error) {\n            console.error('Failed to parse imported file:', error)\n            resolve(null)\n          }\n        }\n        reader.readAsText(file)\n      }\n      \n      input.click()\n    })\n  }\n}\n\nexport const fileSaveSystem = new FileSaveSystem()\n"], "names": [], "mappings": "AAAA,yCAAyC;AACzC,4DAA4D;;;;;;;AAerD,MAAM;IASX,MAAc,0BAA0B;QACtC,IAAI,IAAI,CAAC,UAAU,IAAI,OAAO,WAAW,EAAE;YACzC,IAAI;gBACF,+CAA+C;gBAC/C,IAAI,CAAC,aAAa,GAAG,MAAM,OAAO,WAAW,CAAC,gBAAgB;gBAC9D,MAAM,IAAI,CAAC,yBAAyB;YACtC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wCAAwC;gBACtD,2BAA2B;gBAC3B,IAAI,CAAC,UAAU,GAAG;YACpB;QACF;IACF;IAEA,MAAc,4BAA4B;QACxC,IAAI,IAAI,CAAC,UAAU,IAAI,OAAO,WAAW,EAAE;YACzC,IAAI;gBACF,MAAM,OAAO,WAAW,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa;YAC7D,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF;IACF;IAEA,oBAAoB;IACpB,MAAM,WAAW,QAAkB,EAAE,QAAiB,EAAoB;QACxE,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,WAAW,EAAE;YAC3C,2BAA2B;YAC3B,OAAO,IAAI,CAAC,kBAAkB,CAAC,UAAU;QAC3C;QAEA,IAAI;YACF,MAAM,eAAe,YAAY,AAAC,QAAkB,OAAX,KAAK,GAAG,IAAG;YACpD,MAAM,WAAW,AAAC,GAAwB,OAAtB,IAAI,CAAC,aAAa,EAAC,KAAgB,OAAb;YAE1C,MAAM,aAAa,KAAK,SAAS,CAAC,UAAU,MAAM;YAClD,MAAM,OAAO,WAAW,CAAC,SAAS,CAAC,UAAU;YAE7C,QAAQ,GAAG,CAAC,AAAC,uBAA+B,OAAT;YACnC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;QACT;IACF;IAEA,sBAAsB;IACtB,MAAM,aAAa,QAAgB,EAA4B;QAC7D,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,WAAW,EAAE;YAC3C,2BAA2B;YAC3B,OAAO,IAAI,CAAC,oBAAoB,CAAC;QACnC;QAEA,IAAI;YACF,MAAM,WAAW,AAAC,GAAwB,OAAtB,IAAI,CAAC,aAAa,EAAC,KAAY,OAAT;YAC1C,MAAM,aAAa,MAAM,OAAO,WAAW,CAAC,QAAQ,CAAC;YAErD,IAAI,CAAC,YAAY,OAAO;YAExB,MAAM,WAAqB,KAAK,KAAK,CAAC;YACtC,QAAQ,GAAG,CAAC,AAAC,0BAAkC,OAAT;YACtC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO;QACT;IACF;IAEA,yBAAyB;IACzB,MAAM,eAA4C;QAChD,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,WAAW,EAAE;YAC3C,2BAA2B;YAC3B,OAAO,IAAI,CAAC,oBAAoB;QAClC;QAEA,IAAI;YACF,MAAM,QAAQ,MAAM,OAAO,WAAW,CAAC,SAAS,CAAC,IAAI,CAAC,aAAa,EAAE;YACrE,MAAM,YAAgC,EAAE;YAExC,KAAK,MAAM,QAAQ,MAAO;gBACxB,IAAI;oBACF,MAAM,WAAW,AAAC,GAAwB,OAAtB,IAAI,CAAC,aAAa,EAAC,KAAa,OAAV,KAAK,IAAI;oBACnD,MAAM,aAAa,MAAM,OAAO,WAAW,CAAC,QAAQ,CAAC;oBACrD,MAAM,WAAqB,KAAK,KAAK,CAAC;oBAEtC,UAAU,IAAI,CAAC;wBACb,UAAU,KAAK,IAAI;wBACnB,aAAa,SAAS,MAAM,CAAC,IAAI,IAAI,KAAK,IAAI,CAAC,OAAO,CAAC,SAAS;wBAChE,WAAW,SAAS,SAAS;wBAC7B,aAAa,SAAS,MAAM,CAAC,KAAK;wBAClC,OAAO,SAAS,MAAM,CAAC,KAAK;wBAC5B,UAAU,SAAS,MAAM,CAAC,QAAQ,IAAI;wBACtC,SAAS,SAAS,OAAO;wBACzB,UAAU,KAAK,IAAI,IAAI;oBACzB;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,AAAC,4BAAqC,OAAV,KAAK,IAAI,EAAC,MAAI;gBAC1D;YACF;YAEA,OAAO,UAAU,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;QAC3D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,OAAO,EAAE;QACX;IACF;IAEA,mBAAmB;IACnB,MAAM,eAAe,QAAgB,EAAoB;QACvD,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,WAAW,EAAE;YAC3C,2BAA2B;YAC3B,OAAO,IAAI,CAAC,sBAAsB,CAAC;QACrC;QAEA,IAAI;YACF,MAAM,WAAW,AAAC,GAAwB,OAAtB,IAAI,CAAC,aAAa,EAAC,KAAY,OAAT;YAC1C,MAAM,OAAO,WAAW,CAAC,UAAU,CAAC;YACpC,QAAQ,GAAG,CAAC,AAAC,sBAA8B,OAAT;YAClC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,OAAO;QACT;IACF;IAEA,2CAA2C;IAC3C,MAAM,WAAW,QAAkB,EAAE,aAAsB,EAAoB;QAC7E,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,WAAW,EAAE;YAC3C,+BAA+B;YAC/B,OAAO,IAAI,CAAC,eAAe,CAAC,UAAU;QACxC;QAEA,IAAI;YACF,MAAM,cAAc,iBAAiB,AAAC,oBAA8B,OAAX,KAAK,GAAG,IAAG;YACpE,MAAM,WAAW,MAAM,OAAO,WAAW,CAAC,cAAc,CAAC;YAEzD,IAAI,CAAC,UAAU,OAAO,MAAM,iBAAiB;;YAE7C,MAAM,aAAa,KAAK,SAAS,CAAC,UAAU,MAAM;YAClD,MAAM,OAAO,WAAW,CAAC,SAAS,CAAC,UAAU;YAE7C,QAAQ,GAAG,CAAC,AAAC,qBAA6B,OAAT;YACjC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;IACF;IAEA,6CAA6C;IAC7C,MAAM,aAAuC;QAC3C,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,WAAW,EAAE;YAC3C,iCAAiC;YACjC,OAAO,IAAI,CAAC,iBAAiB;QAC/B;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,OAAO,WAAW,CAAC,cAAc,CAAC;gBAAC;aAAQ;YAElE,IAAI,CAAC,UAAU,OAAO,KAAK,iBAAiB;;YAE5C,MAAM,aAAa,MAAM,OAAO,WAAW,CAAC,QAAQ,CAAC;YACrD,MAAM,WAAqB,KAAK,KAAK,CAAC;YAEtC,QAAQ,GAAG,CAAC,AAAC,uBAA+B,OAAT;YACnC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;IACF;IAEA,6BAA6B;IAC7B,MAAM,aAAa,QAAgB,EAAoB;QACrD,IAAI,CAAC,IAAI,CAAC,UAAU,IAAI,CAAC,OAAO,WAAW,EAAE;YAC3C,OAAO,MAAM,2BAA2B;;QAC1C;QAEA,IAAI;YACF,MAAM,aAAa,AAAC,GAAwB,OAAtB,IAAI,CAAC,aAAa,EAAC,KAAY,OAAT;YAC5C,MAAM,aAAa,AAAC,GAAgC,OAA9B,IAAI,CAAC,aAAa,EAAC,aAA8B,OAAnB,UAAS,YAAqB,OAAX,KAAK,GAAG;YAE/E,MAAM,OAAO,WAAW,CAAC,eAAe,CAAC,AAAC,GAAqB,OAAnB,IAAI,CAAC,aAAa,EAAC;YAC/D,MAAM,OAAO,WAAW,CAAC,QAAQ,CAAC,YAAY;YAE9C,QAAQ,GAAG,CAAC,AAAC,mBAA6B,OAAX;YAC/B,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF;IAEA,2CAA2C;IACnC,mBAAmB,QAAkB,EAAE,QAAiB,EAAW;QACzE,IAAI;YACF,MAAM,MAAM,YAAY,AAAC,QAAkB,OAAX,KAAK,GAAG;YACxC,aAAa,OAAO,CAAC,AAAC,kBAAqB,OAAJ,MAAO,KAAK,SAAS,CAAC;YAC7D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,OAAO;QACT;IACF;IAEQ,qBAAqB,QAAgB,EAAmB;QAC9D,IAAI;YACF,MAAM,aAAa,aAAa,OAAO,CAAC,AAAC,kBAA0B,OAAT;YAC1D,OAAO,aAAa,KAAK,KAAK,CAAC,cAAc;QAC/C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,OAAO;QACT;IACF;IAEQ,uBAA2C;QACjD,MAAM,QAA4B,EAAE;QAEpC,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;YAC5C,MAAM,MAAM,aAAa,GAAG,CAAC;YAC7B,IAAI,gBAAA,0BAAA,IAAK,UAAU,CAAC,oBAAoB;gBACtC,IAAI;oBACF,MAAM,aAAa,aAAa,OAAO,CAAC;oBACxC,IAAI,YAAY;wBACd,MAAM,WAAqB,KAAK,KAAK,CAAC;wBACtC,MAAM,WAAW,IAAI,OAAO,CAAC,mBAAmB;wBAEhD,MAAM,IAAI,CAAC;4BACT;4BACA,aAAa,SAAS,MAAM,CAAC,IAAI,IAAI;4BACrC,WAAW,SAAS,SAAS;4BAC7B,aAAa,SAAS,MAAM,CAAC,KAAK;4BAClC,OAAO,SAAS,MAAM,CAAC,KAAK;4BAC5B,UAAU,SAAS,MAAM,CAAC,QAAQ,IAAI;4BACtC,SAAS,SAAS,OAAO;4BACzB,UAAU,WAAW,MAAM;wBAC7B;oBACF;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,AAAC,wBAA2B,OAAJ,KAAI,MAAI;gBAChD;YACF;QACF;QAEA,OAAO,MAAM,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;IACvD;IAEQ,uBAAuB,QAAgB,EAAW;QACxD,IAAI;YACF,aAAa,UAAU,CAAC,AAAC,kBAA0B,OAAT;YAC1C,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO;QACT;IACF;IAEQ,gBAAgB,QAAkB,EAAE,aAAsB,EAAW;QAC3E,IAAI;YACF,MAAM,aAAa,KAAK,SAAS,CAAC,UAAU,MAAM;YAClD,MAAM,OAAO,IAAI,KAAK;gBAAC;aAAW,EAAE;gBAAE,MAAM;YAAmB;YAC/D,MAAM,MAAM,IAAI,eAAe,CAAC;YAEhC,MAAM,IAAI,SAAS,aAAa,CAAC;YACjC,EAAE,IAAI,GAAG;YACT,EAAE,QAAQ,GAAG,iBAAiB,AAAC,oBAA8B,OAAX,KAAK,GAAG,IAAG;YAC7D,EAAE,KAAK;YAEP,IAAI,eAAe,CAAC;YACpB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;QACT;IACF;IAEQ,oBAA8C;QACpD,OAAO,IAAI,QAAQ,CAAC;YAClB,MAAM,QAAQ,SAAS,aAAa,CAAC;YACrC,MAAM,IAAI,GAAG;YACb,MAAM,MAAM,GAAG;YAEf,MAAM,QAAQ,GAAG,CAAC;oBACH;gBAAb,MAAM,QAAO,SAAA,AAAC,EAAE,MAAM,CAAsB,KAAK,cAApC,6BAAA,MAAsC,CAAC,EAAE;gBACtD,IAAI,CAAC,MAAM;oBACT,QAAQ;oBACR;gBACF;gBAEA,MAAM,SAAS,IAAI;gBACnB,OAAO,MAAM,GAAG,CAAC;oBACf,IAAI;4BACiB;wBAAnB,MAAM,cAAa,YAAA,EAAE,MAAM,cAAR,gCAAA,UAAU,MAAM;wBACnC,MAAM,WAAqB,KAAK,KAAK,CAAC;wBACtC,QAAQ;oBACV,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,kCAAkC;wBAChD,QAAQ;oBACV;gBACF;gBACA,OAAO,UAAU,CAAC;YACpB;YAEA,MAAM,KAAK;QACb;IACF;IAtTA,aAAc;QAHd,+KAAQ,iBAAwB;QAChC,+KAAQ,cAAsB;QAG5B,IAAI,CAAC,UAAU,GAAG,aAAkB,eAAe,OAAO,WAAW,KAAK;QAC1E,IAAI,CAAC,uBAAuB;IAC9B;AAoTF;AAEO,MAAM,iBAAiB,IAAI", "debugId": null}}, {"offset": {"line": 4905, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/SaveLoadModal.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { useLanguage } from '@/contexts/LanguageContext'\nimport { useGame } from '@/contexts/GameContext'\nimport { saveSystem, GameSave } from '@/lib/saveSystem'\nimport { fileSaveSystem, FileSaveMetadata } from '@/lib/fileSaveSystem'\n\ninterface SaveSlot {\n  id: number\n  name: string\n  timestamp: number\n  playerLevel: number\n  money: number\n  bakeryName: string\n  playTime: number\n  isEmpty: boolean\n  data?: GameSave\n}\n\ninterface SaveLoadModalProps {\n  isOpen: boolean\n  onClose: () => void\n  mode: 'save' | 'load'\n  onSaveSuccess?: () => void\n  onLoadSuccess?: () => void\n}\n\nexport function SaveLoadModal({ \n  isOpen, \n  onClose, \n  mode, \n  onSaveSuccess, \n  onLoadSuccess \n}: SaveLoadModalProps) {\n  const { t } = useLanguage()\n  const { player, saveGameState, loadGameState } = useGame()\n  const [saveSlots, setSaveSlots] = useState<SaveSlot[]>([])\n  const [fileSaves, setFileSaves] = useState<FileSaveMetadata[]>([])\n  const [selectedSlot, setSelectedSlot] = useState<number | null>(null)\n  const [selectedFile, setSelectedFile] = useState<string | null>(null)\n  const [saveName, setSaveName] = useState('')\n  const [loading, setLoading] = useState(false)\n  const [showConfirmOverwrite, setShowConfirmOverwrite] = useState(false)\n  const [useFileSystem, setUseFileSystem] = useState(false)\n\n  useEffect(() => {\n    if (isOpen) {\n      loadSaveSlots()\n      loadFileSaves()\n      // Check if we're in Electron environment\n      setUseFileSystem(typeof window !== 'undefined' && window.electronAPI !== undefined)\n    }\n  }, [isOpen])\n\n  const loadFileSaves = async () => {\n    try {\n      const files = await fileSaveSystem.getSaveFiles()\n      setFileSaves(files)\n    } catch (error) {\n      console.error('Failed to load file saves:', error)\n    }\n  }\n\n  const loadSaveSlots = () => {\n    const slots: SaveSlot[] = []\n    \n    // Load up to 8 save slots\n    for (let i = 1; i <= 8; i++) {\n      const slotKey = `bakeItOut_save_slot_${i}`\n      const slotData = localStorage.getItem(slotKey)\n      \n      if (slotData) {\n        try {\n          const save: GameSave = JSON.parse(slotData)\n          slots.push({\n            id: i,\n            name: save.player.name || `Save ${i}`,\n            timestamp: save.timestamp,\n            playerLevel: save.player.level,\n            money: save.player.money,\n            bakeryName: save.bakeries?.[0]?.name || 'Main Bakery',\n            playTime: save.player.playTime || 0,\n            isEmpty: false,\n            data: save\n          })\n        } catch (error) {\n          console.error(`Failed to load save slot ${i}:`, error)\n          slots.push(createEmptySlot(i))\n        }\n      } else {\n        slots.push(createEmptySlot(i))\n      }\n    }\n    \n    setSaveSlots(slots)\n  }\n\n  const createEmptySlot = (id: number): SaveSlot => ({\n    id,\n    name: `Empty Slot ${id}`,\n    timestamp: 0,\n    playerLevel: 0,\n    money: 0,\n    bakeryName: '',\n    playTime: 0,\n    isEmpty: true\n  })\n\n  const handleSave = async (slotId: number) => {\n    if (!selectedSlot) return\n\n    const slot = saveSlots.find(s => s.id === slotId)\n    if (!slot?.isEmpty && !showConfirmOverwrite) {\n      setShowConfirmOverwrite(true)\n      return\n    }\n\n    setLoading(true)\n    try {\n      const success = await saveGameState(slotId, saveName || `Save ${slotId}`)\n      if (success) {\n        onSaveSuccess?.()\n        loadSaveSlots() // Refresh the slots\n        setShowConfirmOverwrite(false)\n        setSaveName('')\n      }\n    } catch (error) {\n      console.error('Save failed:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleLoad = async (slotId: number) => {\n    const slot = saveSlots.find(s => s.id === slotId)\n    if (!slot || slot.isEmpty) return\n\n    setLoading(true)\n    try {\n      const success = await loadGameState(slotId)\n      if (success) {\n        onLoadSuccess?.()\n        onClose()\n      }\n    } catch (error) {\n      console.error('Load failed:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleDeleteSave = (slotId: number) => {\n    const slotKey = `bakeItOut_save_slot_${slotId}`\n    localStorage.removeItem(slotKey)\n    loadSaveSlots()\n  }\n\n  const handleSaveToFile = async () => {\n    if (!saveName.trim()) return\n\n    setLoading(true)\n    try {\n      const gameData = await saveGameState(0, saveName) // Get game data\n      if (gameData) {\n        const fileName = `${saveName.replace(/[^a-zA-Z0-9]/g, '_')}_${Date.now()}.json`\n        const success = await fileSaveSystem.saveToFile(gameData, fileName)\n        if (success) {\n          onSaveSuccess?.()\n          loadFileSaves()\n          setSaveName('')\n        }\n      }\n    } catch (error) {\n      console.error('File save failed:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleLoadFromFile = async (fileName: string) => {\n    setLoading(true)\n    try {\n      const gameData = await fileSaveSystem.loadFromFile(fileName)\n      if (gameData) {\n        // Load the game data into the game state\n        const success = await loadGameState(-1) // Special slot for file loads\n        if (success) {\n          onLoadSuccess?.()\n          onClose()\n        }\n      }\n    } catch (error) {\n      console.error('File load failed:', error)\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const handleDeleteFile = async (fileName: string) => {\n    try {\n      await fileSaveSystem.deleteSaveFile(fileName)\n      loadFileSaves()\n    } catch (error) {\n      console.error('Failed to delete file:', error)\n    }\n  }\n\n  const handleExportSave = async () => {\n    try {\n      const gameData = await saveGameState(0, 'Export') // Get current game data\n      if (gameData) {\n        await fileSaveSystem.exportSave(gameData, saveName || 'bake-it-out-save')\n      }\n    } catch (error) {\n      console.error('Export failed:', error)\n    }\n  }\n\n  const handleImportSave = async () => {\n    try {\n      const gameData = await fileSaveSystem.importSave()\n      if (gameData) {\n        // Load the imported data\n        const success = await loadGameState(-1) // Special slot for imports\n        if (success) {\n          onLoadSuccess?.()\n          onClose()\n        }\n      }\n    } catch (error) {\n      console.error('Import failed:', error)\n    }\n  }\n\n  const formatTimestamp = (timestamp: number) => {\n    if (!timestamp) return ''\n    return new Date(timestamp).toLocaleString()\n  }\n\n  const formatPlayTime = (seconds: number) => {\n    const hours = Math.floor(seconds / 3600)\n    const minutes = Math.floor((seconds % 3600) / 60)\n    return `${hours}h ${minutes}m`\n  }\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden\">\n        {/* Header */}\n        <div className=\"bg-gradient-to-r from-blue-500 to-purple-500 p-6 text-white\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h2 className=\"text-2xl font-bold\">\n                {mode === 'save' ? '💾 Save Game' : '📁 Load Game'}\n              </h2>\n              <p className=\"text-blue-100 text-sm\">\n                {mode === 'save' \n                  ? t('saveLoad.saveDesc', 'Choose a slot to save your progress')\n                  : t('saveLoad.loadDesc', 'Select a save file to load')\n                }\n              </p>\n            </div>\n            <Button\n              variant=\"secondary\"\n              size=\"sm\"\n              className=\"bg-white/20 hover:bg-white/30 text-white border-white/30\"\n              onClick={onClose}\n            >\n              ✕\n            </Button>\n          </div>\n        </div>\n\n        {/* Tabs */}\n        {useFileSystem && (\n          <div className=\"border-b border-gray-200\">\n            <div className=\"flex\">\n              <button\n                className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${\n                  !useFileSystem || selectedSlot !== null\n                    ? 'border-blue-500 text-blue-600 bg-blue-50'\n                    : 'border-transparent text-gray-500 hover:text-gray-700'\n                }`}\n                onClick={() => {\n                  setSelectedSlot(1)\n                  setSelectedFile(null)\n                }}\n              >\n                📁 Save Slots\n              </button>\n              <button\n                className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${\n                  selectedFile !== null\n                    ? 'border-blue-500 text-blue-600 bg-blue-50'\n                    : 'border-transparent text-gray-500 hover:text-gray-700'\n                }`}\n                onClick={() => {\n                  setSelectedSlot(null)\n                  setSelectedFile('')\n                }}\n              >\n                💾 File System\n              </button>\n            </div>\n          </div>\n        )}\n\n        {/* Content */}\n        <div className=\"p-6 overflow-y-auto max-h-[70vh]\">\n          {/* Save Name Input (Save Mode Only) */}\n          {mode === 'save' && selectedSlot && (\n            <div className=\"mb-6 p-4 bg-blue-50 rounded-lg\">\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                {t('saveLoad.saveName', 'Save Name')}\n              </label>\n              <input\n                type=\"text\"\n                value={saveName}\n                onChange={(e) => setSaveName(e.target.value)}\n                placeholder={`Save ${selectedSlot}`}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              />\n            </div>\n          )}\n\n          {/* Save Slots Grid */}\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {saveSlots.map((slot) => (\n              <div\n                key={slot.id}\n                className={`border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 ${\n                  selectedSlot === slot.id\n                    ? 'border-blue-500 bg-blue-50'\n                    : slot.isEmpty\n                    ? 'border-gray-200 bg-gray-50 hover:border-gray-300'\n                    : 'border-gray-300 bg-white hover:border-blue-300 hover:shadow-md'\n                }`}\n                onClick={() => setSelectedSlot(slot.id)}\n              >\n                <div className=\"flex items-start justify-between mb-3\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-12 h-12 bg-gradient-to-br from-orange-400 to-yellow-400 rounded-lg flex items-center justify-center text-white font-bold text-lg\">\n                      {slot.id}\n                    </div>\n                    <div>\n                      <h3 className=\"font-semibold text-gray-800\">\n                        {slot.isEmpty ? `Slot ${slot.id}` : slot.name}\n                      </h3>\n                      {!slot.isEmpty && (\n                        <p className=\"text-sm text-gray-500\">\n                          {formatTimestamp(slot.timestamp)}\n                        </p>\n                      )}\n                    </div>\n                  </div>\n                  \n                  {!slot.isEmpty && (\n                    <Button\n                      variant=\"secondary\"\n                      size=\"sm\"\n                      className=\"text-red-600 hover:bg-red-50\"\n                      onClick={(e) => {\n                        e.stopPropagation()\n                        handleDeleteSave(slot.id)\n                      }}\n                    >\n                      🗑️\n                    </Button>\n                  )}\n                </div>\n\n                {slot.isEmpty ? (\n                  <div className=\"text-center py-8 text-gray-400\">\n                    <div className=\"text-3xl mb-2\">📄</div>\n                    <p className=\"text-sm\">{t('saveLoad.emptySlot', 'Empty Slot')}</p>\n                  </div>\n                ) : (\n                  <div className=\"space-y-2\">\n                    <div className=\"grid grid-cols-2 gap-4 text-sm\">\n                      <div>\n                        <span className=\"text-gray-500\">Level:</span>\n                        <span className=\"ml-2 font-medium\">{slot.playerLevel}</span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Money:</span>\n                        <span className=\"ml-2 font-medium\">${slot.money}</span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Bakery:</span>\n                        <span className=\"ml-2 font-medium\">{slot.bakeryName}</span>\n                      </div>\n                      <div>\n                        <span className=\"text-gray-500\">Play Time:</span>\n                        <span className=\"ml-2 font-medium\">{formatPlayTime(slot.playTime)}</span>\n                      </div>\n                    </div>\n                  </div>\n                )}\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"bg-gray-50 px-6 py-4 flex justify-between items-center\">\n          <div className=\"text-sm text-gray-500\">\n            {selectedSlot && (\n              <span>\n                {mode === 'save' \n                  ? t('saveLoad.selectedSaveSlot', `Selected: Slot ${selectedSlot}`)\n                  : t('saveLoad.selectedLoadSlot', `Selected: Slot ${selectedSlot}`)\n                }\n              </span>\n            )}\n          </div>\n          \n          <div className=\"flex space-x-3\">\n            <Button variant=\"secondary\" onClick={onClose}>\n              {t('common.cancel', 'Cancel')}\n            </Button>\n            \n            {mode === 'save' ? (\n              <Button\n                variant=\"primary\"\n                onClick={() => selectedSlot && handleSave(selectedSlot)}\n                disabled={!selectedSlot || loading}\n                className=\"bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600\"\n              >\n                {loading ? '💾 Saving...' : '💾 Save Game'}\n              </Button>\n            ) : (\n              <Button\n                variant=\"primary\"\n                onClick={() => selectedSlot && handleLoad(selectedSlot)}\n                disabled={!selectedSlot || loading || saveSlots.find(s => s.id === selectedSlot)?.isEmpty}\n                className=\"bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600\"\n              >\n                {loading ? '📁 Loading...' : '📁 Load Game'}\n              </Button>\n            )}\n          </div>\n        </div>\n\n        {/* Confirm Overwrite Dialog */}\n        {showConfirmOverwrite && (\n          <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center\">\n            <div className=\"bg-white rounded-lg p-6 max-w-md mx-4\">\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">\n                {t('saveLoad.confirmOverwrite', 'Overwrite Save?')}\n              </h3>\n              <p className=\"text-gray-600 mb-6\">\n                {t('saveLoad.overwriteWarning', 'This will overwrite the existing save. This action cannot be undone.')}\n              </p>\n              <div className=\"flex space-x-3\">\n                <Button\n                  variant=\"secondary\"\n                  onClick={() => setShowConfirmOverwrite(false)}\n                >\n                  {t('common.cancel', 'Cancel')}\n                </Button>\n                <Button\n                  variant=\"primary\"\n                  className=\"bg-red-500 hover:bg-red-600\"\n                  onClick={() => selectedSlot && handleSave(selectedSlot)}\n                >\n                  {t('saveLoad.overwrite', 'Overwrite')}\n                </Button>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAEA;;;AAPA;;;;;;AA6BO,SAAS,cAAc,KAMT;QANS,EAC5B,MAAM,EACN,OAAO,EACP,IAAI,EACJ,aAAa,EACb,aAAa,EACM,GANS;QAyZwB;;IAlZpD,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IACxB,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB,EAAE;IACjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,QAAQ;gBACV;gBACA;gBACA,yCAAyC;gBACzC,iBAAiB,aAAkB,eAAe,OAAO,WAAW,KAAK;YAC3E;QACF;kCAAG;QAAC;KAAO;IAEX,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,QAAQ,MAAM,+HAAA,CAAA,iBAAc,CAAC,YAAY;YAC/C,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,MAAM,gBAAgB;QACpB,MAAM,QAAoB,EAAE;QAE5B,0BAA0B;QAC1B,IAAK,IAAI,IAAI,GAAG,KAAK,GAAG,IAAK;YAC3B,MAAM,UAAU,AAAC,uBAAwB,OAAF;YACvC,MAAM,WAAW,aAAa,OAAO,CAAC;YAEtC,IAAI,UAAU;gBACZ,IAAI;wBAQY,iBAAA;oBAPd,MAAM,OAAiB,KAAK,KAAK,CAAC;oBAClC,MAAM,IAAI,CAAC;wBACT,IAAI;wBACJ,MAAM,KAAK,MAAM,CAAC,IAAI,IAAI,AAAC,QAAS,OAAF;wBAClC,WAAW,KAAK,SAAS;wBACzB,aAAa,KAAK,MAAM,CAAC,KAAK;wBAC9B,OAAO,KAAK,MAAM,CAAC,KAAK;wBACxB,YAAY,EAAA,iBAAA,KAAK,QAAQ,cAAb,sCAAA,kBAAA,cAAe,CAAC,EAAE,cAAlB,sCAAA,gBAAoB,IAAI,KAAI;wBACxC,UAAU,KAAK,MAAM,CAAC,QAAQ,IAAI;wBAClC,SAAS;wBACT,MAAM;oBACR;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,AAAC,4BAA6B,OAAF,GAAE,MAAI;oBAChD,MAAM,IAAI,CAAC,gBAAgB;gBAC7B;YACF,OAAO;gBACL,MAAM,IAAI,CAAC,gBAAgB;YAC7B;QACF;QAEA,aAAa;IACf;IAEA,MAAM,kBAAkB,CAAC,KAAyB,CAAC;YACjD;YACA,MAAM,AAAC,cAAgB,OAAH;YACpB,WAAW;YACX,aAAa;YACb,OAAO;YACP,YAAY;YACZ,UAAU;YACV,SAAS;QACX,CAAC;IAED,MAAM,aAAa,OAAO;QACxB,IAAI,CAAC,cAAc;QAEnB,MAAM,OAAO,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,IAAI,EAAC,iBAAA,2BAAA,KAAM,OAAO,KAAI,CAAC,sBAAsB;YAC3C,wBAAwB;YACxB;QACF;QAEA,WAAW;QACX,IAAI;YACF,MAAM,UAAU,MAAM,cAAc,QAAQ,YAAY,AAAC,QAAc,OAAP;YAChE,IAAI,SAAS;gBACX,0BAAA,oCAAA;gBACA,iBAAgB,oBAAoB;gBACpC,wBAAwB;gBACxB,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;QAChC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa,OAAO;QACxB,MAAM,OAAO,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC1C,IAAI,CAAC,QAAQ,KAAK,OAAO,EAAE;QAE3B,WAAW;QACX,IAAI;YACF,MAAM,UAAU,MAAM,cAAc;YACpC,IAAI,SAAS;gBACX,0BAAA,oCAAA;gBACA;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gBAAgB;QAChC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,UAAU,AAAC,uBAA6B,OAAP;QACvC,aAAa,UAAU,CAAC;QACxB;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,SAAS,IAAI,IAAI;QAEtB,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,cAAc,GAAG,UAAU,gBAAgB;;YAClE,IAAI,UAAU;gBACZ,MAAM,WAAW,AAAC,GAA4C,OAA1C,SAAS,OAAO,CAAC,iBAAiB,MAAK,KAAc,OAAX,KAAK,GAAG,IAAG;gBACzE,MAAM,UAAU,MAAM,+HAAA,CAAA,iBAAc,CAAC,UAAU,CAAC,UAAU;gBAC1D,IAAI,SAAS;oBACX,0BAAA,oCAAA;oBACA;oBACA,YAAY;gBACd;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,WAAW;QACX,IAAI;YACF,MAAM,WAAW,MAAM,+HAAA,CAAA,iBAAc,CAAC,YAAY,CAAC;YACnD,IAAI,UAAU;gBACZ,yCAAyC;gBACzC,MAAM,UAAU,MAAM,cAAc,CAAC,GAAG,8BAA8B;;gBACtE,IAAI,SAAS;oBACX,0BAAA,oCAAA;oBACA;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI;YACF,MAAM,+HAAA,CAAA,iBAAc,CAAC,cAAc,CAAC;YACpC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,cAAc,GAAG,UAAU,wBAAwB;;YAC1E,IAAI,UAAU;gBACZ,MAAM,+HAAA,CAAA,iBAAc,CAAC,UAAU,CAAC,UAAU,YAAY;YACxD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IAEA,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,+HAAA,CAAA,iBAAc,CAAC,UAAU;YAChD,IAAI,UAAU;gBACZ,yBAAyB;gBACzB,MAAM,UAAU,MAAM,cAAc,CAAC,GAAG,2BAA2B;;gBACnE,IAAI,SAAS;oBACX,0BAAA,oCAAA;oBACA;gBACF;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kBAAkB;QAClC;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,CAAC,WAAW,OAAO;QACvB,OAAO,IAAI,KAAK,WAAW,cAAc;IAC3C;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU;QACnC,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,UAAU,OAAQ;QAC9C,OAAO,AAAC,GAAY,OAAV,OAAM,MAAY,OAAR,SAAQ;IAC9B;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDACX,SAAS,SAAS,iBAAiB;;;;;;kDAEtC,6LAAC;wCAAE,WAAU;kDACV,SAAS,SACN,EAAE,qBAAqB,yCACvB,EAAE,qBAAqB;;;;;;;;;;;;0CAI/B,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CACV;;;;;;;;;;;;;;;;;gBAOJ,+BACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,WAAW,AAAC,8DAIX,OAHC,CAAC,iBAAiB,iBAAiB,OAC/B,6CACA;gCAEN,SAAS;oCACP,gBAAgB;oCAChB,gBAAgB;gCAClB;0CACD;;;;;;0CAGD,6LAAC;gCACC,WAAW,AAAC,8DAIX,OAHC,iBAAiB,OACb,6CACA;gCAEN,SAAS;oCACP,gBAAgB;oCAChB,gBAAgB;gCAClB;0CACD;;;;;;;;;;;;;;;;;8BAQP,6LAAC;oBAAI,WAAU;;wBAEZ,SAAS,UAAU,8BAClB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CACd,EAAE,qBAAqB;;;;;;8CAE1B,6LAAC;oCACC,MAAK;oCACL,OAAO;oCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oCAC3C,aAAa,AAAC,QAAoB,OAAb;oCACrB,WAAU;;;;;;;;;;;;sCAMhB,6LAAC;4BAAI,WAAU;sCACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6LAAC;oCAEC,WAAW,AAAC,sEAMX,OALC,iBAAiB,KAAK,EAAE,GACpB,+BACA,KAAK,OAAO,GACZ,qDACA;oCAEN,SAAS,IAAM,gBAAgB,KAAK,EAAE;;sDAEtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,KAAK,EAAE;;;;;;sEAEV,6LAAC;;8EACC,6LAAC;oEAAG,WAAU;8EACX,KAAK,OAAO,GAAG,AAAC,QAAe,OAAR,KAAK,EAAE,IAAK,KAAK,IAAI;;;;;;gEAE9C,CAAC,KAAK,OAAO,kBACZ,6LAAC;oEAAE,WAAU;8EACV,gBAAgB,KAAK,SAAS;;;;;;;;;;;;;;;;;;gDAMtC,CAAC,KAAK,OAAO,kBACZ,6LAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;oDACV,SAAS,CAAC;wDACR,EAAE,eAAe;wDACjB,iBAAiB,KAAK,EAAE;oDAC1B;8DACD;;;;;;;;;;;;wCAMJ,KAAK,OAAO,iBACX,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,6LAAC;oDAAE,WAAU;8DAAW,EAAE,sBAAsB;;;;;;;;;;;iEAGlD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAK,WAAU;0EAAoB,KAAK,WAAW;;;;;;;;;;;;kEAEtD,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAK,WAAU;;oEAAmB;oEAAE,KAAK,KAAK;;;;;;;;;;;;;kEAEjD,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAK,WAAU;0EAAoB,KAAK,UAAU;;;;;;;;;;;;kEAErD,6LAAC;;0EACC,6LAAC;gEAAK,WAAU;0EAAgB;;;;;;0EAChC,6LAAC;gEAAK,WAAU;0EAAoB,eAAe,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;mCAhEnE,KAAK,EAAE;;;;;;;;;;;;;;;;8BA2EpB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,8BACC,6LAAC;0CACE,SAAS,SACN,EAAE,6BAA6B,AAAC,kBAA8B,OAAb,iBACjD,EAAE,6BAA6B,AAAC,kBAA8B,OAAb;;;;;;;;;;;sCAM3D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAY,SAAS;8CAClC,EAAE,iBAAiB;;;;;;gCAGrB,SAAS,uBACR,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,gBAAgB,WAAW;oCAC1C,UAAU,CAAC,gBAAgB;oCAC3B,WAAU;8CAET,UAAU,iBAAiB;;;;;yDAG9B,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS,IAAM,gBAAgB,WAAW;oCAC1C,UAAU,CAAC,gBAAgB,aAAW,kBAAA,UAAU,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,2BAA7B,sCAAA,gBAA4C,OAAO;oCACzF,WAAU;8CAET,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;gBAOpC,sCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CACX,EAAE,6BAA6B;;;;;;0CAElC,6LAAC;gCAAE,WAAU;0CACV,EAAE,6BAA6B;;;;;;0CAElC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,SAAS,IAAM,wBAAwB;kDAEtC,EAAE,iBAAiB;;;;;;kDAEtB,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,WAAU;wCACV,SAAS,IAAM,gBAAgB,WAAW;kDAEzC,EAAE,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3C;GAjcgB;;QAOA,sIAAA,CAAA,cAAW;QACwB,kIAAA,CAAA,UAAO;;;KAR1C", "debugId": null}}, {"offset": {"line": 5630, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/BakeryLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useLanguage } from '@/contexts/LanguageContext'\nimport { useGame } from '@/contexts/GameContext'\nimport { EquipmentData } from './Equipment'\n\ninterface Customer {\n  id: string\n  name: string\n  avatar: string\n  position: { x: number; y: number }\n  order?: string\n  satisfaction: number\n  waitTime: number\n  status: 'entering' | 'waiting' | 'ordering' | 'eating' | 'leaving'\n}\n\ninterface BakeryLayoutProps {\n  equipment: EquipmentData[]\n  onEquipmentClick: (equipmentId: string, equipmentName: string) => void\n}\n\nexport function BakeryLayout({ equipment, onEquipmentClick }: BakeryLayoutProps) {\n  const { t } = useLanguage()\n  const { orders, player } = useGame()\n  const [customers, setCustomers] = useState<Customer[]>([])\n  const [selectedArea, setSelectedArea] = useState<'kitchen' | 'dining' | 'counter'>('kitchen')\n\n  // Helper functions\n  const getCustomerAvatar = (name: string) => {\n    const avatars = ['👨‍💼', '👩‍💼', '👨‍🎓', '👩‍🎓', '👨‍🍳', '👩‍🍳', '👨‍⚕️', '👩‍⚕️', '👨‍🎨', '👩‍🎨']\n    return avatars[name.length % avatars.length]\n  }\n\n  const getDiningPosition = (index: number) => {\n    const positions = [\n      { x: 20, y: 20 }, { x: 60, y: 20 }, { x: 100, y: 20 },\n      { x: 20, y: 60 }, { x: 60, y: 60 }, { x: 100, y: 60 },\n      { x: 20, y: 100 }, { x: 60, y: 100 }, { x: 100, y: 100 }\n    ]\n    return positions[index % positions.length]\n  }\n\n  // Generate customers based on orders\n  useEffect(() => {\n    const newCustomers: Customer[] = orders.map((order, index) => ({\n      id: order.id,\n      name: order.customerName,\n      avatar: getCustomerAvatar(order.customerName),\n      position: getDiningPosition(index),\n      order: order.items[0],\n      satisfaction: 100 - (order.timeLimit < 180 ? 20 : 0),\n      waitTime: 300 - order.timeLimit,\n      status: order.status === 'pending' ? 'waiting' : order.status === 'in_progress' ? 'ordering' : 'eating'\n    }))\n    setCustomers(newCustomers)\n  }, [orders])\n\n  const getEquipmentIcon = (type: string) => {\n    switch (type) {\n      case 'oven': return '🔥'\n      case 'mixer': return '🥄'\n      case 'counter': return '🏪'\n      case 'auto_oven': return '🤖'\n      case 'auto_mixer': return '⚙️'\n      case 'conveyor': return '🔄'\n      default: return '📦'\n    }\n  }\n\n  const getEquipmentStatus = (eq: EquipmentData) => {\n    if (eq.isActive) return 'bg-green-100 border-green-400'\n    if (eq.automationLevel > 0) return 'bg-blue-100 border-blue-400'\n    return 'bg-gray-100 border-gray-300'\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg shadow-lg p-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center mb-6\">\n        <h2 className=\"text-2xl font-bold text-orange-800\">\n          🏪 {t('bakery.layout.title', 'Bakery Layout')}\n        </h2>\n        <div className=\"flex space-x-2\">\n          <button\n            onClick={() => setSelectedArea('kitchen')}\n            className={`px-4 py-2 rounded-lg font-medium transition-colors ${\n              selectedArea === 'kitchen'\n                ? 'bg-orange-500 text-white'\n                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n            }`}\n          >\n            👨‍🍳 {t('bakery.kitchen', 'Kitchen')}\n          </button>\n          <button\n            onClick={() => setSelectedArea('dining')}\n            className={`px-4 py-2 rounded-lg font-medium transition-colors ${\n              selectedArea === 'dining'\n                ? 'bg-orange-500 text-white'\n                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n            }`}\n          >\n            🍽️ {t('bakery.dining', 'Dining Area')}\n          </button>\n          <button\n            onClick={() => setSelectedArea('counter')}\n            className={`px-4 py-2 rounded-lg font-medium transition-colors ${\n              selectedArea === 'counter'\n                ? 'bg-orange-500 text-white'\n                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n            }`}\n          >\n            🛒 {t('bakery.counter', 'Service Counter')}\n          </button>\n        </div>\n      </div>\n\n      {/* Main Layout Area */}\n      <div className=\"relative bg-gradient-to-br from-yellow-50 to-orange-50 rounded-lg border-2 border-orange-200 min-h-[500px] overflow-hidden\">\n        \n        {/* Kitchen Area */}\n        {selectedArea === 'kitchen' && (\n          <div className=\"p-6\">\n            <div className=\"grid grid-cols-3 gap-4 mb-6\">\n              <div className=\"bg-red-100 rounded-lg p-4 border-2 border-red-300\">\n                <h3 className=\"font-semibold text-red-800 mb-3\">🔥 {t('bakery.baking.area', 'Baking Area')}</h3>\n                <div className=\"space-y-2\">\n                  {equipment.filter(eq => eq.type.includes('oven')).map(eq => (\n                    <div\n                      key={eq.id}\n                      onClick={() => onEquipmentClick(eq.id, eq.name)}\n                      className={`p-3 rounded-lg border-2 cursor-pointer transition-all hover:scale-105 ${getEquipmentStatus(eq)}`}\n                    >\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"text-xl\">{getEquipmentIcon(eq.type)}</span>\n                          <span className=\"font-medium text-sm\">{eq.name}</span>\n                        </div>\n                        {eq.isActive && (\n                          <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                        )}\n                      </div>\n                      {eq.currentRecipe && (\n                        <div className=\"text-xs text-gray-600 mt-1\">\n                          Making: {eq.currentRecipe}\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              <div className=\"bg-blue-100 rounded-lg p-4 border-2 border-blue-300\">\n                <h3 className=\"font-semibold text-blue-800 mb-3\">🥄 {t('bakery.prep.area', 'Prep Area')}</h3>\n                <div className=\"space-y-2\">\n                  {equipment.filter(eq => eq.type.includes('mixer')).map(eq => (\n                    <div\n                      key={eq.id}\n                      onClick={() => onEquipmentClick(eq.id, eq.name)}\n                      className={`p-3 rounded-lg border-2 cursor-pointer transition-all hover:scale-105 ${getEquipmentStatus(eq)}`}\n                    >\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"text-xl\">{getEquipmentIcon(eq.type)}</span>\n                          <span className=\"font-medium text-sm\">{eq.name}</span>\n                        </div>\n                        {eq.isActive && (\n                          <div className=\"w-2 h-2 bg-green-500 rounded-full animate-pulse\"></div>\n                        )}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              <div className=\"bg-purple-100 rounded-lg p-4 border-2 border-purple-300\">\n                <h3 className=\"font-semibold text-purple-800 mb-3\">⚙️ {t('bakery.automation.area', 'Automation')}</h3>\n                <div className=\"space-y-2\">\n                  {equipment.filter(eq => eq.type.includes('conveyor') || eq.automationLevel > 0).map(eq => (\n                    <div\n                      key={eq.id}\n                      onClick={() => onEquipmentClick(eq.id, eq.name)}\n                      className={`p-3 rounded-lg border-2 cursor-pointer transition-all hover:scale-105 ${getEquipmentStatus(eq)}`}\n                    >\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span className=\"text-xl\">{getEquipmentIcon(eq.type)}</span>\n                          <span className=\"font-medium text-sm\">{eq.name}</span>\n                        </div>\n                        <div className=\"text-xs bg-purple-200 px-2 py-1 rounded\">\n                          Auto Lv.{eq.automationLevel}\n                        </div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* Kitchen Stats */}\n            <div className=\"bg-white rounded-lg p-4 border border-gray-300\">\n              <h4 className=\"font-semibold text-gray-800 mb-2\">📊 {t('bakery.kitchen.stats', 'Kitchen Stats')}</h4>\n              <div className=\"grid grid-cols-3 gap-4 text-sm\">\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-green-600\">{equipment.filter(eq => eq.isActive).length}</div>\n                  <div className=\"text-gray-600\">{t('bakery.active.equipment', 'Active Equipment')}</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-blue-600\">{equipment.filter(eq => eq.automationLevel > 0).length}</div>\n                  <div className=\"text-gray-600\">{t('bakery.automated.equipment', 'Automated Equipment')}</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-purple-600\">{Math.round(equipment.reduce((sum, eq) => sum + eq.efficiency, 0) / equipment.length * 100)}%</div>\n                  <div className=\"text-gray-600\">{t('bakery.efficiency', 'Efficiency')}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Dining Area */}\n        {selectedArea === 'dining' && (\n          <div className=\"p-6\">\n            <div className=\"grid grid-cols-4 gap-4 mb-6\">\n              {/* Tables */}\n              {[1, 2, 3, 4, 5, 6, 7, 8].map(tableNum => {\n                const customer = customers[tableNum - 1]\n                return (\n                  <div\n                    key={tableNum}\n                    className=\"bg-amber-800 rounded-lg p-4 border-2 border-amber-600 relative text-white\"\n                  >\n                    <div className=\"text-center mb-2\">\n                      <div className=\"text-2xl\">🪑</div>\n                      <div className=\"text-xs\">Table {tableNum}</div>\n                    </div>\n                    \n                    {customer ? (\n                      <div className=\"text-center\">\n                        <div className=\"text-2xl mb-1\">{customer.avatar}</div>\n                        <div className=\"text-xs font-medium\">{customer.name}</div>\n                        <div className=\"text-xs opacity-75\">{customer.order}</div>\n                        <div className=\"mt-2\">\n                          <div className={`w-full h-1 rounded ${\n                            customer.satisfaction > 70 ? 'bg-green-400' :\n                            customer.satisfaction > 40 ? 'bg-yellow-400' : 'bg-red-400'\n                          }`} style={{ width: `${customer.satisfaction}%` }}></div>\n                        </div>\n                      </div>\n                    ) : (\n                      <div className=\"text-center text-gray-400\">\n                        <div className=\"text-xs\">Empty</div>\n                      </div>\n                    )}\n                  </div>\n                )\n              })}\n            </div>\n\n            {/* Dining Stats */}\n            <div className=\"bg-white rounded-lg p-4 border border-gray-300\">\n              <h4 className=\"font-semibold text-gray-800 mb-2\">🍽️ {t('bakery.dining.stats', 'Dining Stats')}</h4>\n              <div className=\"grid grid-cols-4 gap-4 text-sm\">\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-green-600\">{customers.length}</div>\n                  <div className=\"text-gray-600\">{t('bakery.current.customers', 'Current Customers')}</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-blue-600\">{customers.filter(c => c.status === 'waiting').length}</div>\n                  <div className=\"text-gray-600\">{t('bakery.waiting.customers', 'Waiting')}</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-purple-600\">{customers.filter(c => c.status === 'eating').length}</div>\n                  <div className=\"text-gray-600\">{t('bakery.eating.customers', 'Eating')}</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-lg font-bold text-orange-600\">\n                    {customers.length > 0 ? Math.round(customers.reduce((sum, c) => sum + c.satisfaction, 0) / customers.length) : 100}%\n                  </div>\n                  <div className=\"text-gray-600\">{t('bakery.avg.satisfaction', 'Avg Satisfaction')}</div>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Service Counter */}\n        {selectedArea === 'counter' && (\n          <div className=\"p-6\">\n            <div className=\"bg-gradient-to-r from-yellow-100 to-orange-100 rounded-lg p-6 border-2 border-orange-300\">\n              <h3 className=\"text-xl font-bold text-orange-800 mb-4 text-center\">\n                🛒 {t('bakery.service.counter', 'Service Counter')}\n              </h3>\n              \n              {/* Display Case */}\n              <div className=\"bg-white rounded-lg p-4 border-2 border-gray-300 mb-4\">\n                <h4 className=\"font-semibold text-gray-800 mb-3\">🧁 {t('bakery.display.case', 'Display Case')}</h4>\n                <div className=\"grid grid-cols-4 gap-3\">\n                  {['🍪', '🧁', '🥐', '🍞', '🥧', '🍰', '🥨', '🧇'].map((item, index) => (\n                    <div key={index} className=\"bg-gray-50 rounded-lg p-3 text-center border\">\n                      <div className=\"text-2xl mb-1\">{item}</div>\n                      <div className=\"text-xs text-gray-600\">Fresh</div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n\n              {/* Order Queue */}\n              <div className=\"bg-white rounded-lg p-4 border-2 border-gray-300\">\n                <h4 className=\"font-semibold text-gray-800 mb-3\">📋 {t('bakery.order.queue', 'Order Queue')}</h4>\n                <div className=\"space-y-2\">\n                  {orders.slice(0, 3).map((order, index) => (\n                    <div key={order.id} className=\"flex items-center justify-between p-2 bg-gray-50 rounded\">\n                      <div className=\"flex items-center space-x-2\">\n                        <span className=\"text-lg\">{getCustomerAvatar(order.customerName)}</span>\n                        <div>\n                          <div className=\"font-medium text-sm\">{order.customerName}</div>\n                          <div className=\"text-xs text-gray-600\">{order.items[0]}</div>\n                        </div>\n                      </div>\n                      <div className=\"text-right\">\n                        <div className=\"text-sm font-bold text-green-600\">${order.reward}</div>\n                        <div className=\"text-xs text-gray-500\">{Math.floor(order.timeLimit / 60)}m left</div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Bottom Status Bar */}\n      <div className=\"mt-4 bg-gray-100 rounded-lg p-3\">\n        <div className=\"flex justify-between items-center text-sm\">\n          <div className=\"flex space-x-4\">\n            <span>💰 ${player.money}</span>\n            <span>⭐ Level {player.level}</span>\n            <span>📦 {orders.length} Orders</span>\n          </div>\n          <div className=\"text-gray-600\">\n            {t('bakery.last.updated', 'Last updated')}: {new Date().toLocaleTimeString()}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAuBO,SAAS,aAAa,KAAkD;QAAlD,EAAE,SAAS,EAAE,gBAAgB,EAAqB,GAAlD;;IAC3B,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IACxB,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACjC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoC;IAEnF,mBAAmB;IACnB,MAAM,oBAAoB,CAAC;QACzB,MAAM,UAAU;YAAC;YAAS;YAAS;YAAS;YAAS;YAAS;YAAS;YAAS;YAAS;YAAS;SAAQ;QAC1G,OAAO,OAAO,CAAC,KAAK,MAAM,GAAG,QAAQ,MAAM,CAAC;IAC9C;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,YAAY;YAChB;gBAAE,GAAG;gBAAI,GAAG;YAAG;YAAG;gBAAE,GAAG;gBAAI,GAAG;YAAG;YAAG;gBAAE,GAAG;gBAAK,GAAG;YAAG;YACpD;gBAAE,GAAG;gBAAI,GAAG;YAAG;YAAG;gBAAE,GAAG;gBAAI,GAAG;YAAG;YAAG;gBAAE,GAAG;gBAAK,GAAG;YAAG;YACpD;gBAAE,GAAG;gBAAI,GAAG;YAAI;YAAG;gBAAE,GAAG;gBAAI,GAAG;YAAI;YAAG;gBAAE,GAAG;gBAAK,GAAG;YAAI;SACxD;QACD,OAAO,SAAS,CAAC,QAAQ,UAAU,MAAM,CAAC;IAC5C;IAEA,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,eAA2B,OAAO,GAAG;uDAAC,CAAC,OAAO,QAAU,CAAC;wBAC7D,IAAI,MAAM,EAAE;wBACZ,MAAM,MAAM,YAAY;wBACxB,QAAQ,kBAAkB,MAAM,YAAY;wBAC5C,UAAU,kBAAkB;wBAC5B,OAAO,MAAM,KAAK,CAAC,EAAE;wBACrB,cAAc,MAAM,CAAC,MAAM,SAAS,GAAG,MAAM,KAAK,CAAC;wBACnD,UAAU,MAAM,MAAM,SAAS;wBAC/B,QAAQ,MAAM,MAAM,KAAK,YAAY,YAAY,MAAM,MAAM,KAAK,gBAAgB,aAAa;oBACjG,CAAC;;YACD,aAAa;QACf;iCAAG;QAAC;KAAO;IAEX,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAQ,OAAO;YACpB,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAY,OAAO;YACxB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,GAAG,QAAQ,EAAE,OAAO;QACxB,IAAI,GAAG,eAAe,GAAG,GAAG,OAAO;QACnC,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAAqC;4BAC7C,EAAE,uBAAuB;;;;;;;kCAE/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,AAAC,sDAIX,OAHC,iBAAiB,YACb,6BACA;;oCAEP;oCACQ,EAAE,kBAAkB;;;;;;;0CAE7B,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,AAAC,sDAIX,OAHC,iBAAiB,WACb,6BACA;;oCAEP;oCACM,EAAE,iBAAiB;;;;;;;0CAE1B,6LAAC;gCACC,SAAS,IAAM,gBAAgB;gCAC/B,WAAW,AAAC,sDAIX,OAHC,iBAAiB,YACb,6BACA;;oCAEP;oCACK,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;0BAM9B,6LAAC;gBAAI,WAAU;;oBAGZ,iBAAiB,2BAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;oDAAkC;oDAAI,EAAE,sBAAsB;;;;;;;0DAC5E,6LAAC;gDAAI,WAAU;0DACZ,UAAU,MAAM,CAAC,CAAA,KAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,GAAG,CAAC,CAAA,mBACpD,6LAAC;wDAEC,SAAS,IAAM,iBAAiB,GAAG,EAAE,EAAE,GAAG,IAAI;wDAC9C,WAAW,AAAC,yEAA+F,OAAvB,mBAAmB;;0EAEvG,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;0FAAW,iBAAiB,GAAG,IAAI;;;;;;0FACnD,6LAAC;gFAAK,WAAU;0FAAuB,GAAG,IAAI;;;;;;;;;;;;oEAE/C,GAAG,QAAQ,kBACV,6LAAC;wEAAI,WAAU;;;;;;;;;;;;4DAGlB,GAAG,aAAa,kBACf,6LAAC;gEAAI,WAAU;;oEAA6B;oEACjC,GAAG,aAAa;;;;;;;;uDAfxB,GAAG,EAAE;;;;;;;;;;;;;;;;kDAuBlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;oDAAmC;oDAAI,EAAE,oBAAoB;;;;;;;0DAC3E,6LAAC;gDAAI,WAAU;0DACZ,UAAU,MAAM,CAAC,CAAA,KAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,UAAU,GAAG,CAAC,CAAA,mBACrD,6LAAC;wDAEC,SAAS,IAAM,iBAAiB,GAAG,EAAE,EAAE,GAAG,IAAI;wDAC9C,WAAW,AAAC,yEAA+F,OAAvB,mBAAmB;kEAEvG,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAW,iBAAiB,GAAG,IAAI;;;;;;sFACnD,6LAAC;4EAAK,WAAU;sFAAuB,GAAG,IAAI;;;;;;;;;;;;gEAE/C,GAAG,QAAQ,kBACV,6LAAC;oEAAI,WAAU;;;;;;;;;;;;uDAVd,GAAG,EAAE;;;;;;;;;;;;;;;;kDAkBlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;oDAAqC;oDAAI,EAAE,0BAA0B;;;;;;;0DACnF,6LAAC;gDAAI,WAAU;0DACZ,UAAU,MAAM,CAAC,CAAA,KAAM,GAAG,IAAI,CAAC,QAAQ,CAAC,eAAe,GAAG,eAAe,GAAG,GAAG,GAAG,CAAC,CAAA,mBAClF,6LAAC;wDAEC,SAAS,IAAM,iBAAiB,GAAG,EAAE,EAAE,GAAG,IAAI;wDAC9C,WAAW,AAAC,yEAA+F,OAAvB,mBAAmB;kEAEvG,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAK,WAAU;sFAAW,iBAAiB,GAAG,IAAI;;;;;;sFACnD,6LAAC;4EAAK,WAAU;sFAAuB,GAAG,IAAI;;;;;;;;;;;;8EAEhD,6LAAC;oEAAI,WAAU;;wEAA0C;wEAC9C,GAAG,eAAe;;;;;;;;;;;;;uDAV1B,GAAG,EAAE;;;;;;;;;;;;;;;;;;;;;;0CAoBpB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;4CAAmC;4CAAI,EAAE,wBAAwB;;;;;;;kDAC/E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAoC,UAAU,MAAM,CAAC,CAAA,KAAM,GAAG,QAAQ,EAAE,MAAM;;;;;;kEAC7F,6LAAC;wDAAI,WAAU;kEAAiB,EAAE,2BAA2B;;;;;;;;;;;;0DAE/D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAmC,UAAU,MAAM,CAAC,CAAA,KAAM,GAAG,eAAe,GAAG,GAAG,MAAM;;;;;;kEACvG,6LAAC;wDAAI,WAAU;kEAAiB,EAAE,8BAA8B;;;;;;;;;;;;0DAElE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DAAqC,KAAK,KAAK,CAAC,UAAU,MAAM,CAAC,CAAC,KAAK,KAAO,MAAM,GAAG,UAAU,EAAE,KAAK,UAAU,MAAM,GAAG;4DAAK;;;;;;;kEAC/I,6LAAC;wDAAI,WAAU;kEAAiB,EAAE,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQhE,iBAAiB,0BAChB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAEZ;oCAAC;oCAAG;oCAAG;oCAAG;oCAAG;oCAAG;oCAAG;oCAAG;iCAAE,CAAC,GAAG,CAAC,CAAA;oCAC5B,MAAM,WAAW,SAAS,CAAC,WAAW,EAAE;oCACxC,qBACE,6LAAC;wCAEC,WAAU;;0DAEV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAW;;;;;;kEAC1B,6LAAC;wDAAI,WAAU;;4DAAU;4DAAO;;;;;;;;;;;;;4CAGjC,yBACC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAiB,SAAS,MAAM;;;;;;kEAC/C,6LAAC;wDAAI,WAAU;kEAAuB,SAAS,IAAI;;;;;;kEACnD,6LAAC;wDAAI,WAAU;kEAAsB,SAAS,KAAK;;;;;;kEACnD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAW,AAAC,sBAGhB,OAFC,SAAS,YAAY,GAAG,KAAK,iBAC7B,SAAS,YAAY,GAAG,KAAK,kBAAkB;4DAC7C,OAAO;gEAAE,OAAO,AAAC,GAAwB,OAAtB,SAAS,YAAY,EAAC;4DAAG;;;;;;;;;;;;;;;;qEAIpD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;8DAAU;;;;;;;;;;;;uCAtBxB;;;;;gCA2BX;;;;;;0CAIF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;;4CAAmC;4CAAK,EAAE,uBAAuB;;;;;;;kDAC/E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAoC,UAAU,MAAM;;;;;;kEACnE,6LAAC;wDAAI,WAAU;kEAAiB,EAAE,4BAA4B;;;;;;;;;;;;0DAEhE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAmC,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WAAW,MAAM;;;;;;kEACtG,6LAAC;wDAAI,WAAU;kEAAiB,EAAE,4BAA4B;;;;;;;;;;;;0DAEhE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAqC,UAAU,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,UAAU,MAAM;;;;;;kEACvG,6LAAC;wDAAI,WAAU;kEAAiB,EAAE,2BAA2B;;;;;;;;;;;;0DAE/D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;4DACZ,UAAU,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC,UAAU,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,YAAY,EAAE,KAAK,UAAU,MAAM,IAAI;4DAAI;;;;;;;kEAErH,6LAAC;wDAAI,WAAU;kEAAiB,EAAE,2BAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAQtE,iBAAiB,2BAChB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAAqD;wCAC7D,EAAE,0BAA0B;;;;;;;8CAIlC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;gDAAmC;gDAAI,EAAE,uBAAuB;;;;;;;sDAC9E,6LAAC;4CAAI,WAAU;sDACZ;gDAAC;gDAAM;gDAAM;gDAAM;gDAAM;gDAAM;gDAAM;gDAAM;6CAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC3D,6LAAC;oDAAgB,WAAU;;sEACzB,6LAAC;4DAAI,WAAU;sEAAiB;;;;;;sEAChC,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;mDAF/B;;;;;;;;;;;;;;;;8CAShB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;;gDAAmC;gDAAI,EAAE,sBAAsB;;;;;;;sDAC7E,6LAAC;4CAAI,WAAU;sDACZ,OAAO,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBAC9B,6LAAC;oDAAmB,WAAU;;sEAC5B,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAK,WAAU;8EAAW,kBAAkB,MAAM,YAAY;;;;;;8EAC/D,6LAAC;;sFACC,6LAAC;4EAAI,WAAU;sFAAuB,MAAM,YAAY;;;;;;sFACxD,6LAAC;4EAAI,WAAU;sFAAyB,MAAM,KAAK,CAAC,EAAE;;;;;;;;;;;;;;;;;;sEAG1D,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;;wEAAmC;wEAAE,MAAM,MAAM;;;;;;;8EAChE,6LAAC;oEAAI,WAAU;;wEAAyB,KAAK,KAAK,CAAC,MAAM,SAAS,GAAG;wEAAI;;;;;;;;;;;;;;mDAVnE,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAsBhC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;wCAAK;wCAAK,OAAO,KAAK;;;;;;;8CACvB,6LAAC;;wCAAK;wCAAS,OAAO,KAAK;;;;;;;8CAC3B,6LAAC;;wCAAK;wCAAI,OAAO,MAAM;wCAAC;;;;;;;;;;;;;sCAE1B,6LAAC;4BAAI,WAAU;;gCACZ,EAAE,uBAAuB;gCAAgB;gCAAG,IAAI,OAAO,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;AAMtF;GAtUgB;;QACA,sIAAA,CAAA,cAAW;QACE,kIAAA,CAAA,UAAO;;;KAFpB", "debugId": null}}, {"offset": {"line": 6747, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/CustomerManager.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useLanguage } from '@/contexts/LanguageContext'\nimport { useGame } from '@/contexts/GameContext'\n\ninterface Customer {\n  id: string\n  name: string\n  avatar: string\n  orderItems: string[]\n  patience: number\n  maxPatience: number\n  satisfaction: number\n  status: 'entering' | 'waiting' | 'ordering' | 'served' | 'eating' | 'leaving'\n  tableNumber?: number\n  orderValue: number\n  preferences: string[]\n  mood: 'happy' | 'neutral' | 'impatient' | 'angry'\n}\n\ninterface CustomerManagerProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nexport function CustomerManager({ isOpen, onClose }: CustomerManagerProps) {\n  const { t } = useLanguage()\n  const { orders, player, completeOrder } = useGame()\n  const [customers, setCustomers] = useState<Customer[]>([])\n  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)\n\n  // Helper functions\n  const getCustomerAvatar = (name: string) => {\n    const avatars = [\n      '👨‍💼', '👩‍💼', '👨‍🎓', '👩‍🎓', '👨‍🍳', '👩‍🍳',\n      '👨‍⚕️', '👩‍⚕️', '👨‍🎨', '👩‍🎨', '👨‍💻', '👩‍💻',\n      '👨‍🔬', '👩‍🔬', '👨‍🏫', '👩‍🏫', '👨‍🎤', '👩‍🎤'\n    ]\n    return avatars[name.length % avatars.length]\n  }\n\n  const getCustomerPreferences = (name: string) => {\n    const allPreferences = ['sweet', 'savory', 'healthy', 'indulgent', 'traditional', 'exotic']\n    const hash = name.split('').reduce((a, b) => a + b.charCodeAt(0), 0)\n    return allPreferences.slice(0, 2 + (hash % 3))\n  }\n\n  const getMoodFromPatience = (satisfaction: number): Customer['mood'] => {\n    if (satisfaction > 80) return 'happy'\n    if (satisfaction > 50) return 'neutral'\n    if (satisfaction > 20) return 'impatient'\n    return 'angry'\n  }\n\n  // Generate customers from orders\n  useEffect(() => {\n    const newCustomers: Customer[] = orders.map((order, index) => {\n      const patience = order.timeLimit\n      const maxPatience = 300\n      const satisfaction = Math.max(0, Math.min(100, (patience / maxPatience) * 100))\n\n      return {\n        id: order.id,\n        name: order.customerName,\n        avatar: getCustomerAvatar(order.customerName),\n        orderItems: order.items,\n        patience,\n        maxPatience,\n        satisfaction,\n        status: order.status === 'pending' ? 'waiting' :\n                order.status === 'in_progress' ? 'ordering' : 'served',\n        tableNumber: index + 1,\n        orderValue: order.reward,\n        preferences: getCustomerPreferences(order.customerName),\n        mood: getMoodFromPatience(satisfaction)\n      }\n    })\n    setCustomers(newCustomers)\n  }, [orders])\n\n  const getMoodIcon = (mood: Customer['mood']) => {\n    switch (mood) {\n      case 'happy': return '😊'\n      case 'neutral': return '😐'\n      case 'impatient': return '😤'\n      case 'angry': return '😠'\n    }\n  }\n\n  const getMoodColor = (mood: Customer['mood']) => {\n    switch (mood) {\n      case 'happy': return 'text-green-600 bg-green-100'\n      case 'neutral': return 'text-yellow-600 bg-yellow-100'\n      case 'impatient': return 'text-orange-600 bg-orange-100'\n      case 'angry': return 'text-red-600 bg-red-100'\n    }\n  }\n\n  const getStatusIcon = (status: Customer['status']) => {\n    switch (status) {\n      case 'entering': return '🚶'\n      case 'waiting': return '⏰'\n      case 'ordering': return '📝'\n      case 'served': return '🍽️'\n      case 'eating': return '😋'\n      case 'leaving': return '👋'\n    }\n  }\n\n  const handleServeCustomer = (customer: Customer) => {\n    completeOrder(customer.id)\n    setCustomers(prev => prev.map(c => \n      c.id === customer.id ? { ...c, status: 'served' } : c\n    ))\n  }\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden\">\n        {/* Header */}\n        <div className=\"bg-gradient-to-r from-blue-500 to-purple-500 p-6 text-white\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h2 className=\"text-2xl font-bold\">👥 {t('customers.manager.title', 'Customer Manager')}</h2>\n              <p className=\"text-blue-100 text-sm\">\n                {t('customers.manager.subtitle', 'Monitor and serve your customers')}\n              </p>\n            </div>\n            <button\n              onClick={onClose}\n              className=\"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors\"\n            >\n              ✕ {t('common.close', 'Close')}\n            </button>\n          </div>\n        </div>\n\n        <div className=\"flex h-[70vh]\">\n          {/* Customer List */}\n          <div className=\"w-1/2 p-6 border-r border-gray-200 overflow-y-auto\">\n            <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">\n              📋 {t('customers.current.list', 'Current Customers')} ({customers.length})\n            </h3>\n            \n            <div className=\"space-y-3\">\n              {customers.map(customer => (\n                <div\n                  key={customer.id}\n                  onClick={() => setSelectedCustomer(customer)}\n                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md ${\n                    selectedCustomer?.id === customer.id\n                      ? 'border-blue-400 bg-blue-50'\n                      : 'border-gray-300 bg-white hover:border-gray-400'\n                  }`}\n                >\n                  <div className=\"flex items-center justify-between mb-2\">\n                    <div className=\"flex items-center space-x-3\">\n                      <span className=\"text-2xl\">{customer.avatar}</span>\n                      <div>\n                        <h4 className=\"font-medium text-gray-800\">{customer.name}</h4>\n                        <p className=\"text-sm text-gray-600\">\n                          {t('customers.table', 'Table')} {customer.tableNumber}\n                        </p>\n                      </div>\n                    </div>\n                    <div className=\"text-right\">\n                      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getMoodColor(customer.mood)}`}>\n                        {getMoodIcon(customer.mood)} {customer.mood}\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"text-lg\">{getStatusIcon(customer.status)}</span>\n                      <span className=\"text-sm text-gray-600 capitalize\">{customer.status}</span>\n                    </div>\n                    <div className=\"text-sm font-bold text-green-600\">\n                      ${customer.orderValue}\n                    </div>\n                  </div>\n\n                  {/* Patience Bar */}\n                  <div className=\"mt-2\">\n                    <div className=\"flex justify-between text-xs text-gray-500 mb-1\">\n                      <span>{t('customers.patience', 'Patience')}</span>\n                      <span>{Math.round(customer.satisfaction)}%</span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div\n                        className={`h-2 rounded-full transition-all duration-300 ${\n                          customer.satisfaction > 60 ? 'bg-green-500' :\n                          customer.satisfaction > 30 ? 'bg-yellow-500' : 'bg-red-500'\n                        }`}\n                        style={{ width: `${customer.satisfaction}%` }}\n                      ></div>\n                    </div>\n                  </div>\n                </div>\n              ))}\n\n              {customers.length === 0 && (\n                <div className=\"text-center py-8 text-gray-500\">\n                  <div className=\"text-4xl mb-2\">🏪</div>\n                  <p>{t('customers.no.customers', 'No customers currently')}</p>\n                  <p className=\"text-sm\">{t('customers.waiting.for.orders', 'Waiting for new orders...')}</p>\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Customer Details */}\n          <div className=\"w-1/2 p-6 overflow-y-auto\">\n            {selectedCustomer ? (\n              <div>\n                <div className=\"text-center mb-6\">\n                  <div className=\"text-6xl mb-2\">{selectedCustomer.avatar}</div>\n                  <h3 className=\"text-xl font-bold text-gray-800\">{selectedCustomer.name}</h3>\n                  <p className=\"text-gray-600\">{t('customers.table', 'Table')} {selectedCustomer.tableNumber}</p>\n                </div>\n\n                {/* Order Details */}\n                <div className=\"bg-gray-50 rounded-lg p-4 mb-4\">\n                  <h4 className=\"font-semibold text-gray-800 mb-3\">📝 {t('customers.order.details', 'Order Details')}</h4>\n                  <div className=\"space-y-2\">\n                    {selectedCustomer.orderItems.map((item, index) => (\n                      <div key={index} className=\"flex items-center justify-between p-2 bg-white rounded border\">\n                        <div className=\"flex items-center space-x-2\">\n                          <span>🧁</span>\n                          <span className=\"font-medium\">{item}</span>\n                        </div>\n                        <span className=\"text-green-600 font-bold\">${Math.round(selectedCustomer.orderValue / selectedCustomer.orderItems.length)}</span>\n                      </div>\n                    ))}\n                  </div>\n                  <div className=\"mt-3 pt-3 border-t border-gray-200\">\n                    <div className=\"flex justify-between font-bold\">\n                      <span>{t('customers.total', 'Total')}:</span>\n                      <span className=\"text-green-600\">${selectedCustomer.orderValue}</span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Customer Info */}\n                <div className=\"bg-blue-50 rounded-lg p-4 mb-4\">\n                  <h4 className=\"font-semibold text-blue-800 mb-3\">ℹ️ {t('customers.info', 'Customer Info')}</h4>\n                  <div className=\"space-y-2 text-sm\">\n                    <div className=\"flex justify-between\">\n                      <span>{t('customers.mood', 'Mood')}:</span>\n                      <span className={`font-medium ${getMoodColor(selectedCustomer.mood)}`}>\n                        {getMoodIcon(selectedCustomer.mood)} {selectedCustomer.mood}\n                      </span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>{t('customers.status', 'Status')}:</span>\n                      <span className=\"font-medium capitalize\">\n                        {getStatusIcon(selectedCustomer.status)} {selectedCustomer.status}\n                      </span>\n                    </div>\n                    <div className=\"flex justify-between\">\n                      <span>{t('customers.patience', 'Patience')}:</span>\n                      <span className=\"font-medium\">{Math.round(selectedCustomer.satisfaction)}%</span>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Preferences */}\n                <div className=\"bg-purple-50 rounded-lg p-4 mb-4\">\n                  <h4 className=\"font-semibold text-purple-800 mb-3\">❤️ {t('customers.preferences', 'Preferences')}</h4>\n                  <div className=\"flex flex-wrap gap-2\">\n                    {selectedCustomer.preferences.map((pref, index) => (\n                      <span\n                        key={index}\n                        className=\"px-3 py-1 bg-purple-200 text-purple-800 rounded-full text-sm font-medium\"\n                      >\n                        {pref}\n                      </span>\n                    ))}\n                  </div>\n                </div>\n\n                {/* Actions */}\n                {selectedCustomer.status === 'waiting' && (\n                  <div className=\"space-y-2\">\n                    <button\n                      onClick={() => handleServeCustomer(selectedCustomer)}\n                      className=\"w-full bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-4 rounded-lg transition-colors\"\n                    >\n                      🍽️ {t('customers.serve.order', 'Serve Order')}\n                    </button>\n                  </div>\n                )}\n              </div>\n            ) : (\n              <div className=\"text-center py-12 text-gray-500\">\n                <div className=\"text-4xl mb-4\">👆</div>\n                <p className=\"text-lg\">{t('customers.select.customer', 'Select a customer')}</p>\n                <p className=\"text-sm\">{t('customers.select.to.view.details', 'Select a customer to view details')}</p>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AA0BO,SAAS,gBAAgB,KAAyC;QAAzC,EAAE,MAAM,EAAE,OAAO,EAAwB,GAAzC;;IAC9B,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IACxB,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAChD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAE1E,mBAAmB;IACnB,MAAM,oBAAoB,CAAC;QACzB,MAAM,UAAU;YACd;YAAS;YAAS;YAAS;YAAS;YAAS;YAC7C;YAAS;YAAS;YAAS;YAAS;YAAS;YAC7C;YAAS;YAAS;YAAS;YAAS;YAAS;SAC9C;QACD,OAAO,OAAO,CAAC,KAAK,MAAM,GAAG,QAAQ,MAAM,CAAC;IAC9C;IAEA,MAAM,yBAAyB,CAAC;QAC9B,MAAM,iBAAiB;YAAC;YAAS;YAAU;YAAW;YAAa;YAAe;SAAS;QAC3F,MAAM,OAAO,KAAK,KAAK,CAAC,IAAI,MAAM,CAAC,CAAC,GAAG,IAAM,IAAI,EAAE,UAAU,CAAC,IAAI;QAClE,OAAO,eAAe,KAAK,CAAC,GAAG,IAAK,OAAO;IAC7C;IAEA,MAAM,sBAAsB,CAAC;QAC3B,IAAI,eAAe,IAAI,OAAO;QAC9B,IAAI,eAAe,IAAI,OAAO;QAC9B,IAAI,eAAe,IAAI,OAAO;QAC9B,OAAO;IACT;IAEA,iCAAiC;IACjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM,eAA2B,OAAO,GAAG;0DAAC,CAAC,OAAO;oBAClD,MAAM,WAAW,MAAM,SAAS;oBAChC,MAAM,cAAc;oBACpB,MAAM,eAAe,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,AAAC,WAAW,cAAe;oBAE1E,OAAO;wBACL,IAAI,MAAM,EAAE;wBACZ,MAAM,MAAM,YAAY;wBACxB,QAAQ,kBAAkB,MAAM,YAAY;wBAC5C,YAAY,MAAM,KAAK;wBACvB;wBACA;wBACA;wBACA,QAAQ,MAAM,MAAM,KAAK,YAAY,YAC7B,MAAM,MAAM,KAAK,gBAAgB,aAAa;wBACtD,aAAa,QAAQ;wBACrB,YAAY,MAAM,MAAM;wBACxB,aAAa,uBAAuB,MAAM,YAAY;wBACtD,MAAM,oBAAoB;oBAC5B;gBACF;;YACA,aAAa;QACf;oCAAG;QAAC;KAAO;IAEX,MAAM,cAAc,CAAC;QACnB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAS,OAAO;QACvB;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,OAAQ;YACN,KAAK;gBAAS,OAAO;YACrB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAS,OAAO;QACvB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAW,OAAO;YACvB,KAAK;gBAAY,OAAO;YACxB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAW,OAAO;QACzB;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,cAAc,SAAS,EAAE;QACzB,aAAa,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IAC5B,EAAE,EAAE,KAAK,SAAS,EAAE,GAAG;oBAAE,GAAG,CAAC;oBAAE,QAAQ;gBAAS,IAAI;IAExD;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;4CAAqB;4CAAI,EAAE,2BAA2B;;;;;;;kDACpE,6LAAC;wCAAE,WAAU;kDACV,EAAE,8BAA8B;;;;;;;;;;;;0CAGrC,6LAAC;gCACC,SAAS;gCACT,WAAU;;oCACX;oCACI,EAAE,gBAAgB;;;;;;;;;;;;;;;;;;8BAK3B,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;wCAA2C;wCACnD,EAAE,0BAA0B;wCAAqB;wCAAG,UAAU,MAAM;wCAAC;;;;;;;8CAG3E,6LAAC;oCAAI,WAAU;;wCACZ,UAAU,GAAG,CAAC,CAAA,yBACb,6LAAC;gDAEC,SAAS,IAAM,oBAAoB;gDACnC,WAAW,AAAC,yEAIX,OAHC,CAAA,6BAAA,uCAAA,iBAAkB,EAAE,MAAK,SAAS,EAAE,GAChC,+BACA;;kEAGN,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAY,SAAS,MAAM;;;;;;kFAC3C,6LAAC;;0FACC,6LAAC;gFAAG,WAAU;0FAA6B,SAAS,IAAI;;;;;;0FACxD,6LAAC;gFAAE,WAAU;;oFACV,EAAE,mBAAmB;oFAAS;oFAAE,SAAS,WAAW;;;;;;;;;;;;;;;;;;;0EAI3D,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAW,AAAC,uEAAkG,OAA5B,aAAa,SAAS,IAAI;;wEAC9G,YAAY,SAAS,IAAI;wEAAE;wEAAE,SAAS,IAAI;;;;;;;;;;;;;;;;;;kEAKjD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAK,WAAU;kFAAW,cAAc,SAAS,MAAM;;;;;;kFACxD,6LAAC;wEAAK,WAAU;kFAAoC,SAAS,MAAM;;;;;;;;;;;;0EAErE,6LAAC;gEAAI,WAAU;;oEAAmC;oEAC9C,SAAS,UAAU;;;;;;;;;;;;;kEAKzB,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAM,EAAE,sBAAsB;;;;;;kFAC/B,6LAAC;;4EAAM,KAAK,KAAK,CAAC,SAAS,YAAY;4EAAE;;;;;;;;;;;;;0EAE3C,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,WAAW,AAAC,gDAGX,OAFC,SAAS,YAAY,GAAG,KAAK,iBAC7B,SAAS,YAAY,GAAG,KAAK,kBAAkB;oEAEjD,OAAO;wEAAE,OAAO,AAAC,GAAwB,OAAtB,SAAS,YAAY,EAAC;oEAAG;;;;;;;;;;;;;;;;;;+CA/C7C,SAAS,EAAE;;;;;wCAsDnB,UAAU,MAAM,KAAK,mBACpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DAAgB;;;;;;8DAC/B,6LAAC;8DAAG,EAAE,0BAA0B;;;;;;8DAChC,6LAAC;oDAAE,WAAU;8DAAW,EAAE,gCAAgC;;;;;;;;;;;;;;;;;;;;;;;;sCAOlE,6LAAC;4BAAI,WAAU;sCACZ,iCACC,6LAAC;;kDACC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DAAiB,iBAAiB,MAAM;;;;;;0DACvD,6LAAC;gDAAG,WAAU;0DAAmC,iBAAiB,IAAI;;;;;;0DACtE,6LAAC;gDAAE,WAAU;;oDAAiB,EAAE,mBAAmB;oDAAS;oDAAE,iBAAiB,WAAW;;;;;;;;;;;;;kDAI5F,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;oDAAmC;oDAAI,EAAE,2BAA2B;;;;;;;0DAClF,6LAAC;gDAAI,WAAU;0DACZ,iBAAiB,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,sBACtC,6LAAC;wDAAgB,WAAU;;0EACzB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;kFAAK;;;;;;kFACN,6LAAC;wEAAK,WAAU;kFAAe;;;;;;;;;;;;0EAEjC,6LAAC;gEAAK,WAAU;;oEAA2B;oEAAE,KAAK,KAAK,CAAC,iBAAiB,UAAU,GAAG,iBAAiB,UAAU,CAAC,MAAM;;;;;;;;uDALhH;;;;;;;;;;0DASd,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;;gEAAM,EAAE,mBAAmB;gEAAS;;;;;;;sEACrC,6LAAC;4DAAK,WAAU;;gEAAiB;gEAAE,iBAAiB,UAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAMpE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;oDAAmC;oDAAI,EAAE,kBAAkB;;;;;;;0DACzE,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAM,EAAE,kBAAkB;oEAAQ;;;;;;;0EACnC,6LAAC;gEAAK,WAAW,AAAC,eAAkD,OAApC,aAAa,iBAAiB,IAAI;;oEAC/D,YAAY,iBAAiB,IAAI;oEAAE;oEAAE,iBAAiB,IAAI;;;;;;;;;;;;;kEAG/D,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAM,EAAE,oBAAoB;oEAAU;;;;;;;0EACvC,6LAAC;gEAAK,WAAU;;oEACb,cAAc,iBAAiB,MAAM;oEAAE;oEAAE,iBAAiB,MAAM;;;;;;;;;;;;;kEAGrE,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;oEAAM,EAAE,sBAAsB;oEAAY;;;;;;;0EAC3C,6LAAC;gEAAK,WAAU;;oEAAe,KAAK,KAAK,CAAC,iBAAiB,YAAY;oEAAE;;;;;;;;;;;;;;;;;;;;;;;;;kDAM/E,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;;oDAAqC;oDAAI,EAAE,yBAAyB;;;;;;;0DAClF,6LAAC;gDAAI,WAAU;0DACZ,iBAAiB,WAAW,CAAC,GAAG,CAAC,CAAC,MAAM,sBACvC,6LAAC;wDAEC,WAAU;kEAET;uDAHI;;;;;;;;;;;;;;;;oCAUZ,iBAAiB,MAAM,KAAK,2BAC3B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,SAAS,IAAM,oBAAoB;4CACnC,WAAU;;gDACX;gDACM,EAAE,yBAAyB;;;;;;;;;;;;;;;;;qDAMxC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,6LAAC;wCAAE,WAAU;kDAAW,EAAE,6BAA6B;;;;;;kDACvD,6LAAC;wCAAE,WAAU;kDAAW,EAAE,oCAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9E;GA1RgB;;QACA,sIAAA,CAAA,cAAW;QACiB,kIAAA,CAAA,UAAO;;;KAFnC", "debugId": null}}, {"offset": {"line": 7596, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/game/DiningRoom.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useLanguage } from '@/contexts/LanguageContext'\nimport { useGame } from '@/contexts/GameContext'\n\ninterface DiningTable {\n  id: number\n  position: { x: number; y: number }\n  seats: number\n  isOccupied: boolean\n  customer?: {\n    id: string\n    name: string\n    avatar: string\n    order: string\n    satisfaction: number\n    timeSeated: number\n  }\n}\n\ninterface DiningRoomProps {\n  onCustomerClick?: (customerId: string) => void\n}\n\nexport function DiningRoom({ onCustomerClick }: DiningRoomProps) {\n  const { t } = useLanguage()\n  const { orders } = useGame()\n  const [tables, setTables] = useState<DiningTable[]>([])\n  const [ambientSounds, setAmbientSounds] = useState(true)\n\n  // Helper functions\n  const getCustomerAvatar = (name: string) => {\n    const avatars = ['👨‍💼', '👩‍💼', '👨‍🎓', '👩‍🎓', '👨‍🍳', '👩‍🍳', '👨‍⚕️', '👩‍⚕️', '👨‍🎨', '👩‍🎨']\n    return avatars[name.length % avatars.length]\n  }\n\n  // Initialize dining room layout\n  useEffect(() => {\n    const initialTables: DiningTable[] = [\n      { id: 1, position: { x: 20, y: 20 }, seats: 2, isOccupied: false },\n      { id: 2, position: { x: 60, y: 20 }, seats: 4, isOccupied: false },\n      { id: 3, position: { x: 100, y: 20 }, seats: 2, isOccupied: false },\n      { id: 4, position: { x: 20, y: 60 }, seats: 2, isOccupied: false },\n      { id: 5, position: { x: 60, y: 60 }, seats: 6, isOccupied: false },\n      { id: 6, position: { x: 100, y: 60 }, seats: 2, isOccupied: false },\n      { id: 7, position: { x: 20, y: 100 }, seats: 4, isOccupied: false },\n      { id: 8, position: { x: 60, y: 100 }, seats: 2, isOccupied: false },\n      { id: 9, position: { x: 100, y: 100 }, seats: 2, isOccupied: false },\n    ]\n    setTables(initialTables)\n  }, [])\n\n  // Update tables with customers from orders\n  useEffect(() => {\n    setTables(prevTables => {\n      const updatedTables = [...prevTables]\n\n      // Clear all tables first\n      updatedTables.forEach(table => {\n        table.isOccupied = false\n        table.customer = undefined\n      })\n\n      // Assign customers to tables\n      orders.forEach((order, index) => {\n        const tableIndex = index % updatedTables.length\n        const table = updatedTables[tableIndex]\n\n        if (table) {\n          table.isOccupied = true\n          table.customer = {\n            id: order.id,\n            name: order.customerName,\n            avatar: getCustomerAvatar(order.customerName),\n            order: order.items[0],\n            satisfaction: Math.max(0, Math.min(100, (order.timeLimit / 300) * 100)),\n            timeSeated: 300 - order.timeLimit\n          }\n        }\n      })\n\n      return updatedTables\n    })\n  }, [orders])\n\n  const getTableIcon = (seats: number) => {\n    if (seats <= 2) return '🪑'\n    if (seats <= 4) return '🍽️'\n    return '🏛️'\n  }\n\n  const getSatisfactionColor = (satisfaction: number) => {\n    if (satisfaction > 80) return 'text-green-500'\n    if (satisfaction > 50) return 'text-yellow-500'\n    if (satisfaction > 20) return 'text-orange-500'\n    return 'text-red-500'\n  }\n\n  const getTableStyle = (table: DiningTable) => {\n    const baseStyle = \"absolute transition-all duration-300 hover:scale-105 cursor-pointer\"\n    const sizeStyle = table.seats <= 2 ? \"w-16 h-16\" : table.seats <= 4 ? \"w-20 h-20\" : \"w-24 h-24\"\n    const colorStyle = table.isOccupied ? \"bg-orange-100 border-orange-300\" : \"bg-gray-100 border-gray-300\"\n    \n    return `${baseStyle} ${sizeStyle} ${colorStyle} border-2 rounded-lg flex flex-col items-center justify-center`\n  }\n\n  return (\n    <div className=\"bg-gradient-to-br from-amber-50 to-orange-50 rounded-lg p-6 min-h-[600px] relative overflow-hidden\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center mb-6\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-orange-800\">\n            🍽️ {t('dining.room.title', 'Dining Room')}\n          </h2>\n          <p className=\"text-orange-600\">\n            {t('dining.room.subtitle', 'Watch your customers enjoy their meals')}\n          </p>\n        </div>\n        \n        <div className=\"flex items-center space-x-4\">\n          <div className=\"bg-white rounded-lg px-4 py-2 border border-orange-200\">\n            <div className=\"text-sm text-gray-600\">{t('dining.occupied.tables', 'Occupied Tables')}</div>\n            <div className=\"text-xl font-bold text-orange-600\">\n              {tables.filter(t => t.isOccupied).length}/{tables.length}\n            </div>\n          </div>\n          \n          <button\n            onClick={() => setAmbientSounds(!ambientSounds)}\n            className={`p-2 rounded-lg transition-colors ${\n              ambientSounds ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'\n            }`}\n            title={t('dining.ambient.sounds', 'Toggle ambient sounds')}\n          >\n            {ambientSounds ? '🔊' : '🔇'}\n          </button>\n        </div>\n      </div>\n\n      {/* Dining Room Floor Plan */}\n      <div className=\"relative bg-white rounded-lg border-2 border-orange-200 h-96 overflow-hidden\">\n        {/* Floor Pattern */}\n        <div className=\"absolute inset-0 opacity-10\">\n          <div className=\"grid grid-cols-8 grid-rows-6 h-full\">\n            {Array.from({ length: 48 }).map((_, i) => (\n              <div key={i} className=\"border border-gray-300\"></div>\n            ))}\n          </div>\n        </div>\n\n        {/* Tables */}\n        {tables.map(table => (\n          <div\n            key={table.id}\n            className={getTableStyle(table)}\n            style={{\n              left: `${table.position.x}px`,\n              top: `${table.position.y}px`,\n            }}\n            onClick={() => table.customer && onCustomerClick?.(table.customer.id)}\n          >\n            {/* Table Icon */}\n            <div className=\"text-2xl mb-1\">{getTableIcon(table.seats)}</div>\n            \n            {/* Table Number */}\n            <div className=\"text-xs font-bold text-gray-600\">#{table.id}</div>\n            \n            {/* Customer Info */}\n            {table.customer && (\n              <div className=\"absolute -top-8 left-1/2 transform -translate-x-1/2\">\n                <div className=\"bg-white rounded-full p-1 border-2 border-orange-300 shadow-lg\">\n                  <span className=\"text-lg\">{table.customer.avatar}</span>\n                </div>\n                \n                {/* Satisfaction Indicator */}\n                <div className=\"absolute -bottom-1 -right-1\">\n                  <div className={`w-3 h-3 rounded-full ${\n                    table.customer.satisfaction > 80 ? 'bg-green-500' :\n                    table.customer.satisfaction > 50 ? 'bg-yellow-500' :\n                    table.customer.satisfaction > 20 ? 'bg-orange-500' : 'bg-red-500'\n                  }`}></div>\n                </div>\n              </div>\n            )}\n\n            {/* Eating Animation */}\n            {table.customer && (\n              <div className=\"absolute -bottom-6 left-1/2 transform -translate-x-1/2\">\n                <div className=\"animate-bounce text-xs\">🍽️</div>\n              </div>\n            )}\n          </div>\n        ))}\n\n        {/* Decorative Elements */}\n        <div className=\"absolute top-4 left-4 text-2xl\">🪴</div>\n        <div className=\"absolute top-4 right-4 text-2xl\">🪴</div>\n        <div className=\"absolute bottom-4 left-4 text-2xl\">🕯️</div>\n        <div className=\"absolute bottom-4 right-4 text-2xl\">🕯️</div>\n        \n        {/* Service Counter */}\n        <div className=\"absolute bottom-0 left-1/2 transform -translate-x-1/2 bg-amber-200 rounded-t-lg p-2 border-2 border-amber-300\">\n          <div className=\"text-center\">\n            <div className=\"text-lg\">🛎️</div>\n            <div className=\"text-xs font-bold\">{t('dining.service.counter', 'Service')}</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Customer Status Panel */}\n      <div className=\"mt-6 bg-white rounded-lg p-4 border border-orange-200\">\n        <h3 className=\"font-semibold text-gray-800 mb-3\">\n          👥 {t('dining.customer.status', 'Customer Status')}\n        </h3>\n        \n        {tables.filter(t => t.isOccupied).length > 0 ? (\n          <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\n            {tables.filter(t => t.isOccupied).map(table => (\n              <div\n                key={table.id}\n                className=\"bg-gray-50 rounded-lg p-3 border cursor-pointer hover:bg-gray-100 transition-colors\"\n                onClick={() => table.customer && onCustomerClick?.(table.customer.id)}\n              >\n                <div className=\"flex items-center justify-between mb-2\">\n                  <div className=\"flex items-center space-x-2\">\n                    <span className=\"text-lg\">{table.customer?.avatar}</span>\n                    <div>\n                      <div className=\"font-medium text-sm\">{table.customer?.name}</div>\n                      <div className=\"text-xs text-gray-600\">Table {table.id}</div>\n                    </div>\n                  </div>\n                  <div className={`text-lg ${getSatisfactionColor(table.customer?.satisfaction || 0)}`}>\n                    {table.customer?.satisfaction && table.customer.satisfaction > 80 ? '😊' :\n                     table.customer?.satisfaction && table.customer.satisfaction > 50 ? '😐' :\n                     table.customer?.satisfaction && table.customer.satisfaction > 20 ? '😤' : '😠'}\n                  </div>\n                </div>\n                \n                <div className=\"text-xs text-gray-600 mb-2\">\n                  {t('dining.enjoying', 'Enjoying')}: {table.customer?.order}\n                </div>\n                \n                <div className=\"w-full bg-gray-200 rounded-full h-1\">\n                  <div\n                    className={`h-1 rounded-full transition-all duration-300 ${\n                      (table.customer?.satisfaction || 0) > 60 ? 'bg-green-500' :\n                      (table.customer?.satisfaction || 0) > 30 ? 'bg-yellow-500' : 'bg-red-500'\n                    }`}\n                    style={{ width: `${table.customer?.satisfaction || 0}%` }}\n                  ></div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <div className=\"text-center py-8 text-gray-500\">\n            <div className=\"text-4xl mb-2\">🏪</div>\n            <p>{t('dining.no.customers', 'No customers dining currently')}</p>\n            <p className=\"text-sm\">{t('dining.waiting.for.customers', 'Complete orders to see customers dining')}</p>\n          </div>\n        )}\n      </div>\n\n      {/* Ambient Effects */}\n      {ambientSounds && (\n        <div className=\"absolute top-2 right-2 text-xs text-gray-500 animate-pulse\">\n          🎵 {t('dining.ambient.playing', 'Ambient sounds playing')}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAyBO,SAAS,WAAW,KAAoC;QAApC,EAAE,eAAe,EAAmB,GAApC;;IACzB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IACxB,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACzB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACtD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,mBAAmB;IACnB,MAAM,oBAAoB,CAAC;QACzB,MAAM,UAAU;YAAC;YAAS;YAAS;YAAS;YAAS;YAAS;YAAS;YAAS;YAAS;YAAS;SAAQ;QAC1G,OAAO,OAAO,CAAC,KAAK,MAAM,GAAG,QAAQ,MAAM,CAAC;IAC9C;IAEA,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,MAAM,gBAA+B;gBACnC;oBAAE,IAAI;oBAAG,UAAU;wBAAE,GAAG;wBAAI,GAAG;oBAAG;oBAAG,OAAO;oBAAG,YAAY;gBAAM;gBACjE;oBAAE,IAAI;oBAAG,UAAU;wBAAE,GAAG;wBAAI,GAAG;oBAAG;oBAAG,OAAO;oBAAG,YAAY;gBAAM;gBACjE;oBAAE,IAAI;oBAAG,UAAU;wBAAE,GAAG;wBAAK,GAAG;oBAAG;oBAAG,OAAO;oBAAG,YAAY;gBAAM;gBAClE;oBAAE,IAAI;oBAAG,UAAU;wBAAE,GAAG;wBAAI,GAAG;oBAAG;oBAAG,OAAO;oBAAG,YAAY;gBAAM;gBACjE;oBAAE,IAAI;oBAAG,UAAU;wBAAE,GAAG;wBAAI,GAAG;oBAAG;oBAAG,OAAO;oBAAG,YAAY;gBAAM;gBACjE;oBAAE,IAAI;oBAAG,UAAU;wBAAE,GAAG;wBAAK,GAAG;oBAAG;oBAAG,OAAO;oBAAG,YAAY;gBAAM;gBAClE;oBAAE,IAAI;oBAAG,UAAU;wBAAE,GAAG;wBAAI,GAAG;oBAAI;oBAAG,OAAO;oBAAG,YAAY;gBAAM;gBAClE;oBAAE,IAAI;oBAAG,UAAU;wBAAE,GAAG;wBAAI,GAAG;oBAAI;oBAAG,OAAO;oBAAG,YAAY;gBAAM;gBAClE;oBAAE,IAAI;oBAAG,UAAU;wBAAE,GAAG;wBAAK,GAAG;oBAAI;oBAAG,OAAO;oBAAG,YAAY;gBAAM;aACpE;YACD,UAAU;QACZ;+BAAG,EAAE;IAEL,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR;wCAAU,CAAA;oBACR,MAAM,gBAAgB;2BAAI;qBAAW;oBAErC,yBAAyB;oBACzB,cAAc,OAAO;gDAAC,CAAA;4BACpB,MAAM,UAAU,GAAG;4BACnB,MAAM,QAAQ,GAAG;wBACnB;;oBAEA,6BAA6B;oBAC7B,OAAO,OAAO;gDAAC,CAAC,OAAO;4BACrB,MAAM,aAAa,QAAQ,cAAc,MAAM;4BAC/C,MAAM,QAAQ,aAAa,CAAC,WAAW;4BAEvC,IAAI,OAAO;gCACT,MAAM,UAAU,GAAG;gCACnB,MAAM,QAAQ,GAAG;oCACf,IAAI,MAAM,EAAE;oCACZ,MAAM,MAAM,YAAY;oCACxB,QAAQ,kBAAkB,MAAM,YAAY;oCAC5C,OAAO,MAAM,KAAK,CAAC,EAAE;oCACrB,cAAc,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,AAAC,MAAM,SAAS,GAAG,MAAO;oCAClE,YAAY,MAAM,MAAM,SAAS;gCACnC;4BACF;wBACF;;oBAEA,OAAO;gBACT;;QACF;+BAAG;QAAC;KAAO;IAEX,MAAM,eAAe,CAAC;QACpB,IAAI,SAAS,GAAG,OAAO;QACvB,IAAI,SAAS,GAAG,OAAO;QACvB,OAAO;IACT;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,eAAe,IAAI,OAAO;QAC9B,IAAI,eAAe,IAAI,OAAO;QAC9B,IAAI,eAAe,IAAI,OAAO;QAC9B,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,YAAY;QAClB,MAAM,YAAY,MAAM,KAAK,IAAI,IAAI,cAAc,MAAM,KAAK,IAAI,IAAI,cAAc;QACpF,MAAM,aAAa,MAAM,UAAU,GAAG,oCAAoC;QAE1E,OAAO,AAAC,GAAe,OAAb,WAAU,KAAgB,OAAb,WAAU,KAAc,OAAX,YAAW;IACjD;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;;oCAAqC;oCAC5C,EAAE,qBAAqB;;;;;;;0CAE9B,6LAAC;gCAAE,WAAU;0CACV,EAAE,wBAAwB;;;;;;;;;;;;kCAI/B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAyB,EAAE,0BAA0B;;;;;;kDACpE,6LAAC;wCAAI,WAAU;;4CACZ,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM;4CAAC;4CAAE,OAAO,MAAM;;;;;;;;;;;;;0CAI5D,6LAAC;gCACC,SAAS,IAAM,iBAAiB,CAAC;gCACjC,WAAW,AAAC,oCAEX,OADC,gBAAgB,gCAAgC;gCAElD,OAAO,EAAE,yBAAyB;0CAEjC,gBAAgB,OAAO;;;;;;;;;;;;;;;;;;0BAM9B,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAG,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClC,6LAAC;oCAAY,WAAU;mCAAb;;;;;;;;;;;;;;;oBAMf,OAAO,GAAG,CAAC,CAAA,sBACV,6LAAC;4BAEC,WAAW,cAAc;4BACzB,OAAO;gCACL,MAAM,AAAC,GAAmB,OAAjB,MAAM,QAAQ,CAAC,CAAC,EAAC;gCAC1B,KAAK,AAAC,GAAmB,OAAjB,MAAM,QAAQ,CAAC,CAAC,EAAC;4BAC3B;4BACA,SAAS,IAAM,MAAM,QAAQ,KAAI,4BAAA,sCAAA,gBAAkB,MAAM,QAAQ,CAAC,EAAE;;8CAGpE,6LAAC;oCAAI,WAAU;8CAAiB,aAAa,MAAM,KAAK;;;;;;8CAGxD,6LAAC;oCAAI,WAAU;;wCAAkC;wCAAE,MAAM,EAAE;;;;;;;gCAG1D,MAAM,QAAQ,kBACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAAW,MAAM,QAAQ,CAAC,MAAM;;;;;;;;;;;sDAIlD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAW,AAAC,wBAIhB,OAHC,MAAM,QAAQ,CAAC,YAAY,GAAG,KAAK,iBACnC,MAAM,QAAQ,CAAC,YAAY,GAAG,KAAK,kBACnC,MAAM,QAAQ,CAAC,YAAY,GAAG,KAAK,kBAAkB;;;;;;;;;;;;;;;;;gCAO5D,MAAM,QAAQ,kBACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;kDAAyB;;;;;;;;;;;;2BAnCvC,MAAM,EAAE;;;;;kCA0CjB,6LAAC;wBAAI,WAAU;kCAAiC;;;;;;kCAChD,6LAAC;wBAAI,WAAU;kCAAkC;;;;;;kCACjD,6LAAC;wBAAI,WAAU;kCAAoC;;;;;;kCACnD,6LAAC;wBAAI,WAAU;kCAAqC;;;;;;kCAGpD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CAAU;;;;;;8CACzB,6LAAC;oCAAI,WAAU;8CAAqB,EAAE,0BAA0B;;;;;;;;;;;;;;;;;;;;;;;0BAMtE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;;4BAAmC;4BAC3C,EAAE,0BAA0B;;;;;;;oBAGjC,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,MAAM,GAAG,kBACzC,6LAAC;wBAAI,WAAU;kCACZ,OAAO,MAAM,CAAC,CAAA,IAAK,EAAE,UAAU,EAAE,GAAG,CAAC,CAAA;gCAQH,iBAEa,kBAIM,kBAC7C,kBACA,kBACA,kBAKkC,kBAMhC,kBACA,kBAEgB;iDA9BzB,6LAAC;gCAEC,WAAU;gCACV,SAAS,IAAM,MAAM,QAAQ,KAAI,4BAAA,sCAAA,gBAAkB,MAAM,QAAQ,CAAC,EAAE;;kDAEpE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;mEAAW,kBAAA,MAAM,QAAQ,cAAd,sCAAA,gBAAgB,MAAM;;;;;;kEACjD,6LAAC;;0EACC,6LAAC;gEAAI,WAAU;2EAAuB,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,IAAI;;;;;;0EAC1D,6LAAC;gEAAI,WAAU;;oEAAwB;oEAAO,MAAM,EAAE;;;;;;;;;;;;;;;;;;;0DAG1D,6LAAC;gDAAI,WAAW,AAAC,WAAkE,OAAxD,qBAAqB,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,YAAY,KAAI;0DAC7E,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,YAAY,KAAI,MAAM,QAAQ,CAAC,YAAY,GAAG,KAAK,OACnE,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,YAAY,KAAI,MAAM,QAAQ,CAAC,YAAY,GAAG,KAAK,OACnE,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,YAAY,KAAI,MAAM,QAAQ,CAAC,YAAY,GAAG,KAAK,OAAO;;;;;;;;;;;;kDAI/E,6LAAC;wCAAI,WAAU;;4CACZ,EAAE,mBAAmB;4CAAY;6CAAG,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,KAAK;;;;;;;kDAG5D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAW,AAAC,gDAGX,OAFC,CAAC,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,YAAY,KAAI,CAAC,IAAI,KAAK,iBAC3C,CAAC,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,YAAY,KAAI,CAAC,IAAI,KAAK,kBAAkB;4CAE/D,OAAO;gDAAE,OAAO,AAAC,GAAoC,OAAlC,EAAA,mBAAA,MAAM,QAAQ,cAAd,uCAAA,iBAAgB,YAAY,KAAI,GAAE;4CAAG;;;;;;;;;;;;+BA7BvD,MAAM,EAAE;;;;;;;;;;6CAoCnB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,6LAAC;0CAAG,EAAE,uBAAuB;;;;;;0CAC7B,6LAAC;gCAAE,WAAU;0CAAW,EAAE,gCAAgC;;;;;;;;;;;;;;;;;;YAM/D,+BACC,6LAAC;gBAAI,WAAU;;oBAA6D;oBACtE,EAAE,0BAA0B;;;;;;;;;;;;;AAK1C;GAvPgB;;QACA,sIAAA,CAAA,cAAW;QACN,kIAAA,CAAA,UAAO;;;KAFZ", "debugId": null}}, {"offset": {"line": 8244, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/ui/ClientOnly.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useState } from 'react'\n\ninterface ClientOnlyProps {\n  children: React.ReactNode\n  fallback?: React.ReactNode\n}\n\n/**\n * ClientOnly component ensures that children are only rendered on the client side\n * after hydration is complete, preventing hydration mismatches.\n */\nexport function ClientOnly({ children, fallback = null }: ClientOnlyProps) {\n  const [hasMounted, setHasMounted] = useState(false)\n\n  useEffect(() => {\n    setHasMounted(true)\n  }, [])\n\n  if (!hasMounted) {\n    return <>{fallback}</>\n  }\n\n  return <>{children}</>\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAaO,SAAS,WAAW,KAA8C;QAA9C,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAmB,GAA9C;;IACzB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;gCAAE;YACR,cAAc;QAChB;+BAAG,EAAE;IAEL,IAAI,CAAC,YAAY;QACf,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ;GAZgB;KAAA", "debugId": null}}, {"offset": {"line": 8284, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/ui/GameToolbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { useLanguage } from '@/contexts/LanguageContext'\n\ninterface Player {\n  name: string\n  level: number\n  experience: number\n  maxExperience: number\n  money: number\n  skillPoints: number\n}\n\ninterface GameToolbarProps {\n  player: Player\n  onOpenMenu: () => void\n  onOpenAchievements: () => void\n  onOpenSkills: () => void\n  onOpenBakeries: () => void\n  onOpenSettings: () => void\n}\n\nexport function GameToolbar({ \n  player, \n  onOpenMenu, \n  onOpenAchievements, \n  onOpenSkills, \n  onOpenBakeries,\n  onOpenSettings \n}: GameToolbarProps) {\n  const { t } = useLanguage()\n  const [showQuickMenu, setShowQuickMenu] = useState(false)\n\n  const experiencePercentage = (player.experience / player.maxExperience) * 100\n\n  return (\n    <div className=\"bg-white shadow-lg border-b border-gray-200 relative\">\n      <div className=\"px-6 py-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Left Side - Game Title & Menu */}\n          <div className=\"flex items-center space-x-4\">\n            <Button\n              variant=\"secondary\"\n              size=\"sm\"\n              className=\"bg-orange-100 hover:bg-orange-200 text-orange-800\"\n              onClick={onOpenMenu}\n            >\n              ☰ {t('toolbar.menu', 'Menu')}\n            </Button>\n            \n            <div className=\"hidden md:block\">\n              <h1 className=\"text-xl font-bold text-orange-800\">🥖 Bake It Out</h1>\n            </div>\n          </div>\n\n          {/* Center - Player Stats */}\n          <div className=\"flex items-center space-x-4\">\n            {/* Player Name & Level */}\n            <div className=\"text-center\">\n              <div className=\"text-sm font-medium text-gray-800\">{player.name}</div>\n              <div className=\"text-xs text-gray-500\">Level {player.level}</div>\n            </div>\n\n            {/* Experience Bar */}\n            <div className=\"hidden sm:block\">\n              <div className=\"w-32 bg-gray-200 rounded-full h-2\">\n                <div \n                  className=\"bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300\"\n                  style={{ width: `${experiencePercentage}%` }}\n                />\n              </div>\n              <div className=\"text-xs text-center text-gray-500 mt-1\">\n                {player.experience}/{player.maxExperience} XP\n              </div>\n            </div>\n\n            {/* Money */}\n            <div className=\"bg-green-100 px-3 py-1 rounded-full\">\n              <span className=\"text-green-800 font-medium\">${player.money}</span>\n            </div>\n\n            {/* Skill Points */}\n            {player.skillPoints > 0 && (\n              <div className=\"bg-yellow-100 px-3 py-1 rounded-full relative\">\n                <span className=\"text-yellow-800 font-medium\">⭐ {player.skillPoints}</span>\n                <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse\" />\n              </div>\n            )}\n          </div>\n\n          {/* Right Side - Quick Actions */}\n          <div className=\"flex items-center space-x-2\">\n            {/* Quick Menu Toggle */}\n            <Button\n              variant=\"secondary\"\n              size=\"sm\"\n              className=\"md:hidden\"\n              onClick={() => setShowQuickMenu(!showQuickMenu)}\n            >\n              ⚡\n            </Button>\n\n            {/* Desktop Quick Actions */}\n            <div className=\"hidden md:flex items-center space-x-2\">\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onOpenBakeries}\n              >\n                🏪\n              </Button>\n              \n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onOpenAchievements}\n                className=\"relative\"\n              >\n                🏆\n              </Button>\n              \n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onOpenSkills}\n                className={player.skillPoints > 0 ? 'bg-yellow-100 hover:bg-yellow-200' : ''}\n              >\n                🌟\n                {player.skillPoints > 0 && (\n                  <span className=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse\" />\n                )}\n              </Button>\n              \n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                onClick={onOpenSettings}\n              >\n                ⚙️\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Mobile Quick Menu */}\n        {showQuickMenu && (\n          <div className=\"md:hidden mt-4 pt-4 border-t border-gray-200\">\n            <div className=\"grid grid-cols-4 gap-2\">\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                className=\"flex flex-col items-center py-3\"\n                onClick={() => {\n                  onOpenBakeries()\n                  setShowQuickMenu(false)\n                }}\n              >\n                <span className=\"text-lg\">🏪</span>\n                <span className=\"text-xs\">{t('toolbar.bakeries', 'Bakeries')}</span>\n              </Button>\n              \n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                className=\"flex flex-col items-center py-3\"\n                onClick={() => {\n                  onOpenAchievements()\n                  setShowQuickMenu(false)\n                }}\n              >\n                <span className=\"text-lg\">🏆</span>\n                <span className=\"text-xs\">{t('toolbar.achievements', 'Achievements')}</span>\n              </Button>\n              \n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                className={`flex flex-col items-center py-3 relative ${\n                  player.skillPoints > 0 ? 'bg-yellow-100 hover:bg-yellow-200' : ''\n                }`}\n                onClick={() => {\n                  onOpenSkills()\n                  setShowQuickMenu(false)\n                }}\n              >\n                <span className=\"text-lg\">🌟</span>\n                <span className=\"text-xs\">{t('toolbar.skills', 'Skills')}</span>\n                {player.skillPoints > 0 && (\n                  <div className=\"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse\" />\n                )}\n              </Button>\n              \n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                className=\"flex flex-col items-center py-3\"\n                onClick={() => {\n                  onOpenSettings()\n                  setShowQuickMenu(false)\n                }}\n              >\n                <span className=\"text-lg\">⚙️</span>\n                <span className=\"text-xs\">{t('toolbar.settings', 'Settings')}</span>\n              </Button>\n            </div>\n\n            {/* Mobile Experience Bar */}\n            <div className=\"mt-3\">\n              <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                <div \n                  className=\"bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300\"\n                  style={{ width: `${experiencePercentage}%` }}\n                />\n              </div>\n              <div className=\"text-xs text-center text-gray-500 mt-1\">\n                {player.experience}/{player.maxExperience} XP\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAwBO,SAAS,YAAY,KAOT;QAPS,EAC1B,MAAM,EACN,UAAU,EACV,kBAAkB,EAClB,YAAY,EACZ,cAAc,EACd,cAAc,EACG,GAPS;;IAQ1B,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IACxB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,MAAM,uBAAuB,AAAC,OAAO,UAAU,GAAG,OAAO,aAAa,GAAI;IAE1E,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;;wCACV;wCACI,EAAE,gBAAgB;;;;;;;8CAGvB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAG,WAAU;kDAAoC;;;;;;;;;;;;;;;;;sCAKtD,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAAqC,OAAO,IAAI;;;;;;sDAC/D,6LAAC;4CAAI,WAAU;;gDAAwB;gDAAO,OAAO,KAAK;;;;;;;;;;;;;8CAI5D,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,OAAO,AAAC,GAAuB,OAArB,sBAAqB;gDAAG;;;;;;;;;;;sDAG/C,6LAAC;4CAAI,WAAU;;gDACZ,OAAO,UAAU;gDAAC;gDAAE,OAAO,aAAa;gDAAC;;;;;;;;;;;;;8CAK9C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAK,WAAU;;4CAA6B;4CAAE,OAAO,KAAK;;;;;;;;;;;;gCAI5D,OAAO,WAAW,GAAG,mBACpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;;gDAA8B;gDAAG,OAAO,WAAW;;;;;;;sDACnE,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;sCAMrB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,iBAAiB,CAAC;8CAClC;;;;;;8CAKD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;sDACV;;;;;;sDAID,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;sDACX;;;;;;sDAID,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAW,OAAO,WAAW,GAAG,IAAI,sCAAsC;;gDAC3E;gDAEE,OAAO,WAAW,GAAG,mBACpB,6LAAC;oDAAK,WAAU;;;;;;;;;;;;sDAIpB,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;sDACV;;;;;;;;;;;;;;;;;;;;;;;;gBAQN,+BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;wCACP;wCACA,iBAAiB;oCACnB;;sDAEA,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CAAK,WAAU;sDAAW,EAAE,oBAAoB;;;;;;;;;;;;8CAGnD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;wCACP;wCACA,iBAAiB;oCACnB;;sDAEA,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CAAK,WAAU;sDAAW,EAAE,wBAAwB;;;;;;;;;;;;8CAGvD,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAW,AAAC,4CAEX,OADC,OAAO,WAAW,GAAG,IAAI,sCAAsC;oCAEjE,SAAS;wCACP;wCACA,iBAAiB;oCACnB;;sDAEA,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CAAK,WAAU;sDAAW,EAAE,kBAAkB;;;;;;wCAC9C,OAAO,WAAW,GAAG,mBACpB,6LAAC;4CAAI,WAAU;;;;;;;;;;;;8CAInB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;wCACP;wCACA,iBAAiB;oCACnB;;sDAEA,6LAAC;4CAAK,WAAU;sDAAU;;;;;;sDAC1B,6LAAC;4CAAK,WAAU;sDAAW,EAAE,oBAAoB;;;;;;;;;;;;;;;;;;sCAKrD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCACC,WAAU;wCACV,OAAO;4CAAE,OAAO,AAAC,GAAuB,OAArB,sBAAqB;wCAAG;;;;;;;;;;;8CAG/C,6LAAC;oCAAI,WAAU;;wCACZ,OAAO,UAAU;wCAAC;wCAAE,OAAO,aAAa;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ1D;GAzMgB;;QAQA,sIAAA,CAAA,cAAW;;;KARX", "debugId": null}}, {"offset": {"line": 8773, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/components/menu/GameMenu.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { Button } from '@/components/ui/Button'\nimport { useLanguage } from '@/contexts/LanguageContext'\n\ninterface GameMenuProps {\n  isOpen: boolean\n  onClose: () => void\n  onSaveGame: () => void\n  onLoadGame: () => void\n  onSettings: () => void\n  onMainMenu: () => void\n  onExit?: () => void\n}\n\nexport function GameMenu({ \n  isOpen, \n  onClose, \n  onSaveGame, \n  onLoadGame, \n  onSettings, \n  onMainMenu,\n  onExit \n}: GameMenuProps) {\n  const { t } = useLanguage()\n\n  if (!isOpen) return null\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden\">\n        {/* Header */}\n        <div className=\"bg-gradient-to-r from-orange-500 to-yellow-500 p-6 text-white\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h2 className=\"text-2xl font-bold\">🎮 {t('gameMenu.title', 'Game Menu')}</h2>\n              <p className=\"text-orange-100 text-sm\">\n                {t('gameMenu.subtitle', 'Manage your game')}\n              </p>\n            </div>\n            <Button\n              variant=\"secondary\"\n              size=\"sm\"\n              className=\"bg-white/20 hover:bg-white/30 text-white border-white/30\"\n              onClick={onClose}\n            >\n              ✕\n            </Button>\n          </div>\n        </div>\n\n        {/* Menu Options */}\n        <div className=\"p-6 space-y-3\">\n          <Button\n            variant=\"secondary\"\n            size=\"lg\"\n            className=\"w-full justify-start text-left py-4 hover:bg-orange-50\"\n            onClick={() => {\n              onClose()\n              // Resume game automatically\n            }}\n          >\n            <span className=\"text-2xl mr-3\">▶️</span>\n            <div>\n              <div className=\"font-semibold\">{t('gameMenu.resume', 'Resume Game')}</div>\n              <div className=\"text-sm text-gray-500\">\n                {t('gameMenu.resumeDesc', 'Continue playing')}\n              </div>\n            </div>\n          </Button>\n\n          <Button\n            variant=\"secondary\"\n            size=\"lg\"\n            className=\"w-full justify-start text-left py-4 hover:bg-green-50\"\n            onClick={() => {\n              onSaveGame()\n              onClose()\n            }}\n          >\n            <span className=\"text-2xl mr-3\">💾</span>\n            <div>\n              <div className=\"font-semibold\">{t('gameMenu.save', 'Save Game')}</div>\n              <div className=\"text-sm text-gray-500\">\n                {t('gameMenu.saveDesc', 'Save your progress')}\n              </div>\n            </div>\n          </Button>\n\n          <Button\n            variant=\"secondary\"\n            size=\"lg\"\n            className=\"w-full justify-start text-left py-4 hover:bg-blue-50\"\n            onClick={() => {\n              onLoadGame()\n              onClose()\n            }}\n          >\n            <span className=\"text-2xl mr-3\">📁</span>\n            <div>\n              <div className=\"font-semibold\">{t('gameMenu.load', 'Load Game')}</div>\n              <div className=\"text-sm text-gray-500\">\n                {t('gameMenu.loadDesc', 'Load saved progress')}\n              </div>\n            </div>\n          </Button>\n\n          <Button\n            variant=\"secondary\"\n            size=\"lg\"\n            className=\"w-full justify-start text-left py-4 hover:bg-purple-50\"\n            onClick={() => {\n              onSettings()\n              onClose()\n            }}\n          >\n            <span className=\"text-2xl mr-3\">⚙️</span>\n            <div>\n              <div className=\"font-semibold\">{t('gameMenu.settings', 'Settings')}</div>\n              <div className=\"text-sm text-gray-500\">\n                {t('gameMenu.settingsDesc', 'Game preferences')}\n              </div>\n            </div>\n          </Button>\n\n          <div className=\"border-t pt-3 mt-4\">\n            <Button\n              variant=\"secondary\"\n              size=\"lg\"\n              className=\"w-full justify-start text-left py-4 hover:bg-yellow-50\"\n              onClick={() => {\n                onMainMenu()\n                onClose()\n              }}\n            >\n              <span className=\"text-2xl mr-3\">🏠</span>\n              <div>\n                <div className=\"font-semibold\">{t('gameMenu.mainMenu', 'Main Menu')}</div>\n                <div className=\"text-sm text-gray-500\">\n                  {t('gameMenu.mainMenuDesc', 'Return to main menu')}\n                </div>\n              </div>\n            </Button>\n\n            {onExit && (\n              <Button\n                variant=\"secondary\"\n                size=\"lg\"\n                className=\"w-full justify-start text-left py-4 hover:bg-red-50 text-red-600\"\n                onClick={() => {\n                  onExit()\n                  onClose()\n                }}\n              >\n                <span className=\"text-2xl mr-3\">🚪</span>\n                <div>\n                  <div className=\"font-semibold\">{t('gameMenu.exit', 'Exit Game')}</div>\n                  <div className=\"text-sm text-red-400\">\n                    {t('gameMenu.exitDesc', 'Close the application')}\n                  </div>\n                </div>\n              </Button>\n            )}\n          </div>\n        </div>\n\n        {/* Footer */}\n        <div className=\"bg-gray-50 px-6 py-3 text-center text-sm text-gray-500\">\n          {t('gameMenu.tip', 'Press ESC to open this menu anytime')}\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;;;AAJA;;;AAgBO,SAAS,SAAS,KAQT;QARS,EACvB,MAAM,EACN,OAAO,EACP,UAAU,EACV,UAAU,EACV,UAAU,EACV,UAAU,EACV,MAAM,EACQ,GARS;;IASvB,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IAExB,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;4CAAqB;4CAAI,EAAE,kBAAkB;;;;;;;kDAC3D,6LAAC;wCAAE,WAAU;kDACV,EAAE,qBAAqB;;;;;;;;;;;;0CAG5B,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS;0CACV;;;;;;;;;;;;;;;;;8BAOL,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;gCACP;4BACA,4BAA4B;4BAC9B;;8CAEA,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAiB,EAAE,mBAAmB;;;;;;sDACrD,6LAAC;4CAAI,WAAU;sDACZ,EAAE,uBAAuB;;;;;;;;;;;;;;;;;;sCAKhC,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;gCACP;gCACA;4BACF;;8CAEA,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAiB,EAAE,iBAAiB;;;;;;sDACnD,6LAAC;4CAAI,WAAU;sDACZ,EAAE,qBAAqB;;;;;;;;;;;;;;;;;;sCAK9B,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;gCACP;gCACA;4BACF;;8CAEA,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAiB,EAAE,iBAAiB;;;;;;sDACnD,6LAAC;4CAAI,WAAU;sDACZ,EAAE,qBAAqB;;;;;;;;;;;;;;;;;;sCAK9B,6LAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;gCACP;gCACA;4BACF;;8CAEA,6LAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,6LAAC;;sDACC,6LAAC;4CAAI,WAAU;sDAAiB,EAAE,qBAAqB;;;;;;sDACvD,6LAAC;4CAAI,WAAU;sDACZ,EAAE,yBAAyB;;;;;;;;;;;;;;;;;;sCAKlC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;wCACP;wCACA;oCACF;;sDAEA,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAiB,EAAE,qBAAqB;;;;;;8DACvD,6LAAC;oDAAI,WAAU;8DACZ,EAAE,yBAAyB;;;;;;;;;;;;;;;;;;gCAKjC,wBACC,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS;wCACP;wCACA;oCACF;;sDAEA,6LAAC;4CAAK,WAAU;sDAAgB;;;;;;sDAChC,6LAAC;;8DACC,6LAAC;oDAAI,WAAU;8DAAiB,EAAE,iBAAiB;;;;;;8DACnD,6LAAC;oDAAI,WAAU;8DACZ,EAAE,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BASpC,6LAAC;oBAAI,WAAU;8BACZ,EAAE,gBAAgB;;;;;;;;;;;;;;;;;AAK7B;GA9JgB;;QASA,sIAAA,CAAA,cAAW;;;KATX", "debugId": null}}, {"offset": {"line": 9185, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/app/game/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useLanguage } from '@/contexts/LanguageContext'\nimport { GameProvider, useGame } from '@/contexts/GameContext'\nimport { Button } from '@/components/ui/Button'\nimport { Equipment } from '@/components/game/Equipment'\nimport { Order } from '@/components/game/Order'\nimport { RecipeModal } from '@/components/game/RecipeModal'\nimport { ShopModal } from '@/components/game/ShopModal'\nimport { BakingModal } from '@/components/game/BakingModal'\nimport { NotificationSystem, useNotifications } from '@/components/game/NotificationSystem'\nimport { LevelUpModal } from '@/components/game/LevelUpModal'\nimport { AchievementsModal } from '@/components/game/AchievementsModal'\nimport { SkillTreeModal } from '@/components/game/SkillTreeModal'\nimport { AutomationModal } from '@/components/game/AutomationModal'\nimport { EquipmentShopModal } from '@/components/game/EquipmentShopModal'\nimport { SettingsModal } from '@/components/game/SettingsModal'\nimport { BakeryManagerModal } from '@/components/game/BakeryManagerModal'\nimport { SaveLoadModal } from '@/components/game/SaveLoadModal'\nimport { BakeryLayout } from '@/components/game/BakeryLayout'\nimport { CustomerManager } from '@/components/game/CustomerManager'\nimport { DiningRoom } from '@/components/game/DiningRoom'\nimport { ClientOnly } from '@/components/ui/ClientOnly'\nimport { GameToolbar } from '@/components/ui/GameToolbar'\nimport { GameMenu } from '@/components/menu/GameMenu'\nimport { useDiscordRPC } from '@/contexts/DiscordRPCContext'\n\nfunction GameContent() {\n  const { t } = useLanguage()\n  const { setGameActivity, setBakingActivity } = useDiscordRPC()\n  const {\n    player,\n    equipment,\n    inventory,\n    orders,\n    achievements,\n    skills,\n    levelUpRewards,\n    showLevelUp,\n    updateEquipment,\n    acceptOrder,\n    completeOrder,\n    declineOrder,\n    generateNewOrder,\n    upgradeSkill,\n    checkAchievements,\n    dismissLevelUp,\n    spendMoney\n  } = useGame()\n\n  const [showRecipeModal, setShowRecipeModal] = useState(false)\n  const [showShopModal, setShowShopModal] = useState(false)\n  const [showBakingModal, setShowBakingModal] = useState(false)\n  const [showAchievementsModal, setShowAchievementsModal] = useState(false)\n  const [showSkillTreeModal, setShowSkillTreeModal] = useState(false)\n  const [showAutomationModal, setShowAutomationModal] = useState(false)\n  const [showEquipmentShopModal, setShowEquipmentShopModal] = useState(false)\n  const [showSettingsModal, setShowSettingsModal] = useState(false)\n  const [showBakeryManagerModal, setShowBakeryManagerModal] = useState(false)\n  const [showGameMenu, setShowGameMenu] = useState(false)\n  const [showSaveLoadModal, setShowSaveLoadModal] = useState(false)\n  const [showCustomerManager, setShowCustomerManager] = useState(false)\n  const [saveLoadMode, setSaveLoadMode] = useState<'save' | 'load'>('save')\n  const [selectedEquipment, setSelectedEquipment] = useState<{id: string, name: string} | null>(null)\n  const [gameView, setGameView] = useState<'traditional' | 'layout' | 'dining'>('traditional')\n\n  // Game settings state\n  const [gameSettings, setGameSettings] = useState({\n    language: 'en' as 'en' | 'cs',\n    soundEnabled: true,\n    musicEnabled: true,\n    notificationsEnabled: true,\n    autoSaveEnabled: true,\n    graphicsQuality: 'medium' as 'low' | 'medium' | 'high',\n    animationSpeed: 1,\n    showTutorials: true\n  })\n\n  // Bakery management state\n  const [bakeries, setBakeries] = useState([\n    {\n      id: 'main',\n      name: 'Downtown Delights',\n      location: 'City Center',\n      specialization: 'general' as const,\n      level: 1,\n      equipment: [],\n      inventory: [],\n      orders: [],\n      automationJobs: [],\n      conveyorBelts: [],\n      unlocked: true,\n      purchaseCost: 0\n    }\n  ])\n  const [currentBakeryId, setCurrentBakeryId] = useState('main')\n\n  const { notifications, removeNotification, showSuccess, showError, showInfo } = useNotifications()\n\n  const handleEquipmentClick = (equipmentId: string, equipmentName: string) => {\n    setSelectedEquipment({ id: equipmentId, name: equipmentName })\n    setShowBakingModal(true)\n  }\n\n  const handleOrderAccept = (orderId: string) => {\n    acceptOrder(orderId)\n    showInfo('Order Accepted', 'You have accepted a new order!')\n  }\n\n  const handleOrderComplete = (orderId: string) => {\n    const order = orders.find(o => o.id === orderId)\n    if (order) {\n      completeOrder(orderId)\n      checkAchievements()\n      showSuccess('Order Completed!', `You earned $${order.reward} and gained experience!`)\n    }\n  }\n\n  const handleOrderDecline = (orderId: string) => {\n    declineOrder(orderId)\n    showInfo('Order Declined', 'Order has been removed from your queue.')\n  }\n\n  const handleSettingsChange = (newSettings: Partial<typeof gameSettings>) => {\n    setGameSettings(prev => ({ ...prev, ...newSettings }))\n  }\n\n  const handleSwitchBakery = (bakeryId: string) => {\n    setCurrentBakeryId(bakeryId)\n    showInfo('Bakery Switched', `Switched to ${bakeries.find(b => b.id === bakeryId)?.name}`)\n  }\n\n  const handlePurchaseBakery = (bakery: { name: string; purchaseCost: number }) => {\n    if (spendMoney(bakery.purchaseCost)) {\n      const newBakery = {\n        id: Date.now().toString(),\n        name: bakery.name,\n        location: 'Downtown',\n        specialization: 'general' as const,\n        level: 1,\n        equipment: [],\n        inventory: [],\n        orders: [],\n        automationJobs: [],\n        conveyorBelts: [],\n        unlocked: true,\n        purchaseCost: bakery.purchaseCost\n      }\n      setBakeries(prev => [...prev, newBakery])\n      showSuccess('Bakery Purchased!', `You now own ${bakery.name}!`)\n    }\n  }\n\n  // Game menu handlers\n  const handleSaveGame = () => {\n    setSaveLoadMode('save')\n    setShowSaveLoadModal(true)\n  }\n\n  const handleLoadGame = () => {\n    setSaveLoadMode('load')\n    setShowSaveLoadModal(true)\n  }\n\n  const handleMainMenu = () => {\n    if (typeof window !== 'undefined') {\n      window.location.href = '/'\n    }\n  }\n\n  const handleExit = () => {\n    if (typeof window !== 'undefined' && window.electronAPI) {\n      window.electronAPI.quit()\n    }\n  }\n\n  // Keyboard shortcuts\n  const handleKeyPress = (event: KeyboardEvent) => {\n    if (event.key === 'Escape') {\n      setShowGameMenu(!showGameMenu)\n    }\n  }\n\n  // Add keyboard event listener\n  if (typeof window !== 'undefined') {\n    window.addEventListener('keydown', handleKeyPress)\n  }\n\n  // Watch for achievement completions\n  useEffect(() => {\n    if (typeof window === 'undefined') return // Skip on server side\n\n    const completedAchievements = achievements.filter(a => a.completed)\n    const previousCount = parseInt(localStorage.getItem('completedAchievements') || '0')\n\n    if (completedAchievements.length > previousCount) {\n      const newAchievements = completedAchievements.slice(previousCount)\n      newAchievements.forEach(achievement => {\n        showSuccess('Achievement Unlocked!', `🏆 ${achievement.name}`)\n      })\n      localStorage.setItem('completedAchievements', completedAchievements.length.toString())\n    }\n  }, [achievements, showSuccess])\n\n  // Update Discord RPC when game state changes\n  useEffect(() => {\n    const updateDiscordRPC = async () => {\n      if (orders && orders.length > 0) {\n        // Player is baking\n        const currentOrder = orders[0]\n        const orderItem = currentOrder.items[0]?.name || 'Unknown item'\n        await setBakingActivity(player.level, orderItem)\n      } else {\n        // Player is managing bakery\n        await setGameActivity(player.level, player.money, 'Managing bakery')\n      }\n    }\n\n    updateDiscordRPC()\n  }, [player.level, player.money, orders, setGameActivity, setBakingActivity])\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-orange-50 to-yellow-50\">\n      {/* Game Toolbar */}\n      <GameToolbar\n        player={player}\n        onOpenMenu={() => setShowGameMenu(true)}\n        onOpenAchievements={() => setShowAchievementsModal(true)}\n        onOpenSkills={() => setShowSkillTreeModal(true)}\n        onOpenBakeries={() => setShowBakeryManagerModal(true)}\n        onOpenSettings={() => setShowSettingsModal(true)}\n      />\n\n      {/* View Selector */}\n      <div className=\"max-w-7xl mx-auto px-6 pb-4\">\n        <div className=\"flex justify-center space-x-2\">\n          <button\n            onClick={() => setGameView('traditional')}\n            className={`px-4 py-2 rounded-lg font-medium transition-colors ${\n              gameView === 'traditional'\n                ? 'bg-orange-500 text-white'\n                : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'\n            }`}\n          >\n            📊 {t('game.view.traditional', 'Traditional View')}\n          </button>\n          <button\n            onClick={() => setGameView('layout')}\n            className={`px-4 py-2 rounded-lg font-medium transition-colors ${\n              gameView === 'layout'\n                ? 'bg-orange-500 text-white'\n                : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'\n            }`}\n          >\n            🏪 {t('game.view.layout', 'Bakery Layout')}\n          </button>\n          <button\n            onClick={() => setGameView('dining')}\n            className={`px-4 py-2 rounded-lg font-medium transition-colors ${\n              gameView === 'dining'\n                ? 'bg-orange-500 text-white'\n                : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'\n            }`}\n          >\n            🍽️ {t('game.view.dining', 'Dining Room')}\n          </button>\n          <button\n            onClick={() => setShowCustomerManager(true)}\n            className=\"px-4 py-2 rounded-lg font-medium bg-blue-500 text-white hover:bg-blue-600 transition-colors\"\n          >\n            👥 {t('game.view.customers', 'Customer Manager')}\n          </button>\n        </div>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto p-6 grid grid-cols-1 lg:grid-cols-4 gap-6\">\n        {/* Main Game Area */}\n        <div className=\"lg:col-span-3 space-y-6\">\n          {gameView === 'layout' && (\n            <ClientOnly fallback={<div className=\"bg-white rounded-lg shadow-md p-6 text-center\">Loading bakery layout...</div>}>\n              <BakeryLayout\n                equipment={equipment}\n                onEquipmentClick={handleEquipmentClick}\n              />\n            </ClientOnly>\n          )}\n\n          {gameView === 'dining' && (\n            <ClientOnly fallback={<div className=\"bg-white rounded-lg shadow-md p-6 text-center\">Loading dining room...</div>}>\n              <DiningRoom\n                onCustomerClick={(customerId) => {\n                  setShowCustomerManager(true)\n                }}\n              />\n            </ClientOnly>\n          )}\n\n          {gameView === 'traditional' && (\n            <>\n              <div className=\"bg-white rounded-lg shadow-md p-6\">\n                <div className=\"flex justify-between items-center mb-4\">\n                  <h2 className=\"text-xl font-semibold text-orange-800\">{t('kitchen.title')}</h2>\n                  <div className=\"text-sm text-gray-600\">\n                    Current: {bakeries.find(b => b.id === currentBakeryId)?.name}\n                  </div>\n                </div>\n\n                {/* Equipment Grid */}\n                <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                  {equipment.map((eq) => (\n                    <Equipment\n                      key={eq.id}\n                      equipment={eq}\n                      onClick={handleEquipmentClick}\n                    />\n                  ))}\n                </div>\n              </div>\n\n              {/* Inventory */}\n              <div className=\"bg-white rounded-lg shadow-md p-6\">\n                <h2 className=\"text-xl font-semibold text-orange-800 mb-4\">{t('inventory.title')}</h2>\n                <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                  {inventory.map((ingredient) => (\n                    <div key={ingredient.name} className=\"bg-gray-50 p-3 rounded-lg text-center\">\n                      <div className=\"text-2xl mb-1\">{ingredient.icon}</div>\n                      <div className=\"font-medium text-gray-800\">{ingredient.name}</div>\n                      <div className=\"text-sm text-gray-600\">{t('inventory.quantity', { qty: ingredient.quantity.toString() })}</div>\n                      <div className=\"text-xs text-green-600\">{t('inventory.cost', { cost: ingredient.cost.toString() })}</div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n\n        {/* Orders Panel */}\n        <div className=\"space-y-6\">\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <div className=\"flex justify-between items-center mb-4\">\n              <h2 className=\"text-xl font-semibold text-orange-800\">{t('orders.title')}</h2>\n              <Button\n                size=\"sm\"\n                variant=\"primary\"\n                onClick={generateNewOrder}\n              >\n                {t('orders.newOrder')}\n              </Button>\n            </div>\n            <div className=\"space-y-4\">\n              {orders.map((order) => (\n                <Order\n                  key={order.id}\n                  order={order}\n                  onAccept={handleOrderAccept}\n                  onDecline={handleOrderDecline}\n                  onComplete={handleOrderComplete}\n                />\n              ))}\n            </div>\n          </div>\n\n          {/* Quick Actions */}\n          <div className=\"bg-white rounded-lg shadow-md p-6\">\n            <h2 className=\"text-xl font-semibold text-orange-800 mb-4\">{t('actions.title')}</h2>\n            <div className=\"space-y-2\">\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                className=\"w-full\"\n                onClick={() => setShowShopModal(true)}\n              >\n                {t('actions.buyIngredients')}\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                className=\"w-full\"\n                onClick={() => setShowRecipeModal(true)}\n              >\n                {t('actions.viewRecipes')}\n              </Button>\n              <Button\n                variant=\"secondary\"\n                size=\"sm\"\n                className=\"w-full\"\n                onClick={() => setShowEquipmentShopModal(true)}\n              >\n                {t('actions.equipmentShop')}\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Modals */}\n      <RecipeModal\n        isOpen={showRecipeModal}\n        onClose={() => setShowRecipeModal(false)}\n      />\n      <ShopModal\n        isOpen={showShopModal}\n        onClose={() => setShowShopModal(false)}\n      />\n      <BakingModal\n        isOpen={showBakingModal}\n        onClose={() => setShowBakingModal(false)}\n        equipmentId={selectedEquipment?.id || ''}\n        equipmentName={selectedEquipment?.name || ''}\n      />\n      <AchievementsModal\n        isOpen={showAchievementsModal}\n        onClose={() => setShowAchievementsModal(false)}\n        achievements={achievements}\n      />\n      <SkillTreeModal\n        isOpen={showSkillTreeModal}\n        onClose={() => setShowSkillTreeModal(false)}\n        skills={skills}\n        skillPoints={player.skillPoints}\n        playerLevel={player.level}\n        onUpgradeSkill={upgradeSkill}\n      />\n      <LevelUpModal\n        isOpen={showLevelUp}\n        onClose={dismissLevelUp}\n        newLevel={player.level}\n        rewards={levelUpRewards}\n      />\n      <AutomationModal\n        isOpen={showAutomationModal}\n        onClose={() => setShowAutomationModal(false)}\n      />\n      <EquipmentShopModal\n        isOpen={showEquipmentShopModal}\n        onClose={() => setShowEquipmentShopModal(false)}\n        onShowSuccess={showSuccess}\n      />\n      <SettingsModal\n        isOpen={showSettingsModal}\n        onClose={() => setShowSettingsModal(false)}\n        settings={gameSettings}\n        onSettingsChange={handleSettingsChange}\n      />\n      <BakeryManagerModal\n        isOpen={showBakeryManagerModal}\n        onClose={() => setShowBakeryManagerModal(false)}\n        bakeries={bakeries}\n        currentBakeryId={currentBakeryId}\n        onSwitchBakery={handleSwitchBakery}\n        onPurchaseBakery={handlePurchaseBakery}\n        playerMoney={player.money}\n      />\n      <ClientOnly>\n        <CustomerManager\n          isOpen={showCustomerManager}\n          onClose={() => setShowCustomerManager(false)}\n        />\n      </ClientOnly>\n      <GameMenu\n        isOpen={showGameMenu}\n        onClose={() => setShowGameMenu(false)}\n        onSaveGame={handleSaveGame}\n        onLoadGame={handleLoadGame}\n        onSettings={() => setShowSettingsModal(true)}\n        onMainMenu={handleMainMenu}\n        onExit={typeof window !== 'undefined' && window.electronAPI ? handleExit : undefined}\n      />\n      <SaveLoadModal\n        isOpen={showSaveLoadModal}\n        onClose={() => setShowSaveLoadModal(false)}\n        mode={saveLoadMode}\n        onSaveSuccess={() => {\n          showSuccess('Game Saved!', 'Your progress has been saved successfully.')\n          setShowSaveLoadModal(false)\n        }}\n        onLoadSuccess={() => {\n          showSuccess('Game Loaded!', 'Your saved progress has been loaded.')\n          setShowSaveLoadModal(false)\n        }}\n      />\n\n      {/* Notification System */}\n      <NotificationSystem\n        notifications={notifications}\n        onRemove={removeNotification}\n      />\n    </div>\n  )\n}\n\nexport default function GamePage() {\n  return (\n    <GameProvider>\n      <GameContent />\n    </GameProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AA1BA;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BA,SAAS;QAoRqB;;IAnR5B,MAAM,EAAE,CAAC,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,cAAW,AAAD;IACxB,MAAM,EAAE,eAAe,EAAE,iBAAiB,EAAE,GAAG,CAAA,GAAA,wIAAA,CAAA,gBAAa,AAAD;IAC3D,MAAM,EACJ,MAAM,EACN,SAAS,EACT,SAAS,EACT,MAAM,EACN,YAAY,EACZ,MAAM,EACN,cAAc,EACd,WAAW,EACX,eAAe,EACf,WAAW,EACX,aAAa,EACb,YAAY,EACZ,gBAAgB,EAChB,YAAY,EACZ,iBAAiB,EACjB,cAAc,EACd,UAAU,EACX,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAEV,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAClE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqC;IAC9F,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuC;IAE9E,sBAAsB;IACtB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC/C,UAAU;QACV,cAAc;QACd,cAAc;QACd,sBAAsB;QACtB,iBAAiB;QACjB,iBAAiB;QACjB,gBAAgB;QAChB,eAAe;IACjB;IAEA,0BAA0B;IAC1B,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC;YACE,IAAI;YACJ,MAAM;YACN,UAAU;YACV,gBAAgB;YAChB,OAAO;YACP,WAAW,EAAE;YACb,WAAW,EAAE;YACb,QAAQ,EAAE;YACV,gBAAgB,EAAE;YAClB,eAAe,EAAE;YACjB,UAAU;YACV,cAAc;QAChB;KACD;IACD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,MAAM,EAAE,aAAa,EAAE,kBAAkB,EAAE,WAAW,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,mBAAgB,AAAD;IAE/F,MAAM,uBAAuB,CAAC,aAAqB;QACjD,qBAAqB;YAAE,IAAI;YAAa,MAAM;QAAc;QAC5D,mBAAmB;IACrB;IAEA,MAAM,oBAAoB,CAAC;QACzB,YAAY;QACZ,SAAS,kBAAkB;IAC7B;IAEA,MAAM,sBAAsB,CAAC;QAC3B,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACxC,IAAI,OAAO;YACT,cAAc;YACd;YACA,YAAY,oBAAoB,AAAC,eAA2B,OAAb,MAAM,MAAM,EAAC;QAC9D;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,aAAa;QACb,SAAS,kBAAkB;IAC7B;IAEA,MAAM,uBAAuB,CAAC;QAC5B,gBAAgB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,WAAW;YAAC,CAAC;IACtD;IAEA,MAAM,qBAAqB,CAAC;YAEiB;QAD3C,mBAAmB;QACnB,SAAS,mBAAmB,AAAC,eAA0D,QAA5C,iBAAA,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,uBAA5B,qCAAA,eAAuC,IAAI;IACxF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,IAAI,WAAW,OAAO,YAAY,GAAG;YACnC,MAAM,YAAY;gBAChB,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM,OAAO,IAAI;gBACjB,UAAU;gBACV,gBAAgB;gBAChB,OAAO;gBACP,WAAW,EAAE;gBACb,WAAW,EAAE;gBACb,QAAQ,EAAE;gBACV,gBAAgB,EAAE;gBAClB,eAAe,EAAE;gBACjB,UAAU;gBACV,cAAc,OAAO,YAAY;YACnC;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAU;YACxC,YAAY,qBAAqB,AAAC,eAA0B,OAAZ,OAAO,IAAI,EAAC;QAC9D;IACF;IAEA,qBAAqB;IACrB,MAAM,iBAAiB;QACrB,gBAAgB;QAChB,qBAAqB;IACvB;IAEA,MAAM,iBAAiB;QACrB,gBAAgB;QAChB,qBAAqB;IACvB;IAEA,MAAM,iBAAiB;QACrB,wCAAmC;YACjC,OAAO,QAAQ,CAAC,IAAI,GAAG;QACzB;IACF;IAEA,MAAM,aAAa;QACjB,IAAI,aAAkB,eAAe,OAAO,WAAW,EAAE;YACvD,OAAO,WAAW,CAAC,IAAI;QACzB;IACF;IAEA,qBAAqB;IACrB,MAAM,iBAAiB,CAAC;QACtB,IAAI,MAAM,GAAG,KAAK,UAAU;YAC1B,gBAAgB,CAAC;QACnB;IACF;IAEA,8BAA8B;IAC9B,wCAAmC;QACjC,OAAO,gBAAgB,CAAC,WAAW;IACrC;IAEA,oCAAoC;IACpC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR;;aAA0C,sBAAsB;YAEhE,MAAM,wBAAwB,aAAa,MAAM;+DAAC,CAAA,IAAK,EAAE,SAAS;;YAClE,MAAM,gBAAgB,SAAS,aAAa,OAAO,CAAC,4BAA4B;YAEhF,IAAI,sBAAsB,MAAM,GAAG,eAAe;gBAChD,MAAM,kBAAkB,sBAAsB,KAAK,CAAC;gBACpD,gBAAgB,OAAO;6CAAC,CAAA;wBACtB,YAAY,yBAAyB,AAAC,MAAsB,OAAjB,YAAY,IAAI;oBAC7D;;gBACA,aAAa,OAAO,CAAC,yBAAyB,sBAAsB,MAAM,CAAC,QAAQ;YACrF;QACF;gCAAG;QAAC;QAAc;KAAY;IAE9B,6CAA6C;IAC7C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;0DAAmB;oBACvB,IAAI,UAAU,OAAO,MAAM,GAAG,GAAG;4BAGb;wBAFlB,mBAAmB;wBACnB,MAAM,eAAe,MAAM,CAAC,EAAE;wBAC9B,MAAM,YAAY,EAAA,uBAAA,aAAa,KAAK,CAAC,EAAE,cAArB,2CAAA,qBAAuB,IAAI,KAAI;wBACjD,MAAM,kBAAkB,OAAO,KAAK,EAAE;oBACxC,OAAO;wBACL,4BAA4B;wBAC5B,MAAM,gBAAgB,OAAO,KAAK,EAAE,OAAO,KAAK,EAAE;oBACpD;gBACF;;YAEA;QACF;gCAAG;QAAC,OAAO,KAAK;QAAE,OAAO,KAAK;QAAE;QAAQ;QAAiB;KAAkB;IAE3E,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,0IAAA,CAAA,cAAW;gBACV,QAAQ;gBACR,YAAY,IAAM,gBAAgB;gBAClC,oBAAoB,IAAM,yBAAyB;gBACnD,cAAc,IAAM,sBAAsB;gBAC1C,gBAAgB,IAAM,0BAA0B;gBAChD,gBAAgB,IAAM,qBAAqB;;;;;;0BAI7C,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS,IAAM,YAAY;4BAC3B,WAAW,AAAC,sDAIX,OAHC,aAAa,gBACT,6BACA;;gCAEP;gCACK,EAAE,yBAAyB;;;;;;;sCAEjC,6LAAC;4BACC,SAAS,IAAM,YAAY;4BAC3B,WAAW,AAAC,sDAIX,OAHC,aAAa,WACT,6BACA;;gCAEP;gCACK,EAAE,oBAAoB;;;;;;;sCAE5B,6LAAC;4BACC,SAAS,IAAM,YAAY;4BAC3B,WAAW,AAAC,sDAIX,OAHC,aAAa,WACT,6BACA;;gCAEP;gCACM,EAAE,oBAAoB;;;;;;;sCAE7B,6LAAC;4BACC,SAAS,IAAM,uBAAuB;4BACtC,WAAU;;gCACX;gCACK,EAAE,uBAAuB;;;;;;;;;;;;;;;;;;0BAKnC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;4BACZ,aAAa,0BACZ,6LAAC,yIAAA,CAAA,aAAU;gCAAC,wBAAU,6LAAC;oCAAI,WAAU;8CAAgD;;;;;;0CACnF,cAAA,6LAAC,6IAAA,CAAA,eAAY;oCACX,WAAW;oCACX,kBAAkB;;;;;;;;;;;4BAKvB,aAAa,0BACZ,6LAAC,yIAAA,CAAA,aAAU;gCAAC,wBAAU,6LAAC;oCAAI,WAAU;8CAAgD;;;;;;0CACnF,cAAA,6LAAC,2IAAA,CAAA,aAAU;oCACT,iBAAiB,CAAC;wCAChB,uBAAuB;oCACzB;;;;;;;;;;;4BAKL,aAAa,+BACZ;;kDACE,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAG,WAAU;kEAAyC,EAAE;;;;;;kEACzD,6LAAC;wDAAI,WAAU;;4DAAwB;6DAC3B,iBAAA,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,8BAA5B,qCAAA,eAA8C,IAAI;;;;;;;;;;;;;0DAKhE,6LAAC;gDAAI,WAAU;0DACZ,UAAU,GAAG,CAAC,CAAC,mBACd,6LAAC,0IAAA,CAAA,YAAS;wDAER,WAAW;wDACX,SAAS;uDAFJ,GAAG,EAAE;;;;;;;;;;;;;;;;kDASlB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA8C,EAAE;;;;;;0DAC9D,6LAAC;gDAAI,WAAU;0DACZ,UAAU,GAAG,CAAC,CAAC,2BACd,6LAAC;wDAA0B,WAAU;;0EACnC,6LAAC;gEAAI,WAAU;0EAAiB,WAAW,IAAI;;;;;;0EAC/C,6LAAC;gEAAI,WAAU;0EAA6B,WAAW,IAAI;;;;;;0EAC3D,6LAAC;gEAAI,WAAU;0EAAyB,EAAE,sBAAsB;oEAAE,KAAK,WAAW,QAAQ,CAAC,QAAQ;gEAAG;;;;;;0EACtG,6LAAC;gEAAI,WAAU;0EAA0B,EAAE,kBAAkB;oEAAE,MAAM,WAAW,IAAI,CAAC,QAAQ;gEAAG;;;;;;;uDAJxF,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;;;kCAcrC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyC,EAAE;;;;;;0DACzD,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS;0DAER,EAAE;;;;;;;;;;;;kDAGP,6LAAC;wCAAI,WAAU;kDACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6LAAC,sIAAA,CAAA,QAAK;gDAEJ,OAAO;gDACP,UAAU;gDACV,WAAW;gDACX,YAAY;+CAJP,MAAM,EAAE;;;;;;;;;;;;;;;;0CAWrB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA8C,EAAE;;;;;;kDAC9D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,iBAAiB;0DAE/B,EAAE;;;;;;0DAEL,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,mBAAmB;0DAEjC,EAAE;;;;;;0DAEL,6LAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;gDACV,SAAS,IAAM,0BAA0B;0DAExC,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQb,6LAAC,4IAAA,CAAA,cAAW;gBACV,QAAQ;gBACR,SAAS,IAAM,mBAAmB;;;;;;0BAEpC,6LAAC,0IAAA,CAAA,YAAS;gBACR,QAAQ;gBACR,SAAS,IAAM,iBAAiB;;;;;;0BAElC,6LAAC,4IAAA,CAAA,cAAW;gBACV,QAAQ;gBACR,SAAS,IAAM,mBAAmB;gBAClC,aAAa,CAAA,8BAAA,wCAAA,kBAAmB,EAAE,KAAI;gBACtC,eAAe,CAAA,8BAAA,wCAAA,kBAAmB,IAAI,KAAI;;;;;;0BAE5C,6LAAC,kJAAA,CAAA,oBAAiB;gBAChB,QAAQ;gBACR,SAAS,IAAM,yBAAyB;gBACxC,cAAc;;;;;;0BAEhB,6LAAC,+IAAA,CAAA,iBAAc;gBACb,QAAQ;gBACR,SAAS,IAAM,sBAAsB;gBACrC,QAAQ;gBACR,aAAa,OAAO,WAAW;gBAC/B,aAAa,OAAO,KAAK;gBACzB,gBAAgB;;;;;;0BAElB,6LAAC,6IAAA,CAAA,eAAY;gBACX,QAAQ;gBACR,SAAS;gBACT,UAAU,OAAO,KAAK;gBACtB,SAAS;;;;;;0BAEX,6LAAC,gJAAA,CAAA,kBAAe;gBACd,QAAQ;gBACR,SAAS,IAAM,uBAAuB;;;;;;0BAExC,6LAAC,mJAAA,CAAA,qBAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,0BAA0B;gBACzC,eAAe;;;;;;0BAEjB,6LAAC,8IAAA,CAAA,gBAAa;gBACZ,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,UAAU;gBACV,kBAAkB;;;;;;0BAEpB,6LAAC,mJAAA,CAAA,qBAAkB;gBACjB,QAAQ;gBACR,SAAS,IAAM,0BAA0B;gBACzC,UAAU;gBACV,iBAAiB;gBACjB,gBAAgB;gBAChB,kBAAkB;gBAClB,aAAa,OAAO,KAAK;;;;;;0BAE3B,6LAAC,yIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,gJAAA,CAAA,kBAAe;oBACd,QAAQ;oBACR,SAAS,IAAM,uBAAuB;;;;;;;;;;;0BAG1C,6LAAC,yIAAA,CAAA,WAAQ;gBACP,QAAQ;gBACR,SAAS,IAAM,gBAAgB;gBAC/B,YAAY;gBACZ,YAAY;gBACZ,YAAY,IAAM,qBAAqB;gBACvC,YAAY;gBACZ,QAAQ,aAAkB,eAAe,OAAO,WAAW,GAAG,aAAa;;;;;;0BAE7E,6LAAC,8IAAA,CAAA,gBAAa;gBACZ,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,MAAM;gBACN,eAAe;oBACb,YAAY,eAAe;oBAC3B,qBAAqB;gBACvB;gBACA,eAAe;oBACb,YAAY,gBAAgB;oBAC5B,qBAAqB;gBACvB;;;;;;0BAIF,6LAAC,mJAAA,CAAA,qBAAkB;gBACjB,eAAe;gBACf,UAAU;;;;;;;;;;;;AAIlB;GA/cS;;QACO,sIAAA,CAAA,cAAW;QACsB,wIAAA,CAAA,gBAAa;QAmBxD,kIAAA,CAAA,UAAO;QAiDqE,mJAAA,CAAA,mBAAgB;;;KAtEzF;AAidM,SAAS;IACtB,qBACE,6LAAC,kIAAA,CAAA,eAAY;kBACX,cAAA,6LAAC;;;;;;;;;;AAGP;MANwB", "debugId": null}}]}