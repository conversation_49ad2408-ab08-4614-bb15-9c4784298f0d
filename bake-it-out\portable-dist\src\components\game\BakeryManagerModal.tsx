'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { useLanguage } from '@/contexts/LanguageContext'
import { BakeryLocation } from '@/lib/saveSystem'

interface BakeryManagerModalProps {
  isOpen: boolean
  onClose: () => void
  bakeries: BakeryLocation[]
  currentBakeryId: string
  onSwitchBakery: (bakeryId: string) => void
  onPurchaseBakery: (bakery: BakeryLocation) => void
  playerMoney: number
}

const AVAILABLE_BAKERIES: Omit<BakeryLocation, 'id' | 'unlocked'>[] = [
  {
    name: 'Downtown Delights',
    location: 'City Center',
    specialization: 'general',
    level: 1,
    equipment: [],
    inventory: [],
    orders: [],
    automationJobs: [],
    conveyorBelts: [],
    purchaseCost: 0 // Main bakery is free
  },
  {
    name: 'Cookie Corner',
    location: 'Shopping Mall',
    specialization: 'cookies',
    level: 1,
    equipment: [],
    inventory: [],
    orders: [],
    automationJobs: [],
    conveyorBelts: [],
    purchaseCost: 2500
  },
  {
    name: 'Cake Castle',
    location: 'Wedding District',
    specialization: 'cakes',
    level: 1,
    equipment: [],
    inventory: [],
    orders: [],
    automationJobs: [],
    conveyorBelts: [],
    purchaseCost: 3500
  },
  {
    name: 'Bread Basket',
    location: 'Farmers Market',
    specialization: 'bread',
    level: 1,
    equipment: [],
    inventory: [],
    orders: [],
    automationJobs: [],
    conveyorBelts: [],
    purchaseCost: 3000
  },
  {
    name: 'Pastry Palace',
    location: 'French Quarter',
    specialization: 'pastries',
    level: 1,
    equipment: [],
    inventory: [],
    orders: [],
    automationJobs: [],
    conveyorBelts: [],
    purchaseCost: 4000
  }
]

export function BakeryManagerModal({
  isOpen,
  onClose,
  bakeries,
  currentBakeryId,
  onSwitchBakery,
  onPurchaseBakery,
  playerMoney
}: BakeryManagerModalProps) {
  const { t } = useLanguage()
  const [selectedTab, setSelectedTab] = useState<'owned' | 'available'>('owned')

  if (!isOpen) return null

  const ownedBakeries = bakeries.filter(b => b.unlocked)
  const availableBakeries = AVAILABLE_BAKERIES.filter(ab => 
    !bakeries.some(b => b.name === ab.name && b.unlocked)
  )

  const getSpecializationIcon = (specialization: string) => {
    switch (specialization) {
      case 'cookies': return '🍪'
      case 'cakes': return '🧁'
      case 'bread': return '🍞'
      case 'pastries': return '🥐'
      default: return '🏪'
    }
  }

  const getSpecializationBonus = (specialization: string) => {
    switch (specialization) {
      case 'cookies': return '+20% Cookie Production Speed'
      case 'cakes': return '+25% Cake Profit Margin'
      case 'bread': return '+15% Bread Ingredient Efficiency'
      case 'pastries': return '+30% Pastry Experience Gain'
      default: return 'Balanced Production'
    }
  }

  const handlePurchaseBakery = (bakery: Omit<BakeryLocation, 'id' | 'unlocked'>) => {
    if (playerMoney >= bakery.purchaseCost) {
      const newBakery: BakeryLocation = {
        ...bakery,
        id: `bakery_${Date.now()}`,
        unlocked: true
      }
      onPurchaseBakery(newBakery)
    }
  }

  const tabs = [
    { id: 'owned', name: t('bakeries.owned') || 'My Bakeries', icon: '🏪' },
    { id: 'available', name: t('bakeries.available') || 'Available', icon: '🛒' }
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold text-orange-800">
                {t('bakeries.title') || '🏪 Bakery Manager'}
              </h2>
              <p className="text-gray-600">
                {t('bakeries.subtitle') || 'Manage your bakery empire'}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="bg-green-100 px-3 py-1 rounded-full">
                <span className="text-green-800 font-medium">${playerMoney}</span>
              </div>
              <Button variant="secondary" onClick={onClose}>
                {t('game.close') || '✕ Close'}
              </Button>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <div className="flex space-x-0">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as 'overview' | 'locations' | 'expansion')}
                className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
                  selectedTab === tab.id
                    ? 'border-orange-500 text-orange-600 bg-orange-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                {tab.icon} {tab.name}
              </button>
            ))}
          </div>
        </div>

        <div className="p-6 max-h-[60vh] overflow-y-auto">
          {/* Owned Bakeries */}
          {selectedTab === 'owned' && (
            <div className="space-y-4">
              {ownedBakeries.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {ownedBakeries.map(bakery => (
                    <div
                      key={bakery.id}
                      className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                        bakery.id === currentBakeryId
                          ? 'border-orange-400 bg-orange-50'
                          : 'border-gray-300 bg-white hover:border-orange-300'
                      }`}
                      onClick={() => onSwitchBakery(bakery.id)}
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <span className="text-2xl">{getSpecializationIcon(bakery.specialization)}</span>
                          <div>
                            <h3 className="font-semibold text-gray-800">{bakery.name}</h3>
                            <p className="text-sm text-gray-600">{bakery.location}</p>
                          </div>
                        </div>
                        {bakery.id === currentBakeryId && (
                          <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                            {t('bakeries.current') || 'Current'}
                          </span>
                        )}
                      </div>

                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">{t('bakeries.level') || 'Level'}:</span>
                          <span className="font-medium">{bakery.level}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">{t('bakeries.specialization') || 'Specialization'}:</span>
                          <span className="font-medium capitalize">{bakery.specialization}</span>
                        </div>
                        <div className="text-xs text-blue-600">
                          {getSpecializationBonus(bakery.specialization)}
                        </div>
                      </div>

                      <div className="mt-3 pt-3 border-t border-gray-200">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">{t('bakeries.equipment') || 'Equipment'}:</span>
                          <span>{bakery.equipment.length}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">{t('bakeries.orders') || 'Active Orders'}:</span>
                          <span>{bakery.orders.length}</span>
                        </div>
                      </div>

                      {bakery.id !== currentBakeryId && (
                        <Button
                          variant="primary"
                          size="sm"
                          className="w-full mt-3"
                          onClick={(e) => {
                            e.stopPropagation()
                            onSwitchBakery(bakery.id)
                          }}
                        >
                          {t('bakeries.switchTo') || 'Switch To'}
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <div className="text-4xl mb-2">🏪</div>
                  <p>{t('bakeries.noOwned') || 'You don\'t own any bakeries yet.'}</p>
                </div>
              )}
            </div>
          )}

          {/* Available Bakeries */}
          {selectedTab === 'available' && (
            <div className="space-y-4">
              {availableBakeries.length > 0 ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {availableBakeries.map((bakery, index) => (
                    <div
                      key={index}
                      className="p-4 rounded-lg border-2 border-gray-300 bg-white"
                    >
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <span className="text-2xl">{getSpecializationIcon(bakery.specialization)}</span>
                          <div>
                            <h3 className="font-semibold text-gray-800">{bakery.name}</h3>
                            <p className="text-sm text-gray-600">{bakery.location}</p>
                          </div>
                        </div>
                        <span className="text-lg font-bold text-green-600">${bakery.purchaseCost}</span>
                      </div>

                      <div className="space-y-2 text-sm mb-4">
                        <div className="flex justify-between">
                          <span className="text-gray-600">{t('bakeries.specialization') || 'Specialization'}:</span>
                          <span className="font-medium capitalize">{bakery.specialization}</span>
                        </div>
                        <div className="text-xs text-blue-600">
                          {getSpecializationBonus(bakery.specialization)}
                        </div>
                      </div>

                      <Button
                        variant={playerMoney >= bakery.purchaseCost ? 'success' : 'secondary'}
                        size="sm"
                        className="w-full"
                        disabled={playerMoney < bakery.purchaseCost}
                        onClick={() => handlePurchaseBakery(bakery)}
                      >
                        {playerMoney >= bakery.purchaseCost 
                          ? (t('bakeries.purchase') || '💰 Purchase')
                          : (t('bakeries.tooExpensive') || '💸 Too Expensive')
                        }
                      </Button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <div className="text-4xl mb-2">🎉</div>
                  <p>{t('bakeries.allOwned') || 'You own all available bakeries!'}</p>
                </div>
              )}
            </div>
          )}
        </div>

        <div className="p-4 bg-blue-50 border-t border-gray-200">
          <h3 className="font-medium text-blue-800 mb-2">
            {t('bakeries.tips') || '💡 Bakery Tips'}
          </h3>
          <ul className="text-sm text-blue-700 space-y-1">
            <li>• {t('bakeries.tip1') || 'Each bakery specializes in different products for bonus efficiency'}</li>
            <li>• {t('bakeries.tip2') || 'Switch between bakeries to manage multiple locations'}</li>
            <li>• {t('bakeries.tip3') || 'Specialized bakeries attract customers looking for specific items'}</li>
            <li>• {t('bakeries.tip4') || 'Upgrade each bakery independently for maximum profit'}</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
