{"name": "discord-rpc", "version": "4.0.1", "description": "A simple RPC client for Discord", "keywords": ["discord", "rpc", "rich presence", "remote procedural call"], "main": "src/index.js", "jsdelivr": "browser.js", "unpkg": "browser.js", "author": "snek <<EMAIL>>", "license": "MIT", "homepage": "https://github.com/discordjs/RPC#readme", "bugs": {"url": "https://github.com/discordjs/RPC/issues"}, "repository": {"type": "git", "url": "git+https://github.com/discordjs/RPC.git"}, "scripts": {"lint": "eslint src test --ext=js", "docs": "mkdir -p docs-out && docgen --source src --output docs-out/master.json --jsdoc jsdoc.json --custom docgen.json", "example": "electron example/main.js", "build:browser": "webpack-cli", "prepublishOnly": "npm run lint && npm run build:browser"}, "dependencies": {"node-fetch": "^2.6.1", "ws": "^7.3.1"}, "optionalDependencies": {"register-scheme": "github:devsnek/node-register-scheme"}, "devDependencies": {"babel-eslint": "^10.0.3", "discord.js-docgen": "github:discordjs/docgen", "electron": "^7.1.9", "eslint": "^6.1.0", "eslint-config-airbnb-base": "14.0.0", "eslint-plugin-import": "^2.18.2", "jsdoc-strip-async-await": "^0.1.0", "webpack": "^4.40.0", "webpack-cli": "^3.3.8"}, "browser": {"net": false, "ws": false, "uws": false, "erlpack": false, "electron": false, "register-scheme": false, "./src/transports/IPC.js": false}}