{"name": "@types/discord-rpc", "version": "4.0.9", "description": "TypeScript definitions for discord-rpc", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/discord-rpc", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "dyl<PERSON>", "url": "https://github.com/dylhack"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "k3rn31p4nic", "url": "https://github.com/k3rn31p4nic"}, {"name": "HanchaiN", "githubUsername": "HanchaiN", "url": "https://github.com/HanchaiN"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/discord-rpc"}, "scripts": {}, "dependencies": {"@types/events": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "fe0bbbe18006cb54ed660b85683a3661de9589bbbb1d873071f5d3d757f6c55d", "typeScriptVersion": "5.0"}