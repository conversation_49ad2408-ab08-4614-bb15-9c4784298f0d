import { initializeApp } from 'firebase/app'
import { getAuth, connectAuthEmulator } from 'firebase/auth'
import { getFirestore, connectFirestoreEmulator } from 'firebase/firestore'
import { getStorage, connectStorageEmulator } from 'firebase/storage'

// Firebase configuration
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID
}

// Initialize Firebase
const app = initializeApp(firebaseConfig)

// Initialize Firebase services
export const auth = getAuth(app)
export const db = getFirestore(app)
export const storage = getStorage(app)

// Connect to emulators in development (optional)
if (process.env.NODE_ENV === 'development' && process.env.NEXT_PUBLIC_USE_FIREBASE_EMULATOR === 'true') {
  try {
    connectAuthEmulator(auth, 'http://localhost:9099')
    connectFirestoreEmulator(db, 'localhost', 8080)
    connectStorageEmulator(storage, 'localhost', 9199)
  } catch (error) {
    console.log('Firebase emulators already connected or not available')
  }
}

export default app

// Firestore collection names
export const COLLECTIONS = {
  USERS: 'users',
  GAME_SAVES: 'game_saves',
  USER_STATS: 'user_stats',
  SYNC_LOGS: 'sync_logs'
} as const

// Firebase error codes mapping
export const FIREBASE_ERRORS = {
  'auth/user-not-found': 'User not found',
  'auth/wrong-password': 'Incorrect password',
  'auth/email-already-in-use': 'Email already in use',
  'auth/weak-password': 'Password should be at least 6 characters',
  'auth/invalid-email': 'Invalid email address',
  'auth/too-many-requests': 'Too many failed attempts. Please try again later.',
  'permission-denied': 'Permission denied. Please check your authentication.',
  'unavailable': 'Service temporarily unavailable. Please try again.',
  'not-found': 'Document not found',
  'already-exists': 'Document already exists'
} as const

// Helper function to get user-friendly error message
export function getFirebaseErrorMessage(errorCode: string): string {
  return FIREBASE_ERRORS[errorCode as keyof typeof FIREBASE_ERRORS] || 'An unexpected error occurred'
}
