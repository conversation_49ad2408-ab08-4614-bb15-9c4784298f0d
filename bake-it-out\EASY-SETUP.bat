@echo off
title Bake It Out - One-Click Setup
color 0A

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                    🧁 BAKE IT OUT 🧁                         ║
echo  ║                  One-Click Setup                             ║
echo  ║                                                              ║
echo  ║  This will set up EVERYTHING in under 60 seconds!           ║
echo  ║  • Game Client                                               ║
echo  ║  • Unified Server                                            ║
echo  ║  • Database                                                  ║
echo  ║  • Dashboard                                                 ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

REM Check Node.js
echo [1/5] 🔍 Checking Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found!
    echo.
    echo 📥 Please install Node.js first:
    echo    1. Go to https://nodejs.org/
    echo    2. Download and install Node.js 18+
    echo    3. Restart this script
    echo.
    pause
    exit /b 1
)

for /f "tokens=*" %%i in ('node --version') do set NODE_VERSION=%%i
echo ✅ Node.js %NODE_VERSION% found!

echo.
echo [2/5] 🎮 Setting up game client...
npm install --silent
if %errorlevel% neq 0 (
    echo ❌ Failed to install game dependencies
    pause
    exit /b 1
)

npm run build --silent
if %errorlevel% neq 0 (
    echo ❌ Failed to build game client
    pause
    exit /b 1
)
echo ✅ Game client ready!

echo.
echo [3/5] 🖥️ Setting up server...
cd server

REM Run the easy server setup
call setup-easy.bat
if %errorlevel% neq 0 (
    echo ❌ Server setup failed
    cd ..
    pause
    exit /b 1
)

cd ..
echo ✅ Server ready!

echo.
echo [4/5] 📦 Creating portable distribution...
if exist "portable-dist" (
    echo Updating portable distribution...
    xcopy /E /I /Y server portable-dist\server >nul
    xcopy /E /I /Y out portable-dist\out >nul
) else (
    echo Creating portable distribution...
    mkdir portable-dist
    xcopy /E /I /Y server portable-dist\server >nul
    xcopy /E /I /Y out portable-dist\out >nul
    
    REM Copy start scripts
    echo @echo off > portable-dist\start-server.bat
    echo echo Starting Bake It Out Server... >> portable-dist\start-server.bat
    echo cd server >> portable-dist\start-server.bat
    echo npm start >> portable-dist\start-server.bat
    
    echo @echo off > portable-dist\start-game.bat
    echo echo Starting Bake It Out Game Client... >> portable-dist\start-game.bat
    echo echo Open your browser to: http://localhost:3002 >> portable-dist\start-game.bat
    echo npx serve out -p 3002 >> portable-dist\start-game.bat
)
echo ✅ Portable distribution ready!

echo.
echo [5/5] 🧪 Testing everything...

REM Test server startup
cd server
echo Testing server...
start /B node src/index.js >test.log 2>&1
timeout /t 5 >nul

REM Check if server is responding
curl -s http://localhost:3001/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Server test passed!
) else (
    echo ⚠️ Server test inconclusive (might still be starting)
)

REM Stop test server
taskkill /F /IM node.exe >nul 2>&1
cd ..

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                    🎉 SETUP COMPLETE! 🎉                    ║
echo  ║                                                              ║
echo  ║  Everything is ready! Here's what you can do:               ║
echo  ║                                                              ║
echo  ║  🚀 START SERVER:                                           ║
echo  ║     cd server ^&^& npm start                                    ║
echo  ║     Dashboard: http://localhost:3001/dashboard              ║
echo  ║                                                              ║
echo  ║  🎮 START GAME:                                             ║
echo  ║     npx serve out -p 3002                                   ║
echo  ║     Game: http://localhost:3002                             ║
echo  ║                                                              ║
echo  ║  📦 PORTABLE VERSION:                                       ║
echo  ║     Use files in portable-dist/ folder                     ║
echo  ║                                                              ║
echo  ║  🎯 WHAT'S INCLUDED:                                        ║
echo  ║     ✅ Cloud saves                                          ║
echo  ║     ✅ Multiplayer gaming                                   ║
echo  ║     ✅ User accounts                                        ║
echo  ║     ✅ Web dashboard                                        ║
echo  ║     ✅ Analytics                                            ║
echo  ║     ✅ Real-time monitoring                                 ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

echo 💡 Quick Start:
echo    1. Open a terminal and run: cd server ^&^& npm start
echo    2. Open another terminal and run: npx serve out -p 3002
echo    3. Play the game at: http://localhost:3002
echo    4. Manage server at: http://localhost:3001/dashboard
echo.

echo 🎮 Ready to start your gaming empire? 
echo.
pause
