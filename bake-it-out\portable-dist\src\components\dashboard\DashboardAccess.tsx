'use client'

import { useState } from 'react'
import { useLanguage } from '@/contexts/LanguageContext'
import { useCloudSave } from '@/contexts/CloudSaveContext'
import { Button } from '@/components/ui/Button'

interface DashboardAccessProps {
  isOpen: boolean
  onClose: () => void
}

export function DashboardAccess({ isOpen, onClose }: DashboardAccessProps) {
  const { t } = useLanguage()
  const { isAuthenticated, user } = useCloudSave()
  const [isLoading, setIsLoading] = useState(false)

  if (!isOpen) return null

  const handleOpenDashboard = async () => {
    if (!isAuthenticated) {
      alert(t('dashboard.login_required', 'Please login to access the dashboard'))
      return
    }

    setIsLoading(true)
    try {
      const token = localStorage.getItem('accessToken')
      const dashboardUrl = `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/dashboard?token=${token}`
      
      // Open dashboard in new tab
      window.open(dashboardUrl, '_blank')
    } catch (error) {
      console.error('Failed to open dashboard:', error)
      alert(t('dashboard.open_failed', 'Failed to open dashboard'))
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-800">
            {t('dashboard.title', 'Server Dashboard')}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>

        <div className="text-center">
          <div className="mb-6">
            <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
              <i className="fas fa-chart-bar text-white text-2xl"></i>
            </div>
            <h3 className="text-lg font-semibold text-gray-800 mb-2">
              {t('dashboard.access_title', 'Access Server Dashboard')}
            </h3>
            <p className="text-gray-600 text-sm">
              {t('dashboard.access_description', 'Monitor server statistics, manage users, and view game analytics')}
            </p>
          </div>

          {!isAuthenticated ? (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <i className="fas fa-exclamation-triangle text-yellow-600 mr-2"></i>
                <p className="text-yellow-800 text-sm">
                  {t('dashboard.login_required', 'Please login to access the dashboard')}
                </p>
              </div>
            </div>
          ) : (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
              <div className="flex items-center">
                <i className="fas fa-check-circle text-green-600 mr-2"></i>
                <p className="text-green-800 text-sm">
                  {t('dashboard.logged_in_as', 'Logged in as')}: <strong>{user?.username}</strong>
                </p>
              </div>
            </div>
          )}

          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
              <div className="flex items-center">
                <i className="fas fa-users text-blue-500 mr-2"></i>
                <span>{t('dashboard.feature.users', 'User Management')}</span>
              </div>
              <div className="flex items-center">
                <i className="fas fa-gamepad text-green-500 mr-2"></i>
                <span>{t('dashboard.feature.rooms', 'Game Rooms')}</span>
              </div>
              <div className="flex items-center">
                <i className="fas fa-cloud text-purple-500 mr-2"></i>
                <span>{t('dashboard.feature.saves', 'Cloud Saves')}</span>
              </div>
              <div className="flex items-center">
                <i className="fas fa-chart-line text-orange-500 mr-2"></i>
                <span>{t('dashboard.feature.analytics', 'Analytics')}</span>
              </div>
            </div>
          </div>

          <div className="flex space-x-3 mt-6">
            <Button
              variant="primary"
              onClick={handleOpenDashboard}
              disabled={!isAuthenticated || isLoading}
              className="flex-1"
            >
              {isLoading ? (
                <>
                  <i className="fas fa-spinner fa-spin mr-2"></i>
                  {t('dashboard.opening', 'Opening...')}
                </>
              ) : (
                <>
                  <i className="fas fa-external-link-alt mr-2"></i>
                  {t('dashboard.open', 'Open Dashboard')}
                </>
              )}
            </Button>
            <Button
              variant="secondary"
              onClick={onClose}
              disabled={isLoading}
            >
              {t('dashboard.cancel', 'Cancel')}
            </Button>
          </div>

          <div className="mt-4 text-xs text-gray-500">
            <p>{t('dashboard.new_tab_notice', 'Dashboard will open in a new tab')}</p>
          </div>
        </div>
      </div>
    </div>
  )
}

// Dashboard access button component
export function DashboardButton() {
  const { t } = useLanguage()
  const { isAuthenticated } = useCloudSave()
  const [showDashboard, setShowDashboard] = useState(false)

  return (
    <>
      <button
        onClick={() => setShowDashboard(true)}
        className={`flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ${
          isAuthenticated 
            ? 'bg-blue-100 text-blue-700 hover:bg-blue-200' 
            : 'bg-gray-100 text-gray-500 hover:bg-gray-200'
        }`}
        title={t('dashboard.tooltip', 'Access server dashboard')}
      >
        <i className="fas fa-chart-bar text-sm"></i>
        <span className="text-sm font-medium">
          {t('dashboard.button', 'Dashboard')}
        </span>
        {isAuthenticated && (
          <div className="w-2 h-2 bg-green-500 rounded-full"></div>
        )}
      </button>

      <DashboardAccess
        isOpen={showDashboard}
        onClose={() => setShowDashboard(false)}
      />
    </>
  )
}
