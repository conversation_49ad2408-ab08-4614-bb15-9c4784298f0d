'use client'

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react'
import { useLanguage } from './LanguageContext'
import { auth, db, COLLECTIONS, getFirebaseErrorMessage } from '@/lib/firebase'
import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  updateProfile,
  User as FirebaseUser
} from 'firebase/auth'
import {
  doc,
  setDoc,
  getDoc,
  getDocs,
  deleteDoc,
  collection,
  query,
  where,
  orderBy,
  serverTimestamp,
  Timestamp
} from 'firebase/firestore'

interface CloudSave {
  id: string
  saveName: string
  description: string
  saveType: 'manual' | 'auto' | 'checkpoint'
  metadata: {
    version: number
    gameVersion: string
    platform: string
    level: number
    money: number
    playTime: number
  }
  createdAt: string
  updatedAt: string
  size: number
}

interface User {
  id: string
  username: string
  email: string
  profile: {
    displayName: string
    avatar: string
    preferredLanguage: string
  }
  gameStats: {
    totalPlayTime: number
    highestLevel: number
    totalMoney: number
    achievementsUnlocked: number
    gamesPlayed: number
  }
}

interface CloudSaveContextType {
  // Authentication
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  
  // Auth methods
  login: (username: string, password: string) => Promise<boolean>
  register: (username: string, email: string, password: string) => Promise<boolean>
  logout: () => Promise<void>
  
  // Cloud saves
  cloudSaves: CloudSave[]
  isLoadingSaves: boolean
  
  // Save methods
  saveToCloud: (saveName: string, gameData: any, saveType?: string) => Promise<string | null>
  loadFromCloud: (saveId: string) => Promise<any | null>
  deleteCloudSave: (saveId: string) => Promise<boolean>
  refreshCloudSaves: () => Promise<void>
  
  // Sync
  autoSyncEnabled: boolean
  setAutoSyncEnabled: (enabled: boolean) => void
  lastSyncTime: Date | null
  syncStatus: 'idle' | 'syncing' | 'error' | 'success'
  
  // Error handling
  error: string | null
  clearError: () => void
}

const CloudSaveContext = createContext<CloudSaveContextType | undefined>(undefined)

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api'

export function CloudSaveProvider({ children }: { children: React.ReactNode }) {
  const { t } = useLanguage()
  
  // State
  const [user, setUser] = useState<User | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [cloudSaves, setCloudSaves] = useState<CloudSave[]>([])
  const [isLoadingSaves, setIsLoadingSaves] = useState(false)
  const [autoSyncEnabled, setAutoSyncEnabled] = useState(true)
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null)
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'error' | 'success'>('idle')
  const [error, setError] = useState<string | null>(null)

  // Helper function to get auth headers
  const getAuthHeaders = () => {
    const token = localStorage.getItem('accessToken')
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    }
  }

  // API call wrapper with error handling
  const apiCall = async (endpoint: string, options: RequestInit = {}) => {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        ...options,
        headers: {
          ...getAuthHeaders(),
          ...options.headers
        }
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'API request failed')
      }

      return data
    } catch (error) {
      console.error('API call failed:', error)
      throw error
    }
  }

  // Authentication methods
  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true)
      setError(null)

      // Try to sign in with email/password
      const email = username.includes('@') ? username : `${username}@bakeitout.local`
      const userCredential = await signInWithEmailAndPassword(auth, email, password)

      if (userCredential.user) {
        // User will be set by onAuthStateChanged listener
        return true
      }

      return false
    } catch (error: any) {
      setError(getFirebaseErrorMessage(error.code) || error.message)
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (username: string, email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true)
      setError(null)

      // Create user with Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password)

      if (userCredential.user) {
        // Update the user's display name
        await updateProfile(userCredential.user, {
          displayName: username
        })

        // Create user profile document
        await setDoc(doc(db, COLLECTIONS.USERS, userCredential.user.uid), {
          username: username,
          displayName: username,
          email: email,
          preferredLanguage: 'en',
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        })

        // Create user stats document
        await setDoc(doc(db, COLLECTIONS.USER_STATS, userCredential.user.uid), {
          totalPlayTime: 0,
          highestLevel: 1,
          totalMoney: 0,
          achievementsUnlocked: 0,
          gamesPlayed: 0,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        })

        return true
      }

      return false
    } catch (error: any) {
      setError(getFirebaseErrorMessage(error.code) || error.message)
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async (): Promise<void> => {
    try {
      await signOut(auth)
    } catch (error) {
      // Continue with logout even if API call fails
      console.error('Logout failed:', error)
    } finally {
      // Clear local state (also handled by onAuthStateChanged)
      setUser(null)
      setIsAuthenticated(false)
      setCloudSaves([])
      setSyncStatus('idle')
      setError(null)
    }
  }

  // Cloud save methods
  const saveToCloud = async (saveName: string, gameData: any, saveType: string = 'manual'): Promise<string | null> => {
    try {
      setSyncStatus('syncing')
      setError(null)

      if (!user) {
        throw new Error('User not authenticated')
      }

      // Generate unique save ID
      const saveId = `${user.id}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

      const saveData = {
        userId: user.id,
        saveName: saveName,
        description: `${saveType} save - Level ${gameData.player?.level || 1}`,
        saveType: saveType,
        gameData: gameData,
        metadata: {
          gameVersion: '1.1.0',
          platform: 'web',
          deviceInfo: navigator.userAgent,
          level: gameData.player?.level || 1,
          money: gameData.player?.money || 0,
          playTime: gameData.player?.playTime || 0
        },
        gameVersion: '1.1.0',
        platform: 'web',
        deviceInfo: navigator.userAgent,
        size: JSON.stringify(gameData).length,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp()
      }

      // Save to Firestore
      await setDoc(doc(db, COLLECTIONS.GAME_SAVES, saveId), saveData)

      // Log the sync operation
      await setDoc(doc(db, COLLECTIONS.SYNC_LOGS, `${user.id}_${Date.now()}`), {
        userId: user.id,
        saveId: saveId,
        action: 'upload',
        status: 'success',
        deviceInfo: navigator.userAgent,
        createdAt: serverTimestamp()
      })

      // Refresh saves list
      await refreshCloudSaves()

      setSyncStatus('success')
      setLastSyncTime(new Date())

      return saveId
    } catch (error: any) {
      setError(getFirebaseErrorMessage(error.code) || error.message)
      setSyncStatus('error')

      // Log the error
      if (user) {
        try {
          await setDoc(doc(db, COLLECTIONS.SYNC_LOGS, `${user.id}_${Date.now()}_error`), {
            userId: user.id,
            action: 'upload',
            status: 'error',
            errorMessage: error.message,
            deviceInfo: navigator.userAgent,
            createdAt: serverTimestamp()
          })
        } catch (logError) {
          console.error('Failed to log error:', logError)
        }
      }

      return null
    }
  }

  const loadFromCloud = async (saveId: string): Promise<any | null> => {
    try {
      setSyncStatus('syncing')
      setError(null)

      if (!user) {
        throw new Error('User not authenticated')
      }

      // Get save document from Firestore
      const saveDoc = await getDoc(doc(db, COLLECTIONS.GAME_SAVES, saveId))

      if (!saveDoc.exists()) {
        throw new Error('Save not found')
      }

      const saveData = saveDoc.data()

      // Verify ownership
      if (saveData.userId !== user.id) {
        throw new Error('Access denied')
      }

      // Log the sync operation
      await setDoc(doc(db, COLLECTIONS.SYNC_LOGS, `${user.id}_${Date.now()}`), {
        userId: user.id,
        saveId: saveId,
        action: 'download',
        status: 'success',
        deviceInfo: navigator.userAgent,
        createdAt: serverTimestamp()
      })

      setSyncStatus('success')
      setLastSyncTime(new Date())

      return saveData.gameData
    } catch (error: any) {
      setError(getFirebaseErrorMessage(error.code) || error.message)
      setSyncStatus('error')

      // Log the error
      if (user) {
        try {
          await setDoc(doc(db, COLLECTIONS.SYNC_LOGS, `${user.id}_${Date.now()}_error`), {
            userId: user.id,
            saveId: saveId,
            action: 'download',
            status: 'error',
            errorMessage: error.message,
            deviceInfo: navigator.userAgent,
            createdAt: serverTimestamp()
          })
        } catch (logError) {
          console.error('Failed to log error:', logError)
        }
      }

      return null
    }
  }

  const deleteCloudSave = async (saveId: string): Promise<boolean> => {
    try {
      setError(null)

      if (!user) {
        throw new Error('User not authenticated')
      }

      // Verify ownership before deletion
      const saveDoc = await getDoc(doc(db, COLLECTIONS.GAME_SAVES, saveId))
      if (!saveDoc.exists() || saveDoc.data().userId !== user.id) {
        throw new Error('Save not found or access denied')
      }

      // Delete the save document
      await deleteDoc(doc(db, COLLECTIONS.GAME_SAVES, saveId))

      // Log the sync operation
      await setDoc(doc(db, COLLECTIONS.SYNC_LOGS, `${user.id}_${Date.now()}`), {
        userId: user.id,
        saveId: saveId,
        action: 'delete',
        status: 'success',
        deviceInfo: navigator.userAgent,
        createdAt: serverTimestamp()
      })

      // Remove from local list
      setCloudSaves(prev => prev.filter(save => save.id !== saveId))

      return true
    } catch (error: any) {
      setError(getFirebaseErrorMessage(error.code) || error.message)

      // Log the error
      if (user) {
        try {
          await setDoc(doc(db, COLLECTIONS.SYNC_LOGS, `${user.id}_${Date.now()}_error`), {
            userId: user.id,
            saveId: saveId,
            action: 'delete',
            status: 'error',
            errorMessage: error.message,
            deviceInfo: navigator.userAgent,
            createdAt: serverTimestamp()
          })
        } catch (logError) {
          console.error('Failed to log error:', logError)
        }
      }

      return false
    }
  }

  const refreshCloudSaves = async (): Promise<void> => {
    if (!isAuthenticated || !user) return

    try {
      setIsLoadingSaves(true)
      setError(null)

      // Query user's saves from Firestore
      const savesQuery = query(
        collection(db, COLLECTIONS.GAME_SAVES),
        where('userId', '==', user.id),
        orderBy('updatedAt', 'desc')
      )

      const querySnapshot = await getDocs(savesQuery)

      // Transform Firebase data to CloudSave format
      const cloudSaves: CloudSave[] = querySnapshot.docs.map(doc => {
        const data = doc.data()
        return {
          id: doc.id,
          saveName: data.saveName,
          description: data.description || '',
          saveType: data.saveType as 'manual' | 'auto' | 'checkpoint',
          metadata: {
            version: 1,
            gameVersion: data.gameVersion || '1.1.0',
            platform: data.platform || 'web',
            level: data.metadata?.level || 1,
            money: data.metadata?.money || 0,
            playTime: data.metadata?.playTime || 0
          },
          createdAt: data.createdAt?.toDate?.()?.toISOString() || new Date().toISOString(),
          updatedAt: data.updatedAt?.toDate?.()?.toISOString() || new Date().toISOString(),
          size: data.size || 0
        }
      })

      setCloudSaves(cloudSaves)
    } catch (error: any) {
      setError(getFirebaseErrorMessage(error.code) || error.message)
      setCloudSaves([])
    } finally {
      setIsLoadingSaves(false)
    }
  }

  // Auto-sync functionality
  useEffect(() => {
    if (!autoSyncEnabled || !isAuthenticated) return

    const interval = setInterval(() => {
      refreshCloudSaves()
    }, 5 * 60 * 1000) // Sync every 5 minutes

    return () => clearInterval(interval)
  }, [autoSyncEnabled, isAuthenticated])

  // Initialize Firebase authentication on mount
  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      try {
        if (firebaseUser) {
          // User is signed in
          const userDoc = await getDoc(doc(db, COLLECTIONS.USERS, firebaseUser.uid))
          const statsDoc = await getDoc(doc(db, COLLECTIONS.USER_STATS, firebaseUser.uid))

          const userData = userDoc.data()
          const statsData = statsDoc.data()

          const user: User = {
            id: firebaseUser.uid,
            username: userData?.username || firebaseUser.displayName || firebaseUser.email?.split('@')[0] || 'Player',
            email: firebaseUser.email || '',
            profile: {
              displayName: userData?.displayName || firebaseUser.displayName || 'Player',
              avatar: firebaseUser.photoURL || '',
              preferredLanguage: userData?.preferredLanguage || 'en'
            },
            gameStats: {
              totalPlayTime: statsData?.totalPlayTime || 0,
              highestLevel: statsData?.highestLevel || 1,
              totalMoney: statsData?.totalMoney || 0,
              achievementsUnlocked: statsData?.achievementsUnlocked || 0,
              gamesPlayed: statsData?.gamesPlayed || 0
            }
          }

          setUser(user)
          setIsAuthenticated(true)
          await refreshCloudSaves()
        } else {
          // User is signed out
          setUser(null)
          setIsAuthenticated(false)
          setCloudSaves([])
          setSyncStatus('idle')
          setError(null)
        }
      } catch (error) {
        console.error('Auth state change error:', error)
        setError(getFirebaseErrorMessage((error as any).code) || 'Authentication error')
      } finally {
        setIsLoading(false)
      }
    })

    return () => unsubscribe()
  }, [])

  const clearError = () => setError(null)

  const value: CloudSaveContextType = {
    // Authentication
    user,
    isAuthenticated,
    isLoading,
    
    // Auth methods
    login,
    register,
    logout,
    
    // Cloud saves
    cloudSaves,
    isLoadingSaves,
    
    // Save methods
    saveToCloud,
    loadFromCloud,
    deleteCloudSave,
    refreshCloudSaves,
    
    // Sync
    autoSyncEnabled,
    setAutoSyncEnabled,
    lastSyncTime,
    syncStatus,
    
    // Error handling
    error,
    clearError
  }

  return (
    <CloudSaveContext.Provider value={value}>
      {children}
    </CloudSaveContext.Provider>
  )
}

export function useCloudSave() {
  const context = useContext(CloudSaveContext)
  if (context === undefined) {
    throw new Error('useCloudSave must be used within a CloudSaveProvider')
  }
  return context
}
