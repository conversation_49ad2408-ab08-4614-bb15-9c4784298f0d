<!-- Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Users -->
    <div class="stat-card rounded-lg p-6 text-white card-hover transition-all duration-300">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-white/80 text-sm">Total Users</p>
                <p class="text-3xl font-bold" id="total-users"><%= stats.users.total.toLocaleString() %></p>
            </div>
            <div class="bg-white/20 rounded-full p-3">
                <i class="fas fa-users text-2xl"></i>
            </div>
        </div>
        <div class="mt-4 flex items-center text-sm">
            <i class="fas fa-arrow-up mr-1"></i>
            <span><%= stats.users.active %> active today</span>
        </div>
    </div>

    <!-- Cloud Saves -->
    <div class="stat-card-blue rounded-lg p-6 text-white card-hover transition-all duration-300">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-white/80 text-sm">Cloud Saves</p>
                <p class="text-3xl font-bold" id="total-saves"><%= stats.saves.total.toLocaleString() %></p>
            </div>
            <div class="bg-white/20 rounded-full p-3">
                <i class="fas fa-cloud text-2xl"></i>
            </div>
        </div>
        <div class="mt-4 flex items-center text-sm">
            <i class="fas fa-database mr-1"></i>
            <span><%= (stats.saves.totalSize / 1024 / 1024).toFixed(2) %> MB total</span>
        </div>
    </div>

    <!-- Active Rooms -->
    <div class="stat-card-green rounded-lg p-6 text-white card-hover transition-all duration-300">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-white/80 text-sm">Active Rooms</p>
                <p class="text-3xl font-bold" id="active-rooms"><%= stats.rooms.active.toLocaleString() %></p>
            </div>
            <div class="bg-white/20 rounded-full p-3">
                <i class="fas fa-gamepad text-2xl"></i>
            </div>
        </div>
        <div class="mt-4 flex items-center text-sm">
            <i class="fas fa-play mr-1"></i>
            <span><%= stats.rooms.total %> total rooms</span>
        </div>
    </div>

    <!-- Server Uptime -->
    <div class="stat-card-orange rounded-lg p-6 text-white card-hover transition-all duration-300">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-white/80 text-sm">Server Uptime</p>
                <p class="text-3xl font-bold" id="server-uptime"><%= Math.floor(stats.server.uptime / 3600) %>h</p>
            </div>
            <div class="bg-white/20 rounded-full p-3">
                <i class="fas fa-server text-2xl"></i>
            </div>
        </div>
        <div class="mt-4 flex items-center text-sm">
            <i class="fas fa-memory mr-1"></i>
            <span><%= (stats.server.memory.heapUsed / 1024 / 1024).toFixed(1) %> MB RAM</span>
        </div>
    </div>
</div>

<!-- Charts and Recent Activity -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Server Status Chart -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">
            <i class="fas fa-chart-line text-blue-500 mr-2"></i>
            Server Performance
        </h3>
        <canvas id="performanceChart" width="400" height="200"></canvas>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">
            <i class="fas fa-clock text-green-500 mr-2"></i>
            Recent Activity
        </h3>
        <div class="space-y-4" id="recent-activity">
            <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-white text-sm"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm font-medium text-gray-800">New user registered</p>
                    <p class="text-xs text-gray-500">2 minutes ago</p>
                </div>
            </div>
            <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                    <i class="fas fa-gamepad text-white text-sm"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm font-medium text-gray-800">Game room created</p>
                    <p class="text-xs text-gray-500">5 minutes ago</p>
                </div>
            </div>
            <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                    <i class="fas fa-cloud text-white text-sm"></i>
                </div>
                <div class="flex-1">
                    <p class="text-sm font-medium text-gray-800">Cloud save uploaded</p>
                    <p class="text-xs text-gray-500">8 minutes ago</p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
    <!-- System Info -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">
            <i class="fas fa-info-circle text-blue-500 mr-2"></i>
            System Information
        </h3>
        <div class="space-y-3">
            <div class="flex justify-between">
                <span class="text-gray-600">Version:</span>
                <span class="font-medium"><%= stats.server.version %></span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">Node.js:</span>
                <span class="font-medium"><%= process.version %></span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">Platform:</span>
                <span class="font-medium"><%= process.platform %></span>
            </div>
            <div class="flex justify-between">
                <span class="text-gray-600">Architecture:</span>
                <span class="font-medium"><%= process.arch %></span>
            </div>
        </div>
    </div>

    <!-- Memory Usage -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">
            <i class="fas fa-memory text-green-500 mr-2"></i>
            Memory Usage
        </h3>
        <div class="space-y-3">
            <div>
                <div class="flex justify-between mb-1">
                    <span class="text-gray-600">Heap Used:</span>
                    <span class="font-medium"><%= (stats.server.memory.heapUsed / 1024 / 1024).toFixed(1) %> MB</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-green-500 h-2 rounded-full" style="width: <%= (stats.server.memory.heapUsed / stats.server.memory.heapTotal * 100).toFixed(1) %>%"></div>
                </div>
            </div>
            <div>
                <div class="flex justify-between mb-1">
                    <span class="text-gray-600">Heap Total:</span>
                    <span class="font-medium"><%= (stats.server.memory.heapTotal / 1024 / 1024).toFixed(1) %> MB</span>
                </div>
            </div>
            <div>
                <div class="flex justify-between mb-1">
                    <span class="text-gray-600">RSS:</span>
                    <span class="font-medium"><%= (stats.server.memory.rss / 1024 / 1024).toFixed(1) %> MB</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">
            <i class="fas fa-bolt text-orange-500 mr-2"></i>
            Quick Actions
        </h3>
        <div class="space-y-3">
            <button class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg transition duration-200 flex items-center justify-center">
                <i class="fas fa-sync mr-2"></i>
                Refresh Stats
            </button>
            <button class="w-full bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg transition duration-200 flex items-center justify-center">
                <i class="fas fa-download mr-2"></i>
                Export Data
            </button>
            <button class="w-full bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded-lg transition duration-200 flex items-center justify-center">
                <i class="fas fa-cog mr-2"></i>
                Server Settings
            </button>
        </div>
    </div>
</div>

<script>
    // Performance Chart
    const ctx = document.getElementById('performanceChart').getContext('2d');
    const performanceChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: ['1h ago', '45m ago', '30m ago', '15m ago', 'Now'],
            datasets: [{
                label: 'Memory Usage (MB)',
                data: [
                    <%= (stats.server.memory.heapUsed / 1024 / 1024 * 0.8).toFixed(1) %>,
                    <%= (stats.server.memory.heapUsed / 1024 / 1024 * 0.9).toFixed(1) %>,
                    <%= (stats.server.memory.heapUsed / 1024 / 1024 * 0.85).toFixed(1) %>,
                    <%= (stats.server.memory.heapUsed / 1024 / 1024 * 0.95).toFixed(1) %>,
                    <%= (stats.server.memory.heapUsed / 1024 / 1024).toFixed(1) %>
                ],
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });

    // Update uptime display
    function updateUptime() {
        const uptimeElement = document.getElementById('server-uptime');
        if (uptimeElement) {
            const currentUptime = parseInt(uptimeElement.textContent);
            uptimeElement.textContent = (currentUptime + 1) + 'h';
        }
    }
    setInterval(updateUptime, 3600000); // Update every hour
</script>
