'use client'

import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { discordRPC, PlayerStatus, GameActivity } from '@/lib/discordRPC'
import { useGame } from './GameContext'
import { useMultiplayer } from './MultiplayerContext'

interface DiscordRPCContextType {
  isEnabled: boolean
  isConnected: boolean
  currentActivity: GameActivity | null
  setEnabled: (enabled: boolean) => void
  updateActivity: (activity: GameActivity) => Promise<void>
  setMenuActivity: () => Promise<void>
  setGameActivity: (level: number, money: number, activity?: string) => Promise<void>
  setMultiplayerActivity: (roomId: string, playerCount: number, maxPlayers: number) => Promise<void>
  setBakingActivity: (level: number, currentOrder: string) => Promise<void>
  clearActivity: () => Promise<void>
}

const DiscordRPCContext = createContext<DiscordRPCContextType | undefined>(undefined)

export function useDiscordRPC() {
  const context = useContext(DiscordRPCContext)
  if (context === undefined) {
    // Return a fallback object instead of throwing an error
    return {
      isEnabled: false,
      isConnected: false,
      currentActivity: null,
      setEnabled: () => {},
      updateActivity: async () => {},
      setMenuActivity: async () => {},
      setGameActivity: async () => {},
      setMultiplayerActivity: async () => {},
      setBakingActivity: async () => {},
      clearActivity: async () => {}
    }
  }
  return context
}

interface DiscordRPCProviderProps {
  children: ReactNode
}

export function DiscordRPCProvider({ children }: DiscordRPCProviderProps) {
  const [isEnabled, setIsEnabled] = useState(false) // Start disabled to prevent SSR issues
  const [isConnected, setIsConnected] = useState(false)
  const [currentActivity, setCurrentActivity] = useState<GameActivity | null>(null)
  const [isClient, setIsClient] = useState(false)
  const [isElectron, setIsElectron] = useState(false)

  // Get game state for automatic updates
  const gameContext = useGame()
  const multiplayerContext = useMultiplayer()

  // Initialize client-side state
  useEffect(() => {
    setIsClient(true)

    // Check if we're in Electron environment safely
    const electronAvailable = typeof window !== 'undefined' &&
                              typeof window.electronAPI !== 'undefined'
    setIsElectron(electronAvailable)

    // Load saved preference only after client is mounted
    const saved = localStorage.getItem('discordRPCEnabled')
    if (saved !== null) {
      const enabled = saved === 'true'
      setIsEnabled(enabled)
    } else {
      // Default to enabled if in Electron environment
      setIsEnabled(electronAvailable)
    }
  }, [])

  // Initialize Discord RPC
  useEffect(() => {
    if (!isClient) return

    // Check if Discord RPC should be enabled (only in Electron environment)
    const shouldEnable = isElectron && isEnabled

    if (shouldEnable) {
      discordRPC.setEnabled(true)

      // Check connection status periodically
      const checkConnection = () => {
        setIsConnected(discordRPC.isRPCConnected())
        setCurrentActivity(discordRPC.getCurrentActivity())
      }

      const interval = setInterval(checkConnection, 2000) // Less frequent to reduce load
      checkConnection() // Initial check

      return () => {
        clearInterval(interval)
      }
    } else {
      discordRPC.setEnabled(false)
    }
  }, [isClient, isEnabled, isElectron])

  // Auto-update Discord RPC based on game state
  useEffect(() => {
    if (!isClient || !isEnabled || !isConnected || !gameContext) return

    const updateRPCFromGameState = async () => {
      try {
        const { player, currentOrders } = gameContext
        
        // Determine current activity
        let activity: 'menu' | 'baking' | 'managing' | 'multiplayer' | 'idle' = 'managing'
        let currentOrder: string | undefined
        
        if (currentOrders && currentOrders.length > 0) {
          activity = 'baking'
          currentOrder = currentOrders[0].items[0]?.name || 'Unknown item'
        }
        
        // Check if in multiplayer
        if (multiplayerContext?.gameState === 'playing') {
          activity = 'multiplayer'
        }
        
        const playerStatus: PlayerStatus = {
          level: player.level,
          money: player.money,
          currentActivity: activity,
          currentOrder,
          playTime: player.playTime
        }
        
        // Update Discord RPC
        await discordRPC.updatePlayerStatus(playerStatus)
        
      } catch (error) {
        console.error('Failed to update Discord RPC from game state:', error)
      }
    }

    // Update immediately and then every 30 seconds
    updateRPCFromGameState()
    const interval = setInterval(updateRPCFromGameState, 30000)
    
    return () => clearInterval(interval)
  }, [gameContext?.player, gameContext?.currentOrders, multiplayerContext?.gameState, isClient, isEnabled, isConnected])

  // Auto-update for multiplayer state
  useEffect(() => {
    if (!isClient || !isEnabled || !isConnected || !multiplayerContext) return

    const updateMultiplayerRPC = async () => {
      try {
        const { gameState, currentRoom, players } = multiplayerContext
        
        if (gameState === 'playing' && currentRoom) {
          await discordRPC.setMultiplayerActivity(
            currentRoom.id,
            players.length,
            currentRoom.maxPlayers || 4
          )
        }
      } catch (error) {
        console.error('Failed to update multiplayer Discord RPC:', error)
      }
    }

    updateMultiplayerRPC()
  }, [multiplayerContext?.gameState, multiplayerContext?.currentRoom, multiplayerContext?.players, isClient, isEnabled, isConnected])

  const handleSetEnabled = (enabled: boolean) => {
    setIsEnabled(enabled)
    discordRPC.setEnabled(enabled)

    // Save preference to localStorage
    if (isClient) {
      localStorage.setItem('discordRPCEnabled', enabled.toString())
    }
  }

  const updateActivity = async (activity: GameActivity) => {
    await discordRPC.updateActivity(activity)
    setCurrentActivity(activity)
  }

  const setMenuActivity = async () => {
    await discordRPC.setMenuActivity()
    setCurrentActivity(discordRPC.getCurrentActivity())
  }

  const setGameActivity = async (level: number, money: number, activity?: string) => {
    await discordRPC.setGameActivity(level, money, activity)
    setCurrentActivity(discordRPC.getCurrentActivity())
  }

  const setMultiplayerActivity = async (roomId: string, playerCount: number, maxPlayers: number) => {
    await discordRPC.setMultiplayerActivity(roomId, playerCount, maxPlayers)
    setCurrentActivity(discordRPC.getCurrentActivity())
  }

  const setBakingActivity = async (level: number, currentOrder: string) => {
    await discordRPC.setBakingActivity(level, currentOrder)
    setCurrentActivity(discordRPC.getCurrentActivity())
  }

  const clearActivity = async () => {
    await discordRPC.clearActivity()
    setCurrentActivity(null)
  }

  // This effect is now handled in the client-side initialization above

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      discordRPC.cleanup()
    }
  }, [])

  const value: DiscordRPCContextType = {
    isEnabled,
    isConnected,
    currentActivity,
    setEnabled: handleSetEnabled,
    updateActivity,
    setMenuActivity,
    setGameActivity,
    setMultiplayerActivity,
    setBakingActivity,
    clearActivity
  }

  return (
    <DiscordRPCContext.Provider value={value}>
      {children}
    </DiscordRPCContext.Provider>
  )
}
