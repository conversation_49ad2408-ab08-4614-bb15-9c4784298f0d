'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { useLanguage } from '@/contexts/LanguageContext'
import { useMultiplayer } from '@/contexts/MultiplayerContext'
import { useDiscordRPC } from '@/contexts/DiscordRPCContext'

interface MultiplayerLobbyProps {
  isOpen: boolean
  onClose: () => void
}

export function MultiplayerLobby({ isOpen, onClose }: MultiplayerLobbyProps) {
  const { t } = useLanguage()
  const { setMultiplayerActivity } = useDiscordRPC()
  const {
    isConnected,
    isInRoom,
    connectionError,
    currentRoom,
    currentPlayer,
    players,
    gameState,
    messages,
    createRoom,
    joinRoom,
    leaveRoom,
    startGame,
    sendMessage,
    setPlayerReady
  } = useMultiplayer()

  const [activeTab, setActiveTab] = useState<'create' | 'join' | 'room'>('create')
  const [roomName, setRoomName] = useState('')
  const [playerName, setPlayerName] = useState('')
  const [roomIdToJoin, setRoomIdToJoin] = useState('')
  const [gameMode, setGameMode] = useState<'cooperative' | 'competitive'>('cooperative')
  const [maxPlayers, setMaxPlayers] = useState(4)
  const [chatMessage, setChatMessage] = useState('')
  const [isReady, setIsReady] = useState(false)

  if (!isOpen) return null

  const handleCreateRoom = async () => {
    if (!playerName.trim() || !roomName.trim()) return

    try {
      await createRoom(
        {
          name: roomName,
          mode: gameMode,
          maxPlayers,
          settings: {
            gameMode,
            difficulty: 'medium',
            allowSpectators: true
          }
        },
        {
          name: playerName,
          avatar: '👨‍🍳',
          level: 1
        }
      )
      setActiveTab('room')
    } catch (error) {
      console.error('Failed to create room:', error)
    }
  }

  const handleJoinRoom = async () => {
    if (!playerName.trim() || !roomIdToJoin.trim()) return

    try {
      await joinRoom(roomIdToJoin.toUpperCase(), {
        name: playerName,
        avatar: '👨‍🍳',
        level: 1
      })
      setActiveTab('room')
    } catch (error) {
      console.error('Failed to join room:', error)
    }
  }

  const handleLeaveRoom = () => {
    leaveRoom()
    setActiveTab('create')
    setIsReady(false)
  }

  const handleStartGame = () => {
    if (currentPlayer?.isHost) {
      startGame()
    }
  }

  const handleSendMessage = () => {
    if (chatMessage.trim()) {
      sendMessage(chatMessage)
      setChatMessage('')
    }
  }

  const handleReadyToggle = () => {
    const newReady = !isReady
    setIsReady(newReady)
    setPlayerReady(newReady)
  }

  const tabs = [
    { id: 'create', name: t('multiplayer.createRoom'), icon: '🏗️' },
    { id: 'join', name: t('multiplayer.joinRoom'), icon: '🚪' },
    ...(isInRoom ? [{ id: 'room', name: t('multiplayer.room'), icon: '🏠' }] : [])
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-orange-800">
              {t('multiplayer.lobby')}
            </h2>
            <div className="flex items-center space-x-4">
              <div className={`px-3 py-1 rounded-full text-sm ${
                isConnected ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {isConnected ? t('multiplayer.connected') : t('multiplayer.disconnected')}
              </div>
              <Button variant="secondary" onClick={onClose}>
                {t('game.close')}
              </Button>
            </div>
          </div>
          {connectionError && (
            <div className="mt-2 p-2 bg-red-100 text-red-800 rounded text-sm">
              {t('multiplayer.connection.error', { error: connectionError })}
            </div>
          )}
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <div className="flex space-x-0">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-orange-500 text-orange-600 bg-orange-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                {tab.icon} {tab.name}
              </button>
            ))}
          </div>
        </div>

        <div className="p-6 max-h-[60vh] overflow-y-auto">
          {/* Create Room Tab */}
          {activeTab === 'create' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('multiplayer.yourName')}
                  </label>
                  <input
                    type="text"
                    value={playerName}
                    onChange={(e) => setPlayerName(e.target.value)}
                    placeholder={t('multiplayer.enterName')}
                    className="w-full p-3 border rounded-lg"
                    maxLength={20}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('multiplayer.roomName')}
                  </label>
                  <input
                    type="text"
                    value={roomName}
                    onChange={(e) => setRoomName(e.target.value)}
                    placeholder={t('multiplayer.enterRoomName')}
                    className="w-full p-3 border rounded-lg"
                    maxLength={30}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('multiplayer.gameMode')}
                  </label>
                  <select
                    value={gameMode}
                    onChange={(e) => setGameMode(e.target.value as any)}
                    className="w-full p-3 border rounded-lg"
                  >
                    <option value="cooperative">{t('multiplayer.cooperative')}</option>
                    <option value="competitive">{t('multiplayer.competitive')}</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('multiplayer.maxPlayers', { count: maxPlayers.toString() })}
                  </label>
                  <input
                    type="range"
                    min="2"
                    max="8"
                    value={maxPlayers}
                    onChange={(e) => setMaxPlayers(parseInt(e.target.value))}
                    className="w-full"
                  />
                </div>
              </div>

              <Button
                variant="primary"
                size="lg"
                className="w-full"
                onClick={handleCreateRoom}
                disabled={!isConnected || !playerName.trim() || !roomName.trim()}
              >
                {t('multiplayer.create.title')}
              </Button>
            </div>
          )}

          {/* Join Room Tab */}
          {activeTab === 'join' && (
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('multiplayer.yourName')}
                  </label>
                  <input
                    type="text"
                    value={playerName}
                    onChange={(e) => setPlayerName(e.target.value)}
                    placeholder={t('multiplayer.enterName')}
                    className="w-full p-3 border rounded-lg"
                    maxLength={20}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    {t('multiplayer.roomId')}
                  </label>
                  <input
                    type="text"
                    value={roomIdToJoin}
                    onChange={(e) => setRoomIdToJoin(e.target.value.toUpperCase())}
                    placeholder={t('multiplayer.enterRoomId')}
                    className="w-full p-3 border rounded-lg font-mono"
                    maxLength={6}
                  />
                </div>
              </div>

              <Button
                variant="primary"
                size="lg"
                className="w-full"
                onClick={handleJoinRoom}
                disabled={!isConnected || !playerName.trim() || !roomIdToJoin.trim()}
              >
                {t('multiplayer.join.title')}
              </Button>
            </div>
          )}

          {/* Room Tab */}
          {activeTab === 'room' && currentRoom && (
            <div className="space-y-6">
              {/* Room Info */}
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex justify-between items-center mb-2">
                  <h3 className="font-semibold text-blue-800">{currentRoom.name}</h3>
                  <div className="text-sm text-blue-600">
                    {t('multiplayer.roomId')}: <span className="font-mono font-bold">{currentRoom.id}</span>
                  </div>
                </div>
                <div className="text-sm text-blue-700">
                  {t('multiplayer.room.info', {
                    mode: currentRoom.mode,
                    current: currentRoom.currentPlayers.toString(),
                    max: currentRoom.maxPlayers.toString()
                  })}
                </div>
              </div>

              {/* Players List */}
              <div>
                <h4 className="font-medium text-gray-800 mb-3">{t('multiplayer.players', { count: players.length.toString() })}</h4>
                <div className="space-y-2">
                  {players.map(player => (
                    <div
                      key={player.id}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                    >
                      <div className="flex items-center space-x-3">
                        <span className="text-2xl">{player.avatar}</span>
                        <div>
                          <div className="font-medium">
                            {player.name}
                            {player.isHost && <span className="ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded">{t('multiplayer.host')}</span>}
                          </div>
                          <div className="text-sm text-gray-600">{t('multiplayer.level', { level: player.level.toString() })}</div>
                        </div>
                      </div>
                      <div className={`px-2 py-1 rounded text-xs ${
                        player.isReady ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-600'
                      }`}>
                        {player.isReady ? t('common.ready') : t('common.notReady')}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Game Controls */}
              <div className="flex space-x-3">
                <Button
                  variant={isReady ? 'success' : 'secondary'}
                  onClick={handleReadyToggle}
                  className="flex-1"
                >
                  {isReady ? t('multiplayer.room.readyUp') : t('multiplayer.room.notReady')}
                </Button>
                {currentPlayer?.isHost && (
                  <Button
                    variant="primary"
                    onClick={handleStartGame}
                    disabled={!players.every(p => p.isReady) || players.length < 2}
                  >
                    {t('multiplayer.room.startGame')}
                  </Button>
                )}
                <Button variant="secondary" onClick={handleLeaveRoom}>
                  {t('multiplayer.room.leaveRoom')}
                </Button>
              </div>

              {/* Chat */}
              <div className="border-t pt-4">
                <h4 className="font-medium text-gray-800 mb-3">{t('multiplayer.chat')}</h4>
                <div className="bg-gray-50 p-3 rounded-lg h-32 overflow-y-auto mb-3">
                  {messages.map(message => (
                    <div key={message.id} className="text-sm mb-1">
                      <span className={`font-medium ${
                        message.playerId === 'system' ? 'text-blue-600' : 'text-gray-800'
                      }`}>
                        {message.playerName}:
                      </span>
                      <span className="ml-2">{message.content}</span>
                    </div>
                  ))}
                </div>
                <div className="flex space-x-2">
                  <input
                    type="text"
                    value={chatMessage}
                    onChange={(e) => setChatMessage(e.target.value)}
                    onKeyDown={(e) => e.key === 'Enter' && handleSendMessage()}
                    placeholder={t('multiplayer.typeMessage')}
                    className="flex-1 p-2 border rounded"
                    maxLength={100}
                  />
                  <Button size="sm" onClick={handleSendMessage}>
                    {t('common.send')}
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
