"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"accca380cbe5\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXG1hcmt2XFxEZXNrdG9wXFxjb2Rpbmcgc3R1ZmZcXGJha2UgaXQgb3V0XFxiYWtlLWl0LW91dFxcc3JjXFxhcHBcXGdsb2JhbHMuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiYWNjY2EzODBjYmU1XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/contexts/LanguageContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/LanguageContext.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Comprehensive translations object\nconst translations = {\n    en: {\n        // Main game\n        'game.title': 'Bake It Out',\n        'game.subtitle': 'Master the art of bakery management in this engaging multiplayer game. Complete orders, unlock recipes, automate your processes, and compete with friends!',\n        'game.play': '🎮 Start Playing',\n        'game.singlePlayer': '🎮 Single Player',\n        'game.singlePlayerDesc': 'Play solo and master your bakery skills',\n        'game.multiplayer': '👥 Multiplayer',\n        'game.multiplayerDesc': 'Play with friends in cooperative or competitive modes',\n        'game.english': '🇺🇸 English',\n        'game.czech': '🇨🇿 Čeština',\n        'game.home': '🏠 Home',\n        'game.close': '✕ Close',\n        'game.continue': '🚀 Continue Playing',\n        // Menu options\n        'menu.singlePlayer': 'Single Player',\n        'menu.multiplayer': 'Multiplayer',\n        'menu.settings': 'Settings',\n        'menu.credits': 'Credits',\n        'menu.exit': 'Exit',\n        // Credits\n        'credits.title': 'About Bake It Out',\n        'credits.subtitle': 'Game Information & Credits',\n        'credits.description': 'A multiplayer bakery management game with real-time collaboration and localization support.',\n        'credits.version': 'Version',\n        'credits.release': 'Release Date',\n        'credits.releaseDate': 'January 2025',\n        'credits.platform': 'Platform',\n        'credits.platforms': 'Windows, macOS, Linux',\n        'credits.contact': 'Contact',\n        'credits.facebook': 'Facebook',\n        'credits.close': 'Close',\n        'credits.features': 'Features',\n        'credits.multiplayer': 'Multiplayer Support',\n        'credits.multiplayerDesc': 'Real-time collaboration with friends in cooperative or competitive modes.',\n        'credits.localization': 'Localization',\n        'credits.localizationDesc': 'Full support for English and Czech languages with easy switching.',\n        'credits.progression': 'Progression System',\n        'credits.progressionDesc': 'Level up your bakery, unlock recipes, and master automation.',\n        'credits.automation': 'Automation',\n        'credits.automationDesc': 'Advanced automation systems to optimize your bakery operations.',\n        'credits.technology': 'Technology Stack',\n        'credits.team': 'Development Team',\n        'credits.developedBy': 'Developed by',\n        'credits.teamDesc': 'Created with passion by the Bake It Out development team.',\n        'credits.thanks': 'Special Thanks',\n        'credits.thanksPlayers': 'All our amazing players and beta testers',\n        'credits.thanksTranslators': 'Community translators for localization support',\n        'credits.thanksOpenSource': 'Open source community for incredible tools and libraries',\n        'credits.thanksBakers': 'Real bakers who inspired this game',\n        'credits.website': 'Website',\n        'credits.support': 'Support',\n        'credits.github': 'GitHub',\n        'credits.discord': 'Discord',\n        // Game Views\n        'game.view.traditional': 'Traditional View',\n        'game.view.layout': 'Bakery Layout',\n        'game.view.dining': 'Dining Room',\n        'game.view.customers': 'Customer Manager',\n        // Bakery Layout\n        'bakery.layout.title': 'Bakery Layout',\n        'bakery.kitchen': 'Kitchen',\n        'bakery.dining': 'Dining Area',\n        'bakery.counter': 'Service Counter',\n        'bakery.baking.area': 'Baking Area',\n        'bakery.prep.area': 'Prep Area',\n        'bakery.automation.area': 'Automation',\n        'bakery.kitchen.stats': 'Kitchen Stats',\n        'bakery.active.equipment': 'Active Equipment',\n        'bakery.automated.equipment': 'Automated Equipment',\n        'bakery.efficiency': 'Efficiency',\n        'bakery.dining.stats': 'Dining Stats',\n        'bakery.current.customers': 'Current Customers',\n        'bakery.waiting.customers': 'Waiting',\n        'bakery.eating.customers': 'Eating',\n        'bakery.avg.satisfaction': 'Avg Satisfaction',\n        'bakery.service.counter': 'Service Counter',\n        'bakery.display.case': 'Display Case',\n        'bakery.order.queue': 'Order Queue',\n        'bakery.last.updated': 'Last updated',\n        // Dining Room\n        'dining.room.title': 'Dining Room',\n        'dining.room.subtitle': 'Watch your customers enjoy their meals',\n        'dining.occupied.tables': 'Occupied Tables',\n        'dining.ambient.sounds': 'Toggle ambient sounds',\n        'dining.service.counter': 'Service',\n        'dining.customer.status': 'Customer Status',\n        'dining.enjoying': 'Enjoying',\n        'dining.no.customers': 'No customers dining currently',\n        'dining.waiting.for.customers': 'Complete orders to see customers dining',\n        'dining.ambient.playing': 'Ambient sounds playing',\n        // Customer Manager\n        'customers.manager.title': 'Customer Manager',\n        'customers.manager.subtitle': 'Monitor and serve your customers',\n        'customers.current.list': 'Current Customers',\n        'customers.table': 'Table',\n        'customers.patience': 'Patience',\n        'customers.no.customers': 'No customers currently',\n        'customers.waiting.for.orders': 'Waiting for new orders...',\n        'customers.order.details': 'Order Details',\n        'customers.total': 'Total',\n        'customers.info': 'Customer Info',\n        'customers.mood': 'Mood',\n        'customers.status': 'Status',\n        'customers.preferences': 'Preferences',\n        'customers.select.customer': 'Select a customer',\n        'customers.select.to.view.details': 'Select a customer to view details',\n        'customers.serve.order': 'Serve Order',\n        'menu.newGame': 'New Game',\n        'menu.continueGame': 'Continue Game',\n        'menu.loadGame': 'Load Game',\n        'menu.selectLanguage': 'Select Language',\n        'menu.about': 'About',\n        'menu.help': 'Help',\n        'menu.quit': 'Quit',\n        // Features\n        'features.manage.title': 'Manage Your Bakery',\n        'features.manage.description': 'Take orders, bake delicious goods, and serve happy customers',\n        'features.levelup.title': 'Level Up & Automate',\n        'features.levelup.description': 'Unlock new recipes, buy equipment, and automate your processes',\n        'features.multiplayer.title': 'Play Together',\n        'features.multiplayer.description': 'Cooperative and competitive multiplayer modes with friends',\n        'status.development': '🚧 Game in Development - Phase 5: Multilayer Support! 🚧',\n        // Game interface\n        'ui.level': 'Level {{level}}',\n        'ui.money': '${{amount}}',\n        'ui.experience': 'XP: {{current}}/{{max}}',\n        'ui.skillPoints': 'SP: {{points}}',\n        'ui.achievements': '🏆 Achievements',\n        'ui.skills': '🌟 Skills',\n        'ui.automation': '🤖 Automation',\n        // Kitchen\n        'kitchen.title': '🏪 Kitchen',\n        'kitchen.clickToUse': 'Click to use',\n        'kitchen.making': 'Making: {{recipe}}',\n        'kitchen.timeRemaining': 'Time: {{time}}',\n        // Inventory\n        'inventory.title': '📦 Inventory',\n        'inventory.quantity': 'Qty: {{qty}}',\n        'inventory.cost': '${{cost}} each',\n        // Orders\n        'orders.title': '📋 Orders',\n        'orders.newOrder': '+ New Order',\n        'orders.accept': 'Accept',\n        'orders.decline': 'Decline',\n        'orders.complete': 'Complete',\n        'orders.inProgress': 'In Progress',\n        'orders.timeLimit': 'Time: {{time}}',\n        'orders.reward': '${{amount}}',\n        'orders.customer': 'Customer: {{name}}',\n        // Quick Actions\n        'actions.title': '⚡ Quick Actions',\n        'actions.buyIngredients': '🛒 Buy Ingredients',\n        'actions.viewRecipes': '📖 View Recipes',\n        'actions.equipmentShop': '🔧 Equipment Shop',\n        // Modals\n        'modal.recipes.title': '📖 Recipe Book',\n        'modal.shop.title': '🛒 Ingredient Shop',\n        'modal.baking.title': '🔥 {{equipment}} - Select Recipe',\n        'modal.achievements.title': '🏆 Achievements',\n        'modal.skills.title': '🌟 Skill Tree',\n        'modal.automation.title': '🤖 Automation Control',\n        'modal.equipmentShop.title': '🏪 Equipment Shop',\n        'modal.settings.title': '⚙️ Settings',\n        'modal.bakeries.title': '🏪 Bakery Manager',\n        'modal.levelUp.title': 'Level Up!',\n        'modal.levelUp.subtitle': 'You reached Level {{level}}!',\n        // Recipe Modal\n        'recipes.all': 'All',\n        'recipes.cookies': 'Cookies',\n        'recipes.cakes': 'Cakes',\n        'recipes.bread': 'Bread',\n        'recipes.pastries': 'Pastries',\n        'recipes.ingredients': 'Ingredients:',\n        'recipes.difficulty': 'Difficulty:',\n        'recipes.time': 'Time:',\n        'recipes.canCraft': '✅ Can Craft',\n        'recipes.unlockLevel': 'Unlocked at Level {{level}}',\n        'recipes.noRecipes': 'No recipes available in this category.',\n        'recipes.levelUpToUnlock': 'Level up to unlock more recipes!',\n        // Shop Modal\n        'shop.currentStock': 'Current stock: {{quantity}}',\n        'shop.buy': 'Buy',\n        'shop.tooExpensive': 'Too Expensive',\n        'shop.tips.title': '💡 Shopping Tips',\n        'shop.tips.bulk': '• Buy ingredients in bulk to save time',\n        'shop.tips.stock': '• Keep an eye on your stock levels',\n        'shop.tips.rare': '• Some recipes require rare ingredients',\n        'shop.tips.prices': '• Prices may vary based on availability',\n        // Baking Modal\n        'baking.selectRecipe': 'Select Recipe',\n        'baking.noRecipes': 'No recipes available',\n        'baking.noIngredients': 'You don\\'t have enough ingredients to craft any recipes.',\n        'baking.buyIngredients': 'Buy Ingredients',\n        'baking.startBaking': '🔥 Start Baking',\n        'baking.instructions': '📋 Baking Instructions for {{recipe}}',\n        'baking.expectedReward': 'Expected reward: ${{amount}}',\n        'baking.makesSure': 'Make sure you have all ingredients before starting!',\n        'baking.inProgress': 'Baking in progress...',\n        'baking.completed': 'Baking completed!',\n        'baking.cancelled': 'Baking cancelled',\n        'baking.timeRemaining': 'Time remaining: {{time}}',\n        'baking.clickToCollect': 'Click to collect',\n        // Achievements Modal\n        'achievements.completed': '{{completed}} of {{total}} achievements completed',\n        'achievements.overallProgress': 'Overall Progress',\n        'achievements.progress': 'Progress',\n        'achievements.reward': 'Reward:',\n        'achievements.noAchievements': 'No achievements in this category.',\n        // Skills Modal\n        'skills.availablePoints': 'Available Skill Points: {{points}}',\n        'skills.efficiency': 'Efficiency',\n        'skills.automation': 'Automation',\n        'skills.quality': 'Quality',\n        'skills.business': 'Business',\n        'skills.effects': 'Effects:',\n        'skills.requires': 'Requires: {{requirements}}',\n        'skills.requiresLevel': 'Requires Level {{level}}',\n        'skills.maxed': '✅ Maxed',\n        'skills.upgrade': '⬆️ Upgrade ({{cost}} SP)',\n        'skills.locked': '🔒 Locked',\n        'skills.noSkills': 'No skills in this category.',\n        'skills.tips.title': '💡 Skill Tips',\n        'skills.tips.earnPoints': '• Earn skill points by leveling up (1 point every 2 levels)',\n        'skills.tips.prerequisites': '• Some skills require other skills to be unlocked first',\n        'skills.tips.playstyle': '• Focus on skills that match your playstyle',\n        'skills.tips.efficiency': '• Efficiency skills help with resource management',\n        // Automation Modal\n        'automation.masterControl': '🎛️ Master Control',\n        'automation.enableAutomation': 'Enable Automation',\n        'automation.autoStart': 'Auto-start Equipment',\n        'automation.priorityMode': '🎯 Priority Mode',\n        'automation.efficiency': 'Efficiency (Orders First)',\n        'automation.profit': 'Profit (Highest Value)',\n        'automation.speed': 'Speed (Fastest Recipes)',\n        'automation.priorityDescription': 'How automation chooses what to bake',\n        'automation.performance': '⚡ Performance',\n        'automation.maxJobs': 'Max Concurrent Jobs: {{jobs}}',\n        'automation.safety': '🛡️ Safety',\n        'automation.stopWhenLow': 'Stop when ingredients below: {{threshold}}',\n        'automation.upgrades': '💡 Automation Upgrades',\n        'automation.upgradesDescription': 'Improve your automation efficiency, speed, and intelligence with these upgrades.',\n        'automation.purchase': 'Purchase',\n        'automation.noUpgrades': 'No upgrades available at your current level.',\n        'automation.levelUpForUpgrades': 'Level up to unlock more automation upgrades!',\n        'automation.automatedEquipment': 'Automated Equipment',\n        'automation.activeUpgrades': 'Active Upgrades',\n        'automation.automationStatus': 'Automation Status',\n        'automation.equipmentStatus': '🏭 Equipment Status',\n        'automation.running': 'Running',\n        'automation.idle': 'Idle',\n        'automation.noAutomatedEquipment': 'No automated equipment available.',\n        'automation.purchaseAutoEquipment': 'Purchase auto-equipment from the shop to get started!',\n        // Equipment Shop Modal\n        'equipmentShop.upgradeYourBakery': 'Upgrade your bakery with professional equipment',\n        'equipmentShop.basic': 'Basic',\n        'equipmentShop.automated': 'Automated',\n        'equipmentShop.advanced': 'Advanced',\n        'equipmentShop.efficiency': 'Efficiency: {{efficiency}}x',\n        'equipmentShop.automation': 'Automation:',\n        'equipmentShop.unlockLevel': 'Unlock Level: {{level}}',\n        'equipmentShop.purchase': '💰 Purchase',\n        'equipmentShop.noEquipment': 'No equipment available in this category.',\n        'equipmentShop.levelUpForEquipment': 'Level up to unlock more equipment!',\n        'equipmentShop.tips.title': '💡 Equipment Tips',\n        'equipmentShop.tips.automated': '• Automated equipment can run without your supervision',\n        'equipmentShop.tips.efficiency': '• Higher efficiency means faster production and better quality',\n        'equipmentShop.tips.conveyor': '• Conveyor belts connect equipment for seamless workflow',\n        'equipmentShop.tips.advanced': '• Advanced equipment unlocks at higher levels',\n        // Level Up Modal\n        'levelUp.levelRewards': '🎁 Level Rewards',\n        'levelUp.whatsNext': '💡 What\\'s Next?',\n        'levelUp.checkRecipes': '• Check out new recipes in your recipe book',\n        'levelUp.visitShop': '• Visit the shop for new equipment',\n        'levelUp.challengingOrders': '• Take on more challenging orders',\n        'levelUp.investSkills': '• Invest in skill upgrades',\n        // Settings Modal\n        'settings.title': '⚙️ Settings',\n        'settings.general': 'General',\n        'settings.audio': 'Audio',\n        'settings.graphics': 'Graphics',\n        'settings.save': 'Save & Data',\n        'settings.language': '🌍 Language',\n        'settings.gameplay': '🎮 Gameplay',\n        'settings.notifications': 'Enable Notifications',\n        'settings.tutorials': 'Show Tutorials',\n        'settings.animationSpeed': 'Animation Speed',\n        'settings.sound': 'Sound Effects',\n        'settings.music': 'Background Music',\n        'settings.quality': '🎨 Graphics Quality',\n        'settings.autoSave': '💾 Auto-Save',\n        'settings.enableAutoSave': 'Enable Auto-Save',\n        'settings.dataManagement': '📁 Data Management',\n        'settings.exportSave': '📤 Export Save',\n        'settings.importSave': '📥 Import Save',\n        'settings.cloudSync': '☁️ Cloud Sync',\n        'settings.cloudSyncDescription': 'Cloud sync allows you to save your progress online and play across multiple devices.',\n        // Cloud Save Management\n        'cloud.save.manage_saves': 'Manage Cloud Saves',\n        'cloud.save.login': 'Login / Register',\n        'cloud.save.login_required': 'Please login to use cloud saves',\n        // Bakery Manager Modal\n        'bakeries.title': '🏪 Bakery Manager',\n        'bakeries.subtitle': 'Manage your bakery empire',\n        'bakeries.owned': 'My Bakeries',\n        'bakeries.available': 'Available',\n        'bakeries.current': 'Current',\n        'bakeries.level': 'Level',\n        'bakeries.specialization': 'Specialization',\n        'bakeries.equipment': 'Equipment',\n        'bakeries.orders': 'Active Orders',\n        'bakeries.switchTo': 'Switch To',\n        'bakeries.noOwned': 'You don\\'t own any bakeries yet.',\n        'bakeries.purchase': '💰 Purchase',\n        'bakeries.tooExpensive': '💸 Too Expensive',\n        'bakeries.allOwned': 'You own all available bakeries!',\n        'bakeries.tips': '💡 Bakery Tips',\n        'bakeries.tip1': 'Each bakery specializes in different products for bonus efficiency',\n        'bakeries.tip2': 'Switch between bakeries to manage multiple locations',\n        'bakeries.tip3': 'Specialized bakeries attract customers looking for specific items',\n        'bakeries.tip4': 'Upgrade each bakery independently for maximum profit',\n        // Notifications\n        'notifications.orderAccepted': 'Order Accepted',\n        'notifications.orderAcceptedMessage': 'You have accepted a new order!',\n        'notifications.orderCompleted': 'Order Completed!',\n        'notifications.orderCompletedMessage': 'You earned ${{reward}} and gained experience!',\n        'notifications.orderDeclined': 'Order Declined',\n        'notifications.orderDeclinedMessage': 'Order has been removed from your queue.',\n        'notifications.bakeryPurchased': 'Bakery Purchased!',\n        'notifications.bakeryPurchasedMessage': 'You now own {{name}}!',\n        'notifications.bakerySwitched': 'Bakery Switched',\n        'notifications.bakerySwitchedMessage': 'Switched to {{name}}',\n        // Common buttons and actions\n        'common.accept': 'Accept',\n        'common.decline': 'Decline',\n        'common.complete': 'Complete',\n        'common.purchase': 'Purchase',\n        'common.upgrade': 'Upgrade',\n        'common.cancel': 'Cancel',\n        'common.confirm': 'Confirm',\n        'common.save': 'Save',\n        'common.load': 'Load',\n        'common.delete': 'Delete',\n        'common.edit': 'Edit',\n        'common.back': 'Back',\n        'common.next': 'Next',\n        'common.previous': 'Previous',\n        'common.yes': 'Yes',\n        'common.no': 'No',\n        'common.create': 'Create',\n        'common.join': 'Join',\n        'common.leave': 'Leave',\n        'common.start': 'Start',\n        'common.ready': 'Ready',\n        'common.notReady': 'Not Ready',\n        'common.send': 'Send',\n        'common.refresh': 'Refresh',\n        'common.retry': 'Retry',\n        'common.reset': 'Reset',\n        'common.clear': 'Clear',\n        'common.apply': 'Apply',\n        'common.warning': 'Warning',\n        'common.info': 'Information',\n        'common.success': 'Success',\n        'common.error': 'Error',\n        // Save/Load System\n        'saveLoad.saveDesc': 'Choose a slot to save your progress',\n        'saveLoad.loadDesc': 'Select a save file to load',\n        'saveLoad.saveName': 'Save Name',\n        'saveLoad.emptySlot': 'Empty Slot',\n        'saveLoad.selectedSaveSlot': 'Selected: Slot {{slot}}',\n        'saveLoad.selectedLoadSlot': 'Selected: Slot {{slot}}',\n        'saveLoad.confirmOverwrite': 'Overwrite Save?',\n        'saveLoad.overwriteWarning': 'This will overwrite the existing save. This action cannot be undone.',\n        'saveLoad.overwrite': 'Overwrite',\n        'saveLoad.fileSlots': 'File Slots',\n        'saveLoad.gameSlots': 'Game Slots',\n        'saveLoad.exportSave': 'Export Save',\n        'saveLoad.importSave': 'Import Save',\n        'saveLoad.deleteConfirm': 'Delete Save?',\n        'saveLoad.deleteWarning': 'This will permanently delete this save file. This action cannot be undone.',\n        'saveLoad.delete': 'Delete',\n        // Game Menu\n        'gameMenu.title': 'Game Menu',\n        'gameMenu.subtitle': 'Manage your game',\n        'gameMenu.resume': 'Resume Game',\n        'gameMenu.resumeDesc': 'Continue playing',\n        'gameMenu.save': 'Save Game',\n        'gameMenu.saveDesc': 'Save your progress',\n        'gameMenu.load': 'Load Game',\n        'gameMenu.loadDesc': 'Load saved progress',\n        'gameMenu.settings': 'Settings',\n        'gameMenu.settingsDesc': 'Game preferences',\n        'gameMenu.mainMenu': 'Main Menu',\n        'gameMenu.mainMenuDesc': 'Return to main menu',\n        'gameMenu.exit': 'Exit Game',\n        'gameMenu.exitDesc': 'Close the application',\n        'gameMenu.tip': 'Press ESC to open this menu anytime',\n        // Discord Rich Presence\n        'settings.discord': 'Discord',\n        'settings.discordRichPresence': 'Discord Rich Presence',\n        'settings.discordDescription': 'Show your current game status and activity in Discord.',\n        'settings.enableDiscordRPC': 'Enable Discord Rich Presence',\n        'settings.discordConnected': '✅ Connected to Discord',\n        'settings.discordDisconnected': '❌ Not connected to Discord',\n        'settings.discordInfo': 'What is Discord Rich Presence?',\n        'settings.discordInfoDesc1': 'Discord Rich Presence shows your friends what you\\'re doing in Bake It Out:',\n        'settings.discordFeature1': 'Your current level and money',\n        'settings.discordFeature2': 'What you\\'re currently baking',\n        'settings.discordFeature3': 'Multiplayer room information',\n        'settings.discordFeature4': 'How long you\\'ve been playing',\n        'settings.discordInfoDesc2': 'Your friends can even join your multiplayer games directly from Discord!',\n        'settings.discordTroubleshooting': 'Discord Not Connected',\n        'settings.discordTrouble1': 'Make sure Discord is running on your computer.',\n        'settings.discordTrouble2': 'Discord Rich Presence only works in the desktop version of the game.',\n        'settings.discordTrouble3': 'Try restarting both Discord and the game if the connection fails.',\n        'settings.discordPrivacy': 'Privacy Information',\n        'settings.discordPrivacyDesc1': 'Discord Rich Presence only shares:',\n        'settings.discordPrivacy1': 'Your current game activity (public)',\n        'settings.discordPrivacy2': 'Your player level and progress (public)',\n        'settings.discordPrivacy3': 'Multiplayer room codes (for joining)',\n        'settings.discordPrivacyDesc2': 'No personal information or save data is shared with Discord.',\n        'settings.discordStatus': 'Discord Status',\n        'settings.discordInitializing': '🔄 Initializing Discord RPC...',\n        'settings.discordRetrying': '🔄 Retrying connection...',\n        'settings.discordUnavailable': '❌ Discord not available',\n        'settings.discordDesktopOnly': 'ℹ️ Discord RPC only available in desktop version',\n        // Error messages and status\n        'error.general': 'An error occurred',\n        'error.saveLoad': 'Failed to save/load game',\n        'error.connection': 'Connection error',\n        'error.fileNotFound': 'File not found',\n        'error.invalidData': 'Invalid data format',\n        'error.permissionDenied': 'Permission denied',\n        'status.loading': 'Loading...',\n        'status.saving': 'Saving...',\n        'status.connecting': 'Connecting...',\n        'status.ready': 'Ready',\n        'status.success': 'Success!',\n        'status.failed': 'Failed',\n        'status.offline': 'Offline',\n        'status.online': 'Online',\n        // UI Elements\n        'ui.placeholder': 'Enter text...',\n        'ui.search': 'Search',\n        'ui.filter': 'Filter',\n        'ui.sort': 'Sort',\n        'ui.ascending': 'Ascending',\n        'ui.descending': 'Descending',\n        'ui.selectAll': 'Select All',\n        'ui.deselectAll': 'Deselect All',\n        'ui.noResults': 'No results found',\n        'ui.noData': 'No data available',\n        'ui.loading': 'Loading...',\n        'ui.saving': 'Saving...',\n        'ui.saved': 'Saved!',\n        'ui.failed': 'Failed',\n        'ui.retry': 'Retry',\n        'ui.back': 'Back',\n        'ui.forward': 'Forward',\n        'ui.home': 'Home',\n        'ui.menu': 'Menu',\n        'ui.options': 'Options',\n        'ui.preferences': 'Preferences',\n        // Multiplayer\n        'multiplayer.lobby': '👥 Multiplayer Lobby',\n        'multiplayer.connected': '🟢 Connected',\n        'multiplayer.disconnected': '🔴 Disconnected',\n        'multiplayer.createRoom': 'Create Room',\n        'multiplayer.joinRoom': 'Join Room',\n        'multiplayer.room': 'Room',\n        'multiplayer.yourName': 'Your Name',\n        'multiplayer.enterName': 'Enter your name',\n        'multiplayer.roomName': 'Room Name',\n        'multiplayer.enterRoomName': 'Enter room name',\n        'multiplayer.gameMode': 'Game Mode',\n        'multiplayer.cooperative': '🤝 Cooperative',\n        'multiplayer.competitive': '⚔️ Competitive',\n        'multiplayer.maxPlayers': 'Max Players: {{count}}',\n        'multiplayer.roomId': 'Room ID',\n        'multiplayer.enterRoomId': 'Enter room ID',\n        'multiplayer.players': 'Players ({{count}})',\n        'multiplayer.host': 'HOST',\n        'multiplayer.level': 'Level {{level}}',\n        'multiplayer.chat': 'Chat',\n        'multiplayer.typeMessage': 'Type a message...',\n        'multiplayer.gameTime': 'Game Time: {{time}}',\n        'multiplayer.teamStats': '📊 Team Stats',\n        'multiplayer.ordersCompleted': 'Orders Completed:',\n        'multiplayer.totalRevenue': 'Total Revenue:',\n        'multiplayer.teamExperience': 'Team Experience:',\n        'multiplayer.sharedKitchen': '🏪 Shared Kitchen',\n        'multiplayer.sharedOrders': '📋 Shared Orders',\n        'multiplayer.sharedInventory': '📦 Shared Inventory',\n        'multiplayer.contribution': 'Contribution:',\n        'multiplayer.online': '🟢 Online',\n        'multiplayer.status': 'Status:',\n        'multiplayer.you': '(You)',\n        'multiplayer.teamChat': '💬 Team Chat',\n        'multiplayer.chatPlaceholder': 'Chat messages will appear here...',\n        // Multiplayer game modes\n        'multiplayer.mode.cooperative.description': '🤝 Cooperative Mode: Work together to complete orders and grow your shared bakery!',\n        'multiplayer.mode.competitive.description': '⚔️ Competitive Mode: Compete against other players to complete the most orders!',\n        // Multiplayer game interface\n        'multiplayer.game.title': '🎮 Multiplayer Game - {{roomName}}',\n        'multiplayer.game.mode': 'Mode: {{mode}}',\n        'multiplayer.game.playersCount': 'Players: {{count}}',\n        'multiplayer.game.playing': '🟢 Playing',\n        'multiplayer.game.leaveGame': '🚪 Leave Game',\n        'multiplayer.game.tabs.game': 'Game',\n        'multiplayer.game.tabs.players': 'Players',\n        'multiplayer.game.tabs.chat': 'Chat',\n        // Room creation and joining\n        'multiplayer.create.title': '🏗️ Create Room',\n        'multiplayer.join.title': '🚪 Join Room',\n        'multiplayer.room.info': 'Mode: {{mode}} • Players: {{current}}/{{max}}',\n        'multiplayer.room.readyUp': '✅ Ready',\n        'multiplayer.room.notReady': '⏳ Not Ready',\n        'multiplayer.room.startGame': '🚀 Start Game',\n        'multiplayer.room.leaveRoom': '🚪 Leave',\n        // Connection states\n        'multiplayer.connection.connecting': 'Connecting...',\n        'multiplayer.connection.reconnecting': 'Reconnecting...',\n        'multiplayer.connection.failed': 'Connection failed',\n        'multiplayer.connection.error': '⚠️ {{error}}',\n        // System messages\n        'multiplayer.system.playerJoined': '{{name}} joined the room',\n        'multiplayer.system.playerLeft': '{{name}} left the room',\n        'multiplayer.system.gameStarted': 'Game started!',\n        'multiplayer.system.gameEnded': 'Game ended!',\n        'multiplayer.system.roomCreated': 'Room created successfully',\n        'multiplayer.system.roomJoined': 'Joined room successfully',\n        // Bakery Layout\n        'bakery.layout.title': 'Bakery Layout',\n        'bakery.kitchen': 'Kitchen',\n        'bakery.dining': 'Dining Area',\n        'bakery.counter': 'Service Counter',\n        'bakery.layout.baking_area': 'Baking Area',\n        'bakery.layout.prep_area': 'Prep Area',\n        'bakery.layout.automation_zone': 'Automation Zone',\n        'bakery.layout.efficiency': 'Efficiency',\n        'bakery.layout.active_equipment': 'Active Equipment',\n        'bakery.layout.total_equipment': 'Total Equipment',\n        'bakery.layout.kitchen_stats': 'Kitchen Stats',\n        // Equipment Status\n        'equipment.status.active': 'Active',\n        'equipment.status.idle': 'Idle',\n        'equipment.status.maintenance': 'Maintenance',\n        'equipment.status.automated': 'Automated',\n        'equipment.zones.baking': 'Baking Zone',\n        'equipment.zones.prep': 'Prep Zone',\n        'equipment.zones.automation': 'Automation Zone',\n        // Dining Room\n        'dining.room.title': 'Dining Room',\n        'dining.room.subtitle': 'Watch your customers enjoy their meals',\n        'dining.room.tables': 'Tables',\n        'dining.room.customers': 'Customers',\n        'dining.room.satisfaction': 'Satisfaction',\n        'dining.room.occupancy': 'Occupancy',\n        'dining.room.ambient_sounds': 'Ambient Sounds',\n        'dining.room.decorations': 'Decorations',\n        'dining.occupied.tables': 'Occupied Tables',\n        'dining.ambient.sounds': 'Toggle ambient sounds',\n        'dining.ambient.playing': 'Ambient sounds playing',\n        'dining.customer.status': 'Customer Status',\n        'dining.enjoying': 'Enjoying',\n        'dining.no.customers': 'No customers dining currently',\n        'dining.waiting.for.customers': 'Complete orders to see customers dining',\n        'dining.table.number': 'Table {{number}}',\n        'dining.table.seats': '{{count}} seats',\n        'dining.table.occupied': 'Occupied',\n        'dining.table.available': 'Available',\n        'dining.table.customer_info': 'Customer Info',\n        'dining.service.counter': 'Service',\n        'dining.service.ready': 'Ready to Serve',\n        'dining.service.waiting': 'Waiting',\n        'dining.atmosphere.cozy': 'Cozy',\n        'dining.atmosphere.busy': 'Busy',\n        'dining.atmosphere.peaceful': 'Peaceful',\n        'dining.atmosphere.lively': 'Lively',\n        // Customer Manager\n        'customers.manager.title': 'Customer Manager',\n        'customers.manager.subtitle': 'Monitor and serve your customers',\n        'customers.manager.active_customers': 'Active Customers',\n        'customers.manager.total_served': 'Total Served',\n        'customers.manager.average_satisfaction': 'Average Satisfaction',\n        'customers.manager.serve_order': 'Serve Order',\n        'customers.manager.customer_details': 'Customer Details',\n        'customers.current.list': 'Current Customers',\n        'customers.table': 'Table',\n        'customers.patience': 'Patience',\n        'customers.no.customers': 'No customers currently',\n        'customers.waiting.for.orders': 'Waiting for new orders...',\n        'customers.order.details': 'Order Details',\n        'customers.total': 'Total',\n        'customers.info': 'Customer Info',\n        'customers.mood': 'Mood',\n        'customers.status': 'Status',\n        'customers.preferences': 'Preferences',\n        'customers.serve.order': 'Serve Order',\n        'customers.select.customer': 'Select a customer',\n        'customers.select.to.view.details': 'Select a customer to view details',\n        'customers.status.entering': 'Entering',\n        'customers.status.waiting': 'Waiting',\n        'customers.status.ordering': 'Ordering',\n        'customers.status.served': 'Served',\n        'customers.status.eating': 'Eating',\n        'customers.status.leaving': 'Leaving',\n        'customers.mood.happy': 'Happy',\n        'customers.mood.neutral': 'Neutral',\n        'customers.mood.impatient': 'Impatient',\n        'customers.mood.angry': 'Angry',\n        'customers.info.name': 'Name',\n        'customers.info.order': 'Order',\n        'customers.info.patience': 'Patience',\n        'customers.info.satisfaction': 'Satisfaction',\n        'customers.info.table': 'Table',\n        'customers.info.order_value': 'Order Value',\n        'customers.info.preferences': 'Preferences',\n        'customers.info.time_seated': 'Time Seated',\n        'customers.preferences.sweet': 'Sweet',\n        'customers.preferences.savory': 'Savory',\n        'customers.preferences.healthy': 'Healthy',\n        'customers.preferences.indulgent': 'Indulgent',\n        'customers.preferences.traditional': 'Traditional',\n        'customers.preferences.exotic': 'Exotic',\n        // View Switcher\n        'views.traditional': 'Traditional',\n        'views.layout': 'Bakery Layout',\n        'views.dining': 'Dining Room',\n        'views.customers': 'Customer Manager',\n        'views.switch_view': 'Switch View',\n        'game.view.traditional': 'Traditional View',\n        'game.view.layout': 'Bakery Layout',\n        'game.view.dining': 'Dining Room',\n        'game.view.customers': 'Customer Manager',\n        // Ingredients\n        'ingredient.Flour': 'Flour',\n        'ingredient.Sugar': 'Sugar',\n        'ingredient.Eggs': 'Eggs',\n        'ingredient.Butter': 'Butter',\n        'ingredient.Milk': 'Milk',\n        'ingredient.Vanilla Extract': 'Vanilla Extract',\n        'ingredient.Vanilla': 'Vanilla',\n        'ingredient.Chocolate Chips': 'Chocolate Chips',\n        'ingredient.Baking Powder': 'Baking Powder',\n        'ingredient.Salt': 'Salt',\n        'ingredient.Cinnamon': 'Cinnamon',\n        'ingredient.Nuts': 'Nuts',\n        'ingredient.Cream Cheese': 'Cream Cheese',\n        'ingredient.Honey': 'Honey',\n        'ingredient.Cocoa Powder': 'Cocoa Powder',\n        'ingredient.Yeast': 'Yeast',\n        // Equipment Names\n        'equipment.Basic Oven': 'Basic Oven',\n        'equipment.Hand Mixer': 'Hand Mixer',\n        'equipment.Professional Oven': 'Professional Oven',\n        'equipment.Stand Mixer': 'Stand Mixer',\n        'equipment.Automated Oven': 'Automated Oven',\n        'equipment.Industrial Mixer': 'Industrial Mixer',\n        'equipment.Conveyor Belt': 'Conveyor Belt',\n        'equipment.Display Counter': 'Display Counter',\n        'equipment.Prep Counter': 'Prep Counter',\n        // Recipe Names\n        'recipe.Chocolate Chip Cookies': 'Chocolate Chip Cookies',\n        'recipe.Vanilla Muffins': 'Vanilla Muffins',\n        'recipe.Simple Bread': 'Simple Bread',\n        'recipe.Cinnamon Rolls': 'Cinnamon Rolls',\n        'recipe.Sourdough Bread': 'Sourdough Bread',\n        'recipe.Chocolate Cake': 'Chocolate Cake',\n        'recipe.Apple Pie': 'Apple Pie',\n        'recipe.Croissants': 'Croissants',\n        // Missing UI Elements\n        'toolbar.menu': 'Menu',\n        'equipment.Work Counter': 'Work Counter',\n        'game.level': 'Level',\n        'game.xp': 'XP',\n        'game.current': 'Current',\n        'inventory.title': 'Inventory',\n        'inventory.quantity': 'Quantity',\n        'inventory.price_per_unit': 'per unit',\n        'orders.title': 'Orders',\n        'orders.new_order': 'New Order',\n        'orders.time': 'Time',\n        'orders.accept': 'Accept',\n        'orders.reject': 'Reject',\n        'quick_actions.title': 'Quick Actions',\n        'quick_actions.buy_ingredients': 'Buy Ingredients',\n        'quick_actions.view_recipes': 'View Recipes',\n        'quick_actions.equipment_shop': 'Equipment Shop',\n        'click_to_use': 'Click to use',\n        'currency.czk': 'Kč',\n        // Additional UI Elements\n        'game.title': 'Bake It Out',\n        'location.downtown_delights': 'Downtown Delights',\n        'toolbar.bakery': 'Bakery',\n        'toolbar.achievements': 'Achievements',\n        'toolbar.stars': 'Stars',\n        'toolbar.settings': 'Settings',\n        // Orders\n        'orders.accepted': 'Order Accepted',\n        // Cloud Save\n        'cloud.auth.login': 'Login',\n        'cloud.auth.register': 'Register',\n        'cloud.auth.login_title': 'Login to Cloud Save',\n        'cloud.auth.register_title': 'Create Cloud Save Account',\n        'cloud.auth.username': 'Username',\n        'cloud.auth.email': 'Email',\n        'cloud.auth.password': 'Password',\n        'cloud.auth.confirm_password': 'Confirm Password',\n        'cloud.auth.login_button': 'Login',\n        'cloud.auth.register_button': 'Create Account',\n        'cloud.auth.cancel': 'Cancel',\n        'cloud.auth.loading': 'Loading...',\n        'cloud.auth.username_required': 'Username is required',\n        'cloud.auth.username_too_short': 'Username must be at least 3 characters',\n        'cloud.auth.email_required': 'Email is required',\n        'cloud.auth.email_invalid': 'Please enter a valid email address',\n        'cloud.auth.password_required': 'Password is required',\n        'cloud.auth.password_too_short': 'Password must be at least 6 characters',\n        'cloud.auth.passwords_dont_match': 'Passwords do not match',\n        'cloud.auth.username_placeholder': 'Enter your username',\n        'cloud.auth.email_placeholder': 'Enter your email',\n        'cloud.auth.password_placeholder': 'Enter your password',\n        'cloud.auth.confirm_password_placeholder': 'Confirm your password',\n        'cloud.auth.no_account': \"Don't have an account?\",\n        'cloud.auth.have_account': 'Already have an account?',\n        'cloud.auth.register_link': 'Create one',\n        'cloud.auth.login_link': 'Login',\n        'cloud.save.auth_required': 'Cloud Save Account Required',\n        'cloud.save.auth_description': 'You need to login or create an account to use cloud saves.',\n        'cloud.save.save_title': 'Save to Cloud',\n        'cloud.save.load_title': 'Load from Cloud',\n        'cloud.save.logged_in_as': 'Logged in as',\n        'cloud.save.syncing': 'Syncing...',\n        'cloud.save.sync_success': 'Synced',\n        'cloud.save.sync_error': 'Sync Error',\n        'cloud.save.sync_idle': 'Ready',\n        'cloud.save.last_sync': 'Last sync',\n        'cloud.save.save_name': 'Save Name',\n        'cloud.save.save_name_placeholder': 'Enter a name for your save',\n        'cloud.save.your_saves': 'Your Cloud Saves',\n        'cloud.save.refreshing': 'Refreshing...',\n        'cloud.save.refresh': 'Refresh',\n        'cloud.save.loading_saves': 'Loading saves...',\n        'cloud.save.no_saves': 'No cloud saves found',\n        'cloud.save.level': 'Level',\n        'cloud.save.delete': 'Delete',\n        'cloud.save.confirm_delete': 'Are you sure you want to delete this save?',\n        'cloud.save.cancel': 'Cancel',\n        'cloud.save.saving': 'Saving...',\n        'cloud.save.save_button': 'Save to Cloud',\n        'cloud.save.loading': 'Loading...',\n        'cloud.save.load_button': 'Load Game',\n        // Save/Load Modal\n        'saveLoad.local_saves': 'Local Saves',\n        'saveLoad.file_saves': 'File Saves',\n        'saveLoad.cloud_saves': 'Cloud Saves',\n        // Dashboard\n        'dashboard.title': 'Server Dashboard',\n        'dashboard.button': 'Dashboard',\n        'dashboard.tooltip': 'Access server dashboard',\n        'dashboard.login_required': 'Please login to access the dashboard',\n        'dashboard.logged_in_as': 'Logged in as',\n        'dashboard.access_title': 'Access Server Dashboard',\n        'dashboard.access_description': 'Monitor server statistics, manage users, and view game analytics',\n        'dashboard.feature.users': 'User Management',\n        'dashboard.feature.rooms': 'Game Rooms',\n        'dashboard.feature.saves': 'Cloud Saves',\n        'dashboard.feature.analytics': 'Analytics',\n        'dashboard.open': 'Open Dashboard',\n        'dashboard.opening': 'Opening...',\n        'dashboard.cancel': 'Cancel',\n        'dashboard.new_tab_notice': 'Dashboard will open in a new tab',\n        'dashboard.open_failed': 'Failed to open dashboard',\n        // Recipes\n        'recipe.chocolate_chip_cookies': 'Chocolate Chip Cookies',\n        'recipe.vanilla_muffins': 'Vanilla Muffins',\n        'recipe.cinnamon_rolls': 'Cinnamon Rolls',\n        'recipe.chocolate_brownies': 'Chocolate Brownies',\n        'recipe.blueberry_pie': 'Blueberry Pie',\n        'recipe.sourdough_bread': 'Sourdough Bread',\n        // Ingredients\n        'ingredient.flour': 'Flour',\n        'ingredient.sugar': 'Sugar',\n        'ingredient.butter': 'Butter',\n        'ingredient.chocolate_chips': 'Chocolate Chips',\n        'ingredient.eggs': 'Eggs',\n        'ingredient.vanilla': 'Vanilla',\n        'ingredient.cinnamon': 'Cinnamon',\n        'ingredient.blueberries': 'Blueberries',\n        'ingredient.salt': 'Salt',\n        // Recipe Categories\n        'category.cookies': 'Cookies',\n        'category.cakes': 'Cakes',\n        'category.pastries': 'Pastries',\n        'category.pies': 'Pies',\n        'category.bread': 'Bread',\n        // Equipment Names\n        'equipment.oven': 'Oven',\n        'equipment.mixer': 'Mixer',\n        'equipment.work_counter': 'Work Counter',\n        'equipment.display_case': 'Display Case',\n        'equipment.cash_register': 'Cash Register'\n    },\n    cs: {\n        // Main game\n        'game.title': 'Bake It Out',\n        'game.subtitle': 'Ovládněte umění řízení pekárny v této poutavé multiplayerové hře. Plňte objednávky, odemykejte recepty, automatizujte procesy a soutěžte s přáteli!',\n        'game.play': '🎮 Začít hrát',\n        'game.singlePlayer': '🎮 Jeden hráč',\n        'game.singlePlayerDesc': 'Hrajte sólo a zdokonalte své pekařské dovednosti',\n        'game.multiplayer': '👥 Multiplayer',\n        'game.multiplayerDesc': 'Hrajte s přáteli v kooperativních nebo soutěžních režimech',\n        'game.english': '🇺🇸 English',\n        'game.czech': '🇨🇿 Čeština',\n        'game.home': '🏠 Domů',\n        'game.close': '✕ Zavřít',\n        'game.continue': '🚀 Pokračovat ve hře',\n        // Menu options\n        'menu.singlePlayer': 'Jeden hráč',\n        'menu.multiplayer': 'Multiplayer',\n        'menu.settings': 'Nastavení',\n        'menu.credits': 'Titulky',\n        'menu.exit': 'Ukončit',\n        // Credits\n        'credits.title': 'O hře Bake It Out',\n        'credits.subtitle': 'Informace o hře a titulky',\n        'credits.description': 'Multiplayerová hra na správu pekárny s real-time spoluprací a podporou lokalizace.',\n        'credits.version': 'Verze',\n        'credits.release': 'Datum vydání',\n        'credits.releaseDate': 'Leden 2025',\n        'credits.platform': 'Platforma',\n        'credits.platforms': 'Windows, macOS, Linux',\n        'credits.contact': 'Kontakt',\n        'credits.facebook': 'Facebook',\n        'credits.close': 'Zavřít',\n        'credits.features': 'Funkce',\n        'credits.multiplayer': 'Podpora multiplayeru',\n        'credits.multiplayerDesc': 'Real-time spolupráce s přáteli v kooperativních nebo soutěžních režimech.',\n        'credits.localization': 'Lokalizace',\n        'credits.localizationDesc': 'Plná podpora angličtiny a češtiny s jednoduchým přepínáním.',\n        'credits.progression': 'Systém postupu',\n        'credits.progressionDesc': 'Vylepšujte svou pekárnu, odemykejte recepty a ovládněte automatizaci.',\n        'credits.automation': 'Automatizace',\n        'credits.automationDesc': 'Pokročilé automatizační systémy pro optimalizaci provozu pekárny.',\n        'credits.technology': 'Technologický stack',\n        'credits.team': 'Vývojový tým',\n        'credits.developedBy': 'Vyvinuto týmem',\n        'credits.teamDesc': 'Vytvořeno s láskou vývojovým týmem Bake It Out.',\n        'credits.thanks': 'Speciální poděkování',\n        'credits.thanksPlayers': 'Všem našim úžasným hráčům a beta testerům',\n        'credits.thanksTranslators': 'Komunitním překladatelům za podporu lokalizace',\n        'credits.thanksOpenSource': 'Open source komunitě za neuvěřitelné nástroje a knihovny',\n        'credits.thanksBakers': 'Skutečným pekařům, kteří inspirovali tuto hru',\n        'credits.website': 'Webové stránky',\n        'credits.support': 'Podpora',\n        'credits.github': 'GitHub',\n        'credits.discord': 'Discord',\n        // Game Views\n        'game.view.traditional': 'Tradiční pohled',\n        'game.view.layout': 'Rozložení pekárny',\n        'game.view.dining': 'Jídelna',\n        'game.view.customers': 'Správce zákazníků',\n        // Bakery Layout\n        'bakery.layout.title': 'Rozložení pekárny',\n        'bakery.kitchen': 'Kuchyně',\n        'bakery.dining': 'Jídelní oblast',\n        'bakery.counter': 'Servisní pult',\n        'bakery.baking.area': 'Oblast pečení',\n        'bakery.prep.area': 'Přípravná oblast',\n        'bakery.automation.area': 'Automatizace',\n        'bakery.kitchen.stats': 'Statistiky kuchyně',\n        'bakery.active.equipment': 'Aktivní vybavení',\n        'bakery.automated.equipment': 'Automatizované vybavení',\n        'bakery.efficiency': 'Efektivita',\n        'bakery.dining.stats': 'Statistiky jídelny',\n        'bakery.current.customers': 'Současní zákazníci',\n        'bakery.waiting.customers': 'Čekající',\n        'bakery.eating.customers': 'Jedí',\n        'bakery.avg.satisfaction': 'Průměrná spokojenost',\n        'bakery.service.counter': 'Servisní pult',\n        'bakery.display.case': 'Vitrina',\n        'bakery.order.queue': 'Fronta objednávek',\n        'bakery.last.updated': 'Naposledy aktualizováno',\n        // Dining Room\n        'dining.room.title': 'Jídelna',\n        'dining.room.subtitle': 'Sledujte, jak si vaši zákazníci užívají jídlo',\n        'dining.occupied.tables': 'Obsazené stoly',\n        'dining.ambient.sounds': 'Přepnout okolní zvuky',\n        'dining.service.counter': 'Servis',\n        'dining.customer.status': 'Stav zákazníků',\n        'dining.enjoying': 'Užívá si',\n        'dining.no.customers': 'Momentálně nejsou žádní zákazníci',\n        'dining.waiting.for.customers': 'Dokončete objednávky, abyste viděli zákazníky jíst',\n        'dining.ambient.playing': 'Hrají okolní zvuky',\n        // Customer Manager\n        'customers.manager.title': 'Správce zákazníků',\n        'customers.manager.subtitle': 'Sledujte a obsluhujte své zákazníky',\n        'customers.current.list': 'Současní zákazníci',\n        'customers.table': 'Stůl',\n        'customers.patience': 'Trpělivost',\n        'customers.no.customers': 'Momentálně žádní zákazníci',\n        'customers.waiting.for.orders': 'Čekání na nové objednávky...',\n        'customers.order.details': 'Detaily objednávky',\n        'customers.total': 'Celkem',\n        'customers.info': 'Informace o zákazníkovi',\n        'customers.mood': 'Nálada',\n        'customers.status': 'Stav',\n        'customers.preferences': 'Preference',\n        'customers.select.customer': 'Vyberte zákazníka',\n        'customers.select.to.view.details': 'Vyberte zákazníka pro zobrazení detailů',\n        'customers.serve.order': 'Podávat objednávku',\n        'menu.newGame': 'Nová hra',\n        'menu.continueGame': 'Pokračovat ve hře',\n        'menu.loadGame': 'Načíst hru',\n        'menu.selectLanguage': 'Vybrat jazyk',\n        'menu.about': 'O hře',\n        'menu.help': 'Nápověda',\n        'menu.quit': 'Ukončit',\n        // Features\n        'features.manage.title': 'Spravujte svou pekárnu',\n        'features.manage.description': 'Přijímejte objednávky, pečte lahodné výrobky a obsluhujte spokojené zákazníky',\n        'features.levelup.title': 'Postupujte a automatizujte',\n        'features.levelup.description': 'Odemykejte nové recepty, kupujte vybavení a automatizujte své procesy',\n        'features.multiplayer.title': 'Hrajte společně',\n        'features.multiplayer.description': 'Kooperativní a soutěžní multiplayerové režimy s přáteli',\n        'status.development': '🚧 Hra ve vývoji - Fáze 5: Vícevrstvá podpora! 🚧',\n        // Game interface\n        'ui.level': 'Úroveň {{level}}',\n        'ui.money': '{{amount}} Kč',\n        'ui.experience': 'XP: {{current}}/{{max}}',\n        'ui.skillPoints': 'SP: {{points}}',\n        'ui.achievements': '🏆 Úspěchy',\n        'ui.skills': '🌟 Dovednosti',\n        'ui.automation': '🤖 Automatizace',\n        // Kitchen\n        'kitchen.title': '🏪 Kuchyně',\n        'kitchen.clickToUse': 'Klikněte pro použití',\n        'kitchen.making': 'Připravuje: {{recipe}}',\n        'kitchen.timeRemaining': 'Čas: {{time}}',\n        // Inventory\n        'inventory.title': '📦 Sklad',\n        'inventory.quantity': 'Množství: {{qty}}',\n        'inventory.cost': '{{cost}} Kč za kus',\n        // Orders\n        'orders.title': '📋 Objednávky',\n        'orders.newOrder': '+ Nová objednávka',\n        'orders.accept': 'Přijmout',\n        'orders.decline': 'Odmítnout',\n        'orders.complete': 'Dokončit',\n        'orders.inProgress': 'Probíhá',\n        'orders.timeLimit': 'Čas: {{time}}',\n        'orders.reward': '{{amount}} Kč',\n        'orders.customer': 'Zákazník: {{name}}',\n        // Quick Actions\n        'actions.title': '⚡ Rychlé akce',\n        'actions.buyIngredients': '🛒 Koupit suroviny',\n        'actions.viewRecipes': '📖 Zobrazit recepty',\n        'actions.equipmentShop': '🔧 Obchod s vybavením',\n        // Modals\n        'modal.recipes.title': '📖 Kniha receptů',\n        'modal.shop.title': '🛒 Obchod se surovinami',\n        'modal.baking.title': '🔥 {{equipment}} - Vyberte recept',\n        'modal.achievements.title': '🏆 Úspěchy',\n        'modal.skills.title': '🌟 Strom dovedností',\n        'modal.automation.title': '🤖 Ovládání automatizace',\n        'modal.equipmentShop.title': '🏪 Obchod s vybavením',\n        'modal.settings.title': '⚙️ Nastavení',\n        'modal.bakeries.title': '🏪 Správce pekáren',\n        'modal.levelUp.title': 'Postup na vyšší úroveň!',\n        'modal.levelUp.subtitle': 'Dosáhli jste úrovně {{level}}!',\n        // Recipe Modal\n        'recipes.all': 'Vše',\n        'recipes.cookies': 'Sušenky',\n        'recipes.cakes': 'Dorty',\n        'recipes.bread': 'Chléb',\n        'recipes.pastries': 'Pečivo',\n        'recipes.ingredients': 'Suroviny:',\n        'recipes.difficulty': 'Obtížnost:',\n        'recipes.time': 'Čas:',\n        'recipes.canCraft': '✅ Lze vyrobit',\n        'recipes.unlockLevel': 'Odemčeno na úrovni {{level}}',\n        'recipes.noRecipes': 'V této kategorii nejsou k dispozici žádné recepty.',\n        'recipes.levelUpToUnlock': 'Postupte na vyšší úroveň pro odemčení dalších receptů!',\n        // Shop Modal\n        'shop.currentStock': 'Aktuální zásoba: {{quantity}}',\n        'shop.buy': 'Koupit',\n        'shop.tooExpensive': 'Příliš drahé',\n        'shop.tips.title': '💡 Tipy pro nakupování',\n        'shop.tips.bulk': '• Kupujte suroviny ve velkém množství pro úsporu času',\n        'shop.tips.stock': '• Sledujte úroveň svých zásob',\n        'shop.tips.rare': '• Některé recepty vyžadují vzácné suroviny',\n        'shop.tips.prices': '• Ceny se mohou lišit podle dostupnosti',\n        // Baking Modal\n        'baking.selectRecipe': 'Vyberte recept',\n        'baking.noRecipes': 'Žádné recepty k dispozici',\n        'baking.noIngredients': 'Nemáte dostatek surovin pro výrobu jakéhokoli receptu.',\n        'baking.buyIngredients': 'Koupit suroviny',\n        'baking.startBaking': '🔥 Začít péct',\n        'baking.instructions': '📋 Pokyny pro pečení {{recipe}}',\n        'baking.expectedReward': 'Očekávaná odměna: {{amount}} Kč',\n        'baking.makesSure': 'Ujistěte se, že máte všechny suroviny před začátkem!',\n        'baking.inProgress': 'Pečení probíhá...',\n        'baking.completed': 'Pečení dokončeno!',\n        'baking.cancelled': 'Pečení zrušeno',\n        'baking.timeRemaining': 'Zbývající čas: {{time}}',\n        'baking.clickToCollect': 'Klikněte pro vyzvednutí',\n        // Achievements Modal\n        'achievements.completed': '{{completed}} z {{total}} úspěchů dokončeno',\n        'achievements.overallProgress': 'Celkový pokrok',\n        'achievements.progress': 'Pokrok',\n        'achievements.reward': 'Odměna:',\n        'achievements.noAchievements': 'V této kategorii nejsou žádné úspěchy.',\n        // Skills Modal\n        'skills.availablePoints': 'Dostupné body dovedností: {{points}}',\n        'skills.efficiency': 'Efektivita',\n        'skills.automation': 'Automatizace',\n        'skills.quality': 'Kvalita',\n        'skills.business': 'Podnikání',\n        'skills.effects': 'Efekty:',\n        'skills.requires': 'Vyžaduje: {{requirements}}',\n        'skills.requiresLevel': 'Vyžaduje úroveň {{level}}',\n        'skills.maxed': '✅ Maximální',\n        'skills.upgrade': '⬆️ Vylepšit ({{cost}} SP)',\n        'skills.locked': '🔒 Uzamčeno',\n        'skills.noSkills': 'V této kategorii nejsou žádné dovednosti.',\n        'skills.tips.title': '💡 Tipy pro dovednosti',\n        'skills.tips.earnPoints': '• Získávejte body dovedností postupem na vyšší úroveň (1 bod každé 2 úrovně)',\n        'skills.tips.prerequisites': '• Některé dovednosti vyžadují nejprve odemčení jiných dovedností',\n        'skills.tips.playstyle': '• Zaměřte se na dovednosti, které odpovídají vašemu stylu hry',\n        'skills.tips.efficiency': '• Dovednosti efektivity pomáhají se správou zdrojů',\n        // Automation Modal\n        'automation.masterControl': '🎛️ Hlavní ovládání',\n        'automation.enableAutomation': 'Povolit automatizaci',\n        'automation.autoStart': 'Automatické spuštění vybavení',\n        'automation.priorityMode': '🎯 Režim priority',\n        'automation.efficiency': 'Efektivita (objednávky první)',\n        'automation.profit': 'Zisk (nejvyšší hodnota)',\n        'automation.speed': 'Rychlost (nejrychlejší recepty)',\n        'automation.priorityDescription': 'Jak automatizace vybírá, co péct',\n        'automation.performance': '⚡ Výkon',\n        'automation.maxJobs': 'Max současných úloh: {{jobs}}',\n        'automation.safety': '🛡️ Bezpečnost',\n        'automation.stopWhenLow': 'Zastavit, když suroviny klesnou pod: {{threshold}}',\n        'automation.upgrades': '💡 Vylepšení automatizace',\n        'automation.upgradesDescription': 'Vylepšete efektivitu, rychlost a inteligenci vaší automatizace.',\n        'automation.purchase': 'Koupit',\n        'automation.noUpgrades': 'Na vaší současné úrovni nejsou k dispozici žádná vylepšení.',\n        'automation.levelUpForUpgrades': 'Postupte na vyšší úroveň pro odemčení dalších vylepšení automatizace!',\n        'automation.automatedEquipment': 'Automatizované vybavení',\n        'automation.activeUpgrades': 'Aktivní vylepšení',\n        'automation.automationStatus': 'Stav automatizace',\n        'automation.equipmentStatus': '🏭 Stav vybavení',\n        'automation.running': 'Běží',\n        'automation.idle': 'Nečinné',\n        'automation.noAutomatedEquipment': 'Žádné automatizované vybavení k dispozici.',\n        'automation.purchaseAutoEquipment': 'Kupte si auto-vybavení z obchodu pro začátek!',\n        // Equipment Shop Modal\n        'equipmentShop.upgradeYourBakery': 'Vylepšete svou pekárnu profesionálním vybavením',\n        'equipmentShop.basic': 'Základní',\n        'equipmentShop.automated': 'Automatizované',\n        'equipmentShop.advanced': 'Pokročilé',\n        'equipmentShop.efficiency': 'Efektivita: {{efficiency}}x',\n        'equipmentShop.automation': 'Automatizace:',\n        'equipmentShop.unlockLevel': 'Úroveň odemčení: {{level}}',\n        'equipmentShop.purchase': '💰 Koupit',\n        'equipmentShop.noEquipment': 'V této kategorii není k dispozici žádné vybavení.',\n        'equipmentShop.levelUpForEquipment': 'Postupte na vyšší úroveň pro odemčení dalšího vybavení!',\n        'equipmentShop.tips.title': '💡 Tipy pro vybavení',\n        'equipmentShop.tips.automated': '• Automatizované vybavení může běžet bez vašeho dohledu',\n        'equipmentShop.tips.efficiency': '• Vyšší efektivita znamená rychlejší výrobu a lepší kvalitu',\n        'equipmentShop.tips.conveyor': '• Dopravní pásy spojují vybavení pro bezproblémový pracovní tok',\n        'equipmentShop.tips.advanced': '• Pokročilé vybavení se odemyká na vyšších úrovních',\n        // Level Up Modal\n        'levelUp.levelRewards': '🎁 Odměny za úroveň',\n        'levelUp.whatsNext': '💡 Co dál?',\n        'levelUp.checkRecipes': '• Podívejte se na nové recepty ve své knize receptů',\n        'levelUp.visitShop': '• Navštivte obchod pro nové vybavení',\n        'levelUp.challengingOrders': '• Přijměte náročnější objednávky',\n        'levelUp.investSkills': '• Investujte do vylepšení dovedností',\n        // Settings Modal\n        'settings.title': '⚙️ Nastavení',\n        'settings.general': 'Obecné',\n        'settings.audio': 'Zvuk',\n        'settings.graphics': 'Grafika',\n        'settings.save': 'Uložení a data',\n        'settings.language': '🌍 Jazyk',\n        'settings.gameplay': '🎮 Hratelnost',\n        'settings.notifications': 'Povolit oznámení',\n        'settings.tutorials': 'Zobrazit návody',\n        'settings.animationSpeed': 'Rychlost animace',\n        'settings.sound': 'Zvukové efekty',\n        'settings.music': 'Hudba na pozadí',\n        'settings.quality': '🎨 Kvalita grafiky',\n        'settings.autoSave': '💾 Automatické ukládání',\n        'settings.enableAutoSave': 'Povolit automatické ukládání',\n        'settings.dataManagement': '📁 Správa dat',\n        'settings.exportSave': '📤 Exportovat uložení',\n        'settings.importSave': '📥 Importovat uložení',\n        'settings.cloudSync': '☁️ Cloudová synchronizace',\n        'settings.cloudSyncDescription': 'Cloudová synchronizace vám umožňuje uložit pokrok online a hrát na více zařízeních.',\n        // Cloud Save Management\n        'cloud.save.manage_saves': 'Spravovat cloudová uložení',\n        'cloud.save.login': 'Přihlásit / Registrovat',\n        'cloud.save.login_required': 'Pro použití cloudových uložení se prosím přihlaste',\n        // Bakery Manager Modal\n        'bakeries.title': '🏪 Správce pekáren',\n        'bakeries.subtitle': 'Spravujte své pekárenské impérium',\n        'bakeries.owned': 'Moje pekárny',\n        'bakeries.available': 'Dostupné',\n        'bakeries.current': 'Aktuální',\n        'bakeries.level': 'Úroveň',\n        'bakeries.specialization': 'Specializace',\n        'bakeries.equipment': 'Vybavení',\n        'bakeries.orders': 'Aktivní objednávky',\n        'bakeries.switchTo': 'Přepnout na',\n        'bakeries.noOwned': 'Ještě nevlastníte žádné pekárny.',\n        'bakeries.purchase': '💰 Koupit',\n        'bakeries.tooExpensive': '💸 Příliš drahé',\n        'bakeries.allOwned': 'Vlastníte všechny dostupné pekárny!',\n        'bakeries.tips': '💡 Tipy pro pekárny',\n        'bakeries.tip1': 'Každá pekárna se specializuje na různé produkty pro bonusovou efektivitu',\n        'bakeries.tip2': 'Přepínejte mezi pekárnami pro správu více lokalit',\n        'bakeries.tip3': 'Specializované pekárny přitahují zákazníky hledající konkrétní položky',\n        'bakeries.tip4': 'Vylepšujte každou pekárnu nezávisle pro maximální zisk',\n        // Notifications\n        'notifications.orderAccepted': 'Objednávka přijata',\n        'notifications.orderAcceptedMessage': 'Přijali jste novou objednávku!',\n        'notifications.orderCompleted': 'Objednávka dokončena!',\n        'notifications.orderCompletedMessage': 'Získali jste {{reward}} Kč a zkušenosti!',\n        'notifications.orderDeclined': 'Objednávka odmítnuta',\n        'notifications.orderDeclinedMessage': 'Objednávka byla odstraněna z vaší fronty.',\n        'notifications.bakeryPurchased': 'Pekárna zakoupena!',\n        'notifications.bakeryPurchasedMessage': 'Nyní vlastníte {{name}}!',\n        'notifications.bakerySwitched': 'Pekárna přepnuta',\n        'notifications.bakerySwitchedMessage': 'Přepnuto na {{name}}',\n        // Common buttons and actions\n        'common.accept': 'Přijmout',\n        'common.decline': 'Odmítnout',\n        'common.complete': 'Dokončit',\n        'common.purchase': 'Koupit',\n        'common.upgrade': 'Vylepšit',\n        'common.cancel': 'Zrušit',\n        'common.confirm': 'Potvrdit',\n        'common.save': 'Uložit',\n        'common.load': 'Načíst',\n        'common.delete': 'Smazat',\n        'common.edit': 'Upravit',\n        'common.back': 'Zpět',\n        'common.next': 'Další',\n        'common.previous': 'Předchozí',\n        'common.yes': 'Ano',\n        'common.no': 'Ne',\n        'common.create': 'Vytvořit',\n        'common.join': 'Připojit se',\n        'common.leave': 'Odejít',\n        'common.start': 'Začít',\n        'common.ready': 'Připraven',\n        'common.notReady': 'Nepřipraven',\n        'common.send': 'Odeslat',\n        'common.refresh': 'Obnovit',\n        'common.retry': 'Zkusit znovu',\n        'common.reset': 'Resetovat',\n        'common.clear': 'Vymazat',\n        'common.apply': 'Použít',\n        'common.warning': 'Varování',\n        'common.info': 'Informace',\n        'common.success': 'Úspěch',\n        'common.error': 'Chyba',\n        // Save/Load System\n        'saveLoad.saveDesc': 'Vyberte slot pro uložení vašeho postupu',\n        'saveLoad.loadDesc': 'Vyberte soubor uložení k načtení',\n        'saveLoad.saveName': 'Název uložení',\n        'saveLoad.emptySlot': 'Prázdný slot',\n        'saveLoad.selectedSaveSlot': 'Vybrán: Slot {{slot}}',\n        'saveLoad.selectedLoadSlot': 'Vybrán: Slot {{slot}}',\n        'saveLoad.confirmOverwrite': 'Přepsat uložení?',\n        'saveLoad.overwriteWarning': 'Toto přepíše existující uložení. Tuto akci nelze vrátit zpět.',\n        'saveLoad.overwrite': 'Přepsat',\n        'saveLoad.fileSlots': 'Souborové sloty',\n        'saveLoad.gameSlots': 'Herní sloty',\n        'saveLoad.exportSave': 'Exportovat uložení',\n        'saveLoad.importSave': 'Importovat uložení',\n        'saveLoad.deleteConfirm': 'Smazat uložení?',\n        'saveLoad.deleteWarning': 'Toto trvale smaže tento soubor uložení. Tuto akci nelze vrátit zpět.',\n        'saveLoad.delete': 'Smazat',\n        // Game Menu\n        'gameMenu.title': 'Herní menu',\n        'gameMenu.subtitle': 'Spravujte svou hru',\n        'gameMenu.resume': 'Pokračovat ve hře',\n        'gameMenu.resumeDesc': 'Pokračovat v hraní',\n        'gameMenu.save': 'Uložit hru',\n        'gameMenu.saveDesc': 'Uložit váš postup',\n        'gameMenu.load': 'Načíst hru',\n        'gameMenu.loadDesc': 'Načíst uložený postup',\n        'gameMenu.settings': 'Nastavení',\n        'gameMenu.settingsDesc': 'Herní předvolby',\n        'gameMenu.mainMenu': 'Hlavní menu',\n        'gameMenu.mainMenuDesc': 'Návrat do hlavního menu',\n        'gameMenu.exit': 'Ukončit hru',\n        'gameMenu.exitDesc': 'Zavřít aplikaci',\n        'gameMenu.tip': 'Stiskněte ESC pro otevření tohoto menu kdykoli',\n        // Discord Rich Presence\n        'settings.discord': 'Discord',\n        'settings.discordRichPresence': 'Discord Rich Presence',\n        'settings.discordDescription': 'Zobrazit váš aktuální herní stav a aktivitu v Discordu.',\n        'settings.enableDiscordRPC': 'Povolit Discord Rich Presence',\n        'settings.discordConnected': '✅ Připojeno k Discordu',\n        'settings.discordDisconnected': '❌ Nepřipojeno k Discordu',\n        'settings.discordInfo': 'Co je Discord Rich Presence?',\n        'settings.discordInfoDesc1': 'Discord Rich Presence ukazuje vašim přátelům, co děláte v Bake It Out:',\n        'settings.discordFeature1': 'Vaši aktuální úroveň a peníze',\n        'settings.discordFeature2': 'Co právě pečete',\n        'settings.discordFeature3': 'Informace o multiplayer místnosti',\n        'settings.discordFeature4': 'Jak dlouho hrajete',\n        'settings.discordInfoDesc2': 'Vaši přátelé se mohou připojit k vašim multiplayer hrám přímo z Discordu!',\n        'settings.discordTroubleshooting': 'Discord není připojen',\n        'settings.discordTrouble1': 'Ujistěte se, že Discord běží na vašem počítači.',\n        'settings.discordTrouble2': 'Discord Rich Presence funguje pouze v desktopové verzi hry.',\n        'settings.discordTrouble3': 'Zkuste restartovat Discord i hru, pokud se připojení nezdaří.',\n        'settings.discordPrivacy': 'Informace o soukromí',\n        'settings.discordPrivacyDesc1': 'Discord Rich Presence sdílí pouze:',\n        'settings.discordPrivacy1': 'Vaši aktuální herní aktivitu (veřejné)',\n        'settings.discordPrivacy2': 'Vaši úroveň hráče a postup (veřejné)',\n        'settings.discordPrivacy3': 'Kódy multiplayer místností (pro připojení)',\n        'settings.discordPrivacyDesc2': 'Žádné osobní informace nebo uložená data nejsou sdílena s Discordem.',\n        'settings.discordStatus': 'Stav Discordu',\n        'settings.discordInitializing': '🔄 Inicializace Discord RPC...',\n        'settings.discordRetrying': '🔄 Opakování připojení...',\n        'settings.discordUnavailable': '❌ Discord není dostupný',\n        'settings.discordDesktopOnly': 'ℹ️ Discord RPC dostupný pouze v desktopové verzi',\n        // Error messages and status\n        'error.general': 'Došlo k chybě',\n        'error.saveLoad': 'Nepodařilo se uložit/načíst hru',\n        'error.connection': 'Chyba připojení',\n        'error.fileNotFound': 'Soubor nenalezen',\n        'error.invalidData': 'Neplatný formát dat',\n        'error.permissionDenied': 'Přístup odepřen',\n        'status.loading': 'Načítání...',\n        'status.saving': 'Ukládání...',\n        'status.connecting': 'Připojování...',\n        'status.ready': 'Připraven',\n        'status.success': 'Úspěch!',\n        'status.failed': 'Neúspěšné',\n        'status.offline': 'Offline',\n        'status.online': 'Online',\n        // UI Elements\n        'ui.placeholder': 'Zadejte text...',\n        'ui.search': 'Hledat',\n        'ui.filter': 'Filtrovat',\n        'ui.sort': 'Seřadit',\n        'ui.ascending': 'Vzestupně',\n        'ui.descending': 'Sestupně',\n        'ui.selectAll': 'Vybrat vše',\n        'ui.deselectAll': 'Zrušit výběr',\n        'ui.noResults': 'Žádné výsledky',\n        'ui.noData': 'Žádná data k dispozici',\n        'ui.loading': 'Načítání...',\n        'ui.saving': 'Ukládání...',\n        'ui.saved': 'Uloženo!',\n        'ui.failed': 'Neúspěšné',\n        'ui.retry': 'Zkusit znovu',\n        'ui.back': 'Zpět',\n        'ui.forward': 'Vpřed',\n        'ui.home': 'Domů',\n        'ui.menu': 'Menu',\n        'ui.options': 'Možnosti',\n        'ui.preferences': 'Předvolby',\n        // Multiplayer\n        'multiplayer.lobby': '👥 Multiplayerová lobby',\n        'multiplayer.connected': '🟢 Připojeno',\n        'multiplayer.disconnected': '🔴 Odpojeno',\n        'multiplayer.createRoom': 'Vytvořit místnost',\n        'multiplayer.joinRoom': 'Připojit se k místnosti',\n        'multiplayer.room': 'Místnost',\n        'multiplayer.yourName': 'Vaše jméno',\n        'multiplayer.enterName': 'Zadejte své jméno',\n        'multiplayer.roomName': 'Název místnosti',\n        'multiplayer.enterRoomName': 'Zadejte název místnosti',\n        'multiplayer.gameMode': 'Herní režim',\n        'multiplayer.cooperative': '🤝 Kooperativní',\n        'multiplayer.competitive': '⚔️ Soutěžní',\n        'multiplayer.maxPlayers': 'Max hráčů: {{count}}',\n        'multiplayer.roomId': 'ID místnosti',\n        'multiplayer.enterRoomId': 'Zadejte ID místnosti',\n        'multiplayer.players': 'Hráči ({{count}})',\n        'multiplayer.host': 'HOSTITEL',\n        'multiplayer.level': 'Úroveň {{level}}',\n        'multiplayer.chat': 'Chat',\n        'multiplayer.typeMessage': 'Napište zprávu...',\n        'multiplayer.gameTime': 'Herní čas: {{time}}',\n        'multiplayer.teamStats': '📊 Týmové statistiky',\n        'multiplayer.ordersCompleted': 'Dokončené objednávky:',\n        'multiplayer.totalRevenue': 'Celkový příjem:',\n        'multiplayer.teamExperience': 'Týmové zkušenosti:',\n        'multiplayer.sharedKitchen': '🏪 Sdílená kuchyně',\n        'multiplayer.sharedOrders': '📋 Sdílené objednávky',\n        'multiplayer.sharedInventory': '📦 Sdílený sklad',\n        'multiplayer.contribution': 'Příspěvek:',\n        'multiplayer.online': '🟢 Online',\n        'multiplayer.status': 'Stav:',\n        'multiplayer.you': '(Vy)',\n        'multiplayer.teamChat': '💬 Týmový chat',\n        'multiplayer.chatPlaceholder': 'Zde se zobrazí zprávy chatu...',\n        // Multiplayer game modes\n        'multiplayer.mode.cooperative.description': '🤝 Kooperativní režim: Spolupracujte na dokončování objednávek a rozvoji sdílené pekárny!',\n        'multiplayer.mode.competitive.description': '⚔️ Soutěžní režim: Soutěžte s ostatními hráči o dokončení nejvíce objednávek!',\n        // Multiplayer game interface\n        'multiplayer.game.title': '🎮 Multiplayerová hra - {{roomName}}',\n        'multiplayer.game.mode': 'Režim: {{mode}}',\n        'multiplayer.game.playersCount': 'Hráči: {{count}}',\n        'multiplayer.game.playing': '🟢 Hraje se',\n        'multiplayer.game.leaveGame': '🚪 Opustit hru',\n        'multiplayer.game.tabs.game': 'Hra',\n        'multiplayer.game.tabs.players': 'Hráči',\n        'multiplayer.game.tabs.chat': 'Chat',\n        // Room creation and joining\n        'multiplayer.create.title': '🏗️ Vytvořit místnost',\n        'multiplayer.join.title': '🚪 Připojit se k místnosti',\n        'multiplayer.room.info': 'Režim: {{mode}} • Hráči: {{current}}/{{max}}',\n        'multiplayer.room.readyUp': '✅ Připraven',\n        'multiplayer.room.notReady': '⏳ Nepřipraven',\n        'multiplayer.room.startGame': '🚀 Začít hru',\n        'multiplayer.room.leaveRoom': '🚪 Opustit',\n        // Connection states\n        'multiplayer.connection.connecting': 'Připojování...',\n        'multiplayer.connection.reconnecting': 'Znovu se připojuje...',\n        'multiplayer.connection.failed': 'Připojení selhalo',\n        'multiplayer.connection.error': '⚠️ {{error}}',\n        // System messages\n        'multiplayer.system.playerJoined': '{{name}} se připojil do místnosti',\n        'multiplayer.system.playerLeft': '{{name}} opustil místnost',\n        'multiplayer.system.gameStarted': 'Hra začala!',\n        'multiplayer.system.gameEnded': 'Hra skončila!',\n        'multiplayer.system.roomCreated': 'Místnost byla úspěšně vytvořena',\n        'multiplayer.system.roomJoined': 'Úspěšně jste se připojili do místnosti',\n        // Ingredients\n        'ingredient.Flour': 'Mouka',\n        'ingredient.Sugar': 'Cukr',\n        'ingredient.Eggs': 'Vejce',\n        'ingredient.Butter': 'Máslo',\n        'ingredient.Milk': 'Mléko',\n        'ingredient.Vanilla Extract': 'Vanilkový extrakt',\n        'ingredient.Vanilla': 'Vanilka',\n        'ingredient.Chocolate Chips': 'Čokoládové kousky',\n        'ingredient.Baking Powder': 'Prášek do pečiva',\n        'ingredient.Salt': 'Sůl',\n        'ingredient.Cinnamon': 'Skořice',\n        'ingredient.Nuts': 'Ořechy',\n        'ingredient.Cream Cheese': 'Krémový sýr',\n        'ingredient.Honey': 'Med',\n        'ingredient.Cocoa Powder': 'Kakaový prášek',\n        'ingredient.Yeast': 'Droždí',\n        // Equipment Names\n        'equipment.Basic Oven': 'Základní trouba',\n        'equipment.Hand Mixer': 'Ruční mixér',\n        'equipment.Professional Oven': 'Profesionální trouba',\n        'equipment.Stand Mixer': 'Stojanový mixér',\n        'equipment.Automated Oven': 'Automatická trouba',\n        'equipment.Industrial Mixer': 'Průmyslový mixér',\n        'equipment.Conveyor Belt': 'Dopravní pás',\n        'equipment.Display Counter': 'Výstavní pult',\n        'equipment.Prep Counter': 'Přípravný pult',\n        // Recipe Names\n        'recipe.Chocolate Chip Cookies': 'Sušenky s čokoládou',\n        'recipe.Vanilla Muffins': 'Vanilkové muffiny',\n        'recipe.Simple Bread': 'Jednoduchý chléb',\n        'recipe.Cinnamon Rolls': 'Skořicové záviny',\n        'recipe.Sourdough Bread': 'Kváskový chléb',\n        'recipe.Chocolate Cake': 'Čokoládový dort',\n        'recipe.Apple Pie': 'Jablečný koláč',\n        'recipe.Croissants': 'Croissanty',\n        // Missing UI Elements\n        'toolbar.menu': 'Menu',\n        'equipment.Work Counter': 'Pracovní pult',\n        'game.level': 'Úroveň',\n        'game.xp': 'XP',\n        'game.current': 'Aktuální',\n        'quick_actions.title': 'Rychlé akce',\n        'quick_actions.buy_ingredients': 'Koupit suroviny',\n        'quick_actions.view_recipes': 'Zobrazit recepty',\n        'quick_actions.equipment_shop': 'Obchod s vybavením',\n        'click_to_use': 'Klikněte pro použití',\n        'currency.czk': 'Kč',\n        // Additional UI Elements\n        'location.downtown_delights': 'Downtown Delights',\n        'toolbar.bakery': 'Pekárna',\n        'toolbar.achievements': 'Úspěchy',\n        'toolbar.stars': 'Hvězdy',\n        'toolbar.settings': 'Nastavení',\n        // Orders\n        'orders.accepted': 'Objednávka přijata',\n        // Cloud Save\n        'cloud.auth.login': 'Přihlásit se',\n        'cloud.auth.register': 'Registrovat',\n        'cloud.auth.login_title': 'Přihlášení do cloudového uložení',\n        'cloud.auth.register_title': 'Vytvoření účtu cloudového uložení',\n        'cloud.auth.username': 'Uživatelské jméno',\n        'cloud.auth.email': 'E-mail',\n        'cloud.auth.password': 'Heslo',\n        'cloud.auth.confirm_password': 'Potvrdit heslo',\n        'cloud.auth.login_button': 'Přihlásit se',\n        'cloud.auth.register_button': 'Vytvořit účet',\n        'cloud.auth.cancel': 'Zrušit',\n        'cloud.auth.loading': 'Načítání...',\n        'cloud.auth.username_required': 'Uživatelské jméno je povinné',\n        'cloud.auth.username_too_short': 'Uživatelské jméno musí mít alespoň 3 znaky',\n        'cloud.auth.email_required': 'E-mail je povinný',\n        'cloud.auth.email_invalid': 'Zadejte platnou e-mailovou adresu',\n        'cloud.auth.password_required': 'Heslo je povinné',\n        'cloud.auth.password_too_short': 'Heslo musí mít alespoň 6 znaků',\n        'cloud.auth.passwords_dont_match': 'Hesla se neshodují',\n        'cloud.auth.username_placeholder': 'Zadejte své uživatelské jméno',\n        'cloud.auth.email_placeholder': 'Zadejte svůj e-mail',\n        'cloud.auth.password_placeholder': 'Zadejte své heslo',\n        'cloud.auth.confirm_password_placeholder': 'Potvrďte své heslo',\n        'cloud.auth.no_account': 'Nemáte účet?',\n        'cloud.auth.have_account': 'Již máte účet?',\n        'cloud.auth.register_link': 'Vytvořte si ho',\n        'cloud.auth.login_link': 'Přihlásit se',\n        'cloud.save.auth_required': 'Vyžadován účet cloudového uložení',\n        'cloud.save.auth_description': 'Pro použití cloudového uložení se musíte přihlásit nebo vytvořit účet.',\n        'cloud.save.save_title': 'Uložit do cloudu',\n        'cloud.save.load_title': 'Načíst z cloudu',\n        'cloud.save.logged_in_as': 'Přihlášen jako',\n        'cloud.save.syncing': 'Synchronizace...',\n        'cloud.save.sync_success': 'Synchronizováno',\n        'cloud.save.sync_error': 'Chyba synchronizace',\n        'cloud.save.sync_idle': 'Připraveno',\n        'cloud.save.last_sync': 'Poslední synchronizace',\n        'cloud.save.save_name': 'Název uložení',\n        'cloud.save.save_name_placeholder': 'Zadejte název pro vaše uložení',\n        'cloud.save.your_saves': 'Vaše cloudová uložení',\n        'cloud.save.refreshing': 'Obnovování...',\n        'cloud.save.refresh': 'Obnovit',\n        'cloud.save.loading_saves': 'Načítání uložení...',\n        'cloud.save.no_saves': 'Žádná cloudová uložení nenalezena',\n        'cloud.save.level': 'Úroveň',\n        'cloud.save.delete': 'Smazat',\n        'cloud.save.confirm_delete': 'Opravdu chcete smazat toto uložení?',\n        'cloud.save.cancel': 'Zrušit',\n        'cloud.save.saving': 'Ukládání...',\n        'cloud.save.save_button': 'Uložit do cloudu',\n        'cloud.save.loading': 'Načítání...',\n        'cloud.save.load_button': 'Načíst hru',\n        // Save/Load Modal\n        'saveLoad.local_saves': 'Místní uložení',\n        'saveLoad.file_saves': 'Souborová uložení',\n        'saveLoad.cloud_saves': 'Cloudová uložení',\n        // Dashboard\n        'dashboard.title': 'Serverový dashboard',\n        'dashboard.button': 'Dashboard',\n        'dashboard.tooltip': 'Přístup k serverovému dashboardu',\n        'dashboard.login_required': 'Pro přístup k dashboardu se prosím přihlaste',\n        'dashboard.logged_in_as': 'Přihlášen jako',\n        'dashboard.access_title': 'Přístup k serverovému dashboardu',\n        'dashboard.access_description': 'Sledujte statistiky serveru, spravujte uživatele a prohlížejte herní analytiku',\n        'dashboard.feature.users': 'Správa uživatelů',\n        'dashboard.feature.rooms': 'Herní místnosti',\n        'dashboard.feature.saves': 'Cloudová uložení',\n        'dashboard.feature.analytics': 'Analytika',\n        'dashboard.open': 'Otevřít dashboard',\n        'dashboard.opening': 'Otevírání...',\n        'dashboard.cancel': 'Zrušit',\n        'dashboard.new_tab_notice': 'Dashboard se otevře v nové záložce',\n        'dashboard.open_failed': 'Nepodařilo se otevřít dashboard',\n        // Recipes\n        'recipe.chocolate_chip_cookies': 'Čokoládové sušenky',\n        'recipe.vanilla_muffins': 'Vanilkové muffiny',\n        'recipe.cinnamon_rolls': 'Skořicové záviny',\n        'recipe.chocolate_brownies': 'Čokoládové brownies',\n        'recipe.blueberry_pie': 'Borůvkový koláč',\n        'recipe.sourdough_bread': 'Kváskový chléb',\n        // Ingredients\n        'ingredient.flour': 'Mouka',\n        'ingredient.sugar': 'Cukr',\n        'ingredient.butter': 'Máslo',\n        'ingredient.chocolate_chips': 'Čokoládové kousky',\n        'ingredient.eggs': 'Vejce',\n        'ingredient.vanilla': 'Vanilka',\n        'ingredient.cinnamon': 'Skořice',\n        'ingredient.blueberries': 'Borůvky',\n        'ingredient.salt': 'Sůl',\n        // Recipe Categories\n        'category.cookies': 'Sušenky',\n        'category.cakes': 'Koláče',\n        'category.pastries': 'Pečivo',\n        'category.pies': 'Koláče',\n        'category.bread': 'Chléb',\n        // Equipment Names\n        'equipment.oven': 'Trouba',\n        'equipment.mixer': 'Mixér',\n        'equipment.work_counter': 'Pracovní pult',\n        'equipment.display_case': 'Vitrina',\n        'equipment.cash_register': 'Pokladna'\n    }\n};\nfunction LanguageProvider(param) {\n    let { children } = param;\n    _s();\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('en');\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            // Mark as mounted to prevent hydration mismatch\n            setMounted(true);\n        }\n    }[\"LanguageProvider.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            // Only access localStorage after component is mounted on client side\n            if (!mounted || \"object\" === 'undefined') return;\n            try {\n                const savedLanguage = localStorage.getItem('language');\n                if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'cs')) {\n                    setLanguage(savedLanguage);\n                }\n            } catch (error) {\n                console.warn('Failed to load language from localStorage:', error);\n            }\n        }\n    }[\"LanguageProvider.useEffect\"], [\n        mounted\n    ]);\n    const handleSetLanguage = (lang)=>{\n        setLanguage(lang);\n        // Only save to localStorage if we're on the client side\n        if (mounted && \"object\" !== 'undefined') {\n            try {\n                localStorage.setItem('language', lang);\n            } catch (error) {\n                console.warn('Failed to save language to localStorage:', error);\n            }\n        }\n    };\n    const t = (key, params)=>{\n        let translation = translations[language][key] || key;\n        if (params) {\n            Object.entries(params).forEach((param)=>{\n                let [param1, value] = param;\n                translation = translation.replace(\"{{\".concat(param1, \"}}\"), value);\n            });\n        }\n        return translation;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            setLanguage: handleSetLanguage,\n            t\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\coding stuff\\\\bake it out\\\\bake-it-out\\\\src\\\\contexts\\\\LanguageContext.tsx\",\n        lineNumber: 1670,\n        columnNumber: 5\n    }, this);\n}\n_s(LanguageProvider, \"gT/yCmRg4polcKdoZlcGYMkuUYA=\");\n_c = LanguageProvider;\nfunction useLanguage() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (context === undefined) {\n        // Fallback for when context is not available\n        console.warn('useLanguage called outside of LanguageProvider, using fallback');\n        return {\n            language: 'en',\n            setLanguage: ()=>{},\n            t: (key)=>key\n        };\n    }\n    return context;\n}\n_s1(useLanguage, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"LanguageProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/LanguageContext.tsx\n"));

/***/ })

});