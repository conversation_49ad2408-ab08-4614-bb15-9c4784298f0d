-- Insert initial ingredients
INSERT INTO ingredients (name, cost, availability) VALUES
('Flour', 5, 1.0),
('Sugar', 8, 1.0),
('Eggs', 12, 0.9),
('Butter', 15, 0.8),
('Milk', 6, 1.0),
('Vanilla Extract', 25, 0.7),
('Chocolate Chips', 20, 0.8),
('Baking Powder', 10, 1.0),
('Salt', 3, 1.0),
('Cinnamon', 18, 0.6),
('Nuts', 30, 0.5),
('Cream Cheese', 22, 0.7),
('Honey', 28, 0.6),
('Cocoa Powder', 16, 0.8),
('Yeast', 14, 0.9);

-- Insert basic equipment
INSERT INTO equipment (name, type, level, cost, unlock_level, automation_level, efficiency_multiplier) VALUES
-- Basic equipment (Level 1)
('Basic Oven', 'oven', 1, 100, 1, 0, 1.0),
('Hand Mixer', 'mixer', 1, 50, 1, 0, 1.0),

-- Upgraded equipment (Level 2-3)
('Professional Oven', 'oven', 2, 300, 3, 0, 1.5),
('Stand Mixer', 'mixer', 2, 200, 2, 0, 1.3),
('Convection Oven', 'oven', 3, 600, 5, 0, 2.0),

-- Automation equipment (Level 4+)
('Auto Mixer', 'auto_mixer', 1, 500, 4, 1, 1.2),
('Auto Oven', 'auto_oven', 1, 800, 6, 1, 1.4),
('Conveyor Belt', 'conveyor', 1, 1000, 7, 2, 1.8),
('Advanced Auto Mixer', 'auto_mixer', 2, 1200, 8, 2, 1.6),
('Industrial Oven', 'auto_oven', 2, 1500, 10, 2, 2.2),
('Smart Conveyor System', 'conveyor', 2, 2000, 12, 3, 2.5);

-- Insert basic recipes
INSERT INTO recipes (name, description, ingredients, baking_time, difficulty, unlock_level, base_price) VALUES
-- Level 1 recipes (Beginner)
('Simple Bread', 'Basic white bread perfect for beginners', 
 '["Flour", "Yeast", "Salt", "Milk"]', 180, 1, 1, 25),

('Chocolate Chip Cookies', 'Classic cookies that everyone loves', 
 '["Flour", "Sugar", "Butter", "Eggs", "Chocolate Chips"]', 120, 1, 1, 30),

('Vanilla Muffins', 'Light and fluffy vanilla muffins', 
 '["Flour", "Sugar", "Eggs", "Milk", "Vanilla Extract", "Baking Powder"]', 90, 1, 1, 20),

-- Level 2 recipes (Easy)
('Cinnamon Rolls', 'Sweet and spicy cinnamon rolls', 
 '["Flour", "Sugar", "Butter", "Eggs", "Milk", "Cinnamon", "Yeast"]', 240, 2, 2, 45),

('Chocolate Brownies', 'Rich and fudgy chocolate brownies', 
 '["Flour", "Sugar", "Butter", "Eggs", "Cocoa Powder", "Chocolate Chips"]', 150, 2, 2, 35),

-- Level 3 recipes (Medium)
('Cheesecake', 'Creamy and delicious cheesecake', 
 '["Flour", "Sugar", "Cream Cheese", "Eggs", "Vanilla Extract"]', 300, 3, 3, 60),

('Croissants', 'Buttery and flaky French pastries', 
 '["Flour", "Butter", "Milk", "Eggs", "Sugar", "Salt", "Yeast"]', 360, 3, 4, 55),

-- Level 4 recipes (Hard)
('Wedding Cake', 'Multi-layer celebration cake', 
 '["Flour", "Sugar", "Butter", "Eggs", "Milk", "Vanilla Extract", "Baking Powder"]', 480, 4, 5, 120),

('Macarons', 'Delicate French sandwich cookies', 
 '["Sugar", "Eggs", "Nuts", "Vanilla Extract"]', 200, 4, 6, 80),

-- Level 5 recipes (Expert)
('Sourdough Bread', 'Artisanal bread with complex flavors', 
 '["Flour", "Salt", "Yeast"]', 720, 5, 8, 90),

('Honey Glazed Donuts', 'Sweet and sticky glazed donuts', 
 '["Flour", "Sugar", "Eggs", "Milk", "Honey", "Yeast", "Vanilla Extract"]', 180, 4, 7, 40),

('Chocolate Soufflé', 'Light and airy chocolate dessert', 
 '["Eggs", "Sugar", "Butter", "Cocoa Powder", "Vanilla Extract"]', 240, 5, 9, 100),

-- Level 6+ recipes (Master)
('Croquembouche', 'French tower of cream puffs', 
 '["Flour", "Butter", "Eggs", "Sugar", "Milk", "Vanilla Extract"]', 600, 5, 10, 200),

('Opera Cake', 'Layered French cake with chocolate and coffee', 
 '["Flour", "Sugar", "Butter", "Eggs", "Cocoa Powder", "Nuts"]', 420, 5, 12, 150),

('Artisan Pizza Dough', 'Perfect base for gourmet pizzas', 
 '["Flour", "Yeast", "Salt", "Milk"]', 90, 3, 5, 35);

-- Insert some sample customer names for orders
CREATE TABLE customer_names (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL
);

INSERT INTO customer_names (name) VALUES
('Alice Johnson'),
('Bob Smith'),
('Carol Davis'),
('David Wilson'),
('Emma Brown'),
('Frank Miller'),
('Grace Taylor'),
('Henry Anderson'),
('Ivy Thomas'),
('Jack Martinez'),
('Kate Garcia'),
('Liam Rodriguez'),
('Mia Lopez'),
('Noah Gonzalez'),
('Olivia Hernandez'),
('Paul Perez'),
('Quinn Turner'),
('Ruby Phillips'),
('Sam Campbell'),
('Tina Parker'),
('Uma Evans'),
('Victor Edwards'),
('Wendy Collins'),
('Xavier Stewart'),
('Yara Sanchez'),
('Zoe Morris');
