exports.id=429,exports.ids=[429],exports.modules={440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(1658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},985:(a,b,c)=>{"use strict";c.d(b,{MultiplayerProvider:()=>j,K:()=>k});var d=c(687),e=c(3210),f=c(7405);class g{constructor(){this.socket=null,this.isConnected=!1,this.reconnectAttempts=0,this.maxReconnectAttempts=5,this.currentRoom=null,this.currentPlayer=null}connect(){if(!this.socket?.connected)try{this.socket=(0,f.io)(process.env.NEXT_PUBLIC_SOCKET_URL||"http://localhost:3001",{transports:["websocket","polling"],timeout:1e4,forceNew:!0,autoConnect:!1}),this.setupEventListeners(),this.socket.connect()}catch(a){console.warn("Failed to initialize socket connection:",a)}}setupEventListeners(){this.socket&&(this.socket.on("connect",()=>{console.log("Connected to multiplayer server"),this.isConnected=!0,this.reconnectAttempts=0}),this.socket.on("disconnect",a=>{console.log("Disconnected from multiplayer server:",a),this.isConnected=!1,"io server disconnect"===a&&this.handleReconnect()}),this.socket.on("connect_error",a=>{console.warn("Multiplayer server not available:",a.message),this.isConnected=!1}),this.socket.on("error",a=>{console.warn("Socket error:",a.message)}))}handleReconnect(){this.reconnectAttempts<this.maxReconnectAttempts?(this.reconnectAttempts++,console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`),setTimeout(()=>{this.connect()},1e3*Math.pow(2,this.reconnectAttempts))):console.error("Max reconnection attempts reached")}getConnectionStatus(){return{isConnected:this.isConnected,socket:this.socket}}createRoom(a){return new Promise((b,c)=>{if(!this.socket?.connected)return void c(Error("Not connected to server"));this.socket.emit("create_room",a),this.socket.once("room_created",a=>{this.currentRoom=a,b(a)}),this.socket.once("error",a=>{c(Error(a.message))})})}joinRoom(a,b){return new Promise((c,d)=>{if(!this.socket?.connected)return void d(Error("Not connected to server"));this.socket.emit("join_room",a,b),this.socket.once("room_joined",(a,b)=>{this.currentRoom=a,this.currentPlayer=b,c({room:a,player:b})}),this.socket.once("error",a=>{d(Error(a.message))})})}leaveRoom(){this.socket?.connected&&this.currentRoom&&(this.socket.emit("leave_room",this.currentRoom.id),this.currentRoom=null,this.currentPlayer=null)}sendPlayerAction(a){this.socket?.connected&&this.currentPlayer&&this.socket.emit("player_action",{...a,playerId:this.currentPlayer.id,timestamp:Date.now()})}sendMessage(a){this.socket?.connected&&this.currentPlayer&&this.socket.emit("send_message",{playerId:this.currentPlayer.id,playerName:this.currentPlayer.name,content:a,timestamp:Date.now()})}on(a,b){this.socket?.on(a,b)}off(a,b){this.socket?.off(a,b)}once(a,b){this.socket?.once(a,b)}isSocketConnected(){return this.isConnected&&this.socket?.connected===!0}getCurrentRoom(){return this.currentRoom}getCurrentPlayer(){return this.currentPlayer}disconnect(){this.socket&&(this.socket.disconnect(),this.socket=null,this.isConnected=!1,this.currentRoom=null,this.currentPlayer=null)}}let h=new g,i=(0,e.createContext)(void 0);function j({children:a}){let[b,c]=(0,e.useState)(!1),[f,g]=(0,e.useState)(!1),[j,k]=(0,e.useState)(null),[l,m]=(0,e.useState)(null),[n,o]=(0,e.useState)(null),[p,q]=(0,e.useState)([]),[r,s]=(0,e.useState)("waiting"),[t,u]=(0,e.useState)(null),[v,w]=(0,e.useState)([]),x=(0,e.useCallback)(async(a,b)=>{try{k(null);let{isConnected:c}=h.getConnectionStatus();c||(h.connect(),await new Promise(a=>setTimeout(a,1e3))),await h.createRoom({...a,hostName:b.name,hostAvatar:b.avatar,hostLevel:b.level})}catch(a){throw k(a.message),a}},[]),y=(0,e.useCallback)(async(a,b)=>{try{k(null);let{isConnected:c}=h.getConnectionStatus();c||(h.connect(),await new Promise(a=>setTimeout(a,1e3))),await h.joinRoom(a,b)}catch(a){throw k(a.message),a}},[]),z=(0,e.useCallback)(()=>{h.leaveRoom()},[]),A=(0,e.useCallback)(()=>{l&&n?.isHost&&h.sendPlayerAction({type:"start_game",data:{roomId:l.id}})},[l,n]),B=(0,e.useCallback)(a=>{h.sendMessage(a)},[]),C=(0,e.useCallback)(a=>{h.sendPlayerAction(a)},[]),D=(0,e.useCallback)(a=>{n&&C({type:"player_ready",data:{ready:a}})},[n,C]),E=(0,e.useCallback)(a=>{n?.isHost&&C({type:"kick_player",data:{playerId:a}})},[n,C]),F=(0,e.useCallback)(a=>{n?.isHost&&C({type:"update_room_settings",data:{settings:a}})},[n,C]);return(0,d.jsx)(i.Provider,{value:{isConnected:b,isInRoom:f,connectionError:j,currentRoom:l,currentPlayer:n,players:p,gameState:r,sharedGameState:t,messages:v,createRoom:x,joinRoom:y,leaveRoom:z,startGame:A,sendMessage:B,sendPlayerAction:C,setPlayerReady:D,kickPlayer:E,updateRoomSettings:F},children:a})}function k(){let a=(0,e.useContext)(i);return void 0===a?(console.warn("useMultiplayer called outside of MultiplayerProvider, using fallback"),{isConnected:!1,connectionStatus:"disconnected",currentRoom:null,gameState:null,createRoom:async()=>{},joinRoom:async()=>{},leaveRoom:()=>{},sendChatMessage:()=>{},updateGameState:()=>{},setPlayerReady:()=>{}}):a}},1100:(a,b,c)=>{"use strict";c.d(b,{DiscordRPCProvider:()=>l,l:()=>k});var d=c(687),e=c(3210);class f{constructor(){this.isConnected=!1,this.isEnabled=!0,this.startTime=Date.now(),this.currentActivity=null,this.isElectron=!1,this.CLIENT_ID="1234567890123456789",this.isElectron=!1,this.isElectron?this.initializeRPC():console.log("Discord RPC only available in desktop version")}async initializeRPC(){if(this.isEnabled&&this.isElectron)try{window.electronAPI&&window.electronAPI.initDiscordRPC?await window.electronAPI.initDiscordRPC(this.CLIENT_ID)?(console.log("Discord RPC initialized successfully"),this.isConnected=!0,setTimeout(async()=>{await this.updateActivity({state:"In Main Menu",details:"Starting the bakery adventure",largeImageKey:"bake_it_out_logo",largeImageText:"Bake It Out - Bakery Management Game",startTimestamp:this.startTime})},1e3)):(console.log("Discord RPC initialization failed"),this.isConnected=!1):(console.log("Discord RPC not available - missing Electron API"),this.isConnected=!1)}catch(a){console.log("Discord RPC initialization error:",a.message||a),this.isConnected=!1}}async updatePlayerStatus(a){if(!this.isConnected||!this.client)return;let b=this.createActivityFromPlayerStatus(a);await this.updateActivity(b)}createActivityFromPlayerStatus(a){let b={state:"",details:"",largeImageKey:"bake_it_out_logo",largeImageText:"Bake It Out - Bakery Management Game",startTimestamp:this.startTime};switch(a.currentActivity){case"menu":return{...b,state:"In Main Menu",details:"Choosing game mode",smallImageKey:"menu_icon",smallImageText:"Main Menu"};case"baking":return{...b,state:`Level ${a.level} Baker`,details:a.currentOrder?`Baking: ${a.currentOrder}`:"Managing the bakery",smallImageKey:"baking_icon",smallImageText:"Baking",buttons:[{label:"Play Bake It Out",url:"https://bakeitout.game"}]};case"managing":return{...b,state:`Level ${a.level} - $${a.money}`,details:a.bakeryName?`Managing ${a.bakeryName}`:"Managing bakery",smallImageKey:"management_icon",smallImageText:"Bakery Management"};case"multiplayer":let c={...b,state:`Level ${a.level} Baker`,details:"Playing with friends",smallImageKey:"multiplayer_icon",smallImageText:"Multiplayer",buttons:[{label:"Join Game",url:"https://bakeitout.game/join"}]};return a.multiplayerRoom&&(c.partyId=a.multiplayerRoom.id,c.partySize=a.multiplayerRoom.playerCount,c.partyMax=a.multiplayerRoom.maxPlayers,c.details=`Multiplayer Bakery (${a.multiplayerRoom.playerCount}/${a.multiplayerRoom.maxPlayers})`),c;case"idle":return{...b,state:`Level ${a.level} Baker`,details:"Taking a break",smallImageKey:"idle_icon",smallImageText:"Idle"};default:return{...b,state:"Playing Bake It Out",details:"Bakery Management Game"}}}async updateActivity(a){if(this.isConnected&&this.isElectron)try{window.electronAPI&&window.electronAPI.updateDiscordRPC&&(await window.electronAPI.updateDiscordRPC(a),this.currentActivity=a,console.log("Discord RPC activity updated:",a.details))}catch(a){console.error("Failed to update Discord RPC activity:",a)}}async setMenuActivity(){await this.updateActivity({state:"In Main Menu",details:"Choosing game mode",largeImageKey:"bake_it_out_logo",largeImageText:"Bake It Out - Bakery Management Game",smallImageKey:"menu_icon",smallImageText:"Main Menu",startTimestamp:this.startTime})}async setGameActivity(a,b,c){await this.updateActivity({state:`Level ${a} - $${b}`,details:c||"Managing bakery",largeImageKey:"bake_it_out_logo",largeImageText:"Bake It Out - Bakery Management Game",smallImageKey:"baking_icon",smallImageText:"In Game",startTimestamp:this.startTime,buttons:[{label:"Play Bake It Out",url:"https://bakeitout.game"}]})}async setMultiplayerActivity(a,b,c){await this.updateActivity({state:"Multiplayer Bakery",details:`Playing with friends (${b}/${c})`,largeImageKey:"bake_it_out_logo",largeImageText:"Bake It Out - Bakery Management Game",smallImageKey:"multiplayer_icon",smallImageText:"Multiplayer",startTimestamp:this.startTime,partyId:a,partySize:b,partyMax:c,buttons:[{label:"Join Game",url:`https://bakeitout.game/join/${a}`}]})}async setBakingActivity(a,b){await this.updateActivity({state:`Level ${a} Baker`,details:`Baking: ${b}`,largeImageKey:"bake_it_out_logo",largeImageText:"Bake It Out - Bakery Management Game",smallImageKey:"baking_icon",smallImageText:"Baking",startTimestamp:this.startTime})}async clearActivity(){if(this.isConnected&&this.isElectron)try{window.electronAPI&&window.electronAPI.clearDiscordRPC&&(await window.electronAPI.clearDiscordRPC(),this.currentActivity=null,console.log("Discord RPC activity cleared"))}catch(a){console.error("Failed to clear Discord RPC activity:",a)}}setEnabled(a){this.isEnabled=a,!a&&this.isConnected?(this.clearActivity(),this.disconnect()):a&&!this.isConnected&&this.initializeRPC()}isRPCEnabled(){return this.isEnabled}isRPCConnected(){return this.isConnected}getCurrentActivity(){return this.currentActivity}async disconnect(){if(this.isConnected&&this.isElectron)try{window.electronAPI&&window.electronAPI.disconnectDiscordRPC&&(await window.electronAPI.disconnectDiscordRPC(),console.log("Discord RPC disconnected"))}catch(a){console.error("Error disconnecting Discord RPC:",a)}this.isConnected=!1,this.currentActivity=null}async cleanup(){await this.clearActivity(),await this.disconnect()}}let g=new f;var h=c(2728),i=c(985);let j=(0,e.createContext)(void 0);function k(){let a=(0,e.useContext)(j);return void 0===a?{isEnabled:!1,isConnected:!1,currentActivity:null,setEnabled:()=>{},updateActivity:async()=>{},setMenuActivity:async()=>{},setGameActivity:async()=>{},setMultiplayerActivity:async()=>{},setBakingActivity:async()=>{},clearActivity:async()=>{}}:a}function l({children:a}){let[b,c]=(0,e.useState)(!1),[f,k]=(0,e.useState)(!1),[l,m]=(0,e.useState)(null),[n,o]=(0,e.useState)(!1),[p,q]=(0,e.useState)(!1);(0,h.I)(),(0,i.K)();let r=async a=>{await g.updateActivity(a),m(a)},s=async()=>{await g.setMenuActivity(),m(g.getCurrentActivity())},t=async(a,b,c)=>{await g.setGameActivity(a,b,c),m(g.getCurrentActivity())},u=async(a,b,c)=>{await g.setMultiplayerActivity(a,b,c),m(g.getCurrentActivity())},v=async(a,b)=>{await g.setBakingActivity(a,b),m(g.getCurrentActivity())},w=async()=>{await g.clearActivity(),m(null)};return(0,d.jsx)(j.Provider,{value:{isEnabled:b,isConnected:f,currentActivity:l,setEnabled:a=>{c(a),g.setEnabled(a),n&&localStorage.setItem("discordRPCEnabled",a.toString())},updateActivity:r,setMenuActivity:s,setGameActivity:t,setMultiplayerActivity:u,setBakingActivity:v,clearActivity:w},children:a})}},1135:()=>{},2650:(a,b,c)=>{"use strict";c.d(b,{DiscordRPCProvider:()=>e});var d=c(1369);(0,d.registerClientReference)(function(){throw Error("Attempted to call useDiscordRPC() from the server but useDiscordRPC is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\contexts\\DiscordRPCContext.tsx","useDiscordRPC");let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call DiscordRPCProvider() from the server but DiscordRPCProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\contexts\\DiscordRPCContext.tsx","DiscordRPCProvider")},2728:(a,b,c)=>{"use strict";c.d(b,{S:()=>m,I:()=>n});var d=c(687),e=c(3210),f=c(9721);function g(a){return a<=1?0:Math.floor(100*Math.pow(1.15,a-1))}let h=[{id:"first_order",name:"First Customer",description:"Complete your first order",icon:"\uD83C\uDFAF",category:"baking",requirements:[{type:"orders_completed",target:1}],reward:{type:"money",id:"first_order_bonus",name:"First Order Bonus",description:"Bonus for first order",value:50},unlocked:!0,completed:!1},{id:"baker_apprentice",name:"Baker Apprentice",description:"Complete 10 orders",icon:"\uD83D\uDC68‍\uD83C\uDF73",category:"baking",requirements:[{type:"orders_completed",target:10}],reward:{type:"skill_point",id:"apprentice_skill",name:"Skill Point",description:"Gain 1 skill point",value:1},unlocked:!0,completed:!1},{id:"baker_journeyman",name:"Baker Journeyman",description:"Complete 50 orders",icon:"\uD83D\uDC68‍\uD83C\uDF73",category:"baking",requirements:[{type:"orders_completed",target:50}],reward:{type:"money",id:"journeyman_bonus",name:"Journeyman Bonus",description:"Large money bonus",value:500},unlocked:!0,completed:!1},{id:"master_baker",name:"Master Baker",description:"Complete 100 orders",icon:"\uD83C\uDFC6",category:"baking",requirements:[{type:"orders_completed",target:100}],reward:{type:"skill_point",id:"master_skill",name:"Master Skill Points",description:"Gain 3 skill points",value:3},unlocked:!0,completed:!1},{id:"speed_baker",name:"Speed Baker",description:"Bake 100 items",icon:"⚡",category:"efficiency",requirements:[{type:"items_baked",target:100}],reward:{type:"skill_point",id:"speed_skill",name:"Speed Skill Point",description:"Gain 1 skill point",value:1},unlocked:!0,completed:!1},{id:"money_maker",name:"Money Maker",description:"Earn $1000 total",icon:"\uD83D\uDCB0",category:"business",requirements:[{type:"money_earned",target:1e3}],reward:{type:"skill_point",id:"money_maker_skill",name:"Business Skill Point",description:"Extra skill point for business success",value:1},unlocked:!0,completed:!1},{id:"recipe_collector",name:"Recipe Collector",description:"Unlock 5 different recipes",icon:"\uD83D\uDCDA",category:"collection",requirements:[{type:"recipes_unlocked",target:5}],reward:{type:"money",id:"recipe_bonus",name:"Recipe Collection Bonus",description:"Bonus for collecting recipes",value:200},unlocked:!0,completed:!1},{id:"level_master",name:"Level Master",description:"Reach level 10",icon:"⭐",category:"special",requirements:[{type:"level_reached",target:10}],reward:{type:"skill_point",id:"level_master_skill",name:"Master Level Bonus",description:"Gain 2 skill points",value:2},unlocked:!0,completed:!1},{id:"first_hundred",name:"First Hundred",description:"Earn $100 total",icon:"\uD83D\uDCB5",category:"business",requirements:[{type:"money_earned",target:100}],reward:{type:"money",id:"first_hundred_bonus",name:"Business Bonus",description:"Small business milestone bonus",value:25},unlocked:!0,completed:!1},{id:"entrepreneur",name:"Entrepreneur",description:"Earn $5000 total",icon:"\uD83C\uDFE2",category:"business",requirements:[{type:"money_earned",target:5e3}],reward:{type:"skill_point",id:"entrepreneur_skill",name:"Business Skill Points",description:"Gain 2 skill points",value:2},unlocked:!0,completed:!1},{id:"equipment_enthusiast",name:"Equipment Enthusiast",description:"Own 3 pieces of equipment",icon:"⚙️",category:"collection",requirements:[{type:"equipment_owned",target:3}],reward:{type:"money",id:"equipment_bonus",name:"Equipment Bonus",description:"Equipment investment bonus",value:300},unlocked:!0,completed:!1},{id:"rising_star",name:"Rising Star",description:"Reach level 5",icon:"\uD83C\uDF1F",category:"special",requirements:[{type:"level_reached",target:5}],reward:{type:"skill_point",id:"rising_star_skill",name:"Rising Star Bonus",description:"Gain 1 skill point",value:1},unlocked:!0,completed:!1},{id:"legendary_baker",name:"Legendary Baker",description:"Reach level 20",icon:"\uD83D\uDC51",category:"special",requirements:[{type:"level_reached",target:20}],reward:{type:"skill_point",id:"legendary_skill",name:"Legendary Bonus",description:"Gain 5 skill points",value:5},unlocked:!0,completed:!1}],i=[{id:"baking_speed_1",name:"Quick Hands",description:"Increase baking speed by 10%",icon:"⚡",category:"efficiency",level:0,maxLevel:3,cost:1,requirements:{playerLevel:2},effects:[{type:"baking_speed",value:.1}]},{id:"money_bonus_1",name:"Business Sense",description:"Increase money earned by 15%",icon:"\uD83D\uDCBC",category:"business",level:0,maxLevel:3,cost:1,requirements:{playerLevel:3},effects:[{type:"money_multiplier",value:.15}]},{id:"xp_bonus_1",name:"Fast Learner",description:"Increase experience gained by 20%",icon:"\uD83D\uDCC8",category:"efficiency",level:0,maxLevel:2,cost:2,requirements:{playerLevel:4},effects:[{type:"xp_multiplier",value:.2}]},{id:"ingredient_efficiency_1",name:"Efficient Baker",description:"Use 10% fewer ingredients",icon:"\uD83C\uDF3E",category:"efficiency",level:0,maxLevel:2,cost:2,requirements:{playerLevel:5,skills:["baking_speed_1"]},effects:[{type:"ingredient_efficiency",value:.1}]},{id:"automation_unlock_1",name:"Automation Expert",description:"Unlock advanced automation features",icon:"\uD83E\uDD16",category:"automation",level:0,maxLevel:1,cost:3,requirements:{playerLevel:8,achievements:["baker_apprentice"]},effects:[{type:"automation_unlock",value:1}]}];var j=c(3955),k=c(6005);let l=(0,e.createContext)(void 0);function m({children:a}){let[b,c]=(0,e.useState)({level:1,experience:0,money:100,maxExperience:100,skillPoints:0,totalMoneyEarned:0,totalOrdersCompleted:0,totalItemsBaked:0,unlockedRecipes:["chocolate_chip_cookies","vanilla_muffins"],automationUpgrades:[]}),[m,n]=(0,e.useState)([{id:"oven1",name:"Basic Oven",type:"oven",isActive:!1,level:1,efficiency:1,automationLevel:0},{id:"mixer1",name:"Hand Mixer",type:"mixer",isActive:!1,level:1,efficiency:1,automationLevel:0},{id:"counter1",name:"Work Counter",type:"counter",isActive:!1,level:1,efficiency:1,automationLevel:0}]),[o,p]=(0,e.useState)([{name:"Flour",quantity:15,cost:5,icon:"\uD83C\uDF3E"},{name:"Sugar",quantity:12,cost:8,icon:"\uD83C\uDF6F"},{name:"Eggs",quantity:10,cost:12,icon:"\uD83E\uDD5A"},{name:"Butter",quantity:8,cost:15,icon:"\uD83E\uDDC8"},{name:"Chocolate Chips",quantity:6,cost:20,icon:"\uD83C\uDF6B"},{name:"Vanilla",quantity:5,cost:25,icon:"\uD83C\uDF3F"},{name:"Salt",quantity:10,cost:3,icon:"\uD83E\uDDC2"}]),[q,r]=(0,e.useState)([{id:"1",customerName:"Alice Johnson",items:["Chocolate Chip Cookies"],timeLimit:300,reward:25,status:"pending",difficulty:1}]),[s,t]=(0,e.useState)(h),[u,v]=(0,e.useState)(i),[w,x]=(0,e.useState)([]),[y,z]=(0,e.useState)(!1),[A,B]=(0,e.useState)({enabled:!1,autoStart:!1,preferredRecipes:[],maxConcurrentJobs:2,priorityMode:"efficiency",ingredientThreshold:5}),[C,D]=(0,e.useState)([]),[E,F]=(0,e.useState)([]),G=(a,b)=>{n(c=>c.map(c=>c.id===a?{...c,...b}:c))},H=a=>{c(b=>{let c=b.experience+a,d=function(a){let b=1,c=0;for(;;){let d=g(b+1);if(c+d>a)break;c+=d,b++}let d=g(b+1);return{level:b,experience:a-c,experienceRequired:d,totalExperience:a,rewards:function(a){let b=[];b.push({type:"money",id:`money_${a}`,name:"Level Bonus",description:`Bonus money for reaching level ${a}`,value:25*a});let c={2:["cinnamon_rolls"],3:["chocolate_brownies","sourdough_bread"],4:["croissants"],5:["cheesecake"],6:["macarons"],7:["honey_glazed_donuts"],8:["sourdough_bread"],9:["chocolate_souffle"],10:["croquembouche"],12:["opera_cake"],15:["artisan_pizza_dough"]};c[a]&&c[a].forEach(a=>{b.push({type:"recipe",id:a,name:"New Recipe Unlocked",description:`You can now bake ${a.replace(/_/g," ")}`})});let d={3:["professional_oven"],4:["auto_mixer"],5:["stand_mixer"],6:["auto_oven"],7:["conveyor_belt"],8:["advanced_auto_mixer"],10:["industrial_oven"],12:["smart_conveyor_system"]};return d[a]&&d[a].forEach(a=>{b.push({type:"equipment",id:a,name:"New Equipment Available",description:`${a.replace(/_/g," ")} is now available for purchase`})}),a%2==0&&b.push({type:"skill_point",id:`skill_point_${a}`,name:"Skill Point",description:"Use this to upgrade your skills in the technology tree",value:1}),b}(b)}}(c);if(d.level>b.level){x(d.rewards),z(!0);let a=+(d.level%2==0);return setTimeout(()=>L(),100),{...b,level:d.level,experience:c,maxExperience:d.experienceRequired,skillPoints:b.skillPoints+a}}return{...b,experience:c,maxExperience:d.experienceRequired}})},I=a=>{c(b=>({...b,money:b.money+a,totalMoneyEarned:b.totalMoneyEarned+a})),setTimeout(()=>L(),100)},J=a=>b.money>=a&&(c(b=>({...b,money:b.money-a})),!0),K=(a,b)=>{let c=o.find(b=>b.name===a);return!!c&&c.quantity>=b&&(p(c=>c.map(c=>c.name===a?{...c,quantity:c.quantity-b}:c)),!0)},L=()=>{t(a=>a.map(a=>{if(a.completed)return a;let d=a.requirements.map(a=>{let c=0;switch(a.type){case"orders_completed":c=b.totalOrdersCompleted;break;case"money_earned":c=b.totalMoneyEarned;break;case"recipes_unlocked":c=b.unlockedRecipes.length;break;case"level_reached":c=b.level;break;case"items_baked":c=b.totalItemsBaked;break;case"equipment_owned":c=m.length}return{...a,current:c}}),e=d.every(a=>a.current>=a.target);return e&&!a.completed&&("money"===a.reward.type&&a.reward.value?I(a.reward.value):"skill_point"===a.reward.type&&a.reward.value&&c(b=>({...b,skillPoints:b.skillPoints+a.reward.value}))),{...a,requirements:d,completed:e}}))},M=async(a,c)=>{try{let d={version:"1.0.0",timestamp:Date.now(),player:{level:b.level,experience:b.experience,money:b.money,skillPoints:b.skillPoints,totalMoneyEarned:b.totalMoneyEarned||0,totalOrdersCompleted:b.totalOrdersCompleted||0,totalItemsBaked:b.totalItemsBaked||0,unlockedRecipes:b.unlockedRecipes||[],automationUpgrades:b.automationUpgrades||[],name:c||`Save ${a||1}`,playTime:b.playTime||0},equipment:m,inventory:o,achievements:s,skills:u,automationSettings:A,gameSettings:{language:"en",soundEnabled:!0,musicEnabled:!0,notificationsEnabled:!0,autoSaveEnabled:!0},bakeries:[],currentBakeryId:"main"};if(a){let b=`bakeItOut_save_slot_${a}`;localStorage.setItem(b,JSON.stringify(d))}else k.B.saveToLocal(d);return!0}catch(a){return console.error("Failed to save game:",a),!1}},N=async a=>{try{let b=`bakeItOut_save_slot_${a}`,d=localStorage.getItem(b);if(!d)return!1;let e=JSON.parse(d);return c(a=>({...a,level:e.player.level,experience:e.player.experience,money:e.player.money,skillPoints:e.player.skillPoints,totalMoneyEarned:e.player.totalMoneyEarned||0,totalOrdersCompleted:e.player.totalOrdersCompleted||0,totalItemsBaked:e.player.totalItemsBaked||0,unlockedRecipes:e.player.unlockedRecipes||[],automationUpgrades:e.player.automationUpgrades||[],playTime:e.player.playTime||0})),n(e.equipment||[]),p(e.inventory||[]),t(e.achievements||[]),v(e.skills||[]),B(e.automationSettings||{enabled:!1,efficiency:1,speed:1,qualityBonus:0}),!0}catch(a){return console.error("Failed to load game:",a),!1}},O=async()=>await M(0,"Quick Save"),P=async()=>await N(0),Q=async()=>await M(-1,"Auto Save");return(0,d.jsx)(l.Provider,{value:{player:b,equipment:m,inventory:o,orders:q,achievements:s,skills:u,levelUpRewards:w,showLevelUp:y,automationSettings:A,automationJobs:C,conveyorBelts:E,updatePlayer:a=>{c(b=>({...b,...a}))},updateEquipment:G,addEquipment:a=>{let b={...a,id:Date.now().toString()+Math.random().toString(36).substring(2,11)};n(a=>[...a,b]),setTimeout(()=>L(),100)},addExperience:H,addMoney:I,spendMoney:J,useIngredient:K,addIngredient:(a,b)=>{p(c=>c.map(c=>c.name===a?{...c,quantity:c.quantity+b}:c))},acceptOrder:a=>{r(b=>b.map(b=>b.id===a?{...b,status:"accepted"}:b))},completeOrder:a=>{let b=q.find(b=>b.id===a);if(b&&b.items.every(a=>{let b=(0,f.dU)(a.toLowerCase().replace(/\s+/g,"_"));return!!b&&(0,f.hF)(b,o)})){b.items.forEach(a=>{let b=(0,f.dU)(a.toLowerCase().replace(/\s+/g,"_"));b&&b.ingredients.forEach(a=>{K(a.name,a.quantity)})}),r(b=>b.map(b=>b.id===a?{...b,status:"completed"}:b)),c(a=>({...a,totalOrdersCompleted:a.totalOrdersCompleted+1,totalItemsBaked:a.totalItemsBaked+b.items.length}));let d=b.timeLimit>60,e=(0,f.Qb)(b.difficulty,d);I(b.reward),H(e),setTimeout(()=>L(),100)}},declineOrder:a=>{r(b=>b.filter(b=>b.id!==a))},generateNewOrder:()=>{let a=(0,f.jg)(b.level);r(b=>[...b,a])},upgradeSkill:a=>{let d=u.find(b=>b.id===a);!d||d.level>=d.maxLevel||b.skillPoints<d.cost||(v(b=>b.map(b=>b.id===a?{...b,level:b.level+1}:b)),c(a=>({...a,skillPoints:a.skillPoints-d.cost})))},checkAchievements:L,dismissLevelUp:()=>{z(!1),x([])},updateAutomationSettings:a=>{B(b=>({...b,...a}))},purchaseAutomationUpgrade:a=>{let d=j.sA.find(b=>b.id===a);d&&!(b.money<d.cost)&&J(d.cost)&&c(b=>({...b,automationUpgrades:[...b.automationUpgrades,a]}))},startAutomationJob:a=>{if(!A.enabled)return;let c=m.find(b=>b.id===a);if(!c||c.isActive||0===c.automationLevel)return;let d=(0,f.x0)(b.level),e=(0,j.tX)(d,o,A.priorityMode,q);if(!e)return;let g=(0,f.dU)(e);if(!g||!(0,f.hF)(g,o))return;let h=(0,j.XN)(c.efficiency,c.automationLevel,b.automationUpgrades),i=(0,j.Ws)(a,e,g,h);D(a=>[...a,{...i,status:"running"}]),G(a,{isActive:!0,timeRemaining:i.duration,currentRecipe:g.name}),i.ingredients.forEach(a=>{K(a.name,a.quantity)})},saveGameState:M,loadGameState:N,quickSave:O,quickLoad:P,autoSave:Q},children:a})}function n(){let a=(0,e.useContext)(l);return void 0===a?{player:{level:1,experience:0,money:100,maxExperience:100,skillPoints:0,totalMoneyEarned:0,totalOrdersCompleted:0,totalItemsBaked:0,unlockedRecipes:[],automationUpgrades:[]},equipment:[],inventory:[],orders:[],achievements:[],skills:[],levelUpRewards:[],showLevelUp:!1,updateEquipment:()=>{},acceptOrder:()=>{},completeOrder:()=>{},declineOrder:()=>{},generateNewOrder:()=>{},upgradeSkill:()=>{},checkAchievements:()=>{},dismissLevelUp:()=>{},spendMoney:()=>!1,addMoney:()=>{},addExperience:()=>{},addEquipment:()=>{},removeEquipment:()=>{},addInventoryItem:()=>{},removeInventoryItem:()=>{},updateAutomationSettings:()=>{},purchaseAutomationUpgrade:()=>{},startAutomationJob:()=>{},saveGameState:async()=>!1,loadGameState:async()=>!1,quickSave:async()=>!1,quickLoad:async()=>!1,autoSave:async()=>!1}:a}},3630:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,6133,23)),Promise.resolve().then(c.t.bind(c,6444,23)),Promise.resolve().then(c.t.bind(c,6042,23)),Promise.resolve().then(c.t.bind(c,9477,23)),Promise.resolve().then(c.t.bind(c,9345,23)),Promise.resolve().then(c.t.bind(c,2089,23)),Promise.resolve().then(c.t.bind(c,6577,23)),Promise.resolve().then(c.t.bind(c,1307,23)),Promise.resolve().then(c.t.bind(c,4817,23))},3955:(a,b,c)=>{"use strict";c.d(b,{Ws:()=>f,XN:()=>e,sA:()=>d,tX:()=>g});let d=[{id:"auto_queue_basic",name:"Basic Auto-Queue",description:"Equipment automatically starts the next recipe when finished",type:"intelligence",cost:500,unlockLevel:4,effects:{autoQueueing:!0}},{id:"efficiency_boost_1",name:"Efficiency Boost I",description:"Automated equipment uses 10% fewer ingredients",type:"efficiency",cost:750,unlockLevel:5,effects:{efficiencyBonus:.1}},{id:"speed_boost_1",name:"Speed Boost I",description:"Automated equipment works 15% faster",type:"speed",cost:1e3,unlockLevel:6,effects:{speedMultiplier:1.15}},{id:"smart_prioritization",name:"Smart Prioritization",description:"Automation prioritizes orders based on profit and urgency",type:"intelligence",cost:1500,unlockLevel:8,effects:{smartPrioritization:!0}},{id:"efficiency_boost_2",name:"Efficiency Boost II",description:"Automated equipment uses 20% fewer ingredients",type:"efficiency",cost:2e3,unlockLevel:10,effects:{efficiencyBonus:.2}},{id:"speed_boost_2",name:"Speed Boost II",description:"Automated equipment works 30% faster",type:"speed",cost:2500,unlockLevel:12,effects:{speedMultiplier:1.3}}];function e(a,b,c,f=0){let g=a;return g*=1+.1*b,c.forEach(a=>{let b=d.find(b=>b.id===a);b?.effects.efficiencyBonus&&(g*=1+b.effects.efficiencyBonus)}),Math.min(g*=1+f,2)}function f(a,b,c,d){let e=Math.floor(c.bakingTime/d),f=c.ingredients.map(a=>({...a,quantity:Math.ceil(a.quantity*(1-(d-1)*.1))}));return{id:Date.now().toString()+Math.random().toString(36).substr(2,9),equipmentId:a,recipeId:b,startTime:Date.now(),duration:e,status:"queued",ingredients:f,efficiency:d}}function g(a,b,c,d){let e=a.filter(a=>a.ingredients.every(a=>{let c=b.find(b=>b.name===a.name);return c&&c.quantity>=a.quantity}));if(0===e.length)return null;switch(c){case"speed":return e.reduce((a,b)=>b.bakingTime<a.bakingTime?b:a).id;case"profit":return e.reduce((a,b)=>b.basePrice>a.basePrice?b:a).id;case"efficiency":let f=d.flatMap(a=>a.items),g=e.filter(a=>f.includes(a.name));if(g.length>0)return g[0].id;return e[0].id;default:return e[0].id}}},4393:(a,b,c)=>{"use strict";c.d(b,{LanguageProvider:()=>h,o:()=>i});var d=c(687),e=c(3210);let f=(0,e.createContext)(void 0),g={en:{"game.title":"Bake It Out","game.subtitle":"Master the art of bakery management in this engaging multiplayer game. Complete orders, unlock recipes, automate your processes, and compete with friends!","game.play":"\uD83C\uDFAE Start Playing","game.singlePlayer":"\uD83C\uDFAE Single Player","game.singlePlayerDesc":"Play solo and master your bakery skills","game.multiplayer":"\uD83D\uDC65 Multiplayer","game.multiplayerDesc":"Play with friends in cooperative or competitive modes","game.english":"\uD83C\uDDFA\uD83C\uDDF8 English","game.czech":"\uD83C\uDDE8\uD83C\uDDFF Čeština","game.home":"\uD83C\uDFE0 Home","game.close":"✕ Close","game.continue":"\uD83D\uDE80 Continue Playing","menu.singlePlayer":"Single Player","menu.multiplayer":"Multiplayer","menu.settings":"Settings","menu.credits":"Credits","menu.exit":"Exit","credits.title":"About Bake It Out","credits.subtitle":"Game Information & Credits","credits.description":"A multiplayer bakery management game with real-time collaboration and localization support.","credits.version":"Version","credits.release":"Release Date","credits.releaseDate":"January 2025","credits.platform":"Platform","credits.platforms":"Windows, macOS, Linux","credits.close":"Close","credits.features":"Features","credits.multiplayer":"Multiplayer Support","credits.multiplayerDesc":"Real-time collaboration with friends in cooperative or competitive modes.","credits.localization":"Localization","credits.localizationDesc":"Full support for English and Czech languages with easy switching.","credits.progression":"Progression System","credits.progressionDesc":"Level up your bakery, unlock recipes, and master automation.","credits.automation":"Automation","credits.automationDesc":"Advanced automation systems to optimize your bakery operations.","credits.technology":"Technology Stack","credits.team":"Development Team","credits.developedBy":"Developed by","credits.teamDesc":"Created with passion by the Bake It Out development team.","credits.thanks":"Special Thanks","credits.thanksPlayers":"All our amazing players and beta testers","credits.thanksTranslators":"Community translators for localization support","credits.thanksOpenSource":"Open source community for incredible tools and libraries","credits.thanksBakers":"Real bakers who inspired this game","credits.contact":"Contact & Support","credits.website":"Website","credits.support":"Support","credits.github":"GitHub","credits.discord":"Discord","game.view.traditional":"Traditional View","game.view.layout":"Bakery Layout","game.view.dining":"Dining Room","game.view.customers":"Customer Manager","bakery.layout.title":"Bakery Layout","bakery.kitchen":"Kitchen","bakery.dining":"Dining Area","bakery.counter":"Service Counter","bakery.baking.area":"Baking Area","bakery.prep.area":"Prep Area","bakery.automation.area":"Automation","bakery.kitchen.stats":"Kitchen Stats","bakery.active.equipment":"Active Equipment","bakery.automated.equipment":"Automated Equipment","bakery.efficiency":"Efficiency","bakery.dining.stats":"Dining Stats","bakery.current.customers":"Current Customers","bakery.waiting.customers":"Waiting","bakery.eating.customers":"Eating","bakery.avg.satisfaction":"Avg Satisfaction","bakery.service.counter":"Service Counter","bakery.display.case":"Display Case","bakery.order.queue":"Order Queue","bakery.last.updated":"Last updated","dining.room.title":"Dining Room","dining.room.subtitle":"Watch your customers enjoy their meals","dining.occupied.tables":"Occupied Tables","dining.ambient.sounds":"Toggle ambient sounds","dining.service.counter":"Service","dining.customer.status":"Customer Status","dining.enjoying":"Enjoying","dining.no.customers":"No customers dining currently","dining.waiting.for.customers":"Complete orders to see customers dining","dining.ambient.playing":"Ambient sounds playing","customers.manager.title":"Customer Manager","customers.manager.subtitle":"Monitor and serve your customers","customers.current.list":"Current Customers","customers.table":"Table","customers.patience":"Patience","customers.no.customers":"No customers currently","customers.waiting.for.orders":"Waiting for new orders...","customers.order.details":"Order Details","customers.total":"Total","customers.info":"Customer Info","customers.mood":"Mood","customers.status":"Status","customers.preferences":"Preferences","customers.select.customer":"Select a customer","customers.select.to.view.details":"Select a customer to view details","customers.serve.order":"Serve Order","menu.newGame":"New Game","menu.continueGame":"Continue Game","menu.loadGame":"Load Game","menu.selectLanguage":"Select Language","menu.about":"About","menu.help":"Help","menu.quit":"Quit","features.manage.title":"Manage Your Bakery","features.manage.description":"Take orders, bake delicious goods, and serve happy customers","features.levelup.title":"Level Up & Automate","features.levelup.description":"Unlock new recipes, buy equipment, and automate your processes","features.multiplayer.title":"Play Together","features.multiplayer.description":"Cooperative and competitive multiplayer modes with friends","status.development":"\uD83D\uDEA7 Game in Development - Phase 5: Multilayer Support! \uD83D\uDEA7","ui.level":"Level {{level}}","ui.money":"${{amount}}","ui.experience":"XP: {{current}}/{{max}}","ui.skillPoints":"SP: {{points}}","ui.achievements":"\uD83C\uDFC6 Achievements","ui.skills":"\uD83C\uDF1F Skills","ui.automation":"\uD83E\uDD16 Automation","kitchen.title":"\uD83C\uDFEA Kitchen","kitchen.clickToUse":"Click to use","kitchen.making":"Making: {{recipe}}","kitchen.timeRemaining":"Time: {{time}}","inventory.title":"\uD83D\uDCE6 Inventory","inventory.quantity":"Qty: {{qty}}","inventory.cost":"${{cost}} each","orders.title":"\uD83D\uDCCB Orders","orders.newOrder":"+ New Order","orders.accept":"Accept","orders.decline":"Decline","orders.complete":"Complete","orders.inProgress":"In Progress","orders.timeLimit":"Time: {{time}}","orders.reward":"${{amount}}","orders.customer":"Customer: {{name}}","actions.title":"⚡ Quick Actions","actions.buyIngredients":"\uD83D\uDED2 Buy Ingredients","actions.viewRecipes":"\uD83D\uDCD6 View Recipes","actions.equipmentShop":"\uD83D\uDD27 Equipment Shop","modal.recipes.title":"\uD83D\uDCD6 Recipe Book","modal.shop.title":"\uD83D\uDED2 Ingredient Shop","modal.baking.title":"\uD83D\uDD25 {{equipment}} - Select Recipe","modal.achievements.title":"\uD83C\uDFC6 Achievements","modal.skills.title":"\uD83C\uDF1F Skill Tree","modal.automation.title":"\uD83E\uDD16 Automation Control","modal.equipmentShop.title":"\uD83C\uDFEA Equipment Shop","modal.settings.title":"⚙️ Settings","modal.bakeries.title":"\uD83C\uDFEA Bakery Manager","modal.levelUp.title":"Level Up!","modal.levelUp.subtitle":"You reached Level {{level}}!","recipes.all":"All","recipes.cookies":"Cookies","recipes.cakes":"Cakes","recipes.bread":"Bread","recipes.pastries":"Pastries","recipes.ingredients":"Ingredients:","recipes.difficulty":"Difficulty:","recipes.time":"Time:","recipes.canCraft":"✅ Can Craft","recipes.unlockLevel":"Unlocked at Level {{level}}","recipes.noRecipes":"No recipes available in this category.","recipes.levelUpToUnlock":"Level up to unlock more recipes!","shop.currentStock":"Current stock: {{quantity}}","shop.buy":"Buy","shop.tooExpensive":"Too Expensive","shop.tips.title":"\uD83D\uDCA1 Shopping Tips","shop.tips.bulk":"• Buy ingredients in bulk to save time","shop.tips.stock":"• Keep an eye on your stock levels","shop.tips.rare":"• Some recipes require rare ingredients","shop.tips.prices":"• Prices may vary based on availability","baking.selectRecipe":"Select Recipe","baking.noRecipes":"No recipes available","baking.noIngredients":"You don't have enough ingredients to craft any recipes.","baking.buyIngredients":"Buy Ingredients","baking.startBaking":"\uD83D\uDD25 Start Baking","baking.instructions":"\uD83D\uDCCB Baking Instructions for {{recipe}}","baking.expectedReward":"Expected reward: ${{amount}}","baking.makesSure":"Make sure you have all ingredients before starting!","baking.inProgress":"Baking in progress...","baking.completed":"Baking completed!","baking.cancelled":"Baking cancelled","baking.timeRemaining":"Time remaining: {{time}}","baking.clickToCollect":"Click to collect","achievements.completed":"{{completed}} of {{total}} achievements completed","achievements.overallProgress":"Overall Progress","achievements.progress":"Progress","achievements.reward":"Reward:","achievements.noAchievements":"No achievements in this category.","skills.availablePoints":"Available Skill Points: {{points}}","skills.efficiency":"Efficiency","skills.automation":"Automation","skills.quality":"Quality","skills.business":"Business","skills.effects":"Effects:","skills.requires":"Requires: {{requirements}}","skills.requiresLevel":"Requires Level {{level}}","skills.maxed":"✅ Maxed","skills.upgrade":"⬆️ Upgrade ({{cost}} SP)","skills.locked":"\uD83D\uDD12 Locked","skills.noSkills":"No skills in this category.","skills.tips.title":"\uD83D\uDCA1 Skill Tips","skills.tips.earnPoints":"• Earn skill points by leveling up (1 point every 2 levels)","skills.tips.prerequisites":"• Some skills require other skills to be unlocked first","skills.tips.playstyle":"• Focus on skills that match your playstyle","skills.tips.efficiency":"• Efficiency skills help with resource management","automation.masterControl":"\uD83C\uDF9B️ Master Control","automation.enableAutomation":"Enable Automation","automation.autoStart":"Auto-start Equipment","automation.priorityMode":"\uD83C\uDFAF Priority Mode","automation.efficiency":"Efficiency (Orders First)","automation.profit":"Profit (Highest Value)","automation.speed":"Speed (Fastest Recipes)","automation.priorityDescription":"How automation chooses what to bake","automation.performance":"⚡ Performance","automation.maxJobs":"Max Concurrent Jobs: {{jobs}}","automation.safety":"\uD83D\uDEE1️ Safety","automation.stopWhenLow":"Stop when ingredients below: {{threshold}}","automation.upgrades":"\uD83D\uDCA1 Automation Upgrades","automation.upgradesDescription":"Improve your automation efficiency, speed, and intelligence with these upgrades.","automation.purchase":"Purchase","automation.noUpgrades":"No upgrades available at your current level.","automation.levelUpForUpgrades":"Level up to unlock more automation upgrades!","automation.automatedEquipment":"Automated Equipment","automation.activeUpgrades":"Active Upgrades","automation.automationStatus":"Automation Status","automation.equipmentStatus":"\uD83C\uDFED Equipment Status","automation.running":"Running","automation.idle":"Idle","automation.noAutomatedEquipment":"No automated equipment available.","automation.purchaseAutoEquipment":"Purchase auto-equipment from the shop to get started!","equipmentShop.upgradeYourBakery":"Upgrade your bakery with professional equipment","equipmentShop.basic":"Basic","equipmentShop.automated":"Automated","equipmentShop.advanced":"Advanced","equipmentShop.efficiency":"Efficiency: {{efficiency}}x","equipmentShop.automation":"Automation:","equipmentShop.unlockLevel":"Unlock Level: {{level}}","equipmentShop.purchase":"\uD83D\uDCB0 Purchase","equipmentShop.noEquipment":"No equipment available in this category.","equipmentShop.levelUpForEquipment":"Level up to unlock more equipment!","equipmentShop.tips.title":"\uD83D\uDCA1 Equipment Tips","equipmentShop.tips.automated":"• Automated equipment can run without your supervision","equipmentShop.tips.efficiency":"• Higher efficiency means faster production and better quality","equipmentShop.tips.conveyor":"• Conveyor belts connect equipment for seamless workflow","equipmentShop.tips.advanced":"• Advanced equipment unlocks at higher levels","levelUp.levelRewards":"\uD83C\uDF81 Level Rewards","levelUp.whatsNext":"\uD83D\uDCA1 What's Next?","levelUp.checkRecipes":"• Check out new recipes in your recipe book","levelUp.visitShop":"• Visit the shop for new equipment","levelUp.challengingOrders":"• Take on more challenging orders","levelUp.investSkills":"• Invest in skill upgrades","settings.title":"⚙️ Settings","settings.general":"General","settings.audio":"Audio","settings.graphics":"Graphics","settings.save":"Save & Data","settings.language":"\uD83C\uDF0D Language","settings.gameplay":"\uD83C\uDFAE Gameplay","settings.notifications":"Enable Notifications","settings.tutorials":"Show Tutorials","settings.animationSpeed":"Animation Speed","settings.sound":"Sound Effects","settings.music":"Background Music","settings.quality":"\uD83C\uDFA8 Graphics Quality","settings.autoSave":"\uD83D\uDCBE Auto-Save","settings.enableAutoSave":"Enable Auto-Save","settings.dataManagement":"\uD83D\uDCC1 Data Management","settings.exportSave":"\uD83D\uDCE4 Export Save","settings.importSave":"\uD83D\uDCE5 Import Save","settings.cloudSync":"☁️ Cloud Sync","settings.cloudSyncDescription":"Cloud sync allows you to save your progress online and play across multiple devices.","settings.comingSoon":"Coming Soon","bakeries.title":"\uD83C\uDFEA Bakery Manager","bakeries.subtitle":"Manage your bakery empire","bakeries.owned":"My Bakeries","bakeries.available":"Available","bakeries.current":"Current","bakeries.level":"Level","bakeries.specialization":"Specialization","bakeries.equipment":"Equipment","bakeries.orders":"Active Orders","bakeries.switchTo":"Switch To","bakeries.noOwned":"You don't own any bakeries yet.","bakeries.purchase":"\uD83D\uDCB0 Purchase","bakeries.tooExpensive":"\uD83D\uDCB8 Too Expensive","bakeries.allOwned":"You own all available bakeries!","bakeries.tips":"\uD83D\uDCA1 Bakery Tips","bakeries.tip1":"Each bakery specializes in different products for bonus efficiency","bakeries.tip2":"Switch between bakeries to manage multiple locations","bakeries.tip3":"Specialized bakeries attract customers looking for specific items","bakeries.tip4":"Upgrade each bakery independently for maximum profit","notifications.orderAccepted":"Order Accepted","notifications.orderAcceptedMessage":"You have accepted a new order!","notifications.orderCompleted":"Order Completed!","notifications.orderCompletedMessage":"You earned ${{reward}} and gained experience!","notifications.orderDeclined":"Order Declined","notifications.orderDeclinedMessage":"Order has been removed from your queue.","notifications.bakeryPurchased":"Bakery Purchased!","notifications.bakeryPurchasedMessage":"You now own {{name}}!","notifications.bakerySwitched":"Bakery Switched","notifications.bakerySwitchedMessage":"Switched to {{name}}","common.accept":"Accept","common.decline":"Decline","common.complete":"Complete","common.purchase":"Purchase","common.upgrade":"Upgrade","common.cancel":"Cancel","common.confirm":"Confirm","common.save":"Save","common.load":"Load","common.delete":"Delete","common.edit":"Edit","common.back":"Back","common.next":"Next","common.previous":"Previous","common.yes":"Yes","common.no":"No","common.create":"Create","common.join":"Join","common.leave":"Leave","common.start":"Start","common.ready":"Ready","common.notReady":"Not Ready","common.send":"Send","common.refresh":"Refresh","common.retry":"Retry","common.reset":"Reset","common.clear":"Clear","common.apply":"Apply","common.warning":"Warning","common.info":"Information","common.success":"Success","common.error":"Error","saveLoad.saveDesc":"Choose a slot to save your progress","saveLoad.loadDesc":"Select a save file to load","saveLoad.saveName":"Save Name","saveLoad.emptySlot":"Empty Slot","saveLoad.selectedSaveSlot":"Selected: Slot {{slot}}","saveLoad.selectedLoadSlot":"Selected: Slot {{slot}}","saveLoad.confirmOverwrite":"Overwrite Save?","saveLoad.overwriteWarning":"This will overwrite the existing save. This action cannot be undone.","saveLoad.overwrite":"Overwrite","saveLoad.fileSlots":"File Slots","saveLoad.gameSlots":"Game Slots","saveLoad.exportSave":"Export Save","saveLoad.importSave":"Import Save","saveLoad.deleteConfirm":"Delete Save?","saveLoad.deleteWarning":"This will permanently delete this save file. This action cannot be undone.","saveLoad.delete":"Delete","gameMenu.title":"Game Menu","gameMenu.subtitle":"Manage your game","gameMenu.resume":"Resume Game","gameMenu.resumeDesc":"Continue playing","gameMenu.save":"Save Game","gameMenu.saveDesc":"Save your progress","gameMenu.load":"Load Game","gameMenu.loadDesc":"Load saved progress","gameMenu.settings":"Settings","gameMenu.settingsDesc":"Game preferences","gameMenu.mainMenu":"Main Menu","gameMenu.mainMenuDesc":"Return to main menu","gameMenu.exit":"Exit Game","gameMenu.exitDesc":"Close the application","gameMenu.tip":"Press ESC to open this menu anytime","settings.discord":"Discord","settings.discordRichPresence":"Discord Rich Presence","settings.discordDescription":"Show your current game status and activity in Discord.","settings.enableDiscordRPC":"Enable Discord Rich Presence","settings.discordConnected":"✅ Connected to Discord","settings.discordDisconnected":"❌ Not connected to Discord","settings.discordInfo":"What is Discord Rich Presence?","settings.discordInfoDesc1":"Discord Rich Presence shows your friends what you're doing in Bake It Out:","settings.discordFeature1":"Your current level and money","settings.discordFeature2":"What you're currently baking","settings.discordFeature3":"Multiplayer room information","settings.discordFeature4":"How long you've been playing","settings.discordInfoDesc2":"Your friends can even join your multiplayer games directly from Discord!","settings.discordTroubleshooting":"Discord Not Connected","settings.discordTrouble1":"Make sure Discord is running on your computer.","settings.discordTrouble2":"Discord Rich Presence only works in the desktop version of the game.","settings.discordTrouble3":"Try restarting both Discord and the game if the connection fails.","settings.discordPrivacy":"Privacy Information","settings.discordPrivacyDesc1":"Discord Rich Presence only shares:","settings.discordPrivacy1":"Your current game activity (public)","settings.discordPrivacy2":"Your player level and progress (public)","settings.discordPrivacy3":"Multiplayer room codes (for joining)","settings.discordPrivacyDesc2":"No personal information or save data is shared with Discord.","settings.discordStatus":"Discord Status","settings.discordInitializing":"\uD83D\uDD04 Initializing Discord RPC...","settings.discordRetrying":"\uD83D\uDD04 Retrying connection...","settings.discordUnavailable":"❌ Discord not available","settings.discordDesktopOnly":"ℹ️ Discord RPC only available in desktop version","error.general":"An error occurred","error.saveLoad":"Failed to save/load game","error.connection":"Connection error","error.fileNotFound":"File not found","error.invalidData":"Invalid data format","error.permissionDenied":"Permission denied","status.loading":"Loading...","status.saving":"Saving...","status.connecting":"Connecting...","status.ready":"Ready","status.success":"Success!","status.failed":"Failed","status.offline":"Offline","status.online":"Online","ui.placeholder":"Enter text...","ui.search":"Search","ui.filter":"Filter","ui.sort":"Sort","ui.ascending":"Ascending","ui.descending":"Descending","ui.selectAll":"Select All","ui.deselectAll":"Deselect All","ui.noResults":"No results found","ui.noData":"No data available","ui.loading":"Loading...","ui.saving":"Saving...","ui.saved":"Saved!","ui.failed":"Failed","ui.retry":"Retry","ui.back":"Back","ui.forward":"Forward","ui.home":"Home","ui.menu":"Menu","ui.options":"Options","ui.preferences":"Preferences","multiplayer.lobby":"\uD83D\uDC65 Multiplayer Lobby","multiplayer.connected":"\uD83D\uDFE2 Connected","multiplayer.disconnected":"\uD83D\uDD34 Disconnected","multiplayer.createRoom":"Create Room","multiplayer.joinRoom":"Join Room","multiplayer.room":"Room","multiplayer.yourName":"Your Name","multiplayer.enterName":"Enter your name","multiplayer.roomName":"Room Name","multiplayer.enterRoomName":"Enter room name","multiplayer.gameMode":"Game Mode","multiplayer.cooperative":"\uD83E\uDD1D Cooperative","multiplayer.competitive":"⚔️ Competitive","multiplayer.maxPlayers":"Max Players: {{count}}","multiplayer.roomId":"Room ID","multiplayer.enterRoomId":"Enter room ID","multiplayer.players":"Players ({{count}})","multiplayer.host":"HOST","multiplayer.level":"Level {{level}}","multiplayer.chat":"Chat","multiplayer.typeMessage":"Type a message...","multiplayer.gameTime":"Game Time: {{time}}","multiplayer.teamStats":"\uD83D\uDCCA Team Stats","multiplayer.ordersCompleted":"Orders Completed:","multiplayer.totalRevenue":"Total Revenue:","multiplayer.teamExperience":"Team Experience:","multiplayer.sharedKitchen":"\uD83C\uDFEA Shared Kitchen","multiplayer.sharedOrders":"\uD83D\uDCCB Shared Orders","multiplayer.sharedInventory":"\uD83D\uDCE6 Shared Inventory","multiplayer.contribution":"Contribution:","multiplayer.online":"\uD83D\uDFE2 Online","multiplayer.status":"Status:","multiplayer.you":"(You)","multiplayer.teamChat":"\uD83D\uDCAC Team Chat","multiplayer.chatPlaceholder":"Chat messages will appear here...","multiplayer.mode.cooperative.description":"\uD83E\uDD1D Cooperative Mode: Work together to complete orders and grow your shared bakery!","multiplayer.mode.competitive.description":"⚔️ Competitive Mode: Compete against other players to complete the most orders!","multiplayer.game.title":"\uD83C\uDFAE Multiplayer Game - {{roomName}}","multiplayer.game.mode":"Mode: {{mode}}","multiplayer.game.playersCount":"Players: {{count}}","multiplayer.game.playing":"\uD83D\uDFE2 Playing","multiplayer.game.leaveGame":"\uD83D\uDEAA Leave Game","multiplayer.game.tabs.game":"Game","multiplayer.game.tabs.players":"Players","multiplayer.game.tabs.chat":"Chat","multiplayer.create.title":"\uD83C\uDFD7️ Create Room","multiplayer.join.title":"\uD83D\uDEAA Join Room","multiplayer.room.info":"Mode: {{mode}} • Players: {{current}}/{{max}}","multiplayer.room.readyUp":"✅ Ready","multiplayer.room.notReady":"⏳ Not Ready","multiplayer.room.startGame":"\uD83D\uDE80 Start Game","multiplayer.room.leaveRoom":"\uD83D\uDEAA Leave","multiplayer.connection.connecting":"Connecting...","multiplayer.connection.reconnecting":"Reconnecting...","multiplayer.connection.failed":"Connection failed","multiplayer.connection.error":"⚠️ {{error}}","multiplayer.system.playerJoined":"{{name}} joined the room","multiplayer.system.playerLeft":"{{name}} left the room","multiplayer.system.gameStarted":"Game started!","multiplayer.system.gameEnded":"Game ended!","multiplayer.system.roomCreated":"Room created successfully","multiplayer.system.roomJoined":"Joined room successfully","bakery.layout.title":"Bakery Layout","bakery.kitchen":"Kitchen","bakery.dining":"Dining Area","bakery.counter":"Service Counter","bakery.layout.kitchen":"Kitchen","bakery.layout.dining":"Dining Area","bakery.layout.counter":"Service Counter","bakery.layout.baking_area":"Baking Area","bakery.layout.prep_area":"Prep Area","bakery.layout.automation_zone":"Automation Zone","bakery.layout.efficiency":"Efficiency","bakery.layout.active_equipment":"Active Equipment","bakery.layout.total_equipment":"Total Equipment","bakery.layout.kitchen_stats":"Kitchen Stats","equipment.status.active":"Active","equipment.status.idle":"Idle","equipment.status.maintenance":"Maintenance","equipment.status.automated":"Automated","equipment.zones.baking":"Baking Zone","equipment.zones.prep":"Prep Zone","equipment.zones.automation":"Automation Zone","dining.room.title":"Dining Room","dining.room.subtitle":"Watch your customers enjoy their meals","dining.room.tables":"Tables","dining.room.customers":"Customers","dining.room.satisfaction":"Satisfaction","dining.room.occupancy":"Occupancy","dining.room.ambient_sounds":"Ambient Sounds","dining.room.decorations":"Decorations","dining.occupied.tables":"Occupied Tables","dining.ambient.sounds":"Toggle ambient sounds","dining.ambient.playing":"Ambient sounds playing","dining.customer.status":"Customer Status","dining.enjoying":"Enjoying","dining.no.customers":"No customers dining currently","dining.waiting.for.customers":"Complete orders to see customers dining","dining.table.number":"Table {{number}}","dining.table.seats":"{{count}} seats","dining.table.occupied":"Occupied","dining.table.available":"Available","dining.table.customer_info":"Customer Info","dining.service.counter":"Service","dining.service.ready":"Ready to Serve","dining.service.waiting":"Waiting","dining.atmosphere.cozy":"Cozy","dining.atmosphere.busy":"Busy","dining.atmosphere.peaceful":"Peaceful","dining.atmosphere.lively":"Lively","customers.manager.title":"Customer Manager","customers.manager.subtitle":"Monitor and serve your customers","customers.manager.active_customers":"Active Customers","customers.manager.total_served":"Total Served","customers.manager.average_satisfaction":"Average Satisfaction","customers.manager.serve_order":"Serve Order","customers.manager.customer_details":"Customer Details","customers.current.list":"Current Customers","customers.table":"Table","customers.patience":"Patience","customers.no.customers":"No customers currently","customers.waiting.for.orders":"Waiting for new orders...","customers.order.details":"Order Details","customers.total":"Total","customers.info":"Customer Info","customers.mood":"Mood","customers.status":"Status","customers.preferences":"Preferences","customers.serve.order":"Serve Order","customers.select.customer":"Select a customer","customers.select.to.view.details":"Select a customer to view details","customers.status.entering":"Entering","customers.status.waiting":"Waiting","customers.status.ordering":"Ordering","customers.status.served":"Served","customers.status.eating":"Eating","customers.status.leaving":"Leaving","customers.mood.happy":"Happy","customers.mood.neutral":"Neutral","customers.mood.impatient":"Impatient","customers.mood.angry":"Angry","customers.info.name":"Name","customers.info.order":"Order","customers.info.patience":"Patience","customers.info.satisfaction":"Satisfaction","customers.info.table":"Table","customers.info.order_value":"Order Value","customers.info.preferences":"Preferences","customers.info.time_seated":"Time Seated","customers.preferences.sweet":"Sweet","customers.preferences.savory":"Savory","customers.preferences.healthy":"Healthy","customers.preferences.indulgent":"Indulgent","customers.preferences.traditional":"Traditional","customers.preferences.exotic":"Exotic","views.traditional":"Traditional","views.layout":"Bakery Layout","views.dining":"Dining Room","views.customers":"Customer Manager","views.switch_view":"Switch View","game.view.traditional":"Traditional View","game.view.layout":"Bakery Layout","game.view.dining":"Dining Room","game.view.customers":"Customer Manager","ingredient.Flour":"Flour","ingredient.Sugar":"Sugar","ingredient.Eggs":"Eggs","ingredient.Butter":"Butter","ingredient.Milk":"Milk","ingredient.Vanilla Extract":"Vanilla Extract","ingredient.Vanilla":"Vanilla","ingredient.Chocolate Chips":"Chocolate Chips","ingredient.Baking Powder":"Baking Powder","ingredient.Salt":"Salt","ingredient.Cinnamon":"Cinnamon","ingredient.Nuts":"Nuts","ingredient.Cream Cheese":"Cream Cheese","ingredient.Honey":"Honey","ingredient.Cocoa Powder":"Cocoa Powder","ingredient.Yeast":"Yeast","equipment.Basic Oven":"Basic Oven","equipment.Hand Mixer":"Hand Mixer","equipment.Professional Oven":"Professional Oven","equipment.Stand Mixer":"Stand Mixer","equipment.Automated Oven":"Automated Oven","equipment.Industrial Mixer":"Industrial Mixer","equipment.Conveyor Belt":"Conveyor Belt","equipment.Display Counter":"Display Counter","equipment.Prep Counter":"Prep Counter","recipe.Chocolate Chip Cookies":"Chocolate Chip Cookies","recipe.Vanilla Muffins":"Vanilla Muffins","recipe.Simple Bread":"Simple Bread","recipe.Cinnamon Rolls":"Cinnamon Rolls","recipe.Sourdough Bread":"Sourdough Bread","recipe.Chocolate Cake":"Chocolate Cake","recipe.Apple Pie":"Apple Pie","recipe.Croissants":"Croissants"},cs:{"game.title":"Bake It Out","game.subtitle":"Ovl\xe1dněte uměn\xed ř\xedzen\xed pek\xe1rny v t\xe9to poutav\xe9 multiplayerov\xe9 hře. Plňte objedn\xe1vky, odemykejte recepty, automatizujte procesy a soutěžte s př\xe1teli!","game.play":"\uD83C\uDFAE Zač\xedt hr\xe1t","game.singlePlayer":"\uD83C\uDFAE Jeden hr\xe1č","game.singlePlayerDesc":"Hrajte s\xf3lo a zdokonalte sv\xe9 pekařsk\xe9 dovednosti","game.multiplayer":"\uD83D\uDC65 Multiplayer","game.multiplayerDesc":"Hrajte s př\xe1teli v kooperativn\xedch nebo soutěžn\xedch režimech","game.english":"\uD83C\uDDFA\uD83C\uDDF8 English","game.czech":"\uD83C\uDDE8\uD83C\uDDFF Čeština","game.home":"\uD83C\uDFE0 Domů","game.close":"✕ Zavř\xedt","game.continue":"\uD83D\uDE80 Pokračovat ve hře","menu.singlePlayer":"Jeden hr\xe1č","menu.multiplayer":"Multiplayer","menu.settings":"Nastaven\xed","menu.credits":"Titulky","menu.exit":"Ukončit","credits.title":"O hře Bake It Out","credits.subtitle":"Informace o hře a titulky","credits.description":"Multiplayerov\xe1 hra na spr\xe1vu pek\xe1rny s real-time spoluprac\xed a podporou lokalizace.","credits.version":"Verze","credits.release":"Datum vyd\xe1n\xed","credits.releaseDate":"Leden 2025","credits.platform":"Platforma","credits.platforms":"Windows, macOS, Linux","credits.close":"Zavř\xedt","credits.features":"Funkce","credits.multiplayer":"Podpora multiplayeru","credits.multiplayerDesc":"Real-time spolupr\xe1ce s př\xe1teli v kooperativn\xedch nebo soutěžn\xedch režimech.","credits.localization":"Lokalizace","credits.localizationDesc":"Pln\xe1 podpora angličtiny a češtiny s jednoduch\xfdm přep\xedn\xe1n\xedm.","credits.progression":"Syst\xe9m postupu","credits.progressionDesc":"Vylepšujte svou pek\xe1rnu, odemykejte recepty a ovl\xe1dněte automatizaci.","credits.automation":"Automatizace","credits.automationDesc":"Pokročil\xe9 automatizačn\xed syst\xe9my pro optimalizaci provozu pek\xe1rny.","credits.technology":"Technologick\xfd stack","credits.team":"V\xfdvojov\xfd t\xfdm","credits.developedBy":"Vyvinuto t\xfdmem","credits.teamDesc":"Vytvořeno s l\xe1skou v\xfdvojov\xfdm t\xfdmem Bake It Out.","credits.thanks":"Speci\xe1ln\xed poděkov\xe1n\xed","credits.thanksPlayers":"Všem našim \xfažasn\xfdm hr\xe1čům a beta testerům","credits.thanksTranslators":"Komunitn\xedm překladatelům za podporu lokalizace","credits.thanksOpenSource":"Open source komunitě za neuvěřiteln\xe9 n\xe1stroje a knihovny","credits.thanksBakers":"Skutečn\xfdm pekařům, kteř\xed inspirovali tuto hru","credits.contact":"Kontakt a podpora","credits.website":"Webov\xe9 str\xe1nky","credits.support":"Podpora","credits.github":"GitHub","credits.discord":"Discord","game.view.traditional":"Tradičn\xed pohled","game.view.layout":"Rozložen\xed pek\xe1rny","game.view.dining":"J\xeddelna","game.view.customers":"Spr\xe1vce z\xe1kazn\xedků","bakery.layout.title":"Rozložen\xed pek\xe1rny","bakery.kitchen":"Kuchyně","bakery.dining":"J\xeddeln\xed oblast","bakery.counter":"Servisn\xed pult","bakery.baking.area":"Oblast pečen\xed","bakery.prep.area":"Př\xedpravn\xe1 oblast","bakery.automation.area":"Automatizace","bakery.kitchen.stats":"Statistiky kuchyně","bakery.active.equipment":"Aktivn\xed vybaven\xed","bakery.automated.equipment":"Automatizovan\xe9 vybaven\xed","bakery.efficiency":"Efektivita","bakery.dining.stats":"Statistiky j\xeddelny","bakery.current.customers":"Současn\xed z\xe1kazn\xedci","bakery.waiting.customers":"Čekaj\xedc\xed","bakery.eating.customers":"Jed\xed","bakery.avg.satisfaction":"Průměrn\xe1 spokojenost","bakery.service.counter":"Servisn\xed pult","bakery.display.case":"Vitrina","bakery.order.queue":"Fronta objedn\xe1vek","bakery.last.updated":"Naposledy aktualizov\xe1no","dining.room.title":"J\xeddelna","dining.room.subtitle":"Sledujte, jak si vaši z\xe1kazn\xedci už\xedvaj\xed j\xeddlo","dining.occupied.tables":"Obsazen\xe9 stoly","dining.ambient.sounds":"Přepnout okoln\xed zvuky","dining.service.counter":"Servis","dining.customer.status":"Stav z\xe1kazn\xedků","dining.enjoying":"Už\xedv\xe1 si","dining.no.customers":"Moment\xe1lně nejsou ž\xe1dn\xed z\xe1kazn\xedci","dining.waiting.for.customers":"Dokončete objedn\xe1vky, abyste viděli z\xe1kazn\xedky j\xedst","dining.ambient.playing":"Hraj\xed okoln\xed zvuky","customers.manager.title":"Spr\xe1vce z\xe1kazn\xedků","customers.manager.subtitle":"Sledujte a obsluhujte sv\xe9 z\xe1kazn\xedky","customers.current.list":"Současn\xed z\xe1kazn\xedci","customers.table":"Stůl","customers.patience":"Trpělivost","customers.no.customers":"Moment\xe1lně ž\xe1dn\xed z\xe1kazn\xedci","customers.waiting.for.orders":"Ček\xe1n\xed na nov\xe9 objedn\xe1vky...","customers.order.details":"Detaily objedn\xe1vky","customers.total":"Celkem","customers.info":"Informace o z\xe1kazn\xedkovi","customers.mood":"N\xe1lada","customers.status":"Stav","customers.preferences":"Preference","customers.select.customer":"Vyberte z\xe1kazn\xedka","customers.select.to.view.details":"Vyberte z\xe1kazn\xedka pro zobrazen\xed detailů","customers.serve.order":"Pod\xe1vat objedn\xe1vku","menu.newGame":"Nov\xe1 hra","menu.continueGame":"Pokračovat ve hře","menu.loadGame":"Nač\xedst hru","menu.selectLanguage":"Vybrat jazyk","menu.about":"O hře","menu.help":"N\xe1pověda","menu.quit":"Ukončit","features.manage.title":"Spravujte svou pek\xe1rnu","features.manage.description":"Přij\xedmejte objedn\xe1vky, pečte lahodn\xe9 v\xfdrobky a obsluhujte spokojen\xe9 z\xe1kazn\xedky","features.levelup.title":"Postupujte a automatizujte","features.levelup.description":"Odemykejte nov\xe9 recepty, kupujte vybaven\xed a automatizujte sv\xe9 procesy","features.multiplayer.title":"Hrajte společně","features.multiplayer.description":"Kooperativn\xed a soutěžn\xed multiplayerov\xe9 režimy s př\xe1teli","status.development":"\uD83D\uDEA7 Hra ve v\xfdvoji - F\xe1ze 5: V\xedcevrstv\xe1 podpora! \uD83D\uDEA7","ui.level":"\xdaroveň {{level}}","ui.money":"{{amount}} Kč","ui.experience":"XP: {{current}}/{{max}}","ui.skillPoints":"SP: {{points}}","ui.achievements":"\uD83C\uDFC6 \xdaspěchy","ui.skills":"\uD83C\uDF1F Dovednosti","ui.automation":"\uD83E\uDD16 Automatizace","kitchen.title":"\uD83C\uDFEA Kuchyně","kitchen.clickToUse":"Klikněte pro použit\xed","kitchen.making":"Připravuje: {{recipe}}","kitchen.timeRemaining":"Čas: {{time}}","inventory.title":"\uD83D\uDCE6 Sklad","inventory.quantity":"Množstv\xed: {{qty}}","inventory.cost":"{{cost}} Kč za kus","orders.title":"\uD83D\uDCCB Objedn\xe1vky","orders.newOrder":"+ Nov\xe1 objedn\xe1vka","orders.accept":"Přijmout","orders.decline":"Odm\xedtnout","orders.complete":"Dokončit","orders.inProgress":"Prob\xedh\xe1","orders.timeLimit":"Čas: {{time}}","orders.reward":"{{amount}} Kč","orders.customer":"Z\xe1kazn\xedk: {{name}}","actions.title":"⚡ Rychl\xe9 akce","actions.buyIngredients":"\uD83D\uDED2 Koupit suroviny","actions.viewRecipes":"\uD83D\uDCD6 Zobrazit recepty","actions.equipmentShop":"\uD83D\uDD27 Obchod s vybaven\xedm","modal.recipes.title":"\uD83D\uDCD6 Kniha receptů","modal.shop.title":"\uD83D\uDED2 Obchod se surovinami","modal.baking.title":"\uD83D\uDD25 {{equipment}} - Vyberte recept","modal.achievements.title":"\uD83C\uDFC6 \xdaspěchy","modal.skills.title":"\uD83C\uDF1F Strom dovednost\xed","modal.automation.title":"\uD83E\uDD16 Ovl\xe1d\xe1n\xed automatizace","modal.equipmentShop.title":"\uD83C\uDFEA Obchod s vybaven\xedm","modal.settings.title":"⚙️ Nastaven\xed","modal.bakeries.title":"\uD83C\uDFEA Spr\xe1vce pek\xe1ren","modal.levelUp.title":"Postup na vyšš\xed \xfaroveň!","modal.levelUp.subtitle":"Dos\xe1hli jste \xfarovně {{level}}!","recipes.all":"Vše","recipes.cookies":"Sušenky","recipes.cakes":"Dorty","recipes.bread":"Chl\xe9b","recipes.pastries":"Pečivo","recipes.ingredients":"Suroviny:","recipes.difficulty":"Obt\xedžnost:","recipes.time":"Čas:","recipes.canCraft":"✅ Lze vyrobit","recipes.unlockLevel":"Odemčeno na \xfarovni {{level}}","recipes.noRecipes":"V t\xe9to kategorii nejsou k dispozici ž\xe1dn\xe9 recepty.","recipes.levelUpToUnlock":"Postupte na vyšš\xed \xfaroveň pro odemčen\xed dalš\xedch receptů!","shop.currentStock":"Aktu\xe1ln\xed z\xe1soba: {{quantity}}","shop.buy":"Koupit","shop.tooExpensive":"Př\xedliš drah\xe9","shop.tips.title":"\uD83D\uDCA1 Tipy pro nakupov\xe1n\xed","shop.tips.bulk":"• Kupujte suroviny ve velk\xe9m množstv\xed pro \xfasporu času","shop.tips.stock":"• Sledujte \xfaroveň sv\xfdch z\xe1sob","shop.tips.rare":"• Někter\xe9 recepty vyžaduj\xed vz\xe1cn\xe9 suroviny","shop.tips.prices":"• Ceny se mohou lišit podle dostupnosti","baking.selectRecipe":"Vyberte recept","baking.noRecipes":"Ž\xe1dn\xe9 recepty k dispozici","baking.noIngredients":"Nem\xe1te dostatek surovin pro v\xfdrobu jak\xe9hokoli receptu.","baking.buyIngredients":"Koupit suroviny","baking.startBaking":"\uD83D\uDD25 Zač\xedt p\xe9ct","baking.instructions":"\uD83D\uDCCB Pokyny pro pečen\xed {{recipe}}","baking.expectedReward":"Oček\xe1van\xe1 odměna: {{amount}} Kč","baking.makesSure":"Ujistěte se, že m\xe1te všechny suroviny před zač\xe1tkem!","baking.inProgress":"Pečen\xed prob\xedh\xe1...","baking.completed":"Pečen\xed dokončeno!","baking.cancelled":"Pečen\xed zrušeno","baking.timeRemaining":"Zb\xfdvaj\xedc\xed čas: {{time}}","baking.clickToCollect":"Klikněte pro vyzvednut\xed","achievements.completed":"{{completed}} z {{total}} \xfaspěchů dokončeno","achievements.overallProgress":"Celkov\xfd pokrok","achievements.progress":"Pokrok","achievements.reward":"Odměna:","achievements.noAchievements":"V t\xe9to kategorii nejsou ž\xe1dn\xe9 \xfaspěchy.","skills.availablePoints":"Dostupn\xe9 body dovednost\xed: {{points}}","skills.efficiency":"Efektivita","skills.automation":"Automatizace","skills.quality":"Kvalita","skills.business":"Podnik\xe1n\xed","skills.effects":"Efekty:","skills.requires":"Vyžaduje: {{requirements}}","skills.requiresLevel":"Vyžaduje \xfaroveň {{level}}","skills.maxed":"✅ Maxim\xe1ln\xed","skills.upgrade":"⬆️ Vylepšit ({{cost}} SP)","skills.locked":"\uD83D\uDD12 Uzamčeno","skills.noSkills":"V t\xe9to kategorii nejsou ž\xe1dn\xe9 dovednosti.","skills.tips.title":"\uD83D\uDCA1 Tipy pro dovednosti","skills.tips.earnPoints":"• Z\xedsk\xe1vejte body dovednost\xed postupem na vyšš\xed \xfaroveň (1 bod každ\xe9 2 \xfarovně)","skills.tips.prerequisites":"• Někter\xe9 dovednosti vyžaduj\xed nejprve odemčen\xed jin\xfdch dovednost\xed","skills.tips.playstyle":"• Zaměřte se na dovednosti, kter\xe9 odpov\xeddaj\xed vašemu stylu hry","skills.tips.efficiency":"• Dovednosti efektivity pom\xe1haj\xed se spr\xe1vou zdrojů","automation.masterControl":"\uD83C\uDF9B️ Hlavn\xed ovl\xe1d\xe1n\xed","automation.enableAutomation":"Povolit automatizaci","automation.autoStart":"Automatick\xe9 spuštěn\xed vybaven\xed","automation.priorityMode":"\uD83C\uDFAF Režim priority","automation.efficiency":"Efektivita (objedn\xe1vky prvn\xed)","automation.profit":"Zisk (nejvyšš\xed hodnota)","automation.speed":"Rychlost (nejrychlejš\xed recepty)","automation.priorityDescription":"Jak automatizace vyb\xedr\xe1, co p\xe9ct","automation.performance":"⚡ V\xfdkon","automation.maxJobs":"Max současn\xfdch \xfaloh: {{jobs}}","automation.safety":"\uD83D\uDEE1️ Bezpečnost","automation.stopWhenLow":"Zastavit, když suroviny klesnou pod: {{threshold}}","automation.upgrades":"\uD83D\uDCA1 Vylepšen\xed automatizace","automation.upgradesDescription":"Vylepšete efektivitu, rychlost a inteligenci vaš\xed automatizace.","automation.purchase":"Koupit","automation.noUpgrades":"Na vaš\xed současn\xe9 \xfarovni nejsou k dispozici ž\xe1dn\xe1 vylepšen\xed.","automation.levelUpForUpgrades":"Postupte na vyšš\xed \xfaroveň pro odemčen\xed dalš\xedch vylepšen\xed automatizace!","automation.automatedEquipment":"Automatizovan\xe9 vybaven\xed","automation.activeUpgrades":"Aktivn\xed vylepšen\xed","automation.automationStatus":"Stav automatizace","automation.equipmentStatus":"\uD83C\uDFED Stav vybaven\xed","automation.running":"Běž\xed","automation.idle":"Nečinn\xe9","automation.noAutomatedEquipment":"Ž\xe1dn\xe9 automatizovan\xe9 vybaven\xed k dispozici.","automation.purchaseAutoEquipment":"Kupte si auto-vybaven\xed z obchodu pro zač\xe1tek!","equipmentShop.upgradeYourBakery":"Vylepšete svou pek\xe1rnu profesion\xe1ln\xedm vybaven\xedm","equipmentShop.basic":"Z\xe1kladn\xed","equipmentShop.automated":"Automatizovan\xe9","equipmentShop.advanced":"Pokročil\xe9","equipmentShop.efficiency":"Efektivita: {{efficiency}}x","equipmentShop.automation":"Automatizace:","equipmentShop.unlockLevel":"\xdaroveň odemčen\xed: {{level}}","equipmentShop.purchase":"\uD83D\uDCB0 Koupit","equipmentShop.noEquipment":"V t\xe9to kategorii nen\xed k dispozici ž\xe1dn\xe9 vybaven\xed.","equipmentShop.levelUpForEquipment":"Postupte na vyšš\xed \xfaroveň pro odemčen\xed dalš\xedho vybaven\xed!","equipmentShop.tips.title":"\uD83D\uDCA1 Tipy pro vybaven\xed","equipmentShop.tips.automated":"• Automatizovan\xe9 vybaven\xed může běžet bez vašeho dohledu","equipmentShop.tips.efficiency":"• Vyšš\xed efektivita znamen\xe1 rychlejš\xed v\xfdrobu a lepš\xed kvalitu","equipmentShop.tips.conveyor":"• Dopravn\xed p\xe1sy spojuj\xed vybaven\xed pro bezprobl\xe9mov\xfd pracovn\xed tok","equipmentShop.tips.advanced":"• Pokročil\xe9 vybaven\xed se odemyk\xe1 na vyšš\xedch \xfarovn\xedch","levelUp.levelRewards":"\uD83C\uDF81 Odměny za \xfaroveň","levelUp.whatsNext":"\uD83D\uDCA1 Co d\xe1l?","levelUp.checkRecipes":"• Pod\xedvejte se na nov\xe9 recepty ve sv\xe9 knize receptů","levelUp.visitShop":"• Navštivte obchod pro nov\xe9 vybaven\xed","levelUp.challengingOrders":"• Přijměte n\xe1ročnějš\xed objedn\xe1vky","levelUp.investSkills":"• Investujte do vylepšen\xed dovednost\xed","settings.title":"⚙️ Nastaven\xed","settings.general":"Obecn\xe9","settings.audio":"Zvuk","settings.graphics":"Grafika","settings.save":"Uložen\xed a data","settings.language":"\uD83C\uDF0D Jazyk","settings.gameplay":"\uD83C\uDFAE Hratelnost","settings.notifications":"Povolit ozn\xe1men\xed","settings.tutorials":"Zobrazit n\xe1vody","settings.animationSpeed":"Rychlost animace","settings.sound":"Zvukov\xe9 efekty","settings.music":"Hudba na pozad\xed","settings.quality":"\uD83C\uDFA8 Kvalita grafiky","settings.autoSave":"\uD83D\uDCBE Automatick\xe9 ukl\xe1d\xe1n\xed","settings.enableAutoSave":"Povolit automatick\xe9 ukl\xe1d\xe1n\xed","settings.dataManagement":"\uD83D\uDCC1 Spr\xe1va dat","settings.exportSave":"\uD83D\uDCE4 Exportovat uložen\xed","settings.importSave":"\uD83D\uDCE5 Importovat uložen\xed","settings.cloudSync":"☁️ Cloudov\xe1 synchronizace","settings.cloudSyncDescription":"Cloudov\xe1 synchronizace v\xe1m umožňuje uložit pokrok online a hr\xe1t na v\xedce zař\xedzen\xedch.","settings.comingSoon":"Již brzy","bakeries.title":"\uD83C\uDFEA Spr\xe1vce pek\xe1ren","bakeries.subtitle":"Spravujte sv\xe9 pek\xe1rensk\xe9 imp\xe9rium","bakeries.owned":"Moje pek\xe1rny","bakeries.available":"Dostupn\xe9","bakeries.current":"Aktu\xe1ln\xed","bakeries.level":"\xdaroveň","bakeries.specialization":"Specializace","bakeries.equipment":"Vybaven\xed","bakeries.orders":"Aktivn\xed objedn\xe1vky","bakeries.switchTo":"Přepnout na","bakeries.noOwned":"Ještě nevlastn\xedte ž\xe1dn\xe9 pek\xe1rny.","bakeries.purchase":"\uD83D\uDCB0 Koupit","bakeries.tooExpensive":"\uD83D\uDCB8 Př\xedliš drah\xe9","bakeries.allOwned":"Vlastn\xedte všechny dostupn\xe9 pek\xe1rny!","bakeries.tips":"\uD83D\uDCA1 Tipy pro pek\xe1rny","bakeries.tip1":"Každ\xe1 pek\xe1rna se specializuje na různ\xe9 produkty pro bonusovou efektivitu","bakeries.tip2":"Přep\xednejte mezi pek\xe1rnami pro spr\xe1vu v\xedce lokalit","bakeries.tip3":"Specializovan\xe9 pek\xe1rny přitahuj\xed z\xe1kazn\xedky hledaj\xedc\xed konkr\xe9tn\xed položky","bakeries.tip4":"Vylepšujte každou pek\xe1rnu nez\xe1visle pro maxim\xe1ln\xed zisk","notifications.orderAccepted":"Objedn\xe1vka přijata","notifications.orderAcceptedMessage":"Přijali jste novou objedn\xe1vku!","notifications.orderCompleted":"Objedn\xe1vka dokončena!","notifications.orderCompletedMessage":"Z\xedskali jste {{reward}} Kč a zkušenosti!","notifications.orderDeclined":"Objedn\xe1vka odm\xedtnuta","notifications.orderDeclinedMessage":"Objedn\xe1vka byla odstraněna z vaš\xed fronty.","notifications.bakeryPurchased":"Pek\xe1rna zakoupena!","notifications.bakeryPurchasedMessage":"Nyn\xed vlastn\xedte {{name}}!","notifications.bakerySwitched":"Pek\xe1rna přepnuta","notifications.bakerySwitchedMessage":"Přepnuto na {{name}}","common.accept":"Přijmout","common.decline":"Odm\xedtnout","common.complete":"Dokončit","common.purchase":"Koupit","common.upgrade":"Vylepšit","common.cancel":"Zrušit","common.confirm":"Potvrdit","common.save":"Uložit","common.load":"Nač\xedst","common.delete":"Smazat","common.edit":"Upravit","common.back":"Zpět","common.next":"Dalš\xed","common.previous":"Předchoz\xed","common.yes":"Ano","common.no":"Ne","common.create":"Vytvořit","common.join":"Připojit se","common.leave":"Odej\xedt","common.start":"Zač\xedt","common.ready":"Připraven","common.notReady":"Nepřipraven","common.send":"Odeslat","common.refresh":"Obnovit","common.retry":"Zkusit znovu","common.reset":"Resetovat","common.clear":"Vymazat","common.apply":"Použ\xedt","common.warning":"Varov\xe1n\xed","common.info":"Informace","common.success":"\xdaspěch","common.error":"Chyba","saveLoad.saveDesc":"Vyberte slot pro uložen\xed vašeho postupu","saveLoad.loadDesc":"Vyberte soubor uložen\xed k načten\xed","saveLoad.saveName":"N\xe1zev uložen\xed","saveLoad.emptySlot":"Pr\xe1zdn\xfd slot","saveLoad.selectedSaveSlot":"Vybr\xe1n: Slot {{slot}}","saveLoad.selectedLoadSlot":"Vybr\xe1n: Slot {{slot}}","saveLoad.confirmOverwrite":"Přepsat uložen\xed?","saveLoad.overwriteWarning":"Toto přep\xedše existuj\xedc\xed uložen\xed. Tuto akci nelze vr\xe1tit zpět.","saveLoad.overwrite":"Přepsat","saveLoad.fileSlots":"Souborov\xe9 sloty","saveLoad.gameSlots":"Hern\xed sloty","saveLoad.exportSave":"Exportovat uložen\xed","saveLoad.importSave":"Importovat uložen\xed","saveLoad.deleteConfirm":"Smazat uložen\xed?","saveLoad.deleteWarning":"Toto trvale smaže tento soubor uložen\xed. Tuto akci nelze vr\xe1tit zpět.","saveLoad.delete":"Smazat","gameMenu.title":"Hern\xed menu","gameMenu.subtitle":"Spravujte svou hru","gameMenu.resume":"Pokračovat ve hře","gameMenu.resumeDesc":"Pokračovat v hran\xed","gameMenu.save":"Uložit hru","gameMenu.saveDesc":"Uložit v\xe1š postup","gameMenu.load":"Nač\xedst hru","gameMenu.loadDesc":"Nač\xedst uložen\xfd postup","gameMenu.settings":"Nastaven\xed","gameMenu.settingsDesc":"Hern\xed předvolby","gameMenu.mainMenu":"Hlavn\xed menu","gameMenu.mainMenuDesc":"N\xe1vrat do hlavn\xedho menu","gameMenu.exit":"Ukončit hru","gameMenu.exitDesc":"Zavř\xedt aplikaci","gameMenu.tip":"Stiskněte ESC pro otevřen\xed tohoto menu kdykoli","settings.discord":"Discord","settings.discordRichPresence":"Discord Rich Presence","settings.discordDescription":"Zobrazit v\xe1š aktu\xe1ln\xed hern\xed stav a aktivitu v Discordu.","settings.enableDiscordRPC":"Povolit Discord Rich Presence","settings.discordConnected":"✅ Připojeno k Discordu","settings.discordDisconnected":"❌ Nepřipojeno k Discordu","settings.discordInfo":"Co je Discord Rich Presence?","settings.discordInfoDesc1":"Discord Rich Presence ukazuje vašim př\xe1telům, co děl\xe1te v Bake It Out:","settings.discordFeature1":"Vaši aktu\xe1ln\xed \xfaroveň a pen\xedze","settings.discordFeature2":"Co pr\xe1vě pečete","settings.discordFeature3":"Informace o multiplayer m\xedstnosti","settings.discordFeature4":"Jak dlouho hrajete","settings.discordInfoDesc2":"Vaši př\xe1tel\xe9 se mohou připojit k vašim multiplayer hr\xe1m př\xedmo z Discordu!","settings.discordTroubleshooting":"Discord nen\xed připojen","settings.discordTrouble1":"Ujistěte se, že Discord běž\xed na vašem poč\xedtači.","settings.discordTrouble2":"Discord Rich Presence funguje pouze v desktopov\xe9 verzi hry.","settings.discordTrouble3":"Zkuste restartovat Discord i hru, pokud se připojen\xed nezdař\xed.","settings.discordPrivacy":"Informace o soukrom\xed","settings.discordPrivacyDesc1":"Discord Rich Presence sd\xedl\xed pouze:","settings.discordPrivacy1":"Vaši aktu\xe1ln\xed hern\xed aktivitu (veřejn\xe9)","settings.discordPrivacy2":"Vaši \xfaroveň hr\xe1če a postup (veřejn\xe9)","settings.discordPrivacy3":"K\xf3dy multiplayer m\xedstnost\xed (pro připojen\xed)","settings.discordPrivacyDesc2":"Ž\xe1dn\xe9 osobn\xed informace nebo uložen\xe1 data nejsou sd\xedlena s Discordem.","settings.discordStatus":"Stav Discordu","settings.discordInitializing":"\uD83D\uDD04 Inicializace Discord RPC...","settings.discordRetrying":"\uD83D\uDD04 Opakov\xe1n\xed připojen\xed...","settings.discordUnavailable":"❌ Discord nen\xed dostupn\xfd","settings.discordDesktopOnly":"ℹ️ Discord RPC dostupn\xfd pouze v desktopov\xe9 verzi","error.general":"Došlo k chybě","error.saveLoad":"Nepodařilo se uložit/nač\xedst hru","error.connection":"Chyba připojen\xed","error.fileNotFound":"Soubor nenalezen","error.invalidData":"Neplatn\xfd form\xe1t dat","error.permissionDenied":"Př\xedstup odepřen","status.loading":"Nač\xedt\xe1n\xed...","status.saving":"Ukl\xe1d\xe1n\xed...","status.connecting":"Připojov\xe1n\xed...","status.ready":"Připraven","status.success":"\xdaspěch!","status.failed":"Ne\xfaspěšn\xe9","status.offline":"Offline","status.online":"Online","ui.placeholder":"Zadejte text...","ui.search":"Hledat","ui.filter":"Filtrovat","ui.sort":"Seřadit","ui.ascending":"Vzestupně","ui.descending":"Sestupně","ui.selectAll":"Vybrat vše","ui.deselectAll":"Zrušit v\xfdběr","ui.noResults":"Ž\xe1dn\xe9 v\xfdsledky","ui.noData":"Ž\xe1dn\xe1 data k dispozici","ui.loading":"Nač\xedt\xe1n\xed...","ui.saving":"Ukl\xe1d\xe1n\xed...","ui.saved":"Uloženo!","ui.failed":"Ne\xfaspěšn\xe9","ui.retry":"Zkusit znovu","ui.back":"Zpět","ui.forward":"Vpřed","ui.home":"Domů","ui.menu":"Menu","ui.options":"Možnosti","ui.preferences":"Předvolby","multiplayer.lobby":"\uD83D\uDC65 Multiplayerov\xe1 lobby","multiplayer.connected":"\uD83D\uDFE2 Připojeno","multiplayer.disconnected":"\uD83D\uDD34 Odpojeno","multiplayer.createRoom":"Vytvořit m\xedstnost","multiplayer.joinRoom":"Připojit se k m\xedstnosti","multiplayer.room":"M\xedstnost","multiplayer.yourName":"Vaše jm\xe9no","multiplayer.enterName":"Zadejte sv\xe9 jm\xe9no","multiplayer.roomName":"N\xe1zev m\xedstnosti","multiplayer.enterRoomName":"Zadejte n\xe1zev m\xedstnosti","multiplayer.gameMode":"Hern\xed režim","multiplayer.cooperative":"\uD83E\uDD1D Kooperativn\xed","multiplayer.competitive":"⚔️ Soutěžn\xed","multiplayer.maxPlayers":"Max hr\xe1čů: {{count}}","multiplayer.roomId":"ID m\xedstnosti","multiplayer.enterRoomId":"Zadejte ID m\xedstnosti","multiplayer.players":"Hr\xe1či ({{count}})","multiplayer.host":"HOSTITEL","multiplayer.level":"\xdaroveň {{level}}","multiplayer.chat":"Chat","multiplayer.typeMessage":"Napište zpr\xe1vu...","multiplayer.gameTime":"Hern\xed čas: {{time}}","multiplayer.teamStats":"\uD83D\uDCCA T\xfdmov\xe9 statistiky","multiplayer.ordersCompleted":"Dokončen\xe9 objedn\xe1vky:","multiplayer.totalRevenue":"Celkov\xfd př\xedjem:","multiplayer.teamExperience":"T\xfdmov\xe9 zkušenosti:","multiplayer.sharedKitchen":"\uD83C\uDFEA Sd\xedlen\xe1 kuchyně","multiplayer.sharedOrders":"\uD83D\uDCCB Sd\xedlen\xe9 objedn\xe1vky","multiplayer.sharedInventory":"\uD83D\uDCE6 Sd\xedlen\xfd sklad","multiplayer.contribution":"Př\xedspěvek:","multiplayer.online":"\uD83D\uDFE2 Online","multiplayer.status":"Stav:","multiplayer.you":"(Vy)","multiplayer.teamChat":"\uD83D\uDCAC T\xfdmov\xfd chat","multiplayer.chatPlaceholder":"Zde se zobraz\xed zpr\xe1vy chatu...","multiplayer.mode.cooperative.description":"\uD83E\uDD1D Kooperativn\xed režim: Spolupracujte na dokončov\xe1n\xed objedn\xe1vek a rozvoji sd\xedlen\xe9 pek\xe1rny!","multiplayer.mode.competitive.description":"⚔️ Soutěžn\xed režim: Soutěžte s ostatn\xedmi hr\xe1či o dokončen\xed nejv\xedce objedn\xe1vek!","multiplayer.game.title":"\uD83C\uDFAE Multiplayerov\xe1 hra - {{roomName}}","multiplayer.game.mode":"Režim: {{mode}}","multiplayer.game.playersCount":"Hr\xe1či: {{count}}","multiplayer.game.playing":"\uD83D\uDFE2 Hraje se","multiplayer.game.leaveGame":"\uD83D\uDEAA Opustit hru","multiplayer.game.tabs.game":"Hra","multiplayer.game.tabs.players":"Hr\xe1či","multiplayer.game.tabs.chat":"Chat","multiplayer.create.title":"\uD83C\uDFD7️ Vytvořit m\xedstnost","multiplayer.join.title":"\uD83D\uDEAA Připojit se k m\xedstnosti","multiplayer.room.info":"Režim: {{mode}} • Hr\xe1či: {{current}}/{{max}}","multiplayer.room.readyUp":"✅ Připraven","multiplayer.room.notReady":"⏳ Nepřipraven","multiplayer.room.startGame":"\uD83D\uDE80 Zač\xedt hru","multiplayer.room.leaveRoom":"\uD83D\uDEAA Opustit","multiplayer.connection.connecting":"Připojov\xe1n\xed...","multiplayer.connection.reconnecting":"Znovu se připojuje...","multiplayer.connection.failed":"Připojen\xed selhalo","multiplayer.connection.error":"⚠️ {{error}}","multiplayer.system.playerJoined":"{{name}} se připojil do m\xedstnosti","multiplayer.system.playerLeft":"{{name}} opustil m\xedstnost","multiplayer.system.gameStarted":"Hra začala!","multiplayer.system.gameEnded":"Hra skončila!","multiplayer.system.roomCreated":"M\xedstnost byla \xfaspěšně vytvořena","multiplayer.system.roomJoined":"\xdaspěšně jste se připojili do m\xedstnosti","bakery.layout.title":"Rozložen\xed pek\xe1rny","bakery.kitchen":"Kuchyně","bakery.dining":"J\xeddeln\xed oblast","bakery.counter":"Servisn\xed pult","bakery.layout.kitchen":"Kuchyně","bakery.layout.dining":"J\xeddeln\xed oblast","bakery.layout.counter":"Servisn\xed pult","bakery.layout.baking_area":"Oblast pečen\xed","bakery.layout.prep_area":"Př\xedpravn\xe1 oblast","bakery.layout.automation_zone":"Automatizačn\xed z\xf3na","bakery.layout.efficiency":"Efektivita","bakery.layout.active_equipment":"Aktivn\xed vybaven\xed","bakery.layout.total_equipment":"Celkov\xe9 vybaven\xed","bakery.layout.kitchen_stats":"Statistiky kuchyně","equipment.status.active":"Aktivn\xed","equipment.status.idle":"Nečinn\xe9","equipment.status.maintenance":"\xdadržba","equipment.status.automated":"Automatizovan\xe9","equipment.zones.baking":"Z\xf3na pečen\xed","equipment.zones.prep":"Př\xedpravn\xe1 z\xf3na","equipment.zones.automation":"Automatizačn\xed z\xf3na","dining.room.title":"J\xeddelna","dining.room.subtitle":"Sledujte, jak si vaši z\xe1kazn\xedci už\xedvaj\xed j\xeddlo","dining.room.tables":"Stoly","dining.room.customers":"Z\xe1kazn\xedci","dining.room.satisfaction":"Spokojenost","dining.room.occupancy":"Obsazenost","dining.room.ambient_sounds":"Okoln\xed zvuky","dining.room.decorations":"Dekorace","dining.occupied.tables":"Obsazen\xe9 stoly","dining.ambient.sounds":"Přepnout okoln\xed zvuky","dining.ambient.playing":"Hraj\xed okoln\xed zvuky","dining.customer.status":"Stav z\xe1kazn\xedků","dining.enjoying":"Už\xedv\xe1 si","dining.no.customers":"Moment\xe1lně nejsou ž\xe1dn\xed z\xe1kazn\xedci","dining.waiting.for.customers":"Dokončete objedn\xe1vky, abyste viděli z\xe1kazn\xedky j\xedst","dining.table.number":"Stůl {{number}}","dining.table.seats":"{{count}} m\xedst","dining.table.occupied":"Obsazeno","dining.table.available":"Voln\xe9","dining.table.customer_info":"Informace o z\xe1kazn\xedkovi","dining.service.counter":"Servis","dining.service.ready":"Připraveno k pod\xe1n\xed","dining.service.waiting":"Ček\xe1n\xed","dining.atmosphere.cozy":"\xdatuln\xe9","dining.atmosphere.busy":"Rušn\xe9","dining.atmosphere.peaceful":"Klidn\xe9","dining.atmosphere.lively":"Živ\xe9","customers.manager.title":"Spr\xe1vce z\xe1kazn\xedků","customers.manager.subtitle":"Sledujte a obsluhujte sv\xe9 z\xe1kazn\xedky","customers.manager.active_customers":"Aktivn\xed z\xe1kazn\xedci","customers.manager.total_served":"Celkem obslouženo","customers.manager.average_satisfaction":"Průměrn\xe1 spokojenost","customers.manager.serve_order":"Obsloužit objedn\xe1vku","customers.manager.customer_details":"Detaily z\xe1kazn\xedka","customers.current.list":"Současn\xed z\xe1kazn\xedci","customers.table":"Stůl","customers.patience":"Trpělivost","customers.no.customers":"Moment\xe1lně ž\xe1dn\xed z\xe1kazn\xedci","customers.waiting.for.orders":"Ček\xe1n\xed na nov\xe9 objedn\xe1vky...","customers.order.details":"Detaily objedn\xe1vky","customers.total":"Celkem","customers.info":"Informace o z\xe1kazn\xedkovi","customers.mood":"N\xe1lada","customers.status":"Stav","customers.preferences":"Preference","customers.serve.order":"Obsloužit objedn\xe1vku","customers.select.customer":"Vyberte z\xe1kazn\xedka","customers.select.to.view.details":"Vyberte z\xe1kazn\xedka pro zobrazen\xed detailů","customers.status.entering":"Vstupuje","customers.status.waiting":"Ček\xe1","customers.status.ordering":"Objedn\xe1v\xe1","customers.status.served":"Obsloužen","customers.status.eating":"J\xed","customers.status.leaving":"Odch\xe1z\xed","customers.mood.happy":"Šťastn\xfd","customers.mood.neutral":"Neutr\xe1ln\xed","customers.mood.impatient":"Netrpěliv\xfd","customers.mood.angry":"Rozzloben\xfd","customers.info.name":"Jm\xe9no","customers.info.order":"Objedn\xe1vka","customers.info.patience":"Trpělivost","customers.info.satisfaction":"Spokojenost","customers.info.table":"Stůl","customers.info.order_value":"Hodnota objedn\xe1vky","customers.info.preferences":"Preference","customers.info.time_seated":"Čas u stolu","customers.preferences.sweet":"Sladk\xe9","customers.preferences.savory":"Slan\xe9","customers.preferences.healthy":"Zdrav\xe9","customers.preferences.indulgent":"Lahodn\xe9","customers.preferences.traditional":"Tradičn\xed","customers.preferences.exotic":"Exotick\xe9","views.traditional":"Tradičn\xed","views.layout":"Rozložen\xed pek\xe1rny","views.dining":"J\xeddelna","views.customers":"Spr\xe1vce z\xe1kazn\xedků","views.switch_view":"Přepnout pohled","game.view.traditional":"Tradičn\xed pohled","game.view.layout":"Rozložen\xed pek\xe1rny","game.view.dining":"J\xeddelna","game.view.customers":"Spr\xe1vce z\xe1kazn\xedků","ingredient.Flour":"Mouka","ingredient.Sugar":"Cukr","ingredient.Eggs":"Vejce","ingredient.Butter":"M\xe1slo","ingredient.Milk":"Ml\xe9ko","ingredient.Vanilla Extract":"Vanilkov\xfd extrakt","ingredient.Vanilla":"Vanilka","ingredient.Chocolate Chips":"Čokol\xe1dov\xe9 kousky","ingredient.Baking Powder":"Pr\xe1šek do pečiva","ingredient.Salt":"Sůl","ingredient.Cinnamon":"Skořice","ingredient.Nuts":"Ořechy","ingredient.Cream Cheese":"Kr\xe9mov\xfd s\xfdr","ingredient.Honey":"Med","ingredient.Cocoa Powder":"Kakaov\xfd pr\xe1šek","ingredient.Yeast":"Drožd\xed","equipment.Basic Oven":"Z\xe1kladn\xed trouba","equipment.Hand Mixer":"Ručn\xed mix\xe9r","equipment.Professional Oven":"Profesion\xe1ln\xed trouba","equipment.Stand Mixer":"Stojanov\xfd mix\xe9r","equipment.Automated Oven":"Automatick\xe1 trouba","equipment.Industrial Mixer":"Průmyslov\xfd mix\xe9r","equipment.Conveyor Belt":"Dopravn\xed p\xe1s","equipment.Display Counter":"V\xfdstavn\xed pult","equipment.Prep Counter":"Př\xedpravn\xfd pult","recipe.Chocolate Chip Cookies":"Sušenky s čokol\xe1dou","recipe.Vanilla Muffins":"Vanilkov\xe9 muffiny","recipe.Simple Bread":"Jednoduch\xfd chl\xe9b","recipe.Cinnamon Rolls":"Skořicov\xe9 z\xe1viny","recipe.Sourdough Bread":"Kv\xe1skov\xfd chl\xe9b","recipe.Chocolate Cake":"Čokol\xe1dov\xfd dort","recipe.Apple Pie":"Jablečn\xfd kol\xe1č","recipe.Croissants":"Croissanty"}};function h({children:a}){let[b,c]=(0,e.useState)("en"),[h,i]=(0,e.useState)(!1);return(0,d.jsx)(f.Provider,{value:{language:b,setLanguage:a=>{c(a)},t:(a,c)=>{let d=g[b][a]||a;return c&&Object.entries(c).forEach(([a,b])=>{d=d.replace(`{{${a}}}`,b)}),d}},children:a})}function i(){let a=(0,e.useContext)(f);return void 0===a?(console.warn("useLanguage called outside of LanguageProvider, using fallback"),{language:"en",setLanguage:()=>{},t:a=>a}):a}},4431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>m,metadata:()=>l});var d=c(7413),e=c(2376),f=c.n(e),g=c(8726),h=c.n(g);c(1135);var i=c(7043),j=c(6999),k=c(2650);let l={title:"Bake It Out - Bakery Management Game",description:"Master the art of bakery management in this engaging multiplayer game with Czech and English support"};function m({children:a}){return(0,d.jsxs)("html",{lang:"en",suppressHydrationWarning:!0,children:[(0,d.jsxs)("head",{children:[(0,d.jsx)("link",{rel:"icon",href:"data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>\uD83E\uDDC1</text></svg>"}),(0,d.jsx)("script",{dangerouslySetInnerHTML:{__html:`
              // Prevent hydration mismatches by ensuring consistent initial state
              if (typeof window !== 'undefined') {
                window.__HYDRATION_SAFE__ = true;
              }
            `}})]}),(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,suppressHydrationWarning:!0,children:(0,d.jsx)(i.LanguageProvider,{children:(0,d.jsx)(j.MultiplayerProvider,{children:(0,d.jsx)(k.DiscordRPCProvider,{children:a})})})})]})}},5460:(a,b,c)=>{Promise.resolve().then(c.bind(c,2650)),Promise.resolve().then(c.bind(c,7043)),Promise.resolve().then(c.bind(c,6999))},6005:(a,b,c)=>{"use strict";c.d(b,{B:()=>g});let d="1.0.0",e="bakeItOut_gameSave";class f{constructor(){this.autoSaveInterval=null,this.cloudSyncEnabled=!1,this.initializeAutoSave()}saveToLocal(a){try{let b={version:d,timestamp:Date.now(),...a},c=JSON.stringify(b);return localStorage.setItem(e,c),console.log("Game saved to local storage"),!0}catch(a){return console.error("Failed to save game to local storage:",a),!1}}loadFromLocal(){try{let a=localStorage.getItem(e);if(!a)return null;let b=JSON.parse(a);if(b.version!==d)return console.warn("Save version mismatch, attempting migration"),this.migrateSave(b);return console.log("Game loaded from local storage"),b}catch(a){return console.error("Failed to load game from local storage:",a),null}}deleteLocalSave(){try{return localStorage.removeItem(e),console.log("Local save deleted"),!0}catch(a){return console.error("Failed to delete local save:",a),!1}}initializeAutoSave(){}triggerAutoSave(){let a=new CustomEvent("autoSave");window.dispatchEvent(a)}stopAutoSave(){this.autoSaveInterval&&(clearInterval(this.autoSaveInterval),this.autoSaveInterval=null)}async saveToCloud(a,b){try{let c={id:`${b}_${Date.now()}`,userId:b,deviceId:this.getDeviceId(),lastModified:Date.now(),gameVersion:d,bakeryCount:a.bakeries?.length||1,playerLevel:a.player.level};return console.log("Cloud save would be implemented here",{gameData:a,metadata:c}),!0}catch(a){return console.error("Failed to save to cloud:",a),!1}}async loadFromCloud(a){try{return console.log("Cloud load would be implemented here",{userId:a}),null}catch(a){return console.error("Failed to load from cloud:",a),null}}async syncWithCloud(a,b){try{let c=await this.loadFromCloud(b);if(!c)return await this.saveToCloud(a,b),a;if(c.timestamp>a.timestamp)return console.log("Cloud save is newer, using cloud data"),c;return console.log("Local save is newer, uploading to cloud"),await this.saveToCloud(a,b),a}catch(b){return console.error("Failed to sync with cloud:",b),a}}migrateSave(a){try{let b={version:d,timestamp:a.timestamp||Date.now(),player:{level:a.player?.level||1,experience:a.player?.experience||0,money:a.player?.money||100,skillPoints:a.player?.skillPoints||0,totalMoneyEarned:a.player?.totalMoneyEarned||0,totalOrdersCompleted:a.player?.totalOrdersCompleted||0,totalItemsBaked:a.player?.totalItemsBaked||0,unlockedRecipes:a.player?.unlockedRecipes||["chocolate_chip_cookies","vanilla_muffins"],automationUpgrades:a.player?.automationUpgrades||[]},equipment:a.equipment||[],inventory:a.inventory||[],achievements:a.achievements||[],skills:a.skills||[],automationSettings:a.automationSettings||{},gameSettings:{language:a.gameSettings?.language||"en",soundEnabled:a.gameSettings?.soundEnabled??!0,musicEnabled:a.gameSettings?.musicEnabled??!0,notificationsEnabled:a.gameSettings?.notificationsEnabled??!0,autoSaveEnabled:a.gameSettings?.autoSaveEnabled??!0},bakeries:a.bakeries||[],currentBakeryId:a.currentBakeryId||"main"};return console.log("Save migrated successfully"),b}catch(a){return console.error("Failed to migrate save:",a),null}}getDeviceId(){let a=localStorage.getItem("deviceId");return a||(a="device_"+Date.now()+"_"+Math.random().toString(36).substr(2,9),localStorage.setItem("deviceId",a)),a}exportSave(a){return JSON.stringify(a,null,2)}importSave(a){try{let b=JSON.parse(a);return this.migrateSave(b)}catch(a){return console.error("Failed to import save:",a),null}}createBackup(a){try{let b=`${e}_backup_${Date.now()}`;return localStorage.setItem(b,JSON.stringify(a)),this.cleanupOldBackups(),!0}catch(a){return console.error("Failed to create backup:",a),!1}}cleanupOldBackups(){let a=Object.keys(localStorage).filter(a=>a.startsWith(`${e}_backup_`)).sort();for(;a.length>5;){let b=a.shift();b&&localStorage.removeItem(b)}}getBackups(){let a=[];return Object.keys(localStorage).forEach(b=>{if(b.startsWith(`${e}_backup_`))try{let c=JSON.parse(localStorage.getItem(b)||"{}"),d=parseInt(b.split("_").pop()||"0");a.push({key:b,timestamp:d,data:c})}catch(a){console.error("Failed to parse backup:",a)}}),a.sort((a,b)=>b.timestamp-a.timestamp)}}let g=new f},6999:(a,b,c)=>{"use strict";c.d(b,{MultiplayerProvider:()=>e});var d=c(1369);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call MultiplayerProvider() from the server but MultiplayerProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\contexts\\MultiplayerContext.tsx","MultiplayerProvider");(0,d.registerClientReference)(function(){throw Error("Attempted to call useMultiplayer() from the server but useMultiplayer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\contexts\\MultiplayerContext.tsx","useMultiplayer")},7043:(a,b,c)=>{"use strict";c.d(b,{LanguageProvider:()=>e});var d=c(1369);let e=(0,d.registerClientReference)(function(){throw Error("Attempted to call LanguageProvider() from the server but LanguageProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\contexts\\LanguageContext.tsx","LanguageProvider");(0,d.registerClientReference)(function(){throw Error("Attempted to call useLanguage() from the server but useLanguage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\contexts\\LanguageContext.tsx","useLanguage")},7990:()=>{},8196:(a,b,c)=>{Promise.resolve().then(c.bind(c,1100)),Promise.resolve().then(c.bind(c,4393)),Promise.resolve().then(c.bind(c,985))},9721:(a,b,c)=>{"use strict";c.d(b,{Qb:()=>g,dU:()=>i,hF:()=>h,jg:()=>f,x0:()=>j});let d=[{id:"chocolate_chip_cookies",name:"Chocolate Chip Cookies",ingredients:[{name:"Flour",quantity:2},{name:"Sugar",quantity:1},{name:"Butter",quantity:1},{name:"Chocolate Chips",quantity:1}],bakingTime:45,difficulty:1,unlockLevel:1,basePrice:25,category:"cookies"},{id:"vanilla_muffins",name:"Vanilla Muffins",ingredients:[{name:"Flour",quantity:2},{name:"Sugar",quantity:1},{name:"Eggs",quantity:1},{name:"Vanilla",quantity:1}],bakingTime:60,difficulty:1,unlockLevel:1,basePrice:20,category:"cakes"},{id:"cinnamon_rolls",name:"Cinnamon Rolls",ingredients:[{name:"Flour",quantity:3},{name:"Sugar",quantity:2},{name:"Butter",quantity:2},{name:"Eggs",quantity:1}],bakingTime:90,difficulty:2,unlockLevel:2,basePrice:35,category:"pastries"},{id:"chocolate_brownies",name:"Chocolate Brownies",ingredients:[{name:"Flour",quantity:2},{name:"Sugar",quantity:2},{name:"Butter",quantity:1},{name:"Chocolate Chips",quantity:2}],bakingTime:75,difficulty:2,unlockLevel:2,basePrice:30,category:"cakes"},{id:"sourdough_bread",name:"Sourdough Bread",ingredients:[{name:"Flour",quantity:4},{name:"Salt",quantity:1}],bakingTime:180,difficulty:3,unlockLevel:3,basePrice:45,category:"bread"}],e=["Alice Johnson","Bob Smith","Carol Davis","David Wilson","Emma Brown","Frank Miller","Grace Taylor","Henry Anderson","Ivy Thomas","Jack Martinez","Kate Garcia","Liam Rodriguez","Mia Lopez","Noah Gonzalez","Olivia Hernandez","Paul Perez","Quinn Turner","Ruby Phillips","Sam Campbell","Tina Parker"];function f(a){let b=d.filter(b=>b.unlockLevel<=a);0===b.length&&b.push(d[0]);let c=.7>Math.random()?1:.9>Math.random()?2:3,f=[];for(let a=0;a<c;a++){let a=b[Math.floor(Math.random()*b.length)];f.push(a)}let g=Math.ceil(f.reduce((a,b)=>a+b.difficulty,0)/f.length),h=f.reduce((a,b)=>a+b.basePrice,0),i=1.5+ +Math.random(),j=Math.floor(h*(.8+.4*Math.random())),k=Math.floor(f.reduce((a,b)=>a+b.bakingTime,0)*i);return{id:Date.now().toString()+Math.random().toString(36).substr(2,9),customerName:e[Math.floor(Math.random()*e.length)],items:f.map(a=>a.name),timeLimit:k,reward:j,status:"pending",difficulty:Math.min(5,g)}}function g(a,b=!1){let c=10*a,d=b?Math.floor(.5*c):0;return c+d}function h(a,b){return a.ingredients.every(a=>{let c=b.find(b=>b.name===a.name);return c&&c.quantity>=a.quantity})}function i(a){return d.find(b=>b.id===a)}function j(a){return d.filter(b=>b.unlockLevel<=a)}},9727:()=>{},9974:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5227,23)),Promise.resolve().then(c.t.bind(c,6346,23)),Promise.resolve().then(c.t.bind(c,7924,23)),Promise.resolve().then(c.t.bind(c,99,23)),Promise.resolve().then(c.t.bind(c,8243,23)),Promise.resolve().then(c.t.bind(c,8827,23)),Promise.resolve().then(c.t.bind(c,2763,23)),Promise.resolve().then(c.t.bind(c,7173,23)),Promise.resolve().then(c.bind(c,5587))}};