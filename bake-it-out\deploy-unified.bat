@echo off
echo Bake It Out - Unified Server Deployment
echo ==========================================
echo.

REM Colors for output (Windows 10+)
set "GREEN=[92m"
set "YELLOW=[93m"
set "RED=[91m"
set "BLUE=[94m"
set "NC=[0m"

echo %BLUE%🚀 Starting unified server deployment...%NC%
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo %RED%ERROR: Node.js is not installed or not in PATH%NC%
    echo Please install Node.js 18+ from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo %GREEN%✓ Node.js version:%NC%
node --version
echo.

REM Build the client
echo %BLUE%📦 Building client application...%NC%
npm run build
if %errorlevel% neq 0 (
    echo %RED%ERROR: Client build failed%NC%
    pause
    exit /b 1
)
echo %GREEN%✓ Client built successfully%NC%
echo.

REM Install server dependencies
echo %BLUE%📦 Installing server dependencies...%NC%
cd server
npm install
if %errorlevel% neq 0 (
    echo %RED%ERROR: Server dependency installation failed%NC%
    pause
    exit /b 1
)
echo %GREEN%✓ Server dependencies installed%NC%
echo.

REM Setup environment
echo %BLUE%⚙️ Setting up environment...%NC%
if not exist ".env" (
    echo Creating environment configuration...
    copy ".env.example" ".env" >nul
    echo %GREEN%✓ Environment file created%NC%
) else (
    echo %YELLOW%⚠ Environment file already exists%NC%
)
echo.

REM Create necessary directories
echo %BLUE%📁 Creating directories...%NC%
if not exist "logs" mkdir logs
if not exist "uploads" mkdir uploads
if not exist "backups" mkdir backups
echo %GREEN%✓ Directories created%NC%
echo.

REM Update portable distribution
echo %BLUE%📦 Updating portable distribution...%NC%
cd ..
if exist "portable-dist" (
    echo Updating portable distribution...
    
    REM Copy server files
    xcopy /E /I /Y server portable-dist\server >nul
    
    REM Copy built client
    xcopy /E /I /Y out portable-dist\out >nul
    
    echo %GREEN%✓ Portable distribution updated%NC%
) else (
    echo %YELLOW%⚠ Portable distribution not found, creating...%NC%
    node scripts/create-portable.js
    echo %GREEN%✓ Portable distribution created%NC%
)
echo.

REM Test server startup
echo %BLUE%🧪 Testing server startup...%NC%
cd server
timeout /t 2 >nul

REM Start server in background for testing
start /B node src/index.js >test.log 2>&1
set SERVER_PID=%!

REM Wait for server to start
timeout /t 5 >nul

REM Test health endpoint
curl -s http://localhost:3001/health >nul 2>&1
if %errorlevel% equ 0 (
    echo %GREEN%✓ Server health check passed%NC%
) else (
    echo %YELLOW%⚠ Server health check failed (this is normal if curl is not installed)%NC%
)

REM Stop test server
taskkill /F /IM node.exe >nul 2>&1

echo.
echo %GREEN%🎉 Deployment completed successfully!%NC%
echo.
echo %BLUE%📋 Next steps:%NC%
echo 1. Review and update server/.env file if needed
echo 2. Start the server: cd server ^&^& npm start
echo 3. Or use portable distribution: portable-dist/start-server.bat
echo 4. Access dashboard: http://localhost:3001/dashboard
echo 5. Game client: http://localhost:3002 (if running game server)
echo.
echo %BLUE%🔗 Server endpoints:%NC%
echo • Health check: http://localhost:3001/health
echo • Dashboard: http://localhost:3001/dashboard
echo • API: http://localhost:3001/api
echo • Cloud saves: http://localhost:3001/api/saves
echo • Multiplayer: http://localhost:3001/api/multiplayer
echo.
echo %BLUE%📊 Features enabled:%NC%
echo • ✓ Cloud saves
echo • ✓ Multiplayer gaming
echo • ✓ User authentication
echo • ✓ Web dashboard
echo • ✓ Real-time monitoring
echo • ✓ Game analytics
echo.

REM Check if MongoDB is available
echo %BLUE%🔍 Checking MongoDB...%NC%
where mongod >nul 2>&1
if %errorlevel% equ 0 (
    echo %GREEN%✓ MongoDB is installed%NC%
) else (
    echo %YELLOW%⚠ MongoDB not found locally%NC%
    echo   You can:
    echo   - Install MongoDB locally
    echo   - Use MongoDB Atlas (cloud)
    echo   - Use Docker: docker run -d -p 27017:27017 mongo:6.0
)
echo.

REM Check if Docker is available
echo %BLUE%🐳 Checking Docker...%NC%
where docker >nul 2>&1
if %errorlevel% equ 0 (
    echo %GREEN%✓ Docker is available%NC%
    echo   To run with Docker: docker-compose up -d
) else (
    echo %YELLOW%⚠ Docker not found%NC%
    echo   Install Docker for containerized deployment
)
echo.

echo %GREEN%🚀 Bake It Out Unified Server is ready to launch!%NC%
echo.
pause
