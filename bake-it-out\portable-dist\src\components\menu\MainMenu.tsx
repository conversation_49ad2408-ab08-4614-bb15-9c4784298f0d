'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { useLanguage } from '@/contexts/LanguageContext'

interface MainMenuProps {
  onStartSinglePlayer: () => void
  onStartMultiplayer: () => void
  onShowSettings: () => void
  onShowCredits: () => void
  onExit?: () => void
}

export function MainMenu({ 
  onStartSinglePlayer, 
  onStartMultiplayer, 
  onShowSettings, 
  onShowCredits,
  onExit 
}: MainMenuProps) {
  const { language, setLanguage, t } = useLanguage()
  const [showLanguageMenu, setShowLanguageMenu] = useState(false)

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-100 via-yellow-50 to-orange-200 flex items-center justify-center relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-10 left-10 text-6xl">🥖</div>
        <div className="absolute top-20 right-20 text-4xl">🧁</div>
        <div className="absolute bottom-20 left-20 text-5xl">🍰</div>
        <div className="absolute bottom-10 right-10 text-3xl">🥐</div>
        <div className="absolute top-1/2 left-1/4 text-4xl">🍪</div>
        <div className="absolute top-1/3 right-1/3 text-5xl">🎂</div>
      </div>

      <div className="relative z-10 text-center space-y-8 p-8 max-w-md w-full">
        {/* Game Logo */}
        <div className="space-y-4">
          <div className="text-6xl mb-4">🥖</div>
          <h1 className="text-5xl font-bold text-orange-800 mb-2">
            Bake It Out
          </h1>
          <p className="text-lg text-orange-600 font-medium">
            {t('game.subtitle', 'Multiplayer Bakery Management')}
          </p>
        </div>

        {/* Main Menu Buttons */}
        <div className="space-y-4">
          <Button
            size="lg"
            className="w-full text-lg py-4 bg-gradient-to-r from-orange-500 to-yellow-500 hover:from-orange-600 hover:to-yellow-600 text-white font-semibold shadow-lg transform hover:scale-105 transition-all duration-200"
            onClick={onStartSinglePlayer}
          >
            🎮 {t('menu.singlePlayer', 'Single Player')}
          </Button>

          <Button
            size="lg"
            className="w-full text-lg py-4 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold shadow-lg transform hover:scale-105 transition-all duration-200"
            onClick={onStartMultiplayer}
          >
            👥 {t('menu.multiplayer', 'Multiplayer')}
          </Button>

          <div className="grid grid-cols-2 gap-3">
            <Button
              variant="secondary"
              size="md"
              className="py-3 bg-white/80 hover:bg-white shadow-md"
              onClick={onShowSettings}
            >
              ⚙️ {t('menu.settings', 'Settings')}
            </Button>

            <Button
              variant="secondary"
              size="md"
              className="py-3 bg-white/80 hover:bg-white shadow-md"
              onClick={() => setShowLanguageMenu(!showLanguageMenu)}
            >
              🌍 {language === 'en' ? 'English' : 'Čeština'}
            </Button>
          </div>

          {/* Language Selection */}
          {showLanguageMenu && (
            <div className="bg-white rounded-lg shadow-lg p-4 space-y-2">
              <h3 className="font-semibold text-gray-800 mb-2">
                {t('menu.selectLanguage', 'Select Language')}
              </h3>
              <div className="grid grid-cols-2 gap-2">
                <Button
                  variant={language === 'en' ? 'primary' : 'secondary'}
                  size="sm"
                  onClick={() => {
                    setLanguage('en')
                    setShowLanguageMenu(false)
                  }}
                >
                  🇺🇸 English
                </Button>
                <Button
                  variant={language === 'cs' ? 'primary' : 'secondary'}
                  size="sm"
                  onClick={() => {
                    setLanguage('cs')
                    setShowLanguageMenu(false)
                  }}
                >
                  🇨🇿 Čeština
                </Button>
              </div>
            </div>
          )}

          <div className="grid grid-cols-2 gap-3">
            <Button
              variant="secondary"
              size="md"
              className="py-3 bg-white/80 hover:bg-white shadow-md"
              onClick={onShowCredits}
            >
              ℹ️ {t('menu.about', 'About')}
            </Button>

            {onExit && (
              <Button
                variant="secondary"
                size="md"
                className="py-3 bg-red-100 hover:bg-red-200 text-red-700 shadow-md"
                onClick={onExit}
              >
                🚪 {t('menu.exit', 'Exit')}
              </Button>
            )}
          </div>
        </div>

        {/* Version Info */}
        <div className="text-sm text-orange-500 opacity-75">
          v1.0.0 - {t('menu.version', 'Beta Version')}
        </div>
      </div>
    </div>
  )
}
