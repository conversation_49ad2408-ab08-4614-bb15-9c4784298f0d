// Socket.IO client setup for multiplayer functionality

import { io, Socket } from 'socket.io-client'

export interface Player {
  id: string
  name: string
  avatar: string
  level: number
  isHost: boolean
  isReady: boolean
  bakeryId?: string
}

export interface GameRoom {
  id: string
  name: string
  mode: 'cooperative' | 'competitive' | 'sandbox'
  maxPlayers: number
  currentPlayers: number
  players: Player[]
  gameState: 'waiting' | 'starting' | 'playing' | 'paused' | 'finished'
  settings: {
    gameMode: 'cooperative' | 'competitive'
    timeLimit?: number
    targetScore?: number
    difficulty: 'easy' | 'medium' | 'hard'
    allowSpectators: boolean
  }
  createdAt: number
  startedAt?: number
}

export interface MultiplayerGameState {
  roomId: string
  players: Record<string, {
    playerId: string
    name: string
    level: number
    money: number
    experience: number
    skillPoints: number
    bakeryId: string
    isOnline: boolean
    lastActivity: number
  }>
  sharedResources: {
    inventory: Record<string, number>
    orders: any[]
    equipment: any[]
    automationJobs: any[]
  }
  gameStats: {
    totalOrders: number
    totalRevenue: number
    totalExperience: number
    gameStartTime: number
    gameEndTime?: number
  }
  events: {
    id: string
    type: 'order_completed' | 'equipment_purchased' | 'player_joined' | 'player_left' | 'game_started' | 'game_ended'
    playerId?: string
    data: any
    timestamp: number
  }[]
}

export interface SocketEvents {
  // Room management
  'create_room': (roomData: Partial<GameRoom>) => void
  'join_room': (roomId: string, playerData: Partial<Player>) => void
  'leave_room': (roomId: string) => void
  'room_created': (room: GameRoom) => void
  'room_joined': (room: GameRoom, player: Player) => void
  'room_left': (roomId: string) => void
  'room_updated': (room: GameRoom) => void
  'player_joined': (player: Player) => void
  'player_left': (playerId: string) => void
  'player_updated': (player: Player) => void
  
  // Game state synchronization
  'game_state_update': (gameState: Partial<MultiplayerGameState>) => void
  'game_state_sync': (gameState: MultiplayerGameState) => void
  'player_action': (action: {
    type: string
    playerId: string
    data: any
    timestamp: number
  }) => void
  
  // Game events
  'game_start': (roomId: string) => void
  'game_pause': (roomId: string) => void
  'game_resume': (roomId: string) => void
  'game_end': (roomId: string, results: any) => void
  'game_started': (gameState: MultiplayerGameState) => void
  'game_paused': () => void
  'game_resumed': () => void
  'game_ended': (results: any) => void
  
  // Chat and communication
  'send_message': (message: {
    playerId: string
    playerName: string
    content: string
    timestamp: number
  }) => void
  'message_received': (message: {
    playerId: string
    playerName: string
    content: string
    timestamp: number
  }) => void
  
  // Error handling
  'error': (error: {
    code: string
    message: string
    details?: any
  }) => void
  'disconnect': () => void
  'reconnect': () => void
}

class SocketManager {
  private socket: Socket | null = null
  private isConnected = false
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private currentRoom: GameRoom | null = null
  private currentPlayer: Player | null = null

  constructor() {
    // Don't auto-connect to prevent errors when server isn't running
    // Connection will be initiated when multiplayer features are used
  }

  connect() {
    if (this.socket?.connected) return

    try {
      const token = localStorage.getItem('accessToken')
      this.socket = io(process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001', {
        transports: ['websocket', 'polling'],
        timeout: 10000,
        forceNew: true,
        autoConnect: false, // Don't auto-connect to prevent immediate errors
        auth: {
          token: token
        }
      })

      this.setupEventListeners()
      this.socket.connect() // Manually connect after setting up listeners
    } catch (error) {
      console.warn('Failed to initialize socket connection:', error)
    }
  }

  private setupEventListeners() {
    if (!this.socket) return

    this.socket.on('connect', () => {
      console.log('Connected to multiplayer server')
      this.isConnected = true
      this.reconnectAttempts = 0
    })

    this.socket.on('disconnect', (reason) => {
      console.log('Disconnected from multiplayer server:', reason)
      this.isConnected = false
      
      if (reason === 'io server disconnect') {
        // Server disconnected, try to reconnect
        this.handleReconnect()
      }
    })

    this.socket.on('connect_error', (error) => {
      console.warn('Multiplayer server not available:', error.message)
      this.isConnected = false
      // Don't attempt reconnection immediately to avoid spam
    })

    this.socket.on('error', (error) => {
      console.warn('Socket error:', error.message)
    })
  }

  private handleReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++
      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`)

      setTimeout(() => {
        this.connect()
      }, Math.pow(2, this.reconnectAttempts) * 1000) // Exponential backoff
    } else {
      console.error('Max reconnection attempts reached')
    }
  }

  // Public method to check connection status
  getConnectionStatus() {
    return {
      isConnected: this.isConnected,
      socket: this.socket
    }
  }

  // Room management methods
  createRoom(roomData: Partial<GameRoom>): Promise<GameRoom> {
    return new Promise((resolve, reject) => {
      if (!this.socket?.connected) {
        reject(new Error('Not connected to server'))
        return
      }

      this.socket.emit('create_room', roomData)
      
      this.socket.once('room_created', (room: GameRoom) => {
        this.currentRoom = room
        resolve(room)
      })

      this.socket.once('error', (error) => {
        reject(new Error(error.message))
      })
    })
  }

  joinRoom(roomId: string, playerData: Partial<Player>): Promise<{ room: GameRoom, player: Player }> {
    return new Promise((resolve, reject) => {
      if (!this.socket?.connected) {
        reject(new Error('Not connected to server'))
        return
      }

      this.socket.emit('join_room', roomId, playerData)
      
      this.socket.once('room_joined', (room: GameRoom, player: Player) => {
        this.currentRoom = room
        this.currentPlayer = player
        resolve({ room, player })
      })

      this.socket.once('error', (error) => {
        reject(new Error(error.message))
      })
    })
  }

  leaveRoom() {
    if (this.socket?.connected && this.currentRoom) {
      this.socket.emit('leave_room', this.currentRoom.id)
      this.currentRoom = null
      this.currentPlayer = null
    }
  }

  // Game state methods
  sendPlayerAction(action: {
    type: string
    data: any
  }) {
    if (this.socket?.connected && this.currentPlayer) {
      this.socket.emit('player_action', {
        ...action,
        playerId: this.currentPlayer.id,
        timestamp: Date.now()
      })
    }
  }

  sendMessage(content: string) {
    if (this.socket?.connected && this.currentPlayer) {
      this.socket.emit('send_message', {
        playerId: this.currentPlayer.id,
        playerName: this.currentPlayer.name,
        content,
        timestamp: Date.now()
      })
    }
  }

  // Event subscription methods
  on<K extends keyof SocketEvents>(event: K, callback: SocketEvents[K]) {
    this.socket?.on(event, callback as any)
  }

  off<K extends keyof SocketEvents>(event: K, callback?: SocketEvents[K]) {
    this.socket?.off(event, callback as any)
  }

  once<K extends keyof SocketEvents>(event: K, callback: SocketEvents[K]) {
    this.socket?.once(event, callback as any)
  }

  // Utility methods
  isSocketConnected(): boolean {
    return this.isConnected && this.socket?.connected === true
  }

  getCurrentRoom(): GameRoom | null {
    return this.currentRoom
  }

  getCurrentPlayer(): Player | null {
    return this.currentPlayer
  }

  disconnect() {
    if (this.socket) {
      this.socket.disconnect()
      this.socket = null
      this.isConnected = false
      this.currentRoom = null
      this.currentPlayer = null
    }
  }
}

// Singleton instance
export const socketManager = new SocketManager()

// React hook for using socket in components
export function useSocket() {
  return socketManager
}
