'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { useLanguage } from '@/contexts/LanguageContext'
import { useDiscordRPC } from '@/contexts/DiscordRPCContext'
import { saveSystem } from '@/lib/saveSystem'

interface GameSettings {
  language: 'en' | 'cs'
  soundEnabled: boolean
  musicEnabled: boolean
  notificationsEnabled: boolean
  autoSaveEnabled: boolean
  graphicsQuality: 'low' | 'medium' | 'high'
  animationSpeed: number
  showTutorials: boolean
}

interface SettingsModalProps {
  isOpen: boolean
  onClose: () => void
  settings: GameSettings
  onSettingsChange: (settings: Partial<GameSettings>) => void
}

export function SettingsModal({ isOpen, onClose, settings, onSettingsChange }: SettingsModalProps) {
  const { language, setLanguage, t } = useLanguage()
  const { isEnabled: discordRPCEnabled, isConnected: discordRPCConnected, setEnabled: setDiscordRPCEnabled } = useDiscordRPC()
  const [activeTab, setActiveTab] = useState<'general' | 'audio' | 'graphics' | 'save' | 'discord'>('general')

  if (!isOpen) return null

  const handleSettingChange = (key: keyof GameSettings, value: any) => {
    onSettingsChange({ [key]: value })
  }

  const handleLanguageChange = (newLanguage: 'en' | 'cs') => {
    setLanguage(newLanguage)
    handleSettingChange('language', newLanguage)
  }

  const exportSave = () => {
    // This would get the current game state and export it
    const gameData = {
      version: '1.0.0',
      timestamp: Date.now(),
      player: {}, // Would be filled with actual game data
      equipment: [],
      inventory: [],
      achievements: [],
      skills: [],
      automationSettings: {},
      gameSettings: settings,
      bakeries: [],
      currentBakeryId: 'main'
    }
    
    const saveString = saveSystem.exportSave(gameData as any)
    const blob = new Blob([saveString], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `bake-it-out-save-${Date.now()}.json`
    a.click()
    URL.revokeObjectURL(url)
  }

  const importSave = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const saveString = e.target?.result as string
        const gameData = saveSystem.importSave(saveString)
        if (gameData) {
          // This would load the imported data into the game
          console.log('Save imported successfully:', gameData)
          alert('Save imported successfully!')
        } else {
          alert('Failed to import save file')
        }
      } catch (error) {
        alert('Invalid save file')
      }
    }
    reader.readAsText(file)
  }

  const tabs = [
    { id: 'general', name: t('settings.general') || 'General', icon: '⚙️' },
    { id: 'audio', name: t('settings.audio') || 'Audio', icon: '🔊' },
    { id: 'graphics', name: t('settings.graphics') || 'Graphics', icon: '🎨' },
    { id: 'save', name: t('settings.save') || 'Save & Data', icon: '💾' },
    { id: 'discord', name: t('settings.discord') || 'Discord', icon: '🎮' }
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-orange-800">
              {t('settings.title') || '⚙️ Settings'}
            </h2>
            <Button variant="secondary" onClick={onClose}>
              {t('game.close') || '✕ Close'}
            </Button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <div className="flex space-x-0">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`px-4 py-3 font-medium text-sm border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-orange-500 text-orange-600 bg-orange-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                {tab.icon} {tab.name}
              </button>
            ))}
          </div>
        </div>

        <div className="p-6 max-h-[60vh] overflow-y-auto">
          {/* General Settings */}
          {activeTab === 'general' && (
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold text-gray-800 mb-3">
                  {t('settings.language') || '🌍 Language'}
                </h3>
                <div className="flex space-x-2">
                  <Button
                    variant={language === 'en' ? 'primary' : 'secondary'}
                    size="sm"
                    onClick={() => handleLanguageChange('en')}
                  >
                    🇺🇸 English
                  </Button>
                  <Button
                    variant={language === 'cs' ? 'primary' : 'secondary'}
                    size="sm"
                    onClick={() => handleLanguageChange('cs')}
                  >
                    🇨🇿 Čeština
                  </Button>
                </div>
              </div>

              <div>
                <h3 className="font-semibold text-gray-800 mb-3">
                  {t('settings.gameplay') || '🎮 Gameplay'}
                </h3>
                <div className="space-y-3">
                  <label className="flex items-center justify-between">
                    <span>{t('settings.notifications') || 'Enable Notifications'}</span>
                    <input
                      type="checkbox"
                      checked={settings.notificationsEnabled}
                      onChange={(e) => handleSettingChange('notificationsEnabled', e.target.checked)}
                      className="rounded"
                    />
                  </label>
                  <label className="flex items-center justify-between">
                    <span>{t('settings.tutorials') || 'Show Tutorials'}</span>
                    <input
                      type="checkbox"
                      checked={settings.showTutorials}
                      onChange={(e) => handleSettingChange('showTutorials', e.target.checked)}
                      className="rounded"
                    />
                  </label>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t('settings.animationSpeed') || 'Animation Speed'}: {settings.animationSpeed}x
                    </label>
                    <input
                      type="range"
                      min="0.5"
                      max="2"
                      step="0.1"
                      value={settings.animationSpeed}
                      onChange={(e) => handleSettingChange('animationSpeed', parseFloat(e.target.value))}
                      className="w-full"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Audio Settings */}
          {activeTab === 'audio' && (
            <div className="space-y-6">
              <div className="space-y-4">
                <label className="flex items-center justify-between">
                  <span className="flex items-center space-x-2">
                    <span>🔊</span>
                    <span>{t('settings.sound') || 'Sound Effects'}</span>
                  </span>
                  <input
                    type="checkbox"
                    checked={settings.soundEnabled}
                    onChange={(e) => handleSettingChange('soundEnabled', e.target.checked)}
                    className="rounded"
                  />
                </label>
                <label className="flex items-center justify-between">
                  <span className="flex items-center space-x-2">
                    <span>🎵</span>
                    <span>{t('settings.music') || 'Background Music'}</span>
                  </span>
                  <input
                    type="checkbox"
                    checked={settings.musicEnabled}
                    onChange={(e) => handleSettingChange('musicEnabled', e.target.checked)}
                    className="rounded"
                  />
                </label>
              </div>
            </div>
          )}

          {/* Graphics Settings */}
          {activeTab === 'graphics' && (
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold text-gray-800 mb-3">
                  {t('settings.quality') || '🎨 Graphics Quality'}
                </h3>
                <div className="space-x-2">
                  {['low', 'medium', 'high'].map(quality => (
                    <Button
                      key={quality}
                      variant={settings.graphicsQuality === quality ? 'primary' : 'secondary'}
                      size="sm"
                      onClick={() => handleSettingChange('graphicsQuality', quality)}
                    >
                      {quality.charAt(0).toUpperCase() + quality.slice(1)}
                    </Button>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Save & Data Settings */}
          {activeTab === 'save' && (
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold text-gray-800 mb-3">
                  {t('settings.autoSave') || '💾 Auto-Save'}
                </h3>
                <label className="flex items-center justify-between">
                  <span>{t('settings.enableAutoSave') || 'Enable Auto-Save'}</span>
                  <input
                    type="checkbox"
                    checked={settings.autoSaveEnabled}
                    onChange={(e) => handleSettingChange('autoSaveEnabled', e.target.checked)}
                    className="rounded"
                  />
                </label>
              </div>

              <div>
                <h3 className="font-semibold text-gray-800 mb-3">
                  {t('settings.dataManagement') || '📁 Data Management'}
                </h3>
                <div className="space-y-3">
                  <Button variant="secondary" onClick={exportSave} className="w-full">
                    {t('settings.exportSave') || '📤 Export Save'}
                  </Button>
                  <div>
                    <input
                      type="file"
                      accept=".json"
                      onChange={importSave}
                      className="hidden"
                      id="import-save"
                    />
                    <Button
                      variant="secondary"
                      onClick={() => document.getElementById('import-save')?.click()}
                      className="w-full"
                    >
                      {t('settings.importSave') || '📥 Import Save'}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="bg-yellow-50 p-4 rounded-lg">
                <h4 className="font-medium text-yellow-800 mb-2">
                  {t('settings.cloudSync') || '☁️ Cloud Sync'}
                </h4>
                <p className="text-sm text-yellow-700 mb-3">
                  {t('settings.cloudSyncDescription') || 'Cloud sync allows you to save your progress online and play across multiple devices.'}
                </p>
                <Button variant="secondary" size="sm" disabled>
                  {t('settings.comingSoon') || 'Coming Soon'}
                </Button>
              </div>
            </div>
          )}

          {/* Discord Settings */}
          {activeTab === 'discord' && (
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold text-gray-800 mb-3">
                  🎮 {t('settings.discordRichPresence') || 'Discord Rich Presence'}
                </h3>
                <p className="text-sm text-gray-600 mb-4">
                  {t('settings.discordDescription') || 'Show your current game status and activity in Discord.'}
                </p>

                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-medium text-gray-800">
                      {t('settings.enableDiscordRPC') || 'Enable Discord Rich Presence'}
                    </div>
                    <div className="text-sm text-gray-600">
                      {discordRPCConnected
                        ? t('settings.discordConnected') || '✅ Connected to Discord'
                        : t('settings.discordDisconnected') || '❌ Not connected to Discord'
                      }
                    </div>
                  </div>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={discordRPCEnabled}
                      onChange={(e) => setDiscordRPCEnabled(e.target.checked)}
                      className="sr-only peer"
                    />
                    <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                  </label>
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-800 mb-2">
                  ℹ️ {t('settings.discordInfo') || 'What is Discord Rich Presence?'}
                </h4>
                <div className="text-sm text-blue-700 space-y-2">
                  <p>{t('settings.discordInfoDesc1') || 'Discord Rich Presence shows your friends what you\'re doing in Bake It Out:'}</p>
                  <ul className="list-disc list-inside ml-4 space-y-1">
                    <li>{t('settings.discordFeature1') || 'Your current level and money'}</li>
                    <li>{t('settings.discordFeature2') || 'What you\'re currently baking'}</li>
                    <li>{t('settings.discordFeature3') || 'Multiplayer room information'}</li>
                    <li>{t('settings.discordFeature4') || 'How long you\'ve been playing'}</li>
                  </ul>
                  <p className="mt-2">
                    {t('settings.discordInfoDesc2') || 'Your friends can even join your multiplayer games directly from Discord!'}
                  </p>
                </div>
              </div>

              {discordRPCEnabled && !discordRPCConnected && (
                <div className="bg-yellow-50 p-4 rounded-lg">
                  <h4 className="font-medium text-yellow-800 mb-2">
                    ⚠️ {t('settings.discordTroubleshooting') || 'Discord Not Connected'}
                  </h4>
                  <div className="text-sm text-yellow-700 space-y-2">
                    <p>{t('settings.discordTrouble1') || 'Make sure Discord is running on your computer.'}</p>
                    <p>{t('settings.discordTrouble2') || 'Discord Rich Presence only works in the desktop version of the game.'}</p>
                    <p>{t('settings.discordTrouble3') || 'Try restarting both Discord and the game if the connection fails.'}</p>
                  </div>
                </div>
              )}

              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-medium text-green-800 mb-2">
                  🔒 {t('settings.discordPrivacy') || 'Privacy Information'}
                </h4>
                <div className="text-sm text-green-700 space-y-2">
                  <p>{t('settings.discordPrivacyDesc1') || 'Discord Rich Presence only shares:'}</p>
                  <ul className="list-disc list-inside ml-4 space-y-1">
                    <li>{t('settings.discordPrivacy1') || 'Your current game activity (public)'}</li>
                    <li>{t('settings.discordPrivacy2') || 'Your player level and progress (public)'}</li>
                    <li>{t('settings.discordPrivacy3') || 'Multiplayer room codes (for joining)'}</li>
                  </ul>
                  <p className="mt-2">
                    {t('settings.discordPrivacyDesc2') || 'No personal information or save data is shared with Discord.'}
                  </p>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
