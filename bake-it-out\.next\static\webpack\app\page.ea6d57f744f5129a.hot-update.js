"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/contexts/LanguageContext.tsx":
/*!******************************************!*\
  !*** ./src/contexts/LanguageContext.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LanguageProvider: () => (/* binding */ LanguageProvider),\n/* harmony export */   useLanguage: () => (/* binding */ useLanguage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ LanguageProvider,useLanguage auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\nconst LanguageContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\n// Comprehensive translations object\nconst translations = {\n    en: {\n        // Main game\n        'game.title': 'Bake It Out',\n        'game.subtitle': 'Master the art of bakery management in this engaging multiplayer game. Complete orders, unlock recipes, automate your processes, and compete with friends!',\n        'game.play': '🎮 Start Playing',\n        'game.singlePlayer': '🎮 Single Player',\n        'game.singlePlayerDesc': 'Play solo and master your bakery skills',\n        'game.multiplayer': '👥 Multiplayer',\n        'game.multiplayerDesc': 'Play with friends in cooperative or competitive modes',\n        'game.english': '🇺🇸 English',\n        'game.czech': '🇨🇿 Čeština',\n        'game.home': '🏠 Home',\n        'game.close': '✕ Close',\n        'game.continue': '🚀 Continue Playing',\n        // Menu options\n        'menu.singlePlayer': 'Single Player',\n        'menu.multiplayer': 'Multiplayer',\n        'menu.settings': 'Settings',\n        'menu.credits': 'Credits',\n        'menu.exit': 'Exit',\n        // Credits\n        'credits.title': 'About Bake It Out',\n        'credits.subtitle': 'Game Information & Credits',\n        'credits.description': 'A multiplayer bakery management game with real-time collaboration and localization support.',\n        'credits.version': 'Version',\n        'credits.release': 'Release Date',\n        'credits.releaseDate': 'January 2025',\n        'credits.platform': 'Platform',\n        'credits.platforms': 'Windows, macOS, Linux',\n        'credits.contact': 'Contact',\n        'credits.facebook': 'Facebook',\n        'credits.close': 'Close',\n        'credits.features': 'Features',\n        'credits.multiplayer': 'Multiplayer Support',\n        'credits.multiplayerDesc': 'Real-time collaboration with friends in cooperative or competitive modes.',\n        'credits.localization': 'Localization',\n        'credits.localizationDesc': 'Full support for English and Czech languages with easy switching.',\n        'credits.progression': 'Progression System',\n        'credits.progressionDesc': 'Level up your bakery, unlock recipes, and master automation.',\n        'credits.automation': 'Automation',\n        'credits.automationDesc': 'Advanced automation systems to optimize your bakery operations.',\n        'credits.technology': 'Technology Stack',\n        'credits.team': 'Development Team',\n        'credits.developedBy': 'Developed by',\n        'credits.teamDesc': 'Created with passion by the Bake It Out development team.',\n        'credits.thanks': 'Special Thanks',\n        'credits.thanksPlayers': 'All our amazing players and beta testers',\n        'credits.thanksTranslators': 'Community translators for localization support',\n        'credits.thanksOpenSource': 'Open source community for incredible tools and libraries',\n        'credits.thanksBakers': 'Real bakers who inspired this game',\n        'credits.website': 'Website',\n        'credits.support': 'Support',\n        'credits.github': 'GitHub',\n        'credits.discord': 'Discord',\n        // Game Views\n        'game.view.traditional': 'Traditional View',\n        'game.view.layout': 'Bakery Layout',\n        'game.view.dining': 'Dining Room',\n        'game.view.customers': 'Customer Manager',\n        // Bakery Layout\n        'bakery.layout.title': 'Bakery Layout',\n        'bakery.kitchen': 'Kitchen',\n        'bakery.dining': 'Dining Area',\n        'bakery.counter': 'Service Counter',\n        'bakery.baking.area': 'Baking Area',\n        'bakery.prep.area': 'Prep Area',\n        'bakery.automation.area': 'Automation',\n        'bakery.kitchen.stats': 'Kitchen Stats',\n        'bakery.active.equipment': 'Active Equipment',\n        'bakery.automated.equipment': 'Automated Equipment',\n        'bakery.efficiency': 'Efficiency',\n        'bakery.dining.stats': 'Dining Stats',\n        'bakery.current.customers': 'Current Customers',\n        'bakery.waiting.customers': 'Waiting',\n        'bakery.eating.customers': 'Eating',\n        'bakery.avg.satisfaction': 'Avg Satisfaction',\n        'bakery.service.counter': 'Service Counter',\n        'bakery.display.case': 'Display Case',\n        'bakery.order.queue': 'Order Queue',\n        'bakery.last.updated': 'Last updated',\n        // Dining Room\n        'dining.room.title': 'Dining Room',\n        'dining.room.subtitle': 'Watch your customers enjoy their meals',\n        'dining.occupied.tables': 'Occupied Tables',\n        'dining.ambient.sounds': 'Toggle ambient sounds',\n        'dining.service.counter': 'Service',\n        'dining.customer.status': 'Customer Status',\n        'dining.enjoying': 'Enjoying',\n        'dining.no.customers': 'No customers dining currently',\n        'dining.waiting.for.customers': 'Complete orders to see customers dining',\n        'dining.ambient.playing': 'Ambient sounds playing',\n        // Customer Manager\n        'customers.manager.title': 'Customer Manager',\n        'customers.manager.subtitle': 'Monitor and serve your customers',\n        'customers.current.list': 'Current Customers',\n        'customers.table': 'Table',\n        'customers.patience': 'Patience',\n        'customers.no.customers': 'No customers currently',\n        'customers.waiting.for.orders': 'Waiting for new orders...',\n        'customers.order.details': 'Order Details',\n        'customers.total': 'Total',\n        'customers.info': 'Customer Info',\n        'customers.mood': 'Mood',\n        'customers.status': 'Status',\n        'customers.preferences': 'Preferences',\n        'customers.select.customer': 'Select a customer',\n        'customers.select.to.view.details': 'Select a customer to view details',\n        'customers.serve.order': 'Serve Order',\n        'menu.newGame': 'New Game',\n        'menu.continueGame': 'Continue Game',\n        'menu.loadGame': 'Load Game',\n        'menu.selectLanguage': 'Select Language',\n        'menu.about': 'About',\n        'menu.help': 'Help',\n        'menu.quit': 'Quit',\n        // Features\n        'features.manage.title': 'Manage Your Bakery',\n        'features.manage.description': 'Take orders, bake delicious goods, and serve happy customers',\n        'features.levelup.title': 'Level Up & Automate',\n        'features.levelup.description': 'Unlock new recipes, buy equipment, and automate your processes',\n        'features.multiplayer.title': 'Play Together',\n        'features.multiplayer.description': 'Cooperative and competitive multiplayer modes with friends',\n        'status.development': '🚧 Game in Development - Phase 5: Multilayer Support! 🚧',\n        // Game interface\n        'ui.level': 'Level {{level}}',\n        'ui.money': '${{amount}}',\n        'ui.experience': 'XP: {{current}}/{{max}}',\n        'ui.skillPoints': 'SP: {{points}}',\n        'ui.achievements': '🏆 Achievements',\n        'ui.skills': '🌟 Skills',\n        'ui.automation': '🤖 Automation',\n        // Kitchen\n        'kitchen.title': '🏪 Kitchen',\n        'kitchen.clickToUse': 'Click to use',\n        'kitchen.making': 'Making: {{recipe}}',\n        'kitchen.timeRemaining': 'Time: {{time}}',\n        // Inventory\n        'inventory.title': '📦 Inventory',\n        'inventory.quantity': 'Qty: {{qty}}',\n        'inventory.cost': '${{cost}} each',\n        // Orders\n        'orders.title': '📋 Orders',\n        'orders.newOrder': '+ New Order',\n        'orders.accept': 'Accept',\n        'orders.decline': 'Decline',\n        'orders.complete': 'Complete',\n        'orders.inProgress': 'In Progress',\n        'orders.timeLimit': 'Time: {{time}}',\n        'orders.reward': '${{amount}}',\n        'orders.customer': 'Customer: {{name}}',\n        // Quick Actions\n        'actions.title': '⚡ Quick Actions',\n        'actions.buyIngredients': '🛒 Buy Ingredients',\n        'actions.viewRecipes': '📖 View Recipes',\n        'actions.equipmentShop': '🔧 Equipment Shop',\n        // Modals\n        'modal.recipes.title': '📖 Recipe Book',\n        'modal.shop.title': '🛒 Ingredient Shop',\n        'modal.baking.title': '🔥 {{equipment}} - Select Recipe',\n        'modal.achievements.title': '🏆 Achievements',\n        'modal.skills.title': '🌟 Skill Tree',\n        'modal.automation.title': '🤖 Automation Control',\n        'modal.equipmentShop.title': '🏪 Equipment Shop',\n        'modal.settings.title': '⚙️ Settings',\n        'modal.bakeries.title': '🏪 Bakery Manager',\n        'modal.levelUp.title': 'Level Up!',\n        'modal.levelUp.subtitle': 'You reached Level {{level}}!',\n        // Recipe Modal\n        'recipes.all': 'All',\n        'recipes.cookies': 'Cookies',\n        'recipes.cakes': 'Cakes',\n        'recipes.bread': 'Bread',\n        'recipes.pastries': 'Pastries',\n        'recipes.ingredients': 'Ingredients:',\n        'recipes.difficulty': 'Difficulty:',\n        'recipes.time': 'Time:',\n        'recipes.canCraft': '✅ Can Craft',\n        'recipes.unlockLevel': 'Unlocked at Level {{level}}',\n        'recipes.noRecipes': 'No recipes available in this category.',\n        'recipes.levelUpToUnlock': 'Level up to unlock more recipes!',\n        // Shop Modal\n        'shop.currentStock': 'Current stock: {{quantity}}',\n        'shop.buy': 'Buy',\n        'shop.tooExpensive': 'Too Expensive',\n        'shop.tips.title': '💡 Shopping Tips',\n        'shop.tips.bulk': '• Buy ingredients in bulk to save time',\n        'shop.tips.stock': '• Keep an eye on your stock levels',\n        'shop.tips.rare': '• Some recipes require rare ingredients',\n        'shop.tips.prices': '• Prices may vary based on availability',\n        // Baking Modal\n        'baking.selectRecipe': 'Select Recipe',\n        'baking.noRecipes': 'No recipes available',\n        'baking.noIngredients': 'You don\\'t have enough ingredients to craft any recipes.',\n        'baking.buyIngredients': 'Buy Ingredients',\n        'baking.startBaking': '🔥 Start Baking',\n        'baking.instructions': '📋 Baking Instructions for {{recipe}}',\n        'baking.expectedReward': 'Expected reward: ${{amount}}',\n        'baking.makesSure': 'Make sure you have all ingredients before starting!',\n        'baking.inProgress': 'Baking in progress...',\n        'baking.completed': 'Baking completed!',\n        'baking.cancelled': 'Baking cancelled',\n        'baking.timeRemaining': 'Time remaining: {{time}}',\n        'baking.clickToCollect': 'Click to collect',\n        // Achievements Modal\n        'achievements.completed': '{{completed}} of {{total}} achievements completed',\n        'achievements.overallProgress': 'Overall Progress',\n        'achievements.progress': 'Progress',\n        'achievements.reward': 'Reward:',\n        'achievements.noAchievements': 'No achievements in this category.',\n        // Skills Modal\n        'skills.availablePoints': 'Available Skill Points: {{points}}',\n        'skills.efficiency': 'Efficiency',\n        'skills.automation': 'Automation',\n        'skills.quality': 'Quality',\n        'skills.business': 'Business',\n        'skills.effects': 'Effects:',\n        'skills.requires': 'Requires: {{requirements}}',\n        'skills.requiresLevel': 'Requires Level {{level}}',\n        'skills.maxed': '✅ Maxed',\n        'skills.upgrade': '⬆️ Upgrade ({{cost}} SP)',\n        'skills.locked': '🔒 Locked',\n        'skills.noSkills': 'No skills in this category.',\n        'skills.tips.title': '💡 Skill Tips',\n        'skills.tips.earnPoints': '• Earn skill points by leveling up (1 point every 2 levels)',\n        'skills.tips.prerequisites': '• Some skills require other skills to be unlocked first',\n        'skills.tips.playstyle': '• Focus on skills that match your playstyle',\n        'skills.tips.efficiency': '• Efficiency skills help with resource management',\n        // Automation Modal\n        'automation.masterControl': '🎛️ Master Control',\n        'automation.enableAutomation': 'Enable Automation',\n        'automation.autoStart': 'Auto-start Equipment',\n        'automation.priorityMode': '🎯 Priority Mode',\n        'automation.efficiency': 'Efficiency (Orders First)',\n        'automation.profit': 'Profit (Highest Value)',\n        'automation.speed': 'Speed (Fastest Recipes)',\n        'automation.priorityDescription': 'How automation chooses what to bake',\n        'automation.performance': '⚡ Performance',\n        'automation.maxJobs': 'Max Concurrent Jobs: {{jobs}}',\n        'automation.safety': '🛡️ Safety',\n        'automation.stopWhenLow': 'Stop when ingredients below: {{threshold}}',\n        'automation.upgrades': '💡 Automation Upgrades',\n        'automation.upgradesDescription': 'Improve your automation efficiency, speed, and intelligence with these upgrades.',\n        'automation.purchase': 'Purchase',\n        'automation.noUpgrades': 'No upgrades available at your current level.',\n        'automation.levelUpForUpgrades': 'Level up to unlock more automation upgrades!',\n        'automation.automatedEquipment': 'Automated Equipment',\n        'automation.activeUpgrades': 'Active Upgrades',\n        'automation.automationStatus': 'Automation Status',\n        'automation.equipmentStatus': '🏭 Equipment Status',\n        'automation.running': 'Running',\n        'automation.idle': 'Idle',\n        'automation.noAutomatedEquipment': 'No automated equipment available.',\n        'automation.purchaseAutoEquipment': 'Purchase auto-equipment from the shop to get started!',\n        // Equipment Shop Modal\n        'equipmentShop.upgradeYourBakery': 'Upgrade your bakery with professional equipment',\n        'equipmentShop.basic': 'Basic',\n        'equipmentShop.automated': 'Automated',\n        'equipmentShop.advanced': 'Advanced',\n        'equipmentShop.efficiency': 'Efficiency: {{efficiency}}x',\n        'equipmentShop.automation': 'Automation:',\n        'equipmentShop.unlockLevel': 'Unlock Level: {{level}}',\n        'equipmentShop.purchase': '💰 Purchase',\n        'equipmentShop.noEquipment': 'No equipment available in this category.',\n        'equipmentShop.levelUpForEquipment': 'Level up to unlock more equipment!',\n        'equipmentShop.tips.title': '💡 Equipment Tips',\n        'equipmentShop.tips.automated': '• Automated equipment can run without your supervision',\n        'equipmentShop.tips.efficiency': '• Higher efficiency means faster production and better quality',\n        'equipmentShop.tips.conveyor': '• Conveyor belts connect equipment for seamless workflow',\n        'equipmentShop.tips.advanced': '• Advanced equipment unlocks at higher levels',\n        // Level Up Modal\n        'levelUp.levelRewards': '🎁 Level Rewards',\n        'levelUp.whatsNext': '💡 What\\'s Next?',\n        'levelUp.checkRecipes': '• Check out new recipes in your recipe book',\n        'levelUp.visitShop': '• Visit the shop for new equipment',\n        'levelUp.challengingOrders': '• Take on more challenging orders',\n        'levelUp.investSkills': '• Invest in skill upgrades',\n        // Settings Modal\n        'settings.title': '⚙️ Settings',\n        'settings.general': 'General',\n        'settings.audio': 'Audio',\n        'settings.graphics': 'Graphics',\n        'settings.save': 'Save & Data',\n        'settings.language': '🌍 Language',\n        'settings.gameplay': '🎮 Gameplay',\n        'settings.notifications': 'Enable Notifications',\n        'settings.tutorials': 'Show Tutorials',\n        'settings.animationSpeed': 'Animation Speed',\n        'settings.sound': 'Sound Effects',\n        'settings.music': 'Background Music',\n        'settings.quality': '🎨 Graphics Quality',\n        'settings.autoSave': '💾 Auto-Save',\n        'settings.enableAutoSave': 'Enable Auto-Save',\n        'settings.dataManagement': '📁 Data Management',\n        'settings.exportSave': '📤 Export Save',\n        'settings.importSave': '📥 Import Save',\n        'settings.cloudSync': '☁️ Cloud Sync',\n        'settings.cloudSyncDescription': 'Cloud sync allows you to save your progress online and play across multiple devices.',\n        // Cloud Save Management\n        'cloud.save.manage_saves': 'Manage Cloud Saves',\n        'cloud.save.login': 'Login / Register',\n        'cloud.save.login_required': 'Please login to use cloud saves',\n        // Bakery Manager Modal\n        'bakeries.title': '🏪 Bakery Manager',\n        'bakeries.subtitle': 'Manage your bakery empire',\n        'bakeries.owned': 'My Bakeries',\n        'bakeries.available': 'Available',\n        'bakeries.current': 'Current',\n        'bakeries.level': 'Level',\n        'bakeries.specialization': 'Specialization',\n        'bakeries.equipment': 'Equipment',\n        'bakeries.orders': 'Active Orders',\n        'bakeries.switchTo': 'Switch To',\n        'bakeries.noOwned': 'You don\\'t own any bakeries yet.',\n        'bakeries.purchase': '💰 Purchase',\n        'bakeries.tooExpensive': '💸 Too Expensive',\n        'bakeries.allOwned': 'You own all available bakeries!',\n        'bakeries.tips': '💡 Bakery Tips',\n        'bakeries.tip1': 'Each bakery specializes in different products for bonus efficiency',\n        'bakeries.tip2': 'Switch between bakeries to manage multiple locations',\n        'bakeries.tip3': 'Specialized bakeries attract customers looking for specific items',\n        'bakeries.tip4': 'Upgrade each bakery independently for maximum profit',\n        // Notifications\n        'notifications.orderAccepted': 'Order Accepted',\n        'notifications.orderAcceptedMessage': 'You have accepted a new order!',\n        'notifications.orderCompleted': 'Order Completed!',\n        'notifications.orderCompletedMessage': 'You earned ${{reward}} and gained experience!',\n        'notifications.orderDeclined': 'Order Declined',\n        'notifications.orderDeclinedMessage': 'Order has been removed from your queue.',\n        'notifications.bakeryPurchased': 'Bakery Purchased!',\n        'notifications.bakeryPurchasedMessage': 'You now own {{name}}!',\n        'notifications.bakerySwitched': 'Bakery Switched',\n        'notifications.bakerySwitchedMessage': 'Switched to {{name}}',\n        // Common buttons and actions\n        'common.accept': 'Accept',\n        'common.decline': 'Decline',\n        'common.complete': 'Complete',\n        'common.purchase': 'Purchase',\n        'common.upgrade': 'Upgrade',\n        'common.cancel': 'Cancel',\n        'common.confirm': 'Confirm',\n        'common.save': 'Save',\n        'common.load': 'Load',\n        'common.delete': 'Delete',\n        'common.edit': 'Edit',\n        'common.back': 'Back',\n        'common.next': 'Next',\n        'common.previous': 'Previous',\n        'common.yes': 'Yes',\n        'common.no': 'No',\n        'common.create': 'Create',\n        'common.join': 'Join',\n        'common.leave': 'Leave',\n        'common.start': 'Start',\n        'common.ready': 'Ready',\n        'common.notReady': 'Not Ready',\n        'common.send': 'Send',\n        'common.refresh': 'Refresh',\n        'common.retry': 'Retry',\n        'common.reset': 'Reset',\n        'common.clear': 'Clear',\n        'common.apply': 'Apply',\n        'common.warning': 'Warning',\n        'common.info': 'Information',\n        'common.success': 'Success',\n        'common.error': 'Error',\n        // Save/Load System\n        'saveLoad.saveDesc': 'Choose a slot to save your progress',\n        'saveLoad.loadDesc': 'Select a save file to load',\n        'saveLoad.saveName': 'Save Name',\n        'saveLoad.emptySlot': 'Empty Slot',\n        'saveLoad.selectedSaveSlot': 'Selected: Slot {{slot}}',\n        'saveLoad.selectedLoadSlot': 'Selected: Slot {{slot}}',\n        'saveLoad.confirmOverwrite': 'Overwrite Save?',\n        'saveLoad.overwriteWarning': 'This will overwrite the existing save. This action cannot be undone.',\n        'saveLoad.overwrite': 'Overwrite',\n        'saveLoad.fileSlots': 'File Slots',\n        'saveLoad.gameSlots': 'Game Slots',\n        'saveLoad.exportSave': 'Export Save',\n        'saveLoad.importSave': 'Import Save',\n        'saveLoad.deleteConfirm': 'Delete Save?',\n        'saveLoad.deleteWarning': 'This will permanently delete this save file. This action cannot be undone.',\n        'saveLoad.delete': 'Delete',\n        // Game Menu\n        'gameMenu.title': 'Game Menu',\n        'gameMenu.subtitle': 'Manage your game',\n        'gameMenu.resume': 'Resume Game',\n        'gameMenu.resumeDesc': 'Continue playing',\n        'gameMenu.save': 'Save Game',\n        'gameMenu.saveDesc': 'Save your progress',\n        'gameMenu.load': 'Load Game',\n        'gameMenu.loadDesc': 'Load saved progress',\n        'gameMenu.settings': 'Settings',\n        'gameMenu.settingsDesc': 'Game preferences',\n        'gameMenu.mainMenu': 'Main Menu',\n        'gameMenu.mainMenuDesc': 'Return to main menu',\n        'gameMenu.exit': 'Exit Game',\n        'gameMenu.exitDesc': 'Close the application',\n        'gameMenu.tip': 'Press ESC to open this menu anytime',\n        // Discord Rich Presence\n        'settings.discord': 'Discord',\n        'settings.discordRichPresence': 'Discord Rich Presence',\n        'settings.discordDescription': 'Show your current game status and activity in Discord.',\n        'settings.enableDiscordRPC': 'Enable Discord Rich Presence',\n        'settings.discordConnected': '✅ Connected to Discord',\n        'settings.discordDisconnected': '❌ Not connected to Discord',\n        'settings.discordInfo': 'What is Discord Rich Presence?',\n        'settings.discordInfoDesc1': 'Discord Rich Presence shows your friends what you\\'re doing in Bake It Out:',\n        'settings.discordFeature1': 'Your current level and money',\n        'settings.discordFeature2': 'What you\\'re currently baking',\n        'settings.discordFeature3': 'Multiplayer room information',\n        'settings.discordFeature4': 'How long you\\'ve been playing',\n        'settings.discordInfoDesc2': 'Your friends can even join your multiplayer games directly from Discord!',\n        'settings.discordTroubleshooting': 'Discord Not Connected',\n        'settings.discordTrouble1': 'Make sure Discord is running on your computer.',\n        'settings.discordTrouble2': 'Discord Rich Presence only works in the desktop version of the game.',\n        'settings.discordTrouble3': 'Try restarting both Discord and the game if the connection fails.',\n        'settings.discordPrivacy': 'Privacy Information',\n        'settings.discordPrivacyDesc1': 'Discord Rich Presence only shares:',\n        'settings.discordPrivacy1': 'Your current game activity (public)',\n        'settings.discordPrivacy2': 'Your player level and progress (public)',\n        'settings.discordPrivacy3': 'Multiplayer room codes (for joining)',\n        'settings.discordPrivacyDesc2': 'No personal information or save data is shared with Discord.',\n        'settings.discordStatus': 'Discord Status',\n        'settings.discordInitializing': '🔄 Initializing Discord RPC...',\n        'settings.discordRetrying': '🔄 Retrying connection...',\n        'settings.discordUnavailable': '❌ Discord not available',\n        'settings.discordDesktopOnly': 'ℹ️ Discord RPC only available in desktop version',\n        // Error messages and status\n        'error.general': 'An error occurred',\n        'error.saveLoad': 'Failed to save/load game',\n        'error.connection': 'Connection error',\n        'error.fileNotFound': 'File not found',\n        'error.invalidData': 'Invalid data format',\n        'error.permissionDenied': 'Permission denied',\n        'status.loading': 'Loading...',\n        'status.saving': 'Saving...',\n        'status.connecting': 'Connecting...',\n        'status.ready': 'Ready',\n        'status.success': 'Success!',\n        'status.failed': 'Failed',\n        'status.offline': 'Offline',\n        'status.online': 'Online',\n        // UI Elements\n        'ui.placeholder': 'Enter text...',\n        'ui.search': 'Search',\n        'ui.filter': 'Filter',\n        'ui.sort': 'Sort',\n        'ui.ascending': 'Ascending',\n        'ui.descending': 'Descending',\n        'ui.selectAll': 'Select All',\n        'ui.deselectAll': 'Deselect All',\n        'ui.noResults': 'No results found',\n        'ui.noData': 'No data available',\n        'ui.loading': 'Loading...',\n        'ui.saving': 'Saving...',\n        'ui.saved': 'Saved!',\n        'ui.failed': 'Failed',\n        'ui.retry': 'Retry',\n        'ui.back': 'Back',\n        'ui.forward': 'Forward',\n        'ui.home': 'Home',\n        'ui.menu': 'Menu',\n        'ui.options': 'Options',\n        'ui.preferences': 'Preferences',\n        // Multiplayer\n        'multiplayer.lobby': '👥 Multiplayer Lobby',\n        'multiplayer.connected': '🟢 Connected',\n        'multiplayer.disconnected': '🔴 Disconnected',\n        'multiplayer.createRoom': 'Create Room',\n        'multiplayer.joinRoom': 'Join Room',\n        'multiplayer.room': 'Room',\n        'multiplayer.yourName': 'Your Name',\n        'multiplayer.enterName': 'Enter your name',\n        'multiplayer.roomName': 'Room Name',\n        'multiplayer.enterRoomName': 'Enter room name',\n        'multiplayer.gameMode': 'Game Mode',\n        'multiplayer.cooperative': '🤝 Cooperative',\n        'multiplayer.competitive': '⚔️ Competitive',\n        'multiplayer.maxPlayers': 'Max Players: {{count}}',\n        'multiplayer.roomId': 'Room ID',\n        'multiplayer.enterRoomId': 'Enter room ID',\n        'multiplayer.players': 'Players ({{count}})',\n        'multiplayer.host': 'HOST',\n        'multiplayer.level': 'Level {{level}}',\n        'multiplayer.chat': 'Chat',\n        'multiplayer.typeMessage': 'Type a message...',\n        'multiplayer.gameTime': 'Game Time: {{time}}',\n        'multiplayer.teamStats': '📊 Team Stats',\n        'multiplayer.ordersCompleted': 'Orders Completed:',\n        'multiplayer.totalRevenue': 'Total Revenue:',\n        'multiplayer.teamExperience': 'Team Experience:',\n        'multiplayer.sharedKitchen': '🏪 Shared Kitchen',\n        'multiplayer.sharedOrders': '📋 Shared Orders',\n        'multiplayer.sharedInventory': '📦 Shared Inventory',\n        'multiplayer.contribution': 'Contribution:',\n        'multiplayer.online': '🟢 Online',\n        'multiplayer.status': 'Status:',\n        'multiplayer.you': '(You)',\n        'multiplayer.teamChat': '💬 Team Chat',\n        'multiplayer.chatPlaceholder': 'Chat messages will appear here...',\n        // Multiplayer game modes\n        'multiplayer.mode.cooperative.description': '🤝 Cooperative Mode: Work together to complete orders and grow your shared bakery!',\n        'multiplayer.mode.competitive.description': '⚔️ Competitive Mode: Compete against other players to complete the most orders!',\n        // Multiplayer game interface\n        'multiplayer.game.title': '🎮 Multiplayer Game - {{roomName}}',\n        'multiplayer.game.mode': 'Mode: {{mode}}',\n        'multiplayer.game.playersCount': 'Players: {{count}}',\n        'multiplayer.game.playing': '🟢 Playing',\n        'multiplayer.game.leaveGame': '🚪 Leave Game',\n        'multiplayer.game.tabs.game': 'Game',\n        'multiplayer.game.tabs.players': 'Players',\n        'multiplayer.game.tabs.chat': 'Chat',\n        // Room creation and joining\n        'multiplayer.create.title': '🏗️ Create Room',\n        'multiplayer.join.title': '🚪 Join Room',\n        'multiplayer.room.info': 'Mode: {{mode}} • Players: {{current}}/{{max}}',\n        'multiplayer.room.readyUp': '✅ Ready',\n        'multiplayer.room.notReady': '⏳ Not Ready',\n        'multiplayer.room.startGame': '🚀 Start Game',\n        'multiplayer.room.leaveRoom': '🚪 Leave',\n        // Connection states\n        'multiplayer.connection.connecting': 'Connecting...',\n        'multiplayer.connection.reconnecting': 'Reconnecting...',\n        'multiplayer.connection.failed': 'Connection failed',\n        'multiplayer.connection.error': '⚠️ {{error}}',\n        // System messages\n        'multiplayer.system.playerJoined': '{{name}} joined the room',\n        'multiplayer.system.playerLeft': '{{name}} left the room',\n        'multiplayer.system.gameStarted': 'Game started!',\n        'multiplayer.system.gameEnded': 'Game ended!',\n        'multiplayer.system.roomCreated': 'Room created successfully',\n        'multiplayer.system.roomJoined': 'Joined room successfully',\n        // Bakery Layout\n        'bakery.layout.title': 'Bakery Layout',\n        'bakery.kitchen': 'Kitchen',\n        'bakery.dining': 'Dining Area',\n        'bakery.counter': 'Service Counter',\n        'bakery.layout.baking_area': 'Baking Area',\n        'bakery.layout.prep_area': 'Prep Area',\n        'bakery.layout.automation_zone': 'Automation Zone',\n        'bakery.layout.efficiency': 'Efficiency',\n        'bakery.layout.active_equipment': 'Active Equipment',\n        'bakery.layout.total_equipment': 'Total Equipment',\n        'bakery.layout.kitchen_stats': 'Kitchen Stats',\n        // Equipment Status\n        'equipment.status.active': 'Active',\n        'equipment.status.idle': 'Idle',\n        'equipment.status.maintenance': 'Maintenance',\n        'equipment.status.automated': 'Automated',\n        'equipment.zones.baking': 'Baking Zone',\n        'equipment.zones.prep': 'Prep Zone',\n        'equipment.zones.automation': 'Automation Zone',\n        // Dining Room\n        'dining.room.title': 'Dining Room',\n        'dining.room.subtitle': 'Watch your customers enjoy their meals',\n        'dining.room.tables': 'Tables',\n        'dining.room.customers': 'Customers',\n        'dining.room.satisfaction': 'Satisfaction',\n        'dining.room.occupancy': 'Occupancy',\n        'dining.room.ambient_sounds': 'Ambient Sounds',\n        'dining.room.decorations': 'Decorations',\n        'dining.occupied.tables': 'Occupied Tables',\n        'dining.ambient.sounds': 'Toggle ambient sounds',\n        'dining.ambient.playing': 'Ambient sounds playing',\n        'dining.customer.status': 'Customer Status',\n        'dining.enjoying': 'Enjoying',\n        'dining.no.customers': 'No customers dining currently',\n        'dining.waiting.for.customers': 'Complete orders to see customers dining',\n        'dining.table.number': 'Table {{number}}',\n        'dining.table.seats': '{{count}} seats',\n        'dining.table.occupied': 'Occupied',\n        'dining.table.available': 'Available',\n        'dining.table.customer_info': 'Customer Info',\n        'dining.service.counter': 'Service',\n        'dining.service.ready': 'Ready to Serve',\n        'dining.service.waiting': 'Waiting',\n        'dining.atmosphere.cozy': 'Cozy',\n        'dining.atmosphere.busy': 'Busy',\n        'dining.atmosphere.peaceful': 'Peaceful',\n        'dining.atmosphere.lively': 'Lively',\n        // Customer Manager\n        'customers.manager.title': 'Customer Manager',\n        'customers.manager.subtitle': 'Monitor and serve your customers',\n        'customers.manager.active_customers': 'Active Customers',\n        'customers.manager.total_served': 'Total Served',\n        'customers.manager.average_satisfaction': 'Average Satisfaction',\n        'customers.manager.serve_order': 'Serve Order',\n        'customers.manager.customer_details': 'Customer Details',\n        'customers.current.list': 'Current Customers',\n        'customers.table': 'Table',\n        'customers.patience': 'Patience',\n        'customers.no.customers': 'No customers currently',\n        'customers.waiting.for.orders': 'Waiting for new orders...',\n        'customers.order.details': 'Order Details',\n        'customers.total': 'Total',\n        'customers.info': 'Customer Info',\n        'customers.mood': 'Mood',\n        'customers.status': 'Status',\n        'customers.preferences': 'Preferences',\n        'customers.serve.order': 'Serve Order',\n        'customers.select.customer': 'Select a customer',\n        'customers.select.to.view.details': 'Select a customer to view details',\n        'customers.status.entering': 'Entering',\n        'customers.status.waiting': 'Waiting',\n        'customers.status.ordering': 'Ordering',\n        'customers.status.served': 'Served',\n        'customers.status.eating': 'Eating',\n        'customers.status.leaving': 'Leaving',\n        'customers.mood.happy': 'Happy',\n        'customers.mood.neutral': 'Neutral',\n        'customers.mood.impatient': 'Impatient',\n        'customers.mood.angry': 'Angry',\n        'customers.info.name': 'Name',\n        'customers.info.order': 'Order',\n        'customers.info.patience': 'Patience',\n        'customers.info.satisfaction': 'Satisfaction',\n        'customers.info.table': 'Table',\n        'customers.info.order_value': 'Order Value',\n        'customers.info.preferences': 'Preferences',\n        'customers.info.time_seated': 'Time Seated',\n        'customers.preferences.sweet': 'Sweet',\n        'customers.preferences.savory': 'Savory',\n        'customers.preferences.healthy': 'Healthy',\n        'customers.preferences.indulgent': 'Indulgent',\n        'customers.preferences.traditional': 'Traditional',\n        'customers.preferences.exotic': 'Exotic',\n        // View Switcher\n        'views.traditional': 'Traditional',\n        'views.layout': 'Bakery Layout',\n        'views.dining': 'Dining Room',\n        'views.customers': 'Customer Manager',\n        'views.switch_view': 'Switch View',\n        'game.view.traditional': 'Traditional View',\n        'game.view.layout': 'Bakery Layout',\n        'game.view.dining': 'Dining Room',\n        'game.view.customers': 'Customer Manager',\n        // Ingredients\n        'ingredient.Flour': 'Flour',\n        'ingredient.Sugar': 'Sugar',\n        'ingredient.Eggs': 'Eggs',\n        'ingredient.Butter': 'Butter',\n        'ingredient.Milk': 'Milk',\n        'ingredient.Vanilla Extract': 'Vanilla Extract',\n        'ingredient.Vanilla': 'Vanilla',\n        'ingredient.Chocolate Chips': 'Chocolate Chips',\n        'ingredient.Baking Powder': 'Baking Powder',\n        'ingredient.Salt': 'Salt',\n        'ingredient.Cinnamon': 'Cinnamon',\n        'ingredient.Nuts': 'Nuts',\n        'ingredient.Cream Cheese': 'Cream Cheese',\n        'ingredient.Honey': 'Honey',\n        'ingredient.Cocoa Powder': 'Cocoa Powder',\n        'ingredient.Yeast': 'Yeast',\n        // Equipment Names\n        'equipment.Basic Oven': 'Basic Oven',\n        'equipment.Hand Mixer': 'Hand Mixer',\n        'equipment.Professional Oven': 'Professional Oven',\n        'equipment.Stand Mixer': 'Stand Mixer',\n        'equipment.Automated Oven': 'Automated Oven',\n        'equipment.Industrial Mixer': 'Industrial Mixer',\n        'equipment.Conveyor Belt': 'Conveyor Belt',\n        'equipment.Display Counter': 'Display Counter',\n        'equipment.Prep Counter': 'Prep Counter',\n        // Recipe Names\n        'recipe.Chocolate Chip Cookies': 'Chocolate Chip Cookies',\n        'recipe.Vanilla Muffins': 'Vanilla Muffins',\n        'recipe.Simple Bread': 'Simple Bread',\n        'recipe.Cinnamon Rolls': 'Cinnamon Rolls',\n        'recipe.Sourdough Bread': 'Sourdough Bread',\n        'recipe.Chocolate Cake': 'Chocolate Cake',\n        'recipe.Apple Pie': 'Apple Pie',\n        'recipe.Croissants': 'Croissants',\n        // Missing UI Elements\n        'toolbar.menu': 'Menu',\n        'equipment.Work Counter': 'Work Counter',\n        'game.level': 'Level',\n        'game.xp': 'XP',\n        'game.current': 'Current',\n        'inventory.title': 'Inventory',\n        'inventory.quantity': 'Quantity',\n        'inventory.price_per_unit': 'per unit',\n        'orders.title': 'Orders',\n        'orders.new_order': 'New Order',\n        'orders.time': 'Time',\n        'orders.accept': 'Accept',\n        'orders.reject': 'Reject',\n        'quick_actions.title': 'Quick Actions',\n        'quick_actions.buy_ingredients': 'Buy Ingredients',\n        'quick_actions.view_recipes': 'View Recipes',\n        'quick_actions.equipment_shop': 'Equipment Shop',\n        'click_to_use': 'Click to use',\n        'currency.czk': 'Kč',\n        // Additional UI Elements\n        'game.title': 'Bake It Out',\n        'location.downtown_delights': 'Downtown Delights',\n        'toolbar.bakery': 'Bakery',\n        'toolbar.achievements': 'Achievements',\n        'toolbar.stars': 'Stars',\n        'toolbar.settings': 'Settings',\n        // Orders\n        'orders.accepted': 'Order Accepted',\n        // Cloud Save\n        'cloud.auth.login': 'Login',\n        'cloud.auth.register': 'Register',\n        'cloud.auth.login_title': 'Login to Cloud Save',\n        'cloud.auth.register_title': 'Create Cloud Save Account',\n        'cloud.auth.username': 'Username',\n        'cloud.auth.email': 'Email',\n        'cloud.auth.password': 'Password',\n        'cloud.auth.confirm_password': 'Confirm Password',\n        'cloud.auth.login_button': 'Login',\n        'cloud.auth.register_button': 'Create Account',\n        'cloud.auth.cancel': 'Cancel',\n        'cloud.auth.loading': 'Loading...',\n        'cloud.auth.username_required': 'Username is required',\n        'cloud.auth.username_too_short': 'Username must be at least 3 characters',\n        'cloud.auth.email_required': 'Email is required',\n        'cloud.auth.email_invalid': 'Please enter a valid email address',\n        'cloud.auth.password_required': 'Password is required',\n        'cloud.auth.password_too_short': 'Password must be at least 6 characters',\n        'cloud.auth.passwords_dont_match': 'Passwords do not match',\n        'cloud.auth.username_placeholder': 'Enter your username',\n        'cloud.auth.email_placeholder': 'Enter your email',\n        'cloud.auth.password_placeholder': 'Enter your password',\n        'cloud.auth.confirm_password_placeholder': 'Confirm your password',\n        'cloud.auth.no_account': \"Don't have an account?\",\n        'cloud.auth.have_account': 'Already have an account?',\n        'cloud.auth.register_link': 'Create one',\n        'cloud.auth.login_link': 'Login',\n        'cloud.save.auth_required': 'Cloud Save Account Required',\n        'cloud.save.auth_description': 'You need to login or create an account to use cloud saves.',\n        'cloud.save.save_title': 'Save to Cloud',\n        'cloud.save.load_title': 'Load from Cloud',\n        'cloud.save.logged_in_as': 'Logged in as',\n        'cloud.save.syncing': 'Syncing...',\n        'cloud.save.sync_success': 'Synced',\n        'cloud.save.sync_error': 'Sync Error',\n        'cloud.save.sync_idle': 'Ready',\n        'cloud.save.last_sync': 'Last sync',\n        'cloud.save.save_name': 'Save Name',\n        'cloud.save.save_name_placeholder': 'Enter a name for your save',\n        'cloud.save.your_saves': 'Your Cloud Saves',\n        'cloud.save.refreshing': 'Refreshing...',\n        'cloud.save.refresh': 'Refresh',\n        'cloud.save.loading_saves': 'Loading saves...',\n        'cloud.save.no_saves': 'No cloud saves found',\n        'cloud.save.level': 'Level',\n        'cloud.save.delete': 'Delete',\n        'cloud.save.confirm_delete': 'Are you sure you want to delete this save?',\n        'cloud.save.cancel': 'Cancel',\n        'cloud.save.saving': 'Saving...',\n        'cloud.save.save_button': 'Save to Cloud',\n        'cloud.save.loading': 'Loading...',\n        'cloud.save.load_button': 'Load Game',\n        // Save/Load Modal\n        'saveLoad.local_saves': 'Local Saves',\n        'saveLoad.file_saves': 'File Saves',\n        'saveLoad.cloud_saves': 'Cloud Saves',\n        // Dashboard\n        'dashboard.title': 'Server Dashboard',\n        'dashboard.button': 'Dashboard',\n        'dashboard.tooltip': 'Access server dashboard',\n        'dashboard.login_required': 'Please login to access the dashboard',\n        'dashboard.logged_in_as': 'Logged in as',\n        'dashboard.access_title': 'Access Server Dashboard',\n        'dashboard.access_description': 'Monitor server statistics, manage users, and view game analytics',\n        'dashboard.feature.users': 'User Management',\n        'dashboard.feature.rooms': 'Game Rooms',\n        'dashboard.feature.saves': 'Cloud Saves',\n        'dashboard.feature.analytics': 'Analytics',\n        'dashboard.open': 'Open Dashboard',\n        'dashboard.opening': 'Opening...',\n        'dashboard.cancel': 'Cancel',\n        'dashboard.new_tab_notice': 'Dashboard will open in a new tab',\n        'dashboard.open_failed': 'Failed to open dashboard',\n        // Recipes\n        'recipe.chocolate_chip_cookies': 'Chocolate Chip Cookies',\n        'recipe.vanilla_muffins': 'Vanilla Muffins',\n        'recipe.cinnamon_rolls': 'Cinnamon Rolls',\n        'recipe.chocolate_brownies': 'Chocolate Brownies',\n        'recipe.blueberry_pie': 'Blueberry Pie',\n        'recipe.sourdough_bread': 'Sourdough Bread',\n        // Ingredients\n        'ingredient.flour': 'Flour',\n        'ingredient.sugar': 'Sugar',\n        'ingredient.butter': 'Butter',\n        'ingredient.chocolate_chips': 'Chocolate Chips',\n        'ingredient.eggs': 'Eggs',\n        'ingredient.vanilla': 'Vanilla',\n        'ingredient.cinnamon': 'Cinnamon',\n        'ingredient.blueberries': 'Blueberries',\n        'ingredient.salt': 'Salt',\n        // Recipe Categories\n        'category.cookies': 'Cookies',\n        'category.cakes': 'Cakes',\n        'category.pastries': 'Pastries',\n        'category.pies': 'Pies',\n        'category.bread': 'Bread',\n        // Equipment Names\n        'equipment.oven': 'Oven',\n        'equipment.mixer': 'Mixer',\n        'equipment.work_counter': 'Work Counter',\n        'equipment.display_case': 'Display Case',\n        'equipment.cash_register': 'Cash Register'\n    },\n    cs: {\n        // Main game\n        'game.title': 'Bake It Out',\n        'game.subtitle': 'Ovládněte umění řízení pekárny v této poutavé multiplayerové hře. Plňte objednávky, odemykejte recepty, automatizujte procesy a soutěžte s přáteli!',\n        'game.play': '🎮 Začít hrát',\n        'game.singlePlayer': '🎮 Jeden hráč',\n        'game.singlePlayerDesc': 'Hrajte sólo a zdokonalte své pekařské dovednosti',\n        'game.multiplayer': '👥 Multiplayer',\n        'game.multiplayerDesc': 'Hrajte s přáteli v kooperativních nebo soutěžních režimech',\n        'game.english': '🇺🇸 English',\n        'game.czech': '🇨🇿 Čeština',\n        'game.home': '🏠 Domů',\n        'game.close': '✕ Zavřít',\n        'game.continue': '🚀 Pokračovat ve hře',\n        // Menu options\n        'menu.singlePlayer': 'Jeden hráč',\n        'menu.multiplayer': 'Multiplayer',\n        'menu.settings': 'Nastavení',\n        'menu.credits': 'Titulky',\n        'menu.exit': 'Ukončit',\n        // Credits\n        'credits.title': 'O hře Bake It Out',\n        'credits.subtitle': 'Informace o hře a titulky',\n        'credits.description': 'Multiplayerová hra na správu pekárny s real-time spoluprací a podporou lokalizace.',\n        'credits.version': 'Verze',\n        'credits.release': 'Datum vydání',\n        'credits.releaseDate': 'Leden 2025',\n        'credits.platform': 'Platforma',\n        'credits.platforms': 'Windows, macOS, Linux',\n        'credits.contact': 'Kontakt',\n        'credits.facebook': 'Facebook',\n        'credits.close': 'Zavřít',\n        'credits.features': 'Funkce',\n        'credits.multiplayer': 'Podpora multiplayeru',\n        'credits.multiplayerDesc': 'Real-time spolupráce s přáteli v kooperativních nebo soutěžních režimech.',\n        'credits.localization': 'Lokalizace',\n        'credits.localizationDesc': 'Plná podpora angličtiny a češtiny s jednoduchým přepínáním.',\n        'credits.progression': 'Systém postupu',\n        'credits.progressionDesc': 'Vylepšujte svou pekárnu, odemykejte recepty a ovládněte automatizaci.',\n        'credits.automation': 'Automatizace',\n        'credits.automationDesc': 'Pokročilé automatizační systémy pro optimalizaci provozu pekárny.',\n        'credits.technology': 'Technologický stack',\n        'credits.team': 'Vývojový tým',\n        'credits.developedBy': 'Vyvinuto týmem',\n        'credits.teamDesc': 'Vytvořeno s láskou vývojovým týmem Bake It Out.',\n        'credits.thanks': 'Speciální poděkování',\n        'credits.thanksPlayers': 'Všem našim úžasným hráčům a beta testerům',\n        'credits.thanksTranslators': 'Komunitním překladatelům za podporu lokalizace',\n        'credits.thanksOpenSource': 'Open source komunitě za neuvěřitelné nástroje a knihovny',\n        'credits.thanksBakers': 'Skutečným pekařům, kteří inspirovali tuto hru',\n        'credits.website': 'Webové stránky',\n        'credits.support': 'Podpora',\n        'credits.github': 'GitHub',\n        'credits.discord': 'Discord',\n        // Game Views\n        'game.view.traditional': 'Tradiční pohled',\n        'game.view.layout': 'Rozložení pekárny',\n        'game.view.dining': 'Jídelna',\n        'game.view.customers': 'Správce zákazníků',\n        // Bakery Layout\n        'bakery.layout.title': 'Rozložení pekárny',\n        'bakery.kitchen': 'Kuchyně',\n        'bakery.dining': 'Jídelní oblast',\n        'bakery.counter': 'Servisní pult',\n        'bakery.baking.area': 'Oblast pečení',\n        'bakery.prep.area': 'Přípravná oblast',\n        'bakery.automation.area': 'Automatizace',\n        'bakery.kitchen.stats': 'Statistiky kuchyně',\n        'bakery.active.equipment': 'Aktivní vybavení',\n        'bakery.automated.equipment': 'Automatizované vybavení',\n        'bakery.efficiency': 'Efektivita',\n        'bakery.dining.stats': 'Statistiky jídelny',\n        'bakery.current.customers': 'Současní zákazníci',\n        'bakery.waiting.customers': 'Čekající',\n        'bakery.eating.customers': 'Jedí',\n        'bakery.avg.satisfaction': 'Průměrná spokojenost',\n        'bakery.service.counter': 'Servisní pult',\n        'bakery.display.case': 'Vitrina',\n        'bakery.order.queue': 'Fronta objednávek',\n        'bakery.last.updated': 'Naposledy aktualizováno',\n        // Dining Room\n        'dining.room.title': 'Jídelna',\n        'dining.room.subtitle': 'Sledujte, jak si vaši zákazníci užívají jídlo',\n        'dining.occupied.tables': 'Obsazené stoly',\n        'dining.ambient.sounds': 'Přepnout okolní zvuky',\n        'dining.service.counter': 'Servis',\n        'dining.customer.status': 'Stav zákazníků',\n        'dining.enjoying': 'Užívá si',\n        'dining.no.customers': 'Momentálně nejsou žádní zákazníci',\n        'dining.waiting.for.customers': 'Dokončete objednávky, abyste viděli zákazníky jíst',\n        'dining.ambient.playing': 'Hrají okolní zvuky',\n        // Customer Manager\n        'customers.manager.title': 'Správce zákazníků',\n        'customers.manager.subtitle': 'Sledujte a obsluhujte své zákazníky',\n        'customers.current.list': 'Současní zákazníci',\n        'customers.table': 'Stůl',\n        'customers.patience': 'Trpělivost',\n        'customers.no.customers': 'Momentálně žádní zákazníci',\n        'customers.waiting.for.orders': 'Čekání na nové objednávky...',\n        'customers.order.details': 'Detaily objednávky',\n        'customers.total': 'Celkem',\n        'customers.info': 'Informace o zákazníkovi',\n        'customers.mood': 'Nálada',\n        'customers.status': 'Stav',\n        'customers.preferences': 'Preference',\n        'customers.select.customer': 'Vyberte zákazníka',\n        'customers.select.to.view.details': 'Vyberte zákazníka pro zobrazení detailů',\n        'customers.serve.order': 'Podávat objednávku',\n        'menu.newGame': 'Nová hra',\n        'menu.continueGame': 'Pokračovat ve hře',\n        'menu.loadGame': 'Načíst hru',\n        'menu.selectLanguage': 'Vybrat jazyk',\n        'menu.about': 'O hře',\n        'menu.help': 'Nápověda',\n        'menu.quit': 'Ukončit',\n        // Features\n        'features.manage.title': 'Spravujte svou pekárnu',\n        'features.manage.description': 'Přijímejte objednávky, pečte lahodné výrobky a obsluhujte spokojené zákazníky',\n        'features.levelup.title': 'Postupujte a automatizujte',\n        'features.levelup.description': 'Odemykejte nové recepty, kupujte vybavení a automatizujte své procesy',\n        'features.multiplayer.title': 'Hrajte společně',\n        'features.multiplayer.description': 'Kooperativní a soutěžní multiplayerové režimy s přáteli',\n        'status.development': '🚧 Hra ve vývoji - Fáze 5: Vícevrstvá podpora! 🚧',\n        // Game interface\n        'ui.level': 'Úroveň {{level}}',\n        'ui.money': '{{amount}} Kč',\n        'ui.experience': 'XP: {{current}}/{{max}}',\n        'ui.skillPoints': 'SP: {{points}}',\n        'ui.achievements': '🏆 Úspěchy',\n        'ui.skills': '🌟 Dovednosti',\n        'ui.automation': '🤖 Automatizace',\n        // Kitchen\n        'kitchen.title': '🏪 Kuchyně',\n        'kitchen.clickToUse': 'Klikněte pro použití',\n        'kitchen.making': 'Připravuje: {{recipe}}',\n        'kitchen.timeRemaining': 'Čas: {{time}}',\n        // Inventory\n        'inventory.title': '📦 Sklad',\n        'inventory.quantity': 'Množství: {{qty}}',\n        'inventory.cost': '{{cost}} Kč za kus',\n        // Orders\n        'orders.title': '📋 Objednávky',\n        'orders.newOrder': '+ Nová objednávka',\n        'orders.accept': 'Přijmout',\n        'orders.decline': 'Odmítnout',\n        'orders.complete': 'Dokončit',\n        'orders.inProgress': 'Probíhá',\n        'orders.timeLimit': 'Čas: {{time}}',\n        'orders.reward': '{{amount}} Kč',\n        'orders.customer': 'Zákazník: {{name}}',\n        // Quick Actions\n        'actions.title': '⚡ Rychlé akce',\n        'actions.buyIngredients': '🛒 Koupit suroviny',\n        'actions.viewRecipes': '📖 Zobrazit recepty',\n        'actions.equipmentShop': '🔧 Obchod s vybavením',\n        // Modals\n        'modal.recipes.title': '📖 Kniha receptů',\n        'modal.shop.title': '🛒 Obchod se surovinami',\n        'modal.baking.title': '🔥 {{equipment}} - Vyberte recept',\n        'modal.achievements.title': '🏆 Úspěchy',\n        'modal.skills.title': '🌟 Strom dovedností',\n        'modal.automation.title': '🤖 Ovládání automatizace',\n        'modal.equipmentShop.title': '🏪 Obchod s vybavením',\n        'modal.settings.title': '⚙️ Nastavení',\n        'modal.bakeries.title': '🏪 Správce pekáren',\n        'modal.levelUp.title': 'Postup na vyšší úroveň!',\n        'modal.levelUp.subtitle': 'Dosáhli jste úrovně {{level}}!',\n        // Recipe Modal\n        'recipes.all': 'Vše',\n        'recipes.cookies': 'Sušenky',\n        'recipes.cakes': 'Dorty',\n        'recipes.bread': 'Chléb',\n        'recipes.pastries': 'Pečivo',\n        'recipes.ingredients': 'Suroviny:',\n        'recipes.difficulty': 'Obtížnost:',\n        'recipes.time': 'Čas:',\n        'recipes.canCraft': '✅ Lze vyrobit',\n        'recipes.unlockLevel': 'Odemčeno na úrovni {{level}}',\n        'recipes.noRecipes': 'V této kategorii nejsou k dispozici žádné recepty.',\n        'recipes.levelUpToUnlock': 'Postupte na vyšší úroveň pro odemčení dalších receptů!',\n        // Shop Modal\n        'shop.currentStock': 'Aktuální zásoba: {{quantity}}',\n        'shop.buy': 'Koupit',\n        'shop.tooExpensive': 'Příliš drahé',\n        'shop.tips.title': '💡 Tipy pro nakupování',\n        'shop.tips.bulk': '• Kupujte suroviny ve velkém množství pro úsporu času',\n        'shop.tips.stock': '• Sledujte úroveň svých zásob',\n        'shop.tips.rare': '• Některé recepty vyžadují vzácné suroviny',\n        'shop.tips.prices': '• Ceny se mohou lišit podle dostupnosti',\n        // Baking Modal\n        'baking.selectRecipe': 'Vyberte recept',\n        'baking.noRecipes': 'Žádné recepty k dispozici',\n        'baking.noIngredients': 'Nemáte dostatek surovin pro výrobu jakéhokoli receptu.',\n        'baking.buyIngredients': 'Koupit suroviny',\n        'baking.startBaking': '🔥 Začít péct',\n        'baking.instructions': '📋 Pokyny pro pečení {{recipe}}',\n        'baking.expectedReward': 'Očekávaná odměna: {{amount}} Kč',\n        'baking.makesSure': 'Ujistěte se, že máte všechny suroviny před začátkem!',\n        'baking.inProgress': 'Pečení probíhá...',\n        'baking.completed': 'Pečení dokončeno!',\n        'baking.cancelled': 'Pečení zrušeno',\n        'baking.timeRemaining': 'Zbývající čas: {{time}}',\n        'baking.clickToCollect': 'Klikněte pro vyzvednutí',\n        // Achievements Modal\n        'achievements.completed': '{{completed}} z {{total}} úspěchů dokončeno',\n        'achievements.overallProgress': 'Celkový pokrok',\n        'achievements.progress': 'Pokrok',\n        'achievements.reward': 'Odměna:',\n        'achievements.noAchievements': 'V této kategorii nejsou žádné úspěchy.',\n        // Skills Modal\n        'skills.availablePoints': 'Dostupné body dovedností: {{points}}',\n        'skills.efficiency': 'Efektivita',\n        'skills.automation': 'Automatizace',\n        'skills.quality': 'Kvalita',\n        'skills.business': 'Podnikání',\n        'skills.effects': 'Efekty:',\n        'skills.requires': 'Vyžaduje: {{requirements}}',\n        'skills.requiresLevel': 'Vyžaduje úroveň {{level}}',\n        'skills.maxed': '✅ Maximální',\n        'skills.upgrade': '⬆️ Vylepšit ({{cost}} SP)',\n        'skills.locked': '🔒 Uzamčeno',\n        'skills.noSkills': 'V této kategorii nejsou žádné dovednosti.',\n        'skills.tips.title': '💡 Tipy pro dovednosti',\n        'skills.tips.earnPoints': '• Získávejte body dovedností postupem na vyšší úroveň (1 bod každé 2 úrovně)',\n        'skills.tips.prerequisites': '• Některé dovednosti vyžadují nejprve odemčení jiných dovedností',\n        'skills.tips.playstyle': '• Zaměřte se na dovednosti, které odpovídají vašemu stylu hry',\n        'skills.tips.efficiency': '• Dovednosti efektivity pomáhají se správou zdrojů',\n        // Automation Modal\n        'automation.masterControl': '🎛️ Hlavní ovládání',\n        'automation.enableAutomation': 'Povolit automatizaci',\n        'automation.autoStart': 'Automatické spuštění vybavení',\n        'automation.priorityMode': '🎯 Režim priority',\n        'automation.efficiency': 'Efektivita (objednávky první)',\n        'automation.profit': 'Zisk (nejvyšší hodnota)',\n        'automation.speed': 'Rychlost (nejrychlejší recepty)',\n        'automation.priorityDescription': 'Jak automatizace vybírá, co péct',\n        'automation.performance': '⚡ Výkon',\n        'automation.maxJobs': 'Max současných úloh: {{jobs}}',\n        'automation.safety': '🛡️ Bezpečnost',\n        'automation.stopWhenLow': 'Zastavit, když suroviny klesnou pod: {{threshold}}',\n        'automation.upgrades': '💡 Vylepšení automatizace',\n        'automation.upgradesDescription': 'Vylepšete efektivitu, rychlost a inteligenci vaší automatizace.',\n        'automation.purchase': 'Koupit',\n        'automation.noUpgrades': 'Na vaší současné úrovni nejsou k dispozici žádná vylepšení.',\n        'automation.levelUpForUpgrades': 'Postupte na vyšší úroveň pro odemčení dalších vylepšení automatizace!',\n        'automation.automatedEquipment': 'Automatizované vybavení',\n        'automation.activeUpgrades': 'Aktivní vylepšení',\n        'automation.automationStatus': 'Stav automatizace',\n        'automation.equipmentStatus': '🏭 Stav vybavení',\n        'automation.running': 'Běží',\n        'automation.idle': 'Nečinné',\n        'automation.noAutomatedEquipment': 'Žádné automatizované vybavení k dispozici.',\n        'automation.purchaseAutoEquipment': 'Kupte si auto-vybavení z obchodu pro začátek!',\n        // Equipment Shop Modal\n        'equipmentShop.upgradeYourBakery': 'Vylepšete svou pekárnu profesionálním vybavením',\n        'equipmentShop.basic': 'Základní',\n        'equipmentShop.automated': 'Automatizované',\n        'equipmentShop.advanced': 'Pokročilé',\n        'equipmentShop.efficiency': 'Efektivita: {{efficiency}}x',\n        'equipmentShop.automation': 'Automatizace:',\n        'equipmentShop.unlockLevel': 'Úroveň odemčení: {{level}}',\n        'equipmentShop.purchase': '💰 Koupit',\n        'equipmentShop.noEquipment': 'V této kategorii není k dispozici žádné vybavení.',\n        'equipmentShop.levelUpForEquipment': 'Postupte na vyšší úroveň pro odemčení dalšího vybavení!',\n        'equipmentShop.tips.title': '💡 Tipy pro vybavení',\n        'equipmentShop.tips.automated': '• Automatizované vybavení může běžet bez vašeho dohledu',\n        'equipmentShop.tips.efficiency': '• Vyšší efektivita znamená rychlejší výrobu a lepší kvalitu',\n        'equipmentShop.tips.conveyor': '• Dopravní pásy spojují vybavení pro bezproblémový pracovní tok',\n        'equipmentShop.tips.advanced': '• Pokročilé vybavení se odemyká na vyšších úrovních',\n        // Level Up Modal\n        'levelUp.levelRewards': '🎁 Odměny za úroveň',\n        'levelUp.whatsNext': '💡 Co dál?',\n        'levelUp.checkRecipes': '• Podívejte se na nové recepty ve své knize receptů',\n        'levelUp.visitShop': '• Navštivte obchod pro nové vybavení',\n        'levelUp.challengingOrders': '• Přijměte náročnější objednávky',\n        'levelUp.investSkills': '• Investujte do vylepšení dovedností',\n        // Settings Modal\n        'settings.title': '⚙️ Nastavení',\n        'settings.general': 'Obecné',\n        'settings.audio': 'Zvuk',\n        'settings.graphics': 'Grafika',\n        'settings.save': 'Uložení a data',\n        'settings.language': '🌍 Jazyk',\n        'settings.gameplay': '🎮 Hratelnost',\n        'settings.notifications': 'Povolit oznámení',\n        'settings.tutorials': 'Zobrazit návody',\n        'settings.animationSpeed': 'Rychlost animace',\n        'settings.sound': 'Zvukové efekty',\n        'settings.music': 'Hudba na pozadí',\n        'settings.quality': '🎨 Kvalita grafiky',\n        'settings.autoSave': '💾 Automatické ukládání',\n        'settings.enableAutoSave': 'Povolit automatické ukládání',\n        'settings.dataManagement': '📁 Správa dat',\n        'settings.exportSave': '📤 Exportovat uložení',\n        'settings.importSave': '📥 Importovat uložení',\n        'settings.cloudSync': '☁️ Cloudová synchronizace',\n        'settings.cloudSyncDescription': 'Cloudová synchronizace vám umožňuje uložit pokrok online a hrát na více zařízeních.',\n        // Cloud Save Management\n        'cloud.save.manage_saves': 'Spravovat cloudová uložení',\n        'cloud.save.login': 'Přihlásit / Registrovat',\n        'cloud.save.login_required': 'Pro použití cloudových uložení se prosím přihlaste',\n        // Bakery Manager Modal\n        'bakeries.title': '🏪 Správce pekáren',\n        'bakeries.subtitle': 'Spravujte své pekárenské impérium',\n        'bakeries.owned': 'Moje pekárny',\n        'bakeries.available': 'Dostupné',\n        'bakeries.current': 'Aktuální',\n        'bakeries.level': 'Úroveň',\n        'bakeries.specialization': 'Specializace',\n        'bakeries.equipment': 'Vybavení',\n        'bakeries.orders': 'Aktivní objednávky',\n        'bakeries.switchTo': 'Přepnout na',\n        'bakeries.noOwned': 'Ještě nevlastníte žádné pekárny.',\n        'bakeries.purchase': '💰 Koupit',\n        'bakeries.tooExpensive': '💸 Příliš drahé',\n        'bakeries.allOwned': 'Vlastníte všechny dostupné pekárny!',\n        'bakeries.tips': '💡 Tipy pro pekárny',\n        'bakeries.tip1': 'Každá pekárna se specializuje na různé produkty pro bonusovou efektivitu',\n        'bakeries.tip2': 'Přepínejte mezi pekárnami pro správu více lokalit',\n        'bakeries.tip3': 'Specializované pekárny přitahují zákazníky hledající konkrétní položky',\n        'bakeries.tip4': 'Vylepšujte každou pekárnu nezávisle pro maximální zisk',\n        // Notifications\n        'notifications.orderAccepted': 'Objednávka přijata',\n        'notifications.orderAcceptedMessage': 'Přijali jste novou objednávku!',\n        'notifications.orderCompleted': 'Objednávka dokončena!',\n        'notifications.orderCompletedMessage': 'Získali jste {{reward}} Kč a zkušenosti!',\n        'notifications.orderDeclined': 'Objednávka odmítnuta',\n        'notifications.orderDeclinedMessage': 'Objednávka byla odstraněna z vaší fronty.',\n        'notifications.bakeryPurchased': 'Pekárna zakoupena!',\n        'notifications.bakeryPurchasedMessage': 'Nyní vlastníte {{name}}!',\n        'notifications.bakerySwitched': 'Pekárna přepnuta',\n        'notifications.bakerySwitchedMessage': 'Přepnuto na {{name}}',\n        // Common buttons and actions\n        'common.accept': 'Přijmout',\n        'common.decline': 'Odmítnout',\n        'common.complete': 'Dokončit',\n        'common.purchase': 'Koupit',\n        'common.upgrade': 'Vylepšit',\n        'common.cancel': 'Zrušit',\n        'common.confirm': 'Potvrdit',\n        'common.save': 'Uložit',\n        'common.load': 'Načíst',\n        'common.delete': 'Smazat',\n        'common.edit': 'Upravit',\n        'common.back': 'Zpět',\n        'common.next': 'Další',\n        'common.previous': 'Předchozí',\n        'common.yes': 'Ano',\n        'common.no': 'Ne',\n        'common.create': 'Vytvořit',\n        'common.join': 'Připojit se',\n        'common.leave': 'Odejít',\n        'common.start': 'Začít',\n        'common.ready': 'Připraven',\n        'common.notReady': 'Nepřipraven',\n        'common.send': 'Odeslat',\n        'common.refresh': 'Obnovit',\n        'common.retry': 'Zkusit znovu',\n        'common.reset': 'Resetovat',\n        'common.clear': 'Vymazat',\n        'common.apply': 'Použít',\n        'common.warning': 'Varování',\n        'common.info': 'Informace',\n        'common.success': 'Úspěch',\n        'common.error': 'Chyba',\n        // Save/Load System\n        'saveLoad.saveDesc': 'Vyberte slot pro uložení vašeho postupu',\n        'saveLoad.loadDesc': 'Vyberte soubor uložení k načtení',\n        'saveLoad.saveName': 'Název uložení',\n        'saveLoad.emptySlot': 'Prázdný slot',\n        'saveLoad.selectedSaveSlot': 'Vybrán: Slot {{slot}}',\n        'saveLoad.selectedLoadSlot': 'Vybrán: Slot {{slot}}',\n        'saveLoad.confirmOverwrite': 'Přepsat uložení?',\n        'saveLoad.overwriteWarning': 'Toto přepíše existující uložení. Tuto akci nelze vrátit zpět.',\n        'saveLoad.overwrite': 'Přepsat',\n        'saveLoad.fileSlots': 'Souborové sloty',\n        'saveLoad.gameSlots': 'Herní sloty',\n        'saveLoad.exportSave': 'Exportovat uložení',\n        'saveLoad.importSave': 'Importovat uložení',\n        'saveLoad.deleteConfirm': 'Smazat uložení?',\n        'saveLoad.deleteWarning': 'Toto trvale smaže tento soubor uložení. Tuto akci nelze vrátit zpět.',\n        'saveLoad.delete': 'Smazat',\n        // Game Menu\n        'gameMenu.title': 'Herní menu',\n        'gameMenu.subtitle': 'Spravujte svou hru',\n        'gameMenu.resume': 'Pokračovat ve hře',\n        'gameMenu.resumeDesc': 'Pokračovat v hraní',\n        'gameMenu.save': 'Uložit hru',\n        'gameMenu.saveDesc': 'Uložit váš postup',\n        'gameMenu.load': 'Načíst hru',\n        'gameMenu.loadDesc': 'Načíst uložený postup',\n        'gameMenu.settings': 'Nastavení',\n        'gameMenu.settingsDesc': 'Herní předvolby',\n        'gameMenu.mainMenu': 'Hlavní menu',\n        'gameMenu.mainMenuDesc': 'Návrat do hlavního menu',\n        'gameMenu.exit': 'Ukončit hru',\n        'gameMenu.exitDesc': 'Zavřít aplikaci',\n        'gameMenu.tip': 'Stiskněte ESC pro otevření tohoto menu kdykoli',\n        // Discord Rich Presence\n        'settings.discord': 'Discord',\n        'settings.discordRichPresence': 'Discord Rich Presence',\n        'settings.discordDescription': 'Zobrazit váš aktuální herní stav a aktivitu v Discordu.',\n        'settings.enableDiscordRPC': 'Povolit Discord Rich Presence',\n        'settings.discordConnected': '✅ Připojeno k Discordu',\n        'settings.discordDisconnected': '❌ Nepřipojeno k Discordu',\n        'settings.discordInfo': 'Co je Discord Rich Presence?',\n        'settings.discordInfoDesc1': 'Discord Rich Presence ukazuje vašim přátelům, co děláte v Bake It Out:',\n        'settings.discordFeature1': 'Vaši aktuální úroveň a peníze',\n        'settings.discordFeature2': 'Co právě pečete',\n        'settings.discordFeature3': 'Informace o multiplayer místnosti',\n        'settings.discordFeature4': 'Jak dlouho hrajete',\n        'settings.discordInfoDesc2': 'Vaši přátelé se mohou připojit k vašim multiplayer hrám přímo z Discordu!',\n        'settings.discordTroubleshooting': 'Discord není připojen',\n        'settings.discordTrouble1': 'Ujistěte se, že Discord běží na vašem počítači.',\n        'settings.discordTrouble2': 'Discord Rich Presence funguje pouze v desktopové verzi hry.',\n        'settings.discordTrouble3': 'Zkuste restartovat Discord i hru, pokud se připojení nezdaří.',\n        'settings.discordPrivacy': 'Informace o soukromí',\n        'settings.discordPrivacyDesc1': 'Discord Rich Presence sdílí pouze:',\n        'settings.discordPrivacy1': 'Vaši aktuální herní aktivitu (veřejné)',\n        'settings.discordPrivacy2': 'Vaši úroveň hráče a postup (veřejné)',\n        'settings.discordPrivacy3': 'Kódy multiplayer místností (pro připojení)',\n        'settings.discordPrivacyDesc2': 'Žádné osobní informace nebo uložená data nejsou sdílena s Discordem.',\n        'settings.discordStatus': 'Stav Discordu',\n        'settings.discordInitializing': '🔄 Inicializace Discord RPC...',\n        'settings.discordRetrying': '🔄 Opakování připojení...',\n        'settings.discordUnavailable': '❌ Discord není dostupný',\n        'settings.discordDesktopOnly': 'ℹ️ Discord RPC dostupný pouze v desktopové verzi',\n        // Error messages and status\n        'error.general': 'Došlo k chybě',\n        'error.saveLoad': 'Nepodařilo se uložit/načíst hru',\n        'error.connection': 'Chyba připojení',\n        'error.fileNotFound': 'Soubor nenalezen',\n        'error.invalidData': 'Neplatný formát dat',\n        'error.permissionDenied': 'Přístup odepřen',\n        'status.loading': 'Načítání...',\n        'status.saving': 'Ukládání...',\n        'status.connecting': 'Připojování...',\n        'status.ready': 'Připraven',\n        'status.success': 'Úspěch!',\n        'status.failed': 'Neúspěšné',\n        'status.offline': 'Offline',\n        'status.online': 'Online',\n        // UI Elements\n        'ui.placeholder': 'Zadejte text...',\n        'ui.search': 'Hledat',\n        'ui.filter': 'Filtrovat',\n        'ui.sort': 'Seřadit',\n        'ui.ascending': 'Vzestupně',\n        'ui.descending': 'Sestupně',\n        'ui.selectAll': 'Vybrat vše',\n        'ui.deselectAll': 'Zrušit výběr',\n        'ui.noResults': 'Žádné výsledky',\n        'ui.noData': 'Žádná data k dispozici',\n        'ui.loading': 'Načítání...',\n        'ui.saving': 'Ukládání...',\n        'ui.saved': 'Uloženo!',\n        'ui.failed': 'Neúspěšné',\n        'ui.retry': 'Zkusit znovu',\n        'ui.back': 'Zpět',\n        'ui.forward': 'Vpřed',\n        'ui.home': 'Domů',\n        'ui.menu': 'Menu',\n        'ui.options': 'Možnosti',\n        'ui.preferences': 'Předvolby',\n        // Multiplayer\n        'multiplayer.lobby': '👥 Multiplayerová lobby',\n        'multiplayer.connected': '🟢 Připojeno',\n        'multiplayer.disconnected': '🔴 Odpojeno',\n        'multiplayer.createRoom': 'Vytvořit místnost',\n        'multiplayer.joinRoom': 'Připojit se k místnosti',\n        'multiplayer.room': 'Místnost',\n        'multiplayer.yourName': 'Vaše jméno',\n        'multiplayer.enterName': 'Zadejte své jméno',\n        'multiplayer.roomName': 'Název místnosti',\n        'multiplayer.enterRoomName': 'Zadejte název místnosti',\n        'multiplayer.gameMode': 'Herní režim',\n        'multiplayer.cooperative': '🤝 Kooperativní',\n        'multiplayer.competitive': '⚔️ Soutěžní',\n        'multiplayer.maxPlayers': 'Max hráčů: {{count}}',\n        'multiplayer.roomId': 'ID místnosti',\n        'multiplayer.enterRoomId': 'Zadejte ID místnosti',\n        'multiplayer.players': 'Hráči ({{count}})',\n        'multiplayer.host': 'HOSTITEL',\n        'multiplayer.level': 'Úroveň {{level}}',\n        'multiplayer.chat': 'Chat',\n        'multiplayer.typeMessage': 'Napište zprávu...',\n        'multiplayer.gameTime': 'Herní čas: {{time}}',\n        'multiplayer.teamStats': '📊 Týmové statistiky',\n        'multiplayer.ordersCompleted': 'Dokončené objednávky:',\n        'multiplayer.totalRevenue': 'Celkový příjem:',\n        'multiplayer.teamExperience': 'Týmové zkušenosti:',\n        'multiplayer.sharedKitchen': '🏪 Sdílená kuchyně',\n        'multiplayer.sharedOrders': '📋 Sdílené objednávky',\n        'multiplayer.sharedInventory': '📦 Sdílený sklad',\n        'multiplayer.contribution': 'Příspěvek:',\n        'multiplayer.online': '🟢 Online',\n        'multiplayer.status': 'Stav:',\n        'multiplayer.you': '(Vy)',\n        'multiplayer.teamChat': '💬 Týmový chat',\n        'multiplayer.chatPlaceholder': 'Zde se zobrazí zprávy chatu...',\n        // Multiplayer game modes\n        'multiplayer.mode.cooperative.description': '🤝 Kooperativní režim: Spolupracujte na dokončování objednávek a rozvoji sdílené pekárny!',\n        'multiplayer.mode.competitive.description': '⚔️ Soutěžní režim: Soutěžte s ostatními hráči o dokončení nejvíce objednávek!',\n        // Multiplayer game interface\n        'multiplayer.game.title': '🎮 Multiplayerová hra - {{roomName}}',\n        'multiplayer.game.mode': 'Režim: {{mode}}',\n        'multiplayer.game.playersCount': 'Hráči: {{count}}',\n        'multiplayer.game.playing': '🟢 Hraje se',\n        'multiplayer.game.leaveGame': '🚪 Opustit hru',\n        'multiplayer.game.tabs.game': 'Hra',\n        'multiplayer.game.tabs.players': 'Hráči',\n        'multiplayer.game.tabs.chat': 'Chat',\n        // Room creation and joining\n        'multiplayer.create.title': '🏗️ Vytvořit místnost',\n        'multiplayer.join.title': '🚪 Připojit se k místnosti',\n        'multiplayer.room.info': 'Režim: {{mode}} • Hráči: {{current}}/{{max}}',\n        'multiplayer.room.readyUp': '✅ Připraven',\n        'multiplayer.room.notReady': '⏳ Nepřipraven',\n        'multiplayer.room.startGame': '🚀 Začít hru',\n        'multiplayer.room.leaveRoom': '🚪 Opustit',\n        // Connection states\n        'multiplayer.connection.connecting': 'Připojování...',\n        'multiplayer.connection.reconnecting': 'Znovu se připojuje...',\n        'multiplayer.connection.failed': 'Připojení selhalo',\n        'multiplayer.connection.error': '⚠️ {{error}}',\n        // System messages\n        'multiplayer.system.playerJoined': '{{name}} se připojil do místnosti',\n        'multiplayer.system.playerLeft': '{{name}} opustil místnost',\n        'multiplayer.system.gameStarted': 'Hra začala!',\n        'multiplayer.system.gameEnded': 'Hra skončila!',\n        'multiplayer.system.roomCreated': 'Místnost byla úspěšně vytvořena',\n        'multiplayer.system.roomJoined': 'Úspěšně jste se připojili do místnosti',\n        // Ingredients\n        'ingredient.Flour': 'Mouka',\n        'ingredient.Sugar': 'Cukr',\n        'ingredient.Eggs': 'Vejce',\n        'ingredient.Butter': 'Máslo',\n        'ingredient.Milk': 'Mléko',\n        'ingredient.Vanilla Extract': 'Vanilkový extrakt',\n        'ingredient.Vanilla': 'Vanilka',\n        'ingredient.Chocolate Chips': 'Čokoládové kousky',\n        'ingredient.Baking Powder': 'Prášek do pečiva',\n        'ingredient.Salt': 'Sůl',\n        'ingredient.Cinnamon': 'Skořice',\n        'ingredient.Nuts': 'Ořechy',\n        'ingredient.Cream Cheese': 'Krémový sýr',\n        'ingredient.Honey': 'Med',\n        'ingredient.Cocoa Powder': 'Kakaový prášek',\n        'ingredient.Yeast': 'Droždí',\n        // Equipment Names\n        'equipment.Basic Oven': 'Základní trouba',\n        'equipment.Hand Mixer': 'Ruční mixér',\n        'equipment.Professional Oven': 'Profesionální trouba',\n        'equipment.Stand Mixer': 'Stojanový mixér',\n        'equipment.Automated Oven': 'Automatická trouba',\n        'equipment.Industrial Mixer': 'Průmyslový mixér',\n        'equipment.Conveyor Belt': 'Dopravní pás',\n        'equipment.Display Counter': 'Výstavní pult',\n        'equipment.Prep Counter': 'Přípravný pult',\n        // Recipe Names\n        'recipe.Chocolate Chip Cookies': 'Sušenky s čokoládou',\n        'recipe.Vanilla Muffins': 'Vanilkové muffiny',\n        'recipe.Simple Bread': 'Jednoduchý chléb',\n        'recipe.Cinnamon Rolls': 'Skořicové záviny',\n        'recipe.Sourdough Bread': 'Kváskový chléb',\n        'recipe.Chocolate Cake': 'Čokoládový dort',\n        'recipe.Apple Pie': 'Jablečný koláč',\n        'recipe.Croissants': 'Croissanty',\n        // Missing UI Elements\n        'toolbar.menu': 'Menu',\n        'equipment.Work Counter': 'Pracovní pult',\n        'game.level': 'Úroveň',\n        'game.xp': 'XP',\n        'game.current': 'Aktuální',\n        'quick_actions.title': 'Rychlé akce',\n        'quick_actions.buy_ingredients': 'Koupit suroviny',\n        'quick_actions.view_recipes': 'Zobrazit recepty',\n        'quick_actions.equipment_shop': 'Obchod s vybavením',\n        'click_to_use': 'Klikněte pro použití',\n        'currency.czk': 'Kč',\n        // Additional UI Elements\n        'location.downtown_delights': 'Downtown Delights',\n        'toolbar.bakery': 'Pekárna',\n        'toolbar.achievements': 'Úspěchy',\n        'toolbar.stars': 'Hvězdy',\n        'toolbar.settings': 'Nastavení',\n        // Orders\n        'orders.accepted': 'Objednávka přijata',\n        // Cloud Save\n        'cloud.auth.login_title': 'Přihlášení do cloudového uložení',\n        'cloud.auth.register_title': 'Vytvoření účtu cloudového uložení',\n        'cloud.auth.username': 'Uživatelské jméno',\n        'cloud.auth.email': 'E-mail',\n        'cloud.auth.password': 'Heslo',\n        'cloud.auth.confirm_password': 'Potvrdit heslo',\n        'cloud.auth.login_button': 'Přihlásit se',\n        'cloud.auth.register_button': 'Vytvořit účet',\n        'cloud.auth.cancel': 'Zrušit',\n        'cloud.auth.loading': 'Načítání...',\n        'cloud.auth.username_required': 'Uživatelské jméno je povinné',\n        'cloud.auth.username_too_short': 'Uživatelské jméno musí mít alespoň 3 znaky',\n        'cloud.auth.email_required': 'E-mail je povinný',\n        'cloud.auth.email_invalid': 'Zadejte platnou e-mailovou adresu',\n        'cloud.auth.password_required': 'Heslo je povinné',\n        'cloud.auth.password_too_short': 'Heslo musí mít alespoň 6 znaků',\n        'cloud.auth.passwords_dont_match': 'Hesla se neshodují',\n        'cloud.auth.username_placeholder': 'Zadejte své uživatelské jméno',\n        'cloud.auth.email_placeholder': 'Zadejte svůj e-mail',\n        'cloud.auth.password_placeholder': 'Zadejte své heslo',\n        'cloud.auth.confirm_password_placeholder': 'Potvrďte své heslo',\n        'cloud.auth.no_account': 'Nemáte účet?',\n        'cloud.auth.have_account': 'Již máte účet?',\n        'cloud.auth.register_link': 'Vytvořte si ho',\n        'cloud.auth.login_link': 'Přihlásit se',\n        'cloud.save.auth_required': 'Vyžadován účet cloudového uložení',\n        'cloud.save.auth_description': 'Pro použití cloudového uložení se musíte přihlásit nebo vytvořit účet.',\n        'cloud.save.save_title': 'Uložit do cloudu',\n        'cloud.save.load_title': 'Načíst z cloudu',\n        'cloud.save.logged_in_as': 'Přihlášen jako',\n        'cloud.save.syncing': 'Synchronizace...',\n        'cloud.save.sync_success': 'Synchronizováno',\n        'cloud.save.sync_error': 'Chyba synchronizace',\n        'cloud.save.sync_idle': 'Připraveno',\n        'cloud.save.last_sync': 'Poslední synchronizace',\n        'cloud.save.save_name': 'Název uložení',\n        'cloud.save.save_name_placeholder': 'Zadejte název pro vaše uložení',\n        'cloud.save.your_saves': 'Vaše cloudová uložení',\n        'cloud.save.refreshing': 'Obnovování...',\n        'cloud.save.refresh': 'Obnovit',\n        'cloud.save.loading_saves': 'Načítání uložení...',\n        'cloud.save.no_saves': 'Žádná cloudová uložení nenalezena',\n        'cloud.save.level': 'Úroveň',\n        'cloud.save.delete': 'Smazat',\n        'cloud.save.confirm_delete': 'Opravdu chcete smazat toto uložení?',\n        'cloud.save.cancel': 'Zrušit',\n        'cloud.save.saving': 'Ukládání...',\n        'cloud.save.save_button': 'Uložit do cloudu',\n        'cloud.save.loading': 'Načítání...',\n        'cloud.save.load_button': 'Načíst hru',\n        // Save/Load Modal\n        'saveLoad.local_saves': 'Místní uložení',\n        'saveLoad.file_saves': 'Souborová uložení',\n        'saveLoad.cloud_saves': 'Cloudová uložení',\n        // Dashboard\n        'dashboard.title': 'Serverový dashboard',\n        'dashboard.button': 'Dashboard',\n        'dashboard.tooltip': 'Přístup k serverovému dashboardu',\n        'dashboard.login_required': 'Pro přístup k dashboardu se prosím přihlaste',\n        'dashboard.logged_in_as': 'Přihlášen jako',\n        'dashboard.access_title': 'Přístup k serverovému dashboardu',\n        'dashboard.access_description': 'Sledujte statistiky serveru, spravujte uživatele a prohlížejte herní analytiku',\n        'dashboard.feature.users': 'Správa uživatelů',\n        'dashboard.feature.rooms': 'Herní místnosti',\n        'dashboard.feature.saves': 'Cloudová uložení',\n        'dashboard.feature.analytics': 'Analytika',\n        'dashboard.open': 'Otevřít dashboard',\n        'dashboard.opening': 'Otevírání...',\n        'dashboard.cancel': 'Zrušit',\n        'dashboard.new_tab_notice': 'Dashboard se otevře v nové záložce',\n        'dashboard.open_failed': 'Nepodařilo se otevřít dashboard',\n        // Recipes\n        'recipe.chocolate_chip_cookies': 'Čokoládové sušenky',\n        'recipe.vanilla_muffins': 'Vanilkové muffiny',\n        'recipe.cinnamon_rolls': 'Skořicové záviny',\n        'recipe.chocolate_brownies': 'Čokoládové brownies',\n        'recipe.blueberry_pie': 'Borůvkový koláč',\n        'recipe.sourdough_bread': 'Kváskový chléb',\n        // Ingredients\n        'ingredient.flour': 'Mouka',\n        'ingredient.sugar': 'Cukr',\n        'ingredient.butter': 'Máslo',\n        'ingredient.chocolate_chips': 'Čokoládové kousky',\n        'ingredient.eggs': 'Vejce',\n        'ingredient.vanilla': 'Vanilka',\n        'ingredient.cinnamon': 'Skořice',\n        'ingredient.blueberries': 'Borůvky',\n        'ingredient.salt': 'Sůl',\n        // Recipe Categories\n        'category.cookies': 'Sušenky',\n        'category.cakes': 'Koláče',\n        'category.pastries': 'Pečivo',\n        'category.pies': 'Koláče',\n        'category.bread': 'Chléb',\n        // Equipment Names\n        'equipment.oven': 'Trouba',\n        'equipment.mixer': 'Mixér',\n        'equipment.work_counter': 'Pracovní pult',\n        'equipment.display_case': 'Vitrina',\n        'equipment.cash_register': 'Pokladna'\n    }\n};\nfunction LanguageProvider(param) {\n    let { children } = param;\n    _s();\n    const [language, setLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('en');\n    const [mounted, setMounted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            // Mark as mounted to prevent hydration mismatch\n            setMounted(true);\n        }\n    }[\"LanguageProvider.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LanguageProvider.useEffect\": ()=>{\n            // Only access localStorage after component is mounted on client side\n            if (!mounted || \"object\" === 'undefined') return;\n            try {\n                const savedLanguage = localStorage.getItem('language');\n                if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'cs')) {\n                    setLanguage(savedLanguage);\n                }\n            } catch (error) {\n                console.warn('Failed to load language from localStorage:', error);\n            }\n        }\n    }[\"LanguageProvider.useEffect\"], [\n        mounted\n    ]);\n    const handleSetLanguage = (lang)=>{\n        setLanguage(lang);\n        // Only save to localStorage if we're on the client side\n        if (mounted && \"object\" !== 'undefined') {\n            try {\n                localStorage.setItem('language', lang);\n            } catch (error) {\n                console.warn('Failed to save language to localStorage:', error);\n            }\n        }\n    };\n    const t = (key, params)=>{\n        let translation = translations[language][key] || key;\n        if (params) {\n            Object.entries(params).forEach((param)=>{\n                let [param1, value] = param;\n                translation = translation.replace(\"{{\".concat(param1, \"}}\"), value);\n            });\n        }\n        return translation;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(LanguageContext.Provider, {\n        value: {\n            language,\n            setLanguage: handleSetLanguage,\n            t\n        },\n        children: children\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\coding stuff\\\\bake it out\\\\bake-it-out\\\\src\\\\contexts\\\\LanguageContext.tsx\",\n        lineNumber: 1668,\n        columnNumber: 5\n    }, this);\n}\n_s(LanguageProvider, \"gT/yCmRg4polcKdoZlcGYMkuUYA=\");\n_c = LanguageProvider;\nfunction useLanguage() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(LanguageContext);\n    if (context === undefined) {\n        // Fallback for when context is not available\n        console.warn('useLanguage called outside of LanguageProvider, using fallback');\n        return {\n            language: 'en',\n            setLanguage: ()=>{},\n            t: (key)=>key\n        };\n    }\n    return context;\n}\n_s1(useLanguage, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\nvar _c;\n$RefreshReg$(_c, \"LanguageProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/contexts/LanguageContext.tsx\n"));

/***/ })

});