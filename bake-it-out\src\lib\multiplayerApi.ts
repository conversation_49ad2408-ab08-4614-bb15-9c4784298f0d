import { auth } from '@/lib/firebase'

const API_BASE_URL = process.env.NEXT_PUBLIC_MULTIPLAYER_API_URL || 'http://localhost:3001/api'

class MultiplayerAPI {
  private async getAuthHeaders(): Promise<HeadersInit> {
    const user = auth.currentUser
    if (!user) {
      throw new Error('User not authenticated')
    }

    const token = await user.getIdToken()
    return {
      'Authorization': `Bear<PERSON> ${token}`,
      'Content-Type': 'application/json'
    }
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const headers = await this.getAuthHeaders()
    
    const response = await fetch(`${API_BASE_URL}${endpoint}`, {
      ...options,
      headers: {
        ...headers,
        ...options.headers
      }
    })

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: 'Network error' }))
      throw new Error(error.error || `HTTP ${response.status}`)
    }

    return response.json()
  }

  // Room management
  async getRooms() {
    return this.request('/multiplayer/rooms')
  }

  async createRoom(roomData: {
    name: string
    maxPlayers: number
    gameMode: string
    isPrivate?: boolean
  }) {
    return this.request('/multiplayer/rooms', {
      method: 'POST',
      body: JSON.stringify(roomData)
    })
  }

  async joinRoom(roomId: string, roomCode?: string) {
    return this.request(`/multiplayer/rooms/${roomId}/join`, {
      method: 'POST',
      body: JSON.stringify({ roomCode })
    })
  }

  async leaveRoom(roomId: string) {
    return this.request(`/multiplayer/rooms/${roomId}/leave`, {
      method: 'POST'
    })
  }

  async getRoomDetails(roomId: string) {
    return this.request(`/multiplayer/rooms/${roomId}`)
  }

  async updateGameState(roomId: string, gameState: any) {
    return this.request(`/multiplayer/rooms/${roomId}/state`, {
      method: 'PUT',
      body: JSON.stringify({ gameState })
    })
  }
}

export const multiplayerAPI = new MultiplayerAPI()
