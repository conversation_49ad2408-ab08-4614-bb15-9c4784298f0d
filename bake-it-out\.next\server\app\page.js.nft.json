{"version": 1, "files": ["../../../node_modules/next/dist/client/components/app-router-headers.js", "../../../node_modules/next/dist/compiled/@opentelemetry/api/index.js", "../../../node_modules/next/dist/compiled/@opentelemetry/api/package.json", "../../../node_modules/next/dist/compiled/jsonwebtoken/index.js", "../../../node_modules/next/dist/compiled/jsonwebtoken/package.json", "../../../node_modules/next/dist/compiled/next-server/app-page.runtime.prod.js", "../../../node_modules/next/dist/lib/constants.js", "../../../node_modules/next/dist/lib/interop-default.js", "../../../node_modules/next/dist/lib/is-error.js", "../../../node_modules/next/dist/lib/semver-noop.js", "../../../node_modules/next/dist/server/app-render/action-async-storage-instance.js", "../../../node_modules/next/dist/server/app-render/action-async-storage.external.js", "../../../node_modules/next/dist/server/app-render/after-task-async-storage-instance.js", "../../../node_modules/next/dist/server/app-render/after-task-async-storage.external.js", "../../../node_modules/next/dist/server/app-render/async-local-storage.js", "../../../node_modules/next/dist/server/app-render/cache-signal.js", "../../../node_modules/next/dist/server/app-render/dynamic-access-async-storage-instance.js", "../../../node_modules/next/dist/server/app-render/dynamic-access-async-storage.external.js", "../../../node_modules/next/dist/server/app-render/module-loading/track-module-loading.external.js", "../../../node_modules/next/dist/server/app-render/module-loading/track-module-loading.instance.js", "../../../node_modules/next/dist/server/app-render/work-async-storage-instance.js", "../../../node_modules/next/dist/server/app-render/work-async-storage.external.js", "../../../node_modules/next/dist/server/app-render/work-unit-async-storage-instance.js", "../../../node_modules/next/dist/server/app-render/work-unit-async-storage.external.js", "../../../node_modules/next/dist/server/lib/cache-handlers/default.external.js", "../../../node_modules/next/dist/server/lib/incremental-cache/memory-cache.external.js", "../../../node_modules/next/dist/server/lib/incremental-cache/shared-cache-controls.external.js", "../../../node_modules/next/dist/server/lib/incremental-cache/tags-manifest.external.js", "../../../node_modules/next/dist/server/lib/lru-cache.js", "../../../node_modules/next/dist/server/lib/router-utils/instrumentation-globals.external.js", "../../../node_modules/next/dist/server/lib/trace/constants.js", "../../../node_modules/next/dist/server/lib/trace/tracer.js", "../../../node_modules/next/dist/server/load-manifest.external.js", "../../../node_modules/next/dist/server/response-cache/types.js", "../../../node_modules/next/dist/shared/lib/deep-freeze.js", "../../../node_modules/next/dist/shared/lib/invariant-error.js", "../../../node_modules/next/dist/shared/lib/is-plain-object.js", "../../../node_modules/next/dist/shared/lib/is-thenable.js", "../../../node_modules/next/dist/shared/lib/no-fallback-error.external.js", "../../../node_modules/next/dist/shared/lib/page-path/ensure-leading-slash.js", "../../../node_modules/next/dist/shared/lib/router/utils/app-paths.js", "../../../node_modules/next/dist/shared/lib/router/utils/html-bots.js", "../../../node_modules/next/dist/shared/lib/router/utils/is-bot.js", "../../../node_modules/next/dist/shared/lib/segment.js", "../../../node_modules/next/package.json", "../../../package.json", "../../../src/components/cloud/CloudAuthModal.tsx", "../../../src/components/cloud/CloudSaveModal.tsx", "../../../src/components/dashboard/DashboardAccess.tsx", "../../../src/components/game/AchievementsModal.tsx", "../../../src/components/game/AutomationModal.tsx", "../../../src/components/game/BakeryLayout.tsx", "../../../src/components/game/BakeryManagerModal.tsx", "../../../src/components/game/BakingModal.tsx", "../../../src/components/game/CustomerManager.tsx", "../../../src/components/game/DiningRoom.tsx", "../../../src/components/game/Equipment.tsx", "../../../src/components/game/EquipmentShopModal.tsx", "../../../src/components/game/LevelUpModal.tsx", "../../../src/components/game/NotificationSystem.tsx", "../../../src/components/game/Order.tsx", "../../../src/components/game/RecipeModal.tsx", "../../../src/components/game/SaveLoadModal.tsx", "../../../src/components/game/SettingsModal.tsx", "../../../src/components/game/ShopModal.tsx", "../../../src/components/game/SkillTreeModal.tsx", "../../../src/components/menu/CreditsModal.tsx", "../../../src/components/menu/GameMenu.tsx", "../../../src/components/menu/MainMenu.tsx", "../../../src/components/multiplayer/MultiplayerGame.tsx", "../../../src/components/multiplayer/MultiplayerLobby.tsx", "../../../src/components/ui/Button.tsx", "../../../src/components/ui/ClientOnly.tsx", "../../../src/components/ui/GameToolbar.tsx", "../../../src/contexts/GameContext.tsx", "../../../src/lib/automationSystem.ts", "../../../src/lib/discordRPC.ts", "../../../src/lib/fileSaveSystem.ts", "../../../src/lib/gameLogic.ts", "../../../src/lib/localization.ts", "../../../src/lib/progressionSystem.ts", "../../../src/lib/saveSystem.ts", "../../../src/lib/socket.ts", "../../package.json", "../chunks/181.js", "../chunks/899.js", "../chunks/976.js", "../chunks/985.js", "../webpack-runtime.js", "page_client-reference-manifest.js"]}