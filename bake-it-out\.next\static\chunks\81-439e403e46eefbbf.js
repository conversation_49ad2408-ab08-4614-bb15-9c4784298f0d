"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[81],{2163:(e,s,t)=>{t.d(s,{p:()=>r});var i=t(5155),a=t(3741),n=t(9283);function r(e){let{order:s,onAccept:t,onDecline:r,onComplete:c}=e,{t:l}=(0,n.o)();return(0,i.jsxs)("div",{className:"p-4 rounded-lg border ".concat((()=>{switch(s.status){case"pending":return"border-yellow-300 bg-yellow-50";case"accepted":case"in_progress":return"border-blue-300 bg-blue-50";case"completed":return"border-green-300 bg-green-50";case"failed":return"border-red-300 bg-red-50";default:return"border-gray-300 bg-gray-50"}})()),children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,i.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{className:"text-lg",children:(()=>{let e=["\uD83D\uDC69","\uD83D\uDC68","\uD83D\uDC75","\uD83D\uDC74","\uD83D\uDC67","\uD83D\uDC66"],t=s.customerName.length%e.length;return e[t]})()}),(0,i.jsx)("h3",{className:"font-medium text-gray-800",children:s.customerName})]}),(0,i.jsx)("span",{className:"text-sm font-semibold text-green-600",children:l("orders.reward",{amount:s.reward.toString()})})]}),(0,i.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:s.items.map((e,s)=>(0,i.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,i.jsx)("span",{children:"\uD83E\uDDC1"}),(0,i.jsx)("span",{children:e})]},s))}),(0,i.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,i.jsxs)("div",{className:"text-xs text-gray-500",children:["⏰ ",l("orders.timeLimit",{time:(e=>{let s=Math.floor(e/60);return"".concat(s,":").concat((e%60).toString().padStart(2,"0"))})(s.timeLimit)})]}),(0,i.jsx)("div",{className:"text-xs",title:"Difficulty: ".concat(s.difficulty,"/5"),children:"⭐".repeat(s.difficulty)+"☆".repeat(5-s.difficulty)})]}),"pending"===s.status&&(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsxs)(a.$,{size:"sm",variant:"success",onClick:()=>t(s.id),className:"flex-1",children:["✅ ",l("orders.accept")]}),(0,i.jsxs)(a.$,{size:"sm",variant:"danger",onClick:()=>r(s.id),className:"flex-1",children:["❌ ",l("orders.decline")]})]}),"accepted"===s.status&&(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-blue-600 text-sm font-medium mb-2",children:"\uD83D\uDCCB Order Accepted"}),(0,i.jsxs)(a.$,{size:"sm",variant:"primary",onClick:()=>c&&c(s.id),className:"w-full",children:["\uD83C\uDFAF ",l("orders.complete")]})]}),"in_progress"===s.status&&(0,i.jsxs)("div",{className:"text-center text-orange-600 text-sm font-medium",children:["\uD83D\uDD04 ",l("orders.inProgress")]}),"completed"===s.status&&(0,i.jsx)("div",{className:"text-center text-green-600 text-sm font-medium",children:"✅ Completed!"}),"failed"===s.status&&(0,i.jsx)("div",{className:"text-center text-red-600 text-sm font-medium",children:"❌ Failed"})]})}},2785:(e,s,t)=>{t.d(s,{b:()=>d});var i=t(5155),a=t(2115),n=t(3741),r=t(9283),c=t(2148),l=t(5877);function d(e){let{isOpen:s,onClose:t,settings:d,onSettingsChange:o}=e,{language:m,setLanguage:x,t:u}=(0,r.o)(),{isEnabled:h,isConnected:g,setEnabled:p}=(0,c.l)(),[b,j]=(0,a.useState)("general");if(!s)return null;let v=(e,s)=>{o({[e]:s})},y=e=>{x(e),v("language",e)},f=[{id:"general",name:u("settings.general")||"General",icon:"⚙️"},{id:"audio",name:u("settings.audio")||"Audio",icon:"\uD83D\uDD0A"},{id:"graphics",name:u("settings.graphics")||"Graphics",icon:"\uD83C\uDFA8"},{id:"save",name:u("settings.save")||"Save & Data",icon:"\uD83D\uDCBE"},{id:"discord",name:u("settings.discord")||"Discord",icon:"\uD83C\uDFAE"}];return(0,i.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,i.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden",children:[(0,i.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,i.jsxs)("div",{className:"flex justify-between items-center",children:[(0,i.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:u("settings.title")||"⚙️ Settings"}),(0,i.jsx)(n.$,{variant:"secondary",onClick:t,children:u("game.close")||"✕ Close"})]})}),(0,i.jsx)("div",{className:"border-b border-gray-200",children:(0,i.jsx)("div",{className:"flex space-x-0",children:f.map(e=>(0,i.jsxs)("button",{onClick:()=>j(e.id),className:"px-4 py-3 font-medium text-sm border-b-2 transition-colors ".concat(b===e.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[e.icon," ",e.name]},e.id))})}),(0,i.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["general"===b&&(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:u("settings.language")||"\uD83C\uDF0D Language"}),(0,i.jsxs)("div",{className:"flex space-x-2",children:[(0,i.jsx)(n.$,{variant:"en"===m?"primary":"secondary",size:"sm",onClick:()=>y("en"),children:"\uD83C\uDDFA\uD83C\uDDF8 English"}),(0,i.jsx)(n.$,{variant:"cs"===m?"primary":"secondary",size:"sm",onClick:()=>y("cs"),children:"\uD83C\uDDE8\uD83C\uDDFF Čeština"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:u("settings.gameplay")||"\uD83C\uDFAE Gameplay"}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsxs)("label",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{children:u("settings.notifications")||"Enable Notifications"}),(0,i.jsx)("input",{type:"checkbox",checked:d.notificationsEnabled,onChange:e=>v("notificationsEnabled",e.target.checked),className:"rounded"})]}),(0,i.jsxs)("label",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{children:u("settings.tutorials")||"Show Tutorials"}),(0,i.jsx)("input",{type:"checkbox",checked:d.showTutorials,onChange:e=>v("showTutorials",e.target.checked),className:"rounded"})]}),(0,i.jsxs)("div",{children:[(0,i.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[u("settings.animationSpeed")||"Animation Speed",": ",d.animationSpeed,"x"]}),(0,i.jsx)("input",{type:"range",min:"0.5",max:"2",step:"0.1",value:d.animationSpeed,onChange:e=>v("animationSpeed",parseFloat(e.target.value)),className:"w-full"})]})]})]})]}),"audio"===b&&(0,i.jsx)("div",{className:"space-y-6",children:(0,i.jsxs)("div",{className:"space-y-4",children:[(0,i.jsxs)("label",{className:"flex items-center justify-between",children:[(0,i.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{children:"\uD83D\uDD0A"}),(0,i.jsx)("span",{children:u("settings.sound")||"Sound Effects"})]}),(0,i.jsx)("input",{type:"checkbox",checked:d.soundEnabled,onChange:e=>v("soundEnabled",e.target.checked),className:"rounded"})]}),(0,i.jsxs)("label",{className:"flex items-center justify-between",children:[(0,i.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,i.jsx)("span",{children:"\uD83C\uDFB5"}),(0,i.jsx)("span",{children:u("settings.music")||"Background Music"})]}),(0,i.jsx)("input",{type:"checkbox",checked:d.musicEnabled,onChange:e=>v("musicEnabled",e.target.checked),className:"rounded"})]})]})}),"graphics"===b&&(0,i.jsx)("div",{className:"space-y-6",children:(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:u("settings.quality")||"\uD83C\uDFA8 Graphics Quality"}),(0,i.jsx)("div",{className:"space-x-2",children:["low","medium","high"].map(e=>(0,i.jsx)(n.$,{variant:d.graphicsQuality===e?"primary":"secondary",size:"sm",onClick:()=>v("graphicsQuality",e),children:e.charAt(0).toUpperCase()+e.slice(1)},e))})]})}),"save"===b&&(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:u("settings.autoSave")||"\uD83D\uDCBE Auto-Save"}),(0,i.jsxs)("label",{className:"flex items-center justify-between",children:[(0,i.jsx)("span",{children:u("settings.enableAutoSave")||"Enable Auto-Save"}),(0,i.jsx)("input",{type:"checkbox",checked:d.autoSaveEnabled,onChange:e=>v("autoSaveEnabled",e.target.checked),className:"rounded"})]})]}),(0,i.jsxs)("div",{children:[(0,i.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:u("settings.dataManagement")||"\uD83D\uDCC1 Data Management"}),(0,i.jsxs)("div",{className:"space-y-3",children:[(0,i.jsx)(n.$,{variant:"secondary",onClick:()=>{let e={version:"1.0.0",timestamp:Date.now(),player:{},equipment:[],inventory:[],achievements:[],skills:[],automationSettings:{},gameSettings:d,bakeries:[],currentBakeryId:"main"},s=new Blob([l.B.exportSave(e)],{type:"application/json"}),t=URL.createObjectURL(s),i=document.createElement("a");i.href=t,i.download="bake-it-out-save-".concat(Date.now(),".json"),i.click(),URL.revokeObjectURL(t)},className:"w-full",children:u("settings.exportSave")||"\uD83D\uDCE4 Export Save"}),(0,i.jsxs)("div",{children:[(0,i.jsx)("input",{type:"file",accept:".json",onChange:e=>{var s;let t=null==(s=e.target.files)?void 0:s[0];if(!t)return;let i=new FileReader;i.onload=e=>{try{var s;let t=null==(s=e.target)?void 0:s.result,i=l.B.importSave(t);i?(console.log("Save imported successfully:",i),alert("Save imported successfully!")):alert("Failed to import save file")}catch(e){alert("Invalid save file")}},i.readAsText(t)},className:"hidden",id:"import-save"}),(0,i.jsx)(n.$,{variant:"secondary",onClick:()=>{var e;return null==(e=document.getElementById("import-save"))?void 0:e.click()},className:"w-full",children:u("settings.importSave")||"\uD83D\uDCE5 Import Save"})]})]})]}),(0,i.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,i.jsx)("h4",{className:"font-medium text-yellow-800 mb-2",children:u("settings.cloudSync")||"☁️ Cloud Sync"}),(0,i.jsx)("p",{className:"text-sm text-yellow-700 mb-3",children:u("settings.cloudSyncDescription")||"Cloud sync allows you to save your progress online and play across multiple devices."}),(0,i.jsx)(n.$,{variant:"secondary",size:"sm",disabled:!0,children:u("settings.comingSoon")||"Coming Soon"})]})]}),"discord"===b&&(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsxs)("h3",{className:"font-semibold text-gray-800 mb-3",children:["\uD83C\uDFAE ",u("settings.discordRichPresence")||"Discord Rich Presence"]}),(0,i.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:u("settings.discordDescription")||"Show your current game status and activity in Discord."}),(0,i.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("div",{className:"font-medium text-gray-800",children:u("settings.enableDiscordRPC")||"Enable Discord Rich Presence"}),(0,i.jsx)("div",{className:"text-sm text-gray-600",children:g?u("settings.discordConnected")||"✅ Connected to Discord":u("settings.discordDisconnected")||"❌ Not connected to Discord"})]}),(0,i.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,i.jsx)("input",{type:"checkbox",checked:h,onChange:e=>p(e.target.checked),className:"sr-only peer"}),(0,i.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]}),(0,i.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,i.jsxs)("h4",{className:"font-medium text-blue-800 mb-2",children:["ℹ️ ",u("settings.discordInfo")||"What is Discord Rich Presence?"]}),(0,i.jsxs)("div",{className:"text-sm text-blue-700 space-y-2",children:[(0,i.jsx)("p",{children:u("settings.discordInfoDesc1")||"Discord Rich Presence shows your friends what you're doing in Bake It Out:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside ml-4 space-y-1",children:[(0,i.jsx)("li",{children:u("settings.discordFeature1")||"Your current level and money"}),(0,i.jsx)("li",{children:u("settings.discordFeature2")||"What you're currently baking"}),(0,i.jsx)("li",{children:u("settings.discordFeature3")||"Multiplayer room information"}),(0,i.jsx)("li",{children:u("settings.discordFeature4")||"How long you've been playing"})]}),(0,i.jsx)("p",{className:"mt-2",children:u("settings.discordInfoDesc2")||"Your friends can even join your multiplayer games directly from Discord!"})]})]}),h&&!g&&(0,i.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,i.jsxs)("h4",{className:"font-medium text-yellow-800 mb-2",children:["⚠️ ",u("settings.discordTroubleshooting")||"Discord Not Connected"]}),(0,i.jsxs)("div",{className:"text-sm text-yellow-700 space-y-2",children:[(0,i.jsx)("p",{children:u("settings.discordTrouble1")||"Make sure Discord is running on your computer."}),(0,i.jsx)("p",{children:u("settings.discordTrouble2")||"Discord Rich Presence only works in the desktop version of the game."}),(0,i.jsx)("p",{children:u("settings.discordTrouble3")||"Try restarting both Discord and the game if the connection fails."})]})]}),(0,i.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,i.jsxs)("h4",{className:"font-medium text-green-800 mb-2",children:["\uD83D\uDD12 ",u("settings.discordPrivacy")||"Privacy Information"]}),(0,i.jsxs)("div",{className:"text-sm text-green-700 space-y-2",children:[(0,i.jsx)("p",{children:u("settings.discordPrivacyDesc1")||"Discord Rich Presence only shares:"}),(0,i.jsxs)("ul",{className:"list-disc list-inside ml-4 space-y-1",children:[(0,i.jsx)("li",{children:u("settings.discordPrivacy1")||"Your current game activity (public)"}),(0,i.jsx)("li",{children:u("settings.discordPrivacy2")||"Your player level and progress (public)"}),(0,i.jsx)("li",{children:u("settings.discordPrivacy3")||"Multiplayer room codes (for joining)"})]}),(0,i.jsx)("p",{className:"mt-2",children:u("settings.discordPrivacyDesc2")||"No personal information or save data is shared with Discord."})]})]})]})]})]})})}},3741:(e,s,t)=>{t.d(s,{$:()=>a});var i=t(5155);t(2115);let a=e=>{let{variant:s="primary",size:t="md",className:a="",children:n,...r}=e,c=["font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2",{primary:"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500",secondary:"bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500",danger:"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500",success:"bg-green-600 hover:bg-green-700 text-white focus:ring-green-500"}[s],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"}[t],a].join(" ");return(0,i.jsx)("button",{className:c,...r,children:n})}},9419:(e,s,t)=>{t.d(s,{$:()=>n});var i=t(5155),a=t(9283);function n(e){let{equipment:s,onClick:t}=e,{t:n}=(0,a.o)();return(0,i.jsx)("div",{className:"p-4 rounded-lg border-2 cursor-pointer transition-all ".concat(s.isActive?"border-green-400 bg-green-50":"border-gray-200 bg-gray-50 hover:border-orange-300 hover:bg-orange-50"),onClick:()=>!s.isActive&&t(s.id,s.name),children:(0,i.jsxs)("div",{className:"text-center",children:[(0,i.jsx)("div",{className:"text-3xl mb-2",children:(e=>{switch(e){case"oven":return"\uD83D\uDD25";case"mixer":return"\uD83E\uDD44";case"counter":return"\uD83C\uDF7D️";default:return"⚙️"}})(s.type)}),(0,i.jsx)("h3",{className:"font-medium text-gray-800",children:s.name}),(0,i.jsxs)("div",{className:"text-xs text-gray-500",children:["Level ",s.level]}),s.isActive&&s.timeRemaining?(0,i.jsxs)("div",{className:"mt-2",children:[(0,i.jsx)("div",{className:"text-sm text-green-600",children:n("kitchen.making",{recipe:s.currentRecipe||""})}),(0,i.jsx)("div",{className:"text-lg font-mono text-green-700",children:(e=>{let s=Math.floor(e/60);return"".concat(s,":").concat((e%60).toString().padStart(2,"0"))})(s.timeRemaining)}),(0,i.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,i.jsx)("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-1000",style:{width:"".concat(100-s.timeRemaining/60*100,"%")}})})]}):(0,i.jsx)("div",{className:"text-sm text-gray-500 mt-2",children:s.isActive?"Busy":n("kitchen.clickToUse")})]})})}}}]);