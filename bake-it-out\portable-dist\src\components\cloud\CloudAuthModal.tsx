'use client'

import { useState } from 'react'
import { useLanguage } from '@/contexts/LanguageContext'
import { useCloudSave } from '@/contexts/CloudSaveContext'
import { Button } from '@/components/ui/Button'

interface CloudAuthModalProps {
  isOpen: boolean
  onClose: () => void
  mode: 'login' | 'register'
  onModeChange: (mode: 'login' | 'register') => void
}

export function CloudAuthModal({ isOpen, onClose, mode, onModeChange }: CloudAuthModalProps) {
  const { t } = useLanguage()
  const { login, register, isLoading, error, clearError } = useCloudSave()
  
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  })
  const [formErrors, setFormErrors] = useState<Record<string, string>>({})

  if (!isOpen) return null

  const validateForm = () => {
    const errors: Record<string, string> = {}

    if (!formData.username.trim()) {
      errors.username = t('cloud.auth.username_required', 'Username is required')
    } else if (formData.username.length < 3) {
      errors.username = t('cloud.auth.username_too_short', 'Username must be at least 3 characters')
    }

    if (mode === 'register') {
      if (!formData.email.trim()) {
        errors.email = t('cloud.auth.email_required', 'Email is required')
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
        errors.email = t('cloud.auth.email_invalid', 'Please enter a valid email address')
      }

      if (formData.password !== formData.confirmPassword) {
        errors.confirmPassword = t('cloud.auth.passwords_dont_match', 'Passwords do not match')
      }
    }

    if (!formData.password) {
      errors.password = t('cloud.auth.password_required', 'Password is required')
    } else if (formData.password.length < 6) {
      errors.password = t('cloud.auth.password_too_short', 'Password must be at least 6 characters')
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    clearError()

    try {
      let success = false
      
      if (mode === 'login') {
        success = await login(formData.username, formData.password)
      } else {
        success = await register(formData.username, formData.email, formData.password)
      }

      if (success) {
        onClose()
        setFormData({ username: '', email: '', password: '', confirmPassword: '' })
        setFormErrors({})
      }
    } catch (error) {
      console.error('Auth error:', error)
    }
  }

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }))
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-800">
            {mode === 'login' 
              ? t('cloud.auth.login_title', 'Login to Cloud Save')
              : t('cloud.auth.register_title', 'Create Cloud Save Account')
            }
          </h2>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('cloud.auth.username', 'Username')}
            </label>
            <input
              type="text"
              value={formData.username}
              onChange={(e) => handleInputChange('username', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 ${
                formErrors.username ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder={t('cloud.auth.username_placeholder', 'Enter your username')}
              disabled={isLoading}
            />
            {formErrors.username && (
              <p className="text-red-500 text-sm mt-1">{formErrors.username}</p>
            )}
          </div>

          {mode === 'register' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('cloud.auth.email', 'Email')}
              </label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => handleInputChange('email', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 ${
                  formErrors.email ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder={t('cloud.auth.email_placeholder', 'Enter your email')}
                disabled={isLoading}
              />
              {formErrors.email && (
                <p className="text-red-500 text-sm mt-1">{formErrors.email}</p>
              )}
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('cloud.auth.password', 'Password')}
            </label>
            <input
              type="password"
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 ${
                formErrors.password ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder={t('cloud.auth.password_placeholder', 'Enter your password')}
              disabled={isLoading}
            />
            {formErrors.password && (
              <p className="text-red-500 text-sm mt-1">{formErrors.password}</p>
            )}
          </div>

          {mode === 'register' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('cloud.auth.confirm_password', 'Confirm Password')}
              </label>
              <input
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 ${
                  formErrors.confirmPassword ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder={t('cloud.auth.confirm_password_placeholder', 'Confirm your password')}
                disabled={isLoading}
              />
              {formErrors.confirmPassword && (
                <p className="text-red-500 text-sm mt-1">{formErrors.confirmPassword}</p>
              )}
            </div>
          )}

          <div className="flex space-x-3 pt-4">
            <Button
              type="submit"
              variant="primary"
              className="flex-1"
              disabled={isLoading}
            >
              {isLoading 
                ? t('cloud.auth.loading', 'Loading...')
                : mode === 'login' 
                  ? t('cloud.auth.login_button', 'Login')
                  : t('cloud.auth.register_button', 'Create Account')
              }
            </Button>
            <Button
              type="button"
              variant="secondary"
              onClick={onClose}
              disabled={isLoading}
            >
              {t('cloud.auth.cancel', 'Cancel')}
            </Button>
          </div>
        </form>

        <div className="mt-6 text-center">
          <p className="text-sm text-gray-600">
            {mode === 'login' 
              ? t('cloud.auth.no_account', "Don't have an account?")
              : t('cloud.auth.have_account', 'Already have an account?')
            }
            <button
              onClick={() => onModeChange(mode === 'login' ? 'register' : 'login')}
              className="ml-2 text-orange-600 hover:text-orange-800 font-medium"
              disabled={isLoading}
            >
              {mode === 'login' 
                ? t('cloud.auth.register_link', 'Create one')
                : t('cloud.auth.login_link', 'Login')
              }
            </button>
          </p>
        </div>
      </div>
    </div>
  )
}
