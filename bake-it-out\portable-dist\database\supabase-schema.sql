-- Supabase Cloud Save Schema for Bake It Out
-- Run this in your Supabase SQL editor to set up cloud saves

-- Enable Row Level Security
ALTER DATABASE postgres SET "app.jwt_secret" TO 'your-jwt-secret-here';

-- Create users table (extends Supabase auth.users)
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users(id) PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    display_name TEXT,
    avatar_url TEXT,
    preferred_language TEXT DEFAULT 'en',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create game_saves table
CREATE TABLE IF NOT EXISTS public.game_saves (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    save_name TEXT NOT NULL,
    description TEXT,
    save_type TEXT DEFAULT 'manual' CHECK (save_type IN ('manual', 'auto', 'checkpoint')),
    game_data JSONB NOT NULL,
    metadata JSONB DEFAULT '{}',
    version INTEGER DEFAULT 1,
    game_version TEXT DEFAULT '1.1.0',
    platform TEXT DEFAULT 'web',
    device_info TEXT,
    size_bytes INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT unique_user_save_name UNIQUE (user_id, save_name),
    CONSTRAINT max_saves_per_user CHECK (
        (SELECT COUNT(*) FROM game_saves WHERE user_id = game_saves.user_id) <= 50
    )
);

-- Create game_stats table for user statistics
CREATE TABLE IF NOT EXISTS public.game_stats (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
    total_play_time INTEGER DEFAULT 0,
    highest_level INTEGER DEFAULT 1,
    total_money INTEGER DEFAULT 0,
    achievements_unlocked INTEGER DEFAULT 0,
    games_played INTEGER DEFAULT 0,
    recipes_unlocked INTEGER DEFAULT 0,
    equipment_purchased INTEGER DEFAULT 0,
    orders_completed INTEGER DEFAULT 0,
    multiplayer_sessions INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create sync_logs table for debugging sync issues
CREATE TABLE IF NOT EXISTS public.sync_logs (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    save_id UUID REFERENCES game_saves(id) ON DELETE CASCADE,
    action TEXT NOT NULL CHECK (action IN ('upload', 'download', 'delete', 'conflict')),
    status TEXT NOT NULL CHECK (status IN ('success', 'error', 'pending')),
    error_message TEXT,
    device_info TEXT,
    ip_address INET,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_game_saves_user_id ON game_saves(user_id);
CREATE INDEX IF NOT EXISTS idx_game_saves_updated_at ON game_saves(updated_at DESC);
CREATE INDEX IF NOT EXISTS idx_game_saves_save_type ON game_saves(save_type);
CREATE INDEX IF NOT EXISTS idx_sync_logs_user_id ON sync_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_sync_logs_created_at ON sync_logs(created_at DESC);

-- Enable Row Level Security
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.game_saves ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.game_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sync_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for profiles
CREATE POLICY "Users can view own profile" ON public.profiles
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

-- Create RLS policies for game_saves
CREATE POLICY "Users can view own saves" ON public.game_saves
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own saves" ON public.game_saves
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own saves" ON public.game_saves
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete own saves" ON public.game_saves
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for game_stats
CREATE POLICY "Users can view own stats" ON public.game_stats
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own stats" ON public.game_stats
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own stats" ON public.game_stats
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create RLS policies for sync_logs
CREATE POLICY "Users can view own sync logs" ON public.sync_logs
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own sync logs" ON public.sync_logs
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Create functions for automatic timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic timestamps
CREATE TRIGGER update_profiles_updated_at BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_game_saves_updated_at BEFORE UPDATE ON public.game_saves
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_game_stats_updated_at BEFORE UPDATE ON public.game_stats
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function to automatically create profile on user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, username, display_name)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'username', NEW.email),
        COALESCE(NEW.raw_user_meta_data->>'display_name', NEW.email)
    );
    
    INSERT INTO public.game_stats (user_id)
    VALUES (NEW.id);
    
    RETURN NEW;
END;
$$ language 'plpgsql' SECURITY DEFINER;

-- Create trigger for new user signup
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Create function to update save size automatically
CREATE OR REPLACE FUNCTION update_save_size()
RETURNS TRIGGER AS $$
BEGIN
    NEW.size_bytes = LENGTH(NEW.game_data::text);
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for save size calculation
CREATE TRIGGER calculate_save_size BEFORE INSERT OR UPDATE ON public.game_saves
    FOR EACH ROW EXECUTE FUNCTION update_save_size();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Create view for save metadata (without game_data for performance)
CREATE OR REPLACE VIEW public.save_metadata AS
SELECT 
    id,
    user_id,
    save_name,
    description,
    save_type,
    metadata,
    version,
    game_version,
    platform,
    device_info,
    size_bytes,
    created_at,
    updated_at
FROM public.game_saves;

-- Grant access to the view
GRANT SELECT ON public.save_metadata TO anon, authenticated;
