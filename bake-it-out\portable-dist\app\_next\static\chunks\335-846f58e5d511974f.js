"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[335],{5335:(e,s,t)=>{t.r(s),t.d(s,{default:()=>$});var a=t(5155),i=t(2115),r=t(9283),l=t(2517),n=t(3741),c=t(9419),d=t(2163),o=t(4983);function m(e){let{isOpen:s,onClose:t}=e,{player:c,inventory:d}=(0,l.I)(),{t:m}=(0,r.o)(),[x,u]=(0,i.useState)("all");if(!s)return null;let h=(0,o.x0)(c.level),g="all"===x?h:h.filter(e=>e.category===x),p=e=>e.ingredients.every(e=>{let s=d.find(s=>s.name===e.name);return s&&s.quantity>=e.quantity}),v=[{id:"all",name:m("recipes.all"),icon:"\uD83C\uDF7D️"},{id:"cookies",name:m("recipes.cookies"),icon:"\uD83C\uDF6A"},{id:"cakes",name:m("recipes.cakes"),icon:"\uD83E\uDDC1"},{id:"bread",name:m("recipes.bread"),icon:"\uD83C\uDF5E"},{id:"pastries",name:m("recipes.pastries"),icon:"\uD83E\uDD50"}];return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:m("modal.recipes.title")}),(0,a.jsx)(n.$,{variant:"secondary",onClick:t,children:m("game.close")})]})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:v.map(e=>(0,a.jsxs)(n.$,{variant:x===e.id?"primary":"secondary",size:"sm",onClick:()=>u(e.id),children:[e.icon," ",e.name]},e.id))}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto",children:g.map(e=>{let s;return(0,a.jsxs)("div",{className:"p-4 rounded-lg border-2 ".concat(p(e)?"border-green-300 bg-green-50":"border-gray-300 bg-gray-50"),children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name}),(0,a.jsxs)("span",{className:"text-sm text-green-600",children:["$",e.basePrice]})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mb-2",children:[(s=e.difficulty,"⭐".repeat(s)+"☆".repeat(5-s))," • ⏱️ ",(e=>{let s=Math.floor(e/60);return"".concat(s,":").concat((e%60).toString().padStart(2,"0"))})(e.bakingTime)]}),(0,a.jsxs)("div",{className:"space-y-1 mb-3",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-700",children:m("recipes.ingredients")}),e.ingredients.map((e,s)=>{let t=d.find(s=>s.name===e.name),i=t&&t.quantity>=e.quantity;return(0,a.jsxs)("div",{className:"text-xs flex justify-between ".concat(i?"text-green-600":"text-red-600"),children:[(0,a.jsx)("span",{children:e.name}),(0,a.jsxs)("span",{children:[e.quantity,t&&(0,a.jsxs)("span",{className:"ml-1",children:["(",t.quantity," available)"]})]})]},s)})]}),(0,a.jsx)("div",{className:"text-xs text-gray-500",children:m("recipes.unlockLevel",{level:e.unlockLevel.toString()})}),p(e)&&(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)(n.$,{size:"sm",variant:"success",className:"w-full",children:m("recipes.canCraft")})})]},e.id)})}),0===g.length&&(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("div",{className:"text-4xl mb-2",children:"\uD83D\uDCDD"}),(0,a.jsx)("p",{children:m("recipes.noRecipes")}),(0,a.jsx)("p",{className:"text-sm",children:m("recipes.levelUpToUnlock")})]})]})]})})}function x(e){let{isOpen:s,onClose:t}=e,{player:c,inventory:d,spendMoney:o,addIngredient:m}=(0,l.I)(),{t:x}=(0,r.o)(),[u,h]=(0,i.useState)({});if(!s)return null;let g=(e,s)=>{h(t=>({...t,[e]:Math.max(0,s)}))},p=(e,s)=>s*(u[e]||1),v=(e,s)=>c.money>=p(e,s);return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:x("modal.shop.title")}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,a.jsx)("span",{className:"text-green-800 font-medium",children:x("ui.money",{amount:c.money.toString()})})}),(0,a.jsx)(n.$,{variant:"secondary",onClick:t,children:x("game.close")})]})]})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"space-y-4 max-h-[60vh] overflow-y-auto",children:d.map(e=>{let s=u[e.name]||1,t=p(e.name,e.cost),i=v(e.name,e.cost);return(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"text-2xl",children:e.icon}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-800",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:x("shop.currentStock",{quantity:e.quantity.toString()})}),(0,a.jsx)("p",{className:"text-sm text-green-600",children:x("inventory.cost",{cost:e.cost.toString()})})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n.$,{size:"sm",variant:"secondary",onClick:()=>g(e.name,s-1),disabled:s<=1,children:"-"}),(0,a.jsx)("span",{className:"w-12 text-center font-mono",children:s}),(0,a.jsx)(n.$,{size:"sm",variant:"secondary",onClick:()=>g(e.name,s+1),disabled:!v(e.name,e.cost)&&s>=1,children:"+"})]}),(0,a.jsxs)("div",{className:"text-right min-w-[80px]",children:[(0,a.jsxs)("div",{className:"font-medium ".concat(i?"text-green-600":"text-red-600"),children:["$",t]}),(0,a.jsx)(n.$,{size:"sm",variant:i?"success":"secondary",onClick:()=>((e,s)=>{let t=u[e]||1;o(s*t)&&(m(e,t),h(s=>({...s,[e]:0})))})(e.name,e.cost),disabled:!i,className:"mt-1",children:i?x("shop.buy"):x("shop.tooExpensive")})]})]})]},e.name)})}),(0,a.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:x("shop.tips.title")}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,a.jsx)("li",{children:x("shop.tips.bulk")}),(0,a.jsx)("li",{children:x("shop.tips.stock")}),(0,a.jsx)("li",{children:x("shop.tips.rare")}),(0,a.jsx)("li",{children:x("shop.tips.prices")})]})]})]})]})})}function u(e){let{isOpen:s,onClose:t,equipmentId:r,equipmentName:c}=e,{player:d,inventory:m,updateEquipment:x,useIngredient:u}=(0,l.I)(),[h,g]=(0,i.useState)(null);if(!s)return null;let p=(0,o.x0)(d.level).filter(e=>(0,o.hF)(e,m)),v=e=>{let s=Math.floor(e/60);return"".concat(s,":").concat((e%60).toString().padStart(2,"0"))},b=e=>"⭐".repeat(e)+"☆".repeat(5-e);return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-orange-800",children:["\uD83D\uDD25 ",c," - Select Recipe"]}),(0,a.jsx)(n.$,{variant:"secondary",onClick:t,children:"✕ Close"})]})}),(0,a.jsxs)("div",{className:"p-6",children:[0===p.length?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDE14"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-2",children:"No recipes available"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have enough ingredients to craft any recipes."}),(0,a.jsx)(n.$,{variant:"primary",onClick:t,children:"Buy Ingredients"})]}):(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[60vh] overflow-y-auto",children:p.map(e=>(0,a.jsxs)("div",{className:"p-4 rounded-lg border-2 cursor-pointer transition-all ".concat((null==h?void 0:h.id)===e.id?"border-orange-400 bg-orange-50":"border-gray-300 bg-gray-50 hover:border-orange-300"),onClick:()=>g(e),children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:(e=>{switch(e){case"cookies":return"\uD83C\uDF6A";case"cakes":return"\uD83E\uDDC1";case"bread":return"\uD83C\uDF5E";case"pastries":return"\uD83E\uDD50";default:return"\uD83C\uDF7D️"}})(e.category)}),(0,a.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name})]}),(0,a.jsxs)("span",{className:"text-sm text-green-600",children:["$",e.basePrice]})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500 mb-2",children:[b(e.difficulty)," • ⏱️ ",v(e.bakingTime)]}),(0,a.jsxs)("div",{className:"space-y-1 mb-3",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-700",children:"Ingredients:"}),e.ingredients.map((e,s)=>{let t=m.find(s=>s.name===e.name);return(0,a.jsxs)("div",{className:"text-xs flex justify-between text-green-600",children:[(0,a.jsx)("span",{children:e.name}),(0,a.jsxs)("span",{children:[e.quantity,(0,a.jsxs)("span",{className:"ml-1",children:["(",(null==t?void 0:t.quantity)||0," available)"]})]})]},s)})]}),(null==h?void 0:h.id)===e.id&&(0,a.jsx)(n.$,{variant:"success",size:"sm",className:"w-full",onClick:()=>{var s;return s=e,void((0,o.hF)(s,m)&&s.ingredients.every(e=>{let s=m.find(s=>s.name===e.name);return s&&s.quantity>=e.quantity})&&(s.ingredients.forEach(e=>{u(e.name,e.quantity)}),x(r,{isActive:!0,timeRemaining:s.bakingTime,currentRecipe:s.name}),t()))},children:"\uD83D\uDD25 Start Baking"})]},e.id))}),h&&p.length>0&&(0,a.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,a.jsxs)("h3",{className:"font-medium text-blue-800 mb-2",children:["\uD83D\uDCCB Baking Instructions for ",h.name]}),(0,a.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,a.jsxs)("p",{children:["• Baking time: ",v(h.bakingTime)]}),(0,a.jsxs)("p",{children:["• Difficulty: ",b(h.difficulty)]}),(0,a.jsxs)("p",{children:["• Expected reward: $",h.basePrice]}),(0,a.jsx)("p",{children:"• Make sure you have all ingredients before starting!"})]})]})]})]})})}function h(e){let{notifications:s,onRemove:t}=e;return((0,i.useEffect)(()=>{s.forEach(e=>{if(e.duration){let s=setTimeout(()=>{t(e.id)},e.duration);return()=>clearTimeout(s)}})},[s,t]),0===s.length)?null:(0,a.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2 max-w-sm",children:s.map(e=>(0,a.jsx)("div",{className:"p-4 rounded-lg border-l-4 shadow-lg transition-all duration-300 ".concat((e=>{switch(e){case"success":return"bg-green-100 border-green-400 text-green-800";case"error":return"bg-red-100 border-red-400 text-red-800";case"warning":return"bg-yellow-100 border-yellow-400 text-yellow-800";case"info":return"bg-blue-100 border-blue-400 text-blue-800";default:return"bg-gray-100 border-gray-400 text-gray-800"}})(e.type)),children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,a.jsx)("span",{className:"text-lg",children:(e=>{switch(e){case"success":return"✅";case"error":return"❌";case"warning":return"⚠️";case"info":return"ℹ️";default:return"\uD83D\uDCE2"}})(e.type)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:e.title}),(0,a.jsx)("p",{className:"text-sm opacity-90",children:e.message})]})]}),(0,a.jsx)("button",{onClick:()=>t(e.id),className:"text-lg opacity-60 hover:opacity-100 transition-opacity",children:"\xd7"})]})},e.id))})}function g(e){let{isOpen:s,onClose:t,newLevel:i,rewards:r}=e;return s?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full overflow-hidden",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-yellow-400 to-orange-500 p-6 text-center",children:[(0,a.jsx)("div",{className:"text-6xl mb-2",children:"\uD83C\uDF89"}),(0,a.jsx)("h2",{className:"text-3xl font-bold text-white mb-2",children:"Level Up!"}),(0,a.jsxs)("p",{className:"text-xl text-yellow-100",children:["You reached Level ",i,"!"]})]}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"\uD83C\uDF81 Level Rewards"}),(0,a.jsx)("div",{className:"space-y-3 mb-6",children:r.map((e,s)=>(0,a.jsx)("div",{className:"p-3 rounded-lg border ".concat((e=>{switch(e){case"recipe":return"bg-blue-50 border-blue-300 text-blue-800";case"equipment":return"bg-purple-50 border-purple-300 text-purple-800";case"money":return"bg-green-50 border-green-300 text-green-800";case"skill_point":return"bg-yellow-50 border-yellow-300 text-yellow-800";case"achievement":return"bg-orange-50 border-orange-300 text-orange-800";default:return"bg-gray-50 border-gray-300 text-gray-800"}})(e.type)),children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-2xl",children:(e=>{switch(e){case"recipe":return"\uD83D\uDCD6";case"equipment":return"⚙️";case"money":return"\uD83D\uDCB0";case"skill_point":return"⭐";case"achievement":return"\uD83C\uDFC6";default:return"\uD83C\uDF81"}})(e.type)}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-sm opacity-80",children:e.description}),e.value&&(0,a.jsx)("p",{className:"text-sm font-semibold",children:"money"===e.type?"$".concat(e.value):"+".concat(e.value)})]})]})},s))}),(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg mb-6",children:[(0,a.jsx)("h4",{className:"font-medium text-blue-800 mb-2",children:"\uD83D\uDCA1 What's Next?"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,a.jsx)("li",{children:"• Check out new recipes in your recipe book"}),(0,a.jsx)("li",{children:"• Visit the shop for new equipment"}),(0,a.jsx)("li",{children:"• Take on more challenging orders"}),(0,a.jsx)("li",{children:"• Invest in skill upgrades"})]})]}),(0,a.jsx)(n.$,{variant:"primary",size:"lg",className:"w-full",onClick:t,children:"\uD83D\uDE80 Continue Playing"})]})]})}):null}function p(e){let{isOpen:s,onClose:t,achievements:r}=e,[l,c]=(0,i.useState)("all");if(!s)return null;let d="all"===l?r:r.filter(e=>e.category===l),o=r.filter(e=>e.completed).length,m=r.length;return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:"\uD83C\uDFC6 Achievements"}),(0,a.jsxs)("p",{className:"text-gray-600",children:[o," of ",m," achievements completed"]})]}),(0,a.jsx)(n.$,{variant:"secondary",onClick:t,children:"✕ Close"})]})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Overall Progress"}),(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:[Math.round(o/m*100),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-yellow-400 to-orange-500 h-3 rounded-full transition-all duration-500",style:{width:"".concat(o/m*100,"%")}})})]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:[{id:"all",name:"All",icon:"\uD83C\uDFC6"},{id:"baking",name:"Baking",icon:"\uD83D\uDC68‍\uD83C\uDF73"},{id:"business",name:"Business",icon:"\uD83D\uDCBC"},{id:"efficiency",name:"Efficiency",icon:"⚡"},{id:"collection",name:"Collection",icon:"\uD83D\uDCDA"},{id:"special",name:"Special",icon:"⭐"}].map(e=>(0,a.jsxs)(n.$,{variant:l===e.id?"primary":"secondary",size:"sm",onClick:()=>c(e.id),children:[e.icon," ",e.name]},e.id))}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[50vh] overflow-y-auto",children:d.map(e=>{let s=e.completed?100:Math.min(...e.requirements.map(e=>e.current?Math.min(100,e.current/e.target*100):0)),t=e.completed,i=e.unlocked;return(0,a.jsx)("div",{className:"p-4 rounded-lg border-2 ".concat(t?"border-green-400 bg-green-50":i?"border-gray-300 bg-white":"border-gray-200 bg-gray-50 opacity-60"),children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)("div",{className:"text-3xl ".concat(t?"grayscale-0":"grayscale"),children:e.icon}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsxs)("h3",{className:"font-semibold ".concat(t?"text-green-800":"text-gray-800"),children:[e.name,t&&(0,a.jsx)("span",{className:"ml-2",children:"✅"})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:e.description}),i&&!t&&(0,a.jsxs)("div",{className:"mb-2",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,a.jsx)("span",{className:"text-xs text-gray-500",children:"Progress"}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:(e=>{if(e.completed)return"Completed!";let s=e.requirements[0];return"".concat(s.current||0," / ").concat(s.target)})(e)})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(s,"%")}})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Reward:"}),(0,a.jsx)("span",{className:"text-lg",children:(e=>{switch(e){case"recipe":return"\uD83D\uDCD6";case"equipment":return"⚙️";case"money":return"\uD83D\uDCB0";case"skill_point":return"⭐";default:return"\uD83C\uDF81"}})(e.reward.type)}),(0,a.jsx)("span",{className:"text-gray-700",children:e.reward.name}),e.reward.value&&(0,a.jsx)("span",{className:"text-green-600 font-medium",children:"money"===e.reward.type?"$".concat(e.reward.value):"+".concat(e.reward.value)})]})]})]})},e.id)})}),0===d.length&&(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFC6"}),(0,a.jsx)("p",{children:"No achievements in this category."})]})]})]})})}function v(e){let{isOpen:s,onClose:t,skills:r,skillPoints:l,playerLevel:c,onUpgradeSkill:d}=e,[o,m]=(0,i.useState)("all");if(!s)return null;let x="all"===o?r:r.filter(e=>e.category===o),u=e=>!(e.level>=e.maxLevel)&&!(l<e.cost)&&(!e.requirements.playerLevel||!(c<e.requirements.playerLevel))&&(!e.requirements.skills||e.requirements.skills.every(e=>{let s=r.find(s=>s.id===e);return s&&s.level>0}));return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:"\uD83C\uDF1F Skill Tree"}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Available Skill Points: ",(0,a.jsx)("span",{className:"font-semibold text-blue-600",children:l})]})]}),(0,a.jsx)(n.$,{variant:"secondary",onClick:t,children:"✕ Close"})]})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:[{id:"all",name:"All",icon:"\uD83C\uDF1F"},{id:"efficiency",name:"Efficiency",icon:"⚡"},{id:"automation",name:"Automation",icon:"\uD83E\uDD16"},{id:"quality",name:"Quality",icon:"\uD83D\uDC8E"},{id:"business",name:"Business",icon:"\uD83D\uDCBC"}].map(e=>(0,a.jsxs)(n.$,{variant:o===e.id?"primary":"secondary",size:"sm",onClick:()=>m(e.id),children:[e.icon," ",e.name]},e.id))}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto",children:x.map(e=>{let s=e.level>=e.maxLevel?"maxed":u(e)?"available":"locked",t=u(e);return(0,a.jsxs)("div",{className:"p-4 rounded-lg border-2 ".concat((e=>{switch(e){case"maxed":return"border-green-400 bg-green-50";case"available":return"border-blue-400 bg-blue-50";default:return"border-gray-300 bg-gray-50"}})(s)),children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:e.icon}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name}),(0,a.jsx)("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:e.category})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-sm font-medium text-gray-700",children:["Level ",e.level,"/",e.maxLevel]}),"maxed"!==s&&(0,a.jsxs)("div",{className:"text-xs text-blue-600",children:["Cost: ",e.cost," SP"]})]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,a.jsxs)("div",{className:"mb-3",children:[(0,a.jsx)("h4",{className:"text-xs font-medium text-gray-700 mb-1",children:"Effects:"}),e.effects.map((e,s)=>(0,a.jsxs)("div",{className:"text-xs text-green-600",children:["• ",(e=>{let s=Math.round(100*e.value);switch(e.type){case"baking_speed":return"+".concat(s,"% baking speed");case"money_multiplier":return"+".concat(s,"% money earned");case"xp_multiplier":return"+".concat(s,"% experience gained");case"ingredient_efficiency":return"".concat(s,"% less ingredients used");case"automation_unlock":return"Unlock automation features";default:return"+".concat(s,"% bonus")}})(e)]},s))]}),e.requirements.playerLevel&&c<e.requirements.playerLevel&&(0,a.jsx)("div",{className:"mb-3",children:(0,a.jsxs)("div",{className:"text-xs text-red-600",children:["Requires Level ",e.requirements.playerLevel]})}),e.requirements.skills&&(0,a.jsx)("div",{className:"mb-3",children:(0,a.jsxs)("div",{className:"text-xs text-gray-600",children:["Requires: ",e.requirements.skills.map(e=>{let s=r.find(s=>s.id===e);return null==s?void 0:s.name}).join(", ")]})}),(0,a.jsx)("div",{className:"mb-3",children:(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(e.level/e.maxLevel*100,"%")}})})}),"maxed"===s?(0,a.jsx)(n.$,{variant:"success",size:"sm",className:"w-full",disabled:!0,children:"✅ Maxed"}):t?(0,a.jsxs)(n.$,{variant:"primary",size:"sm",className:"w-full",onClick:()=>d(e.id),children:["⬆️ Upgrade (",e.cost," SP)"]}):(0,a.jsx)(n.$,{variant:"secondary",size:"sm",className:"w-full",disabled:!0,children:"\uD83D\uDD12 Locked"})]},e.id)})}),0===x.length&&(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDF1F"}),(0,a.jsx)("p",{children:"No skills in this category."})]}),(0,a.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"\uD83D\uDCA1 Skill Tips"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,a.jsx)("li",{children:"• Earn skill points by leveling up (1 point every 2 levels)"}),(0,a.jsx)("li",{children:"• Some skills require other skills to be unlocked first"}),(0,a.jsx)("li",{children:"• Focus on skills that match your playstyle"}),(0,a.jsx)("li",{children:"• Efficiency skills help with resource management"})]})]})]})]})})}var b=t(7871);function j(e){var s;let{isOpen:t,onClose:r}=e,{player:c,equipment:d,automationSettings:o,updateAutomationSettings:m,purchaseAutomationUpgrade:x}=(0,l.I)(),[u,h]=(0,i.useState)("settings");if(!t)return null;let g=d.filter(e=>e.automationLevel>0),p=b.sA.filter(e=>{var s;return c.level>=e.unlockLevel&&!(null==(s=c.automationUpgrades)?void 0:s.includes(e.id))}),v=(e,s)=>{m({[e]:s})};return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:"\uD83E\uDD16 Automation Control"}),(0,a.jsx)(n.$,{variant:"secondary",onClick:r,children:"✕ Close"})]})}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("div",{className:"flex space-x-0",children:[{id:"settings",name:"Settings",icon:"⚙️"},{id:"upgrades",name:"Upgrades",icon:"\uD83D\uDD27"},{id:"status",name:"Status",icon:"\uD83D\uDCCA"}].map(e=>(0,a.jsxs)("button",{onClick:()=>h(e.id),className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat(u===e.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[e.icon," ",e.name]},e.id))})}),(0,a.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["settings"===u&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"\uD83C\uDF9B️ Master Control"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:(null==o?void 0:o.enabled)||!1,onChange:e=>v("enabled",e.target.checked),className:"rounded"}),(0,a.jsx)("span",{className:"text-sm",children:"Enable Automation"})]}),(0,a.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",checked:(null==o?void 0:o.autoStart)||!1,onChange:e=>v("autoStart",e.target.checked),className:"rounded"}),(0,a.jsx)("span",{className:"text-sm",children:"Auto-start Equipment"})]})]})]}),(0,a.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-semibold text-green-800 mb-3",children:"\uD83C\uDFAF Priority Mode"}),(0,a.jsxs)("select",{value:(null==o?void 0:o.priorityMode)||"efficiency",onChange:e=>v("priorityMode",e.target.value),className:"w-full p-2 border rounded-lg",children:[(0,a.jsx)("option",{value:"efficiency",children:"Efficiency (Orders First)"}),(0,a.jsx)("option",{value:"profit",children:"Profit (Highest Value)"}),(0,a.jsx)("option",{value:"speed",children:"Speed (Fastest Recipes)"})]}),(0,a.jsx)("p",{className:"text-xs text-green-600 mt-1",children:"How automation chooses what to bake"})]}),(0,a.jsxs)("div",{className:"bg-purple-50 p-4 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-semibold text-purple-800 mb-3",children:"⚡ Performance"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"block text-sm",children:["Max Concurrent Jobs: ",(null==o?void 0:o.maxConcurrentJobs)||2]}),(0,a.jsx)("input",{type:"range",min:"1",max:"5",value:(null==o?void 0:o.maxConcurrentJobs)||2,onChange:e=>v("maxConcurrentJobs",parseInt(e.target.value)),className:"w-full"})]})]}),(0,a.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-semibold text-yellow-800 mb-3",children:"\uD83D\uDEE1️ Safety"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"block text-sm",children:["Stop when ingredients below: ",(null==o?void 0:o.ingredientThreshold)||5]}),(0,a.jsx)("input",{type:"range",min:"0",max:"20",value:(null==o?void 0:o.ingredientThreshold)||5,onChange:e=>v("ingredientThreshold",parseInt(e.target.value)),className:"w-full"})]})]})]})}),"upgrades"===u&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg mb-4",children:[(0,a.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"\uD83D\uDCA1 Automation Upgrades"}),(0,a.jsx)("p",{className:"text-sm text-blue-700",children:"Improve your automation efficiency, speed, and intelligence with these upgrades."})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:p.map(e=>(0,a.jsxs)("div",{className:"p-4 border rounded-lg bg-white hover:bg-gray-50 transition-colors",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,a.jsx)("h4",{className:"font-semibold text-gray-800",children:e.name}),(0,a.jsxs)("span",{className:"text-sm text-green-600 font-medium",children:["$",e.cost]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-xs text-gray-500 uppercase tracking-wide",children:e.type}),(0,a.jsx)(n.$,{size:"sm",variant:c.money>=e.cost?"primary":"secondary",disabled:c.money<e.cost,onClick:()=>x(e.id),children:c.money>=e.cost?"Purchase":"Too Expensive"})]})]},e.id))}),0===p.length&&(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("div",{className:"text-4xl mb-2",children:"\uD83D\uDD27"}),(0,a.jsx)("p",{children:"No upgrades available at your current level."}),(0,a.jsx)("p",{className:"text-sm",children:"Level up to unlock more automation upgrades!"})]})]}),"status"===u&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"text-2xl text-green-600 mb-1",children:g.length}),(0,a.jsx)("div",{className:"text-sm text-green-800",children:"Automated Equipment"})]}),(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"text-2xl text-blue-600 mb-1",children:(null==o?void 0:o.enabled)?"✅":"❌"}),(0,a.jsx)("div",{className:"text-sm text-blue-800",children:"Automation Status"})]}),(0,a.jsxs)("div",{className:"bg-purple-50 p-4 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"text-2xl text-purple-600 mb-1",children:(null==(s=c.automationUpgrades)?void 0:s.length)||0}),(0,a.jsx)("div",{className:"text-sm text-purple-800",children:"Active Upgrades"})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800",children:"\uD83C\uDFED Equipment Status"}),g.length>0?(0,a.jsx)("div",{className:"space-y-2",children:g.map(e=>(0,a.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-medium",children:e.name}),(0,a.jsxs)("span",{className:"text-sm text-gray-500 ml-2",children:["Level ",e.automationLevel," • ",e.efficiency,"x efficiency"]})]}),(0,a.jsx)("div",{className:"px-2 py-1 rounded text-xs ".concat(e.isActive?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"),children:e.isActive?"Running":"Idle"})]},e.id))}):(0,a.jsxs)("div",{className:"text-center py-4 text-gray-500",children:[(0,a.jsx)("p",{children:"No automated equipment available."}),(0,a.jsx)("p",{className:"text-sm",children:"Purchase auto-equipment from the shop to get started!"})]})]})]})]})]})})}let y=[{id:"professional_oven",name:"Professional Oven",type:"oven",description:"Faster and more efficient than basic oven",cost:500,unlockLevel:3,automationLevel:0,efficiency:1.3,icon:"\uD83D\uDD25",category:"basic"},{id:"auto_oven",name:"Automated Oven",type:"auto_oven",description:"Fully automated oven that can run without supervision",cost:1500,unlockLevel:5,automationLevel:2,efficiency:1.5,icon:"\uD83E\uDD16",category:"automated"},{id:"industrial_mixer",name:"Industrial Mixer",type:"mixer",description:"High-capacity mixer for large batches",cost:750,unlockLevel:4,automationLevel:0,efficiency:1.4,icon:"\uD83E\uDD44",category:"basic"},{id:"auto_mixer",name:"Automated Mixer",type:"auto_mixer",description:"Self-operating mixer with ingredient dispensing",cost:2e3,unlockLevel:6,automationLevel:2,efficiency:1.6,icon:"\uD83E\uDD16",category:"automated"},{id:"conveyor_belt_basic",name:"Basic Conveyor Belt",type:"conveyor",description:"Moves items between equipment automatically",cost:1e3,unlockLevel:7,automationLevel:1,efficiency:1.2,icon:"\uD83D\uDD04",category:"automated"},{id:"smart_conveyor",name:"Smart Conveyor System",type:"conveyor",description:"Intelligent conveyor with sorting and routing",cost:3e3,unlockLevel:10,automationLevel:3,efficiency:1.8,icon:"\uD83E\uDDE0",category:"advanced"},{id:"master_oven",name:"Master Oven",type:"oven",description:"The ultimate baking machine with AI assistance",cost:5e3,unlockLevel:12,automationLevel:3,efficiency:2,icon:"\uD83D\uDC51",category:"advanced"}];function f(e){let{isOpen:s,onClose:t,onShowSuccess:r}=e,{player:c,equipment:d,spendMoney:o,addEquipment:m}=(0,l.I)(),[x,u]=(0,i.useState)("all");if(!s)return null;let h=y.filter(e=>c.level>=e.unlockLevel&&("all"===x||e.category===x));return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:"\uD83C\uDFEA Equipment Shop"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Upgrade your bakery with professional equipment"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,a.jsxs)("span",{className:"text-green-800 font-medium",children:["$",c.money]})}),(0,a.jsx)(n.$,{variant:"secondary",onClick:t,children:"✕ Close"})]})]})}),(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:[{id:"all",name:"All",icon:"\uD83C\uDFEA"},{id:"basic",name:"Basic",icon:"\uD83D\uDD27"},{id:"automated",name:"Automated",icon:"\uD83E\uDD16"},{id:"advanced",name:"Advanced",icon:"⚡"}].map(e=>(0,a.jsxs)(n.$,{variant:x===e.id?"primary":"secondary",size:"sm",onClick:()=>u(e.id),children:[e.icon," ",e.name]},e.id))}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto",children:h.map(e=>{var s;return(0,a.jsxs)("div",{className:"p-4 rounded-lg border-2 ".concat((e=>{switch(e){case"basic":return"border-gray-300 bg-gray-50";case"automated":return"border-blue-300 bg-blue-50";case"advanced":return"border-purple-300 bg-purple-50";default:return"border-gray-300 bg-white"}})(e.category)),children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:e.icon}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name}),(0,a.jsx)("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:e.category})]})]}),(0,a.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["$",e.cost]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,a.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Efficiency:"}),(0,a.jsxs)("span",{className:"font-medium",children:[e.efficiency,"x"]})]}),e.automationLevel>0&&(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Automation:"}),0===(s=e.automationLevel)?null:(0,a.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:["\uD83E\uDD16 Auto Level ",s]})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Unlock Level:"}),(0,a.jsx)("span",{className:"font-medium",children:e.unlockLevel})]})]}),(0,a.jsx)(n.$,{variant:c.money>=e.cost?"success":"secondary",size:"sm",className:"w-full",disabled:c.money<e.cost,onClick:()=>{!(c.money<e.cost)&&o(e.cost)&&(m({name:e.name,type:e.type,isActive:!1,level:1,efficiency:e.efficiency,automationLevel:e.automationLevel}),r&&r("Equipment Purchased!","You bought ".concat(e.name,"!")))},children:c.money>=e.cost?"\uD83D\uDCB0 Purchase":"\uD83D\uDCB8 Too Expensive"})]},e.id)})}),0===h.length&&(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFEA"}),(0,a.jsx)("p",{children:"No equipment available in this category."}),(0,a.jsx)("p",{className:"text-sm",children:"Level up to unlock more equipment!"})]}),(0,a.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"\uD83D\uDCA1 Equipment Tips"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,a.jsx)("li",{children:"• Automated equipment can run without your supervision"}),(0,a.jsx)("li",{children:"• Higher efficiency means faster production and better quality"}),(0,a.jsx)("li",{children:"• Conveyor belts connect equipment for seamless workflow"}),(0,a.jsx)("li",{children:"• Advanced equipment unlocks at higher levels"})]})]})]})]})})}var N=t(2785);let w=[{name:"Downtown Delights",location:"City Center",specialization:"general",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:0},{name:"Cookie Corner",location:"Shopping Mall",specialization:"cookies",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:2500},{name:"Cake Castle",location:"Wedding District",specialization:"cakes",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:3500},{name:"Bread Basket",location:"Farmers Market",specialization:"bread",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:3e3},{name:"Pastry Palace",location:"French Quarter",specialization:"pastries",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:4e3}];function k(e){let{isOpen:s,onClose:t,bakeries:l,currentBakeryId:c,onSwitchBakery:d,onPurchaseBakery:o,playerMoney:m}=e,{t:x}=(0,r.o)(),[u,h]=(0,i.useState)("owned");if(!s)return null;let g=l.filter(e=>e.unlocked),p=w.filter(e=>!l.some(s=>s.name===e.name&&s.unlocked)),v=e=>{switch(e){case"cookies":return"\uD83C\uDF6A";case"cakes":return"\uD83E\uDDC1";case"bread":return"\uD83C\uDF5E";case"pastries":return"\uD83E\uDD50";default:return"\uD83C\uDFEA"}},b=e=>{switch(e){case"cookies":return"+20% Cookie Production Speed";case"cakes":return"+25% Cake Profit Margin";case"bread":return"+15% Bread Ingredient Efficiency";case"pastries":return"+30% Pastry Experience Gain";default:return"Balanced Production"}},j=[{id:"owned",name:x("bakeries.owned")||"My Bakeries",icon:"\uD83C\uDFEA"},{id:"available",name:x("bakeries.available")||"Available",icon:"\uD83D\uDED2"}];return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:x("bakeries.title")||"\uD83C\uDFEA Bakery Manager"}),(0,a.jsx)("p",{className:"text-gray-600",children:x("bakeries.subtitle")||"Manage your bakery empire"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,a.jsxs)("span",{className:"text-green-800 font-medium",children:["$",m]})}),(0,a.jsx)(n.$,{variant:"secondary",onClick:t,children:x("game.close")||"✕ Close"})]})]})}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("div",{className:"flex space-x-0",children:j.map(e=>(0,a.jsxs)("button",{onClick:()=>h(e.id),className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat(u===e.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[e.icon," ",e.name]},e.id))})}),(0,a.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["owned"===u&&(0,a.jsx)("div",{className:"space-y-4",children:g.length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:g.map(e=>(0,a.jsxs)("div",{className:"p-4 rounded-lg border-2 cursor-pointer transition-all ".concat(e.id===c?"border-orange-400 bg-orange-50":"border-gray-300 bg-white hover:border-orange-300"),onClick:()=>d(e.id),children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:v(e.specialization)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.location})]})]}),e.id===c&&(0,a.jsx)("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full",children:x("bakeries.current")||"Current"})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:[x("bakeries.level")||"Level",":"]}),(0,a.jsx)("span",{className:"font-medium",children:e.level})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:[x("bakeries.specialization")||"Specialization",":"]}),(0,a.jsx)("span",{className:"font-medium capitalize",children:e.specialization})]}),(0,a.jsx)("div",{className:"text-xs text-blue-600",children:b(e.specialization)})]}),(0,a.jsxs)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:[x("bakeries.equipment")||"Equipment",":"]}),(0,a.jsx)("span",{children:e.equipment.length})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:[x("bakeries.orders")||"Active Orders",":"]}),(0,a.jsx)("span",{children:e.orders.length})]})]}),e.id!==c&&(0,a.jsx)(n.$,{variant:"primary",size:"sm",className:"w-full mt-3",onClick:s=>{s.stopPropagation(),d(e.id)},children:x("bakeries.switchTo")||"Switch To"})]},e.id))}):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFEA"}),(0,a.jsx)("p",{children:x("bakeries.noOwned")||"You don't own any bakeries yet."})]})}),"available"===u&&(0,a.jsx)("div",{className:"space-y-4",children:p.length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:p.map((e,s)=>(0,a.jsxs)("div",{className:"p-4 rounded-lg border-2 border-gray-300 bg-white",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-2xl",children:v(e.specialization)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.location})]})]}),(0,a.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["$",e.purchaseCost]})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm mb-4",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:[x("bakeries.specialization")||"Specialization",":"]}),(0,a.jsx)("span",{className:"font-medium capitalize",children:e.specialization})]}),(0,a.jsx)("div",{className:"text-xs text-blue-600",children:b(e.specialization)})]}),(0,a.jsx)(n.$,{variant:m>=e.purchaseCost?"success":"secondary",size:"sm",className:"w-full",disabled:m<e.purchaseCost,onClick:()=>{m>=e.purchaseCost&&o({...e,id:"bakery_".concat(Date.now()),unlocked:!0})},children:m>=e.purchaseCost?x("bakeries.purchase")||"\uD83D\uDCB0 Purchase":x("bakeries.tooExpensive")||"\uD83D\uDCB8 Too Expensive"})]},s))}):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDF89"}),(0,a.jsx)("p",{children:x("bakeries.allOwned")||"You own all available bakeries!"})]})})]}),(0,a.jsxs)("div",{className:"p-4 bg-blue-50 border-t border-gray-200",children:[(0,a.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:x("bakeries.tips")||"\uD83D\uDCA1 Bakery Tips"}),(0,a.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,a.jsxs)("li",{children:["• ",x("bakeries.tip1")||"Each bakery specializes in different products for bonus efficiency"]}),(0,a.jsxs)("li",{children:["• ",x("bakeries.tip2")||"Switch between bakeries to manage multiple locations"]}),(0,a.jsxs)("li",{children:["• ",x("bakeries.tip3")||"Specialized bakeries attract customers looking for specific items"]}),(0,a.jsxs)("li",{children:["• ",x("bakeries.tip4")||"Upgrade each bakery independently for maximum profit"]})]})]})]})})}class D{async initializeSaveDirectory(){if(this.isElectron&&window.electronAPI)try{this.saveDirectory=await window.electronAPI.getSaveDirectory(),await this.ensureSaveDirectoryExists()}catch(e){console.error("Failed to initialize save directory:",e),this.isElectron=!1}}async ensureSaveDirectoryExists(){if(this.isElectron&&window.electronAPI)try{await window.electronAPI.ensureDirectory(this.saveDirectory)}catch(e){console.error("Failed to create save directory:",e)}}async saveToFile(e,s){if(!this.isElectron||!window.electronAPI)return this.saveToLocalStorage(e,s);try{let t=s||"save_".concat(Date.now(),".json"),a="".concat(this.saveDirectory,"/").concat(t),i=JSON.stringify(e,null,2);return await window.electronAPI.writeFile(a,i),console.log("Game saved to file: ".concat(a)),!0}catch(e){return console.error("Failed to save to file:",e),!1}}async loadFromFile(e){if(!this.isElectron||!window.electronAPI)return this.loadFromLocalStorage(e);try{let s="".concat(this.saveDirectory,"/").concat(e),t=await window.electronAPI.readFile(s);if(!t)return null;let a=JSON.parse(t);return console.log("Game loaded from file: ".concat(s)),a}catch(e){return console.error("Failed to load from file:",e),null}}async getSaveFiles(){if(!this.isElectron||!window.electronAPI)return this.getLocalStorageSaves();try{let e=await window.electronAPI.listFiles(this.saveDirectory,".json"),s=[];for(let t of e)try{let e="".concat(this.saveDirectory,"/").concat(t.name),a=await window.electronAPI.readFile(e),i=JSON.parse(a);s.push({fileName:t.name,displayName:i.player.name||t.name.replace(".json",""),timestamp:i.timestamp,playerLevel:i.player.level,money:i.player.money,playTime:i.player.playTime||0,version:i.version,fileSize:t.size||0})}catch(e){console.error("Failed to read save file ".concat(t.name,":"),e)}return s.sort((e,s)=>s.timestamp-e.timestamp)}catch(e){return console.error("Failed to get save files:",e),[]}}async deleteSaveFile(e){if(!this.isElectron||!window.electronAPI)return this.deleteFromLocalStorage(e);try{let s="".concat(this.saveDirectory,"/").concat(e);return await window.electronAPI.deleteFile(s),console.log("Save file deleted: ".concat(s)),!0}catch(e){return console.error("Failed to delete save file:",e),!1}}async exportSave(e,s){if(!this.isElectron||!window.electronAPI)return this.exportToBrowser(e,s);try{let t=s||"bake-it-out-save-".concat(Date.now(),".json"),a=await window.electronAPI.showSaveDialog(t);if(!a)return!1;let i=JSON.stringify(e,null,2);return await window.electronAPI.writeFile(a,i),console.log("Save exported to: ".concat(a)),!0}catch(e){return console.error("Failed to export save:",e),!1}}async importSave(){if(!this.isElectron||!window.electronAPI)return this.importFromBrowser();try{let e=await window.electronAPI.showOpenDialog([".json"]);if(!e)return null;let s=await window.electronAPI.readFile(e),t=JSON.parse(s);return console.log("Save imported from: ".concat(e)),t}catch(e){return console.error("Failed to import save:",e),null}}async createBackup(e){if(!this.isElectron||!window.electronAPI)return!1;try{let s="".concat(this.saveDirectory,"/").concat(e),t="".concat(this.saveDirectory,"/backups/").concat(e,".backup.").concat(Date.now());return await window.electronAPI.ensureDirectory("".concat(this.saveDirectory,"/backups")),await window.electronAPI.copyFile(s,t),console.log("Backup created: ".concat(t)),!0}catch(e){return console.error("Failed to create backup:",e),!1}}saveToLocalStorage(e,s){try{let t=s||"save_".concat(Date.now());return localStorage.setItem("bakeItOut_file_".concat(t),JSON.stringify(e)),!0}catch(e){return console.error("Failed to save to localStorage:",e),!1}}loadFromLocalStorage(e){try{let s=localStorage.getItem("bakeItOut_file_".concat(e));return s?JSON.parse(s):null}catch(e){return console.error("Failed to load from localStorage:",e),null}}getLocalStorageSaves(){let e=[];for(let s=0;s<localStorage.length;s++){let t=localStorage.key(s);if(null==t?void 0:t.startsWith("bakeItOut_file_"))try{let s=localStorage.getItem(t);if(s){let a=JSON.parse(s),i=t.replace("bakeItOut_file_","");e.push({fileName:i,displayName:a.player.name||i,timestamp:a.timestamp,playerLevel:a.player.level,money:a.player.money,playTime:a.player.playTime||0,version:a.version,fileSize:s.length})}}catch(e){console.error("Failed to parse save ".concat(t,":"),e)}}return e.sort((e,s)=>s.timestamp-e.timestamp)}deleteFromLocalStorage(e){try{return localStorage.removeItem("bakeItOut_file_".concat(e)),!0}catch(e){return console.error("Failed to delete from localStorage:",e),!1}}exportToBrowser(e,s){try{let t=JSON.stringify(e,null,2),a=new Blob([t],{type:"application/json"}),i=URL.createObjectURL(a),r=document.createElement("a");return r.href=i,r.download=s||"bake-it-out-save-".concat(Date.now(),".json"),r.click(),URL.revokeObjectURL(i),!0}catch(e){return console.error("Failed to export to browser:",e),!1}}importFromBrowser(){return new Promise(e=>{let s=document.createElement("input");s.type="file",s.accept=".json",s.onchange=s=>{var t;let a=null==(t=s.target.files)?void 0:t[0];if(!a)return void e(null);let i=new FileReader;i.onload=s=>{try{var t;let a=null==(t=s.target)?void 0:t.result,i=JSON.parse(a);e(i)}catch(s){console.error("Failed to parse imported file:",s),e(null)}},i.readAsText(a)},s.click()})}constructor(){this.saveDirectory="",this.isElectron=!1,this.isElectron=void 0!==window.electronAPI,this.initializeSaveDirectory()}}let C=new D;function S(e){var s;let{isOpen:t,onClose:c,mode:d,onSaveSuccess:o,onLoadSuccess:m}=e,{t:x}=(0,r.o)(),{player:u,saveGameState:h,loadGameState:g}=(0,l.I)(),[p,v]=(0,i.useState)([]),[b,j]=(0,i.useState)([]),[y,f]=(0,i.useState)(null),[N,w]=(0,i.useState)(null),[k,D]=(0,i.useState)(""),[S,E]=(0,i.useState)(!1),[L,A]=(0,i.useState)(!1),[F,z]=(0,i.useState)(!1);(0,i.useEffect)(()=>{t&&(q(),O(),z(void 0!==window.electronAPI))},[t]);let O=async()=>{try{let e=await C.getSaveFiles();j(e)}catch(e){console.error("Failed to load file saves:",e)}},q=()=>{let e=[];for(let a=1;a<=8;a++){let i="bakeItOut_save_slot_".concat(a),r=localStorage.getItem(i);if(r)try{var s,t;let i=JSON.parse(r);e.push({id:a,name:i.player.name||"Save ".concat(a),timestamp:i.timestamp,playerLevel:i.player.level,money:i.player.money,bakeryName:(null==(t=i.bakeries)||null==(s=t[0])?void 0:s.name)||"Main Bakery",playTime:i.player.playTime||0,isEmpty:!1,data:i})}catch(s){console.error("Failed to load save slot ".concat(a,":"),s),e.push($(a))}else e.push($(a))}v(e)},$=e=>({id:e,name:"Empty Slot ".concat(e),timestamp:0,playerLevel:0,money:0,bakeryName:"",playTime:0,isEmpty:!0}),P=async e=>{if(!y)return;let s=p.find(s=>s.id===e);if(!(null==s?void 0:s.isEmpty)&&!L)return void A(!0);E(!0);try{await h(e,k||"Save ".concat(e))&&(null==o||o(),q(),A(!1),D(""))}catch(e){console.error("Save failed:",e)}finally{E(!1)}},I=async e=>{let s=p.find(s=>s.id===e);if(s&&!s.isEmpty){E(!0);try{await g(e)&&(null==m||m(),c())}catch(e){console.error("Load failed:",e)}finally{E(!1)}}};return t?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-500 p-6 text-white",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold",children:"save"===d?"\uD83D\uDCBE Save Game":"\uD83D\uDCC1 Load Game"}),(0,a.jsx)("p",{className:"text-blue-100 text-sm",children:"save"===d?x("saveLoad.saveDesc","Choose a slot to save your progress"):x("saveLoad.loadDesc","Select a save file to load")})]}),(0,a.jsx)(n.$,{variant:"secondary",size:"sm",className:"bg-white/20 hover:bg-white/30 text-white border-white/30",onClick:c,children:"✕"})]})}),F&&(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex",children:[(0,a.jsx)("button",{className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat(F&&null===y?"border-transparent text-gray-500 hover:text-gray-700":"border-blue-500 text-blue-600 bg-blue-50"),onClick:()=>{f(1),w(null)},children:"\uD83D\uDCC1 Save Slots"}),(0,a.jsx)("button",{className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat(null!==N?"border-blue-500 text-blue-600 bg-blue-50":"border-transparent text-gray-500 hover:text-gray-700"),onClick:()=>{f(null),w("")},children:"\uD83D\uDCBE File System"})]})}),(0,a.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[70vh]",children:["save"===d&&y&&(0,a.jsxs)("div",{className:"mb-6 p-4 bg-blue-50 rounded-lg",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:x("saveLoad.saveName","Save Name")}),(0,a.jsx)("input",{type:"text",value:k,onChange:e=>D(e.target.value),placeholder:"Save ".concat(y),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:p.map(e=>{var s;return(0,a.jsxs)("div",{className:"border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 ".concat(y===e.id?"border-blue-500 bg-blue-50":e.isEmpty?"border-gray-200 bg-gray-50 hover:border-gray-300":"border-gray-300 bg-white hover:border-blue-300 hover:shadow-md"),onClick:()=>f(e.id),children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-orange-400 to-yellow-400 rounded-lg flex items-center justify-center text-white font-bold text-lg",children:e.id}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800",children:e.isEmpty?"Slot ".concat(e.id):e.name}),!e.isEmpty&&(0,a.jsx)("p",{className:"text-sm text-gray-500",children:(s=e.timestamp)?new Date(s).toLocaleString():""})]})]}),!e.isEmpty&&(0,a.jsx)(n.$,{variant:"secondary",size:"sm",className:"text-red-600 hover:bg-red-50",onClick:s=>{var t;s.stopPropagation(),t=e.id,localStorage.removeItem("bakeItOut_save_slot_".concat(t)),q()},children:"\uD83D\uDDD1️"})]}),e.isEmpty?(0,a.jsxs)("div",{className:"text-center py-8 text-gray-400",children:[(0,a.jsx)("div",{className:"text-3xl mb-2",children:"\uD83D\uDCC4"}),(0,a.jsx)("p",{className:"text-sm",children:x("saveLoad.emptySlot","Empty Slot")})]}):(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Level:"}),(0,a.jsx)("span",{className:"ml-2 font-medium",children:e.playerLevel})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Money:"}),(0,a.jsxs)("span",{className:"ml-2 font-medium",children:["$",e.money]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Bakery:"}),(0,a.jsx)("span",{className:"ml-2 font-medium",children:e.bakeryName})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-500",children:"Play Time:"}),(0,a.jsx)("span",{className:"ml-2 font-medium",children:(e=>{let s=Math.floor(e/3600),t=Math.floor(e%3600/60);return"".concat(s,"h ").concat(t,"m")})(e.playTime)})]})]})})]},e.id)})})]}),(0,a.jsxs)("div",{className:"bg-gray-50 px-6 py-4 flex justify-between items-center",children:[(0,a.jsx)("div",{className:"text-sm text-gray-500",children:y&&(0,a.jsx)("span",{children:"save"===d?x("saveLoad.selectedSaveSlot","Selected: Slot ".concat(y)):x("saveLoad.selectedLoadSlot","Selected: Slot ".concat(y))})}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(n.$,{variant:"secondary",onClick:c,children:x("common.cancel","Cancel")}),"save"===d?(0,a.jsx)(n.$,{variant:"primary",onClick:()=>y&&P(y),disabled:!y||S,className:"bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600",children:S?"\uD83D\uDCBE Saving...":"\uD83D\uDCBE Save Game"}):(0,a.jsx)(n.$,{variant:"primary",onClick:()=>y&&I(y),disabled:!y||S||(null==(s=p.find(e=>e.id===y))?void 0:s.isEmpty),className:"bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600",children:S?"\uD83D\uDCC1 Loading...":"\uD83D\uDCC1 Load Game"})]})]}),L&&(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md mx-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:x("saveLoad.confirmOverwrite","Overwrite Save?")}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:x("saveLoad.overwriteWarning","This will overwrite the existing save. This action cannot be undone.")}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(n.$,{variant:"secondary",onClick:()=>A(!1),children:x("common.cancel","Cancel")}),(0,a.jsx)(n.$,{variant:"primary",className:"bg-red-500 hover:bg-red-600",onClick:()=>y&&P(y),children:x("saveLoad.overwrite","Overwrite")})]})]})})]})}):null}function E(e){let{equipment:s,onEquipmentClick:t}=e,{t:n}=(0,r.o)(),{orders:c,player:d}=(0,l.I)(),[o,m]=(0,i.useState)([]),[x,u]=(0,i.useState)("kitchen");(0,i.useEffect)(()=>{m(c.map((e,s)=>({id:e.id,name:e.customerName,avatar:h(e.customerName),position:g(s),order:e.items[0],satisfaction:100-20*(e.timeLimit<180),waitTime:300-e.timeLimit,status:"pending"===e.status?"waiting":"in_progress"===e.status?"ordering":"eating"})))},[c]);let h=e=>{let s=["\uD83D\uDC68‍\uD83D\uDCBC","\uD83D\uDC69‍\uD83D\uDCBC","\uD83D\uDC68‍\uD83C\uDF93","\uD83D\uDC69‍\uD83C\uDF93","\uD83D\uDC68‍\uD83C\uDF73","\uD83D\uDC69‍\uD83C\uDF73","\uD83D\uDC68‍⚕️","\uD83D\uDC69‍⚕️","\uD83D\uDC68‍\uD83C\uDFA8","\uD83D\uDC69‍\uD83C\uDFA8"];return s[e.length%s.length]},g=e=>{let s=[{x:20,y:20},{x:60,y:20},{x:100,y:20},{x:20,y:60},{x:60,y:60},{x:100,y:60},{x:20,y:100},{x:60,y:100},{x:100,y:100}];return s[e%s.length]},p=e=>{switch(e){case"oven":return"\uD83D\uDD25";case"mixer":return"\uD83E\uDD44";case"counter":return"\uD83C\uDFEA";case"auto_oven":return"\uD83E\uDD16";case"auto_mixer":return"⚙️";case"conveyor":return"\uD83D\uDD04";default:return"\uD83D\uDCE6"}},v=e=>e.isActive?"bg-green-100 border-green-400":e.automationLevel>0?"bg-blue-100 border-blue-400":"bg-gray-100 border-gray-300";return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-orange-800",children:["\uD83C\uDFEA ",n("bakery.layout.title","Bakery Layout")]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>u("kitchen"),className:"px-4 py-2 rounded-lg font-medium transition-colors ".concat("kitchen"===x?"bg-orange-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:["\uD83D\uDC68‍\uD83C\uDF73 ",n("bakery.kitchen","Kitchen")]}),(0,a.jsxs)("button",{onClick:()=>u("dining"),className:"px-4 py-2 rounded-lg font-medium transition-colors ".concat("dining"===x?"bg-orange-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:["\uD83C\uDF7D️ ",n("bakery.dining","Dining Area")]}),(0,a.jsxs)("button",{onClick:()=>u("counter"),className:"px-4 py-2 rounded-lg font-medium transition-colors ".concat("counter"===x?"bg-orange-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:["\uD83D\uDED2 ",n("bakery.counter","Service Counter")]})]})]}),(0,a.jsxs)("div",{className:"relative bg-gradient-to-br from-yellow-50 to-orange-50 rounded-lg border-2 border-orange-200 min-h-[500px] overflow-hidden",children:["kitchen"===x&&(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-6",children:[(0,a.jsxs)("div",{className:"bg-red-100 rounded-lg p-4 border-2 border-red-300",children:[(0,a.jsxs)("h3",{className:"font-semibold text-red-800 mb-3",children:["\uD83D\uDD25 ",n("bakery.baking.area","Baking Area")]}),(0,a.jsx)("div",{className:"space-y-2",children:s.filter(e=>e.type.includes("oven")).map(e=>(0,a.jsxs)("div",{onClick:()=>t(e.id,e.name),className:"p-3 rounded-lg border-2 cursor-pointer transition-all hover:scale-105 ".concat(v(e)),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-xl",children:p(e.type)}),(0,a.jsx)("span",{className:"font-medium text-sm",children:e.name})]}),e.isActive&&(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"})]}),e.currentRecipe&&(0,a.jsxs)("div",{className:"text-xs text-gray-600 mt-1",children:["Making: ",e.currentRecipe]})]},e.id))})]}),(0,a.jsxs)("div",{className:"bg-blue-100 rounded-lg p-4 border-2 border-blue-300",children:[(0,a.jsxs)("h3",{className:"font-semibold text-blue-800 mb-3",children:["\uD83E\uDD44 ",n("bakery.prep.area","Prep Area")]}),(0,a.jsx)("div",{className:"space-y-2",children:s.filter(e=>e.type.includes("mixer")).map(e=>(0,a.jsx)("div",{onClick:()=>t(e.id,e.name),className:"p-3 rounded-lg border-2 cursor-pointer transition-all hover:scale-105 ".concat(v(e)),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-xl",children:p(e.type)}),(0,a.jsx)("span",{className:"font-medium text-sm",children:e.name})]}),e.isActive&&(0,a.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"})]})},e.id))})]}),(0,a.jsxs)("div",{className:"bg-purple-100 rounded-lg p-4 border-2 border-purple-300",children:[(0,a.jsxs)("h3",{className:"font-semibold text-purple-800 mb-3",children:["⚙️ ",n("bakery.automation.area","Automation")]}),(0,a.jsx)("div",{className:"space-y-2",children:s.filter(e=>e.type.includes("conveyor")||e.automationLevel>0).map(e=>(0,a.jsx)("div",{onClick:()=>t(e.id,e.name),className:"p-3 rounded-lg border-2 cursor-pointer transition-all hover:scale-105 ".concat(v(e)),children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-xl",children:p(e.type)}),(0,a.jsx)("span",{className:"font-medium text-sm",children:e.name})]}),(0,a.jsxs)("div",{className:"text-xs bg-purple-200 px-2 py-1 rounded",children:["Auto Lv.",e.automationLevel]})]})},e.id))})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 border border-gray-300",children:[(0,a.jsxs)("h4",{className:"font-semibold text-gray-800 mb-2",children:["\uD83D\uDCCA ",n("bakery.kitchen.stats","Kitchen Stats")]}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-green-600",children:s.filter(e=>e.isActive).length}),(0,a.jsx)("div",{className:"text-gray-600",children:n("bakery.active.equipment","Active Equipment")})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-blue-600",children:s.filter(e=>e.automationLevel>0).length}),(0,a.jsx)("div",{className:"text-gray-600",children:n("bakery.automated.equipment","Automated Equipment")})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-purple-600",children:[Math.round(s.reduce((e,s)=>e+s.efficiency,0)/s.length*100),"%"]}),(0,a.jsx)("div",{className:"text-gray-600",children:n("bakery.efficiency","Efficiency")})]})]})]})]}),"dining"===x&&(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("div",{className:"grid grid-cols-4 gap-4 mb-6",children:[1,2,3,4,5,6,7,8].map(e=>{let s=o[e-1];return(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 border-2 border-brown-300 relative",style:{backgroundColor:"#8B4513",color:"white"},children:[(0,a.jsxs)("div",{className:"text-center mb-2",children:[(0,a.jsx)("div",{className:"text-2xl",children:"\uD83E\uDE91"}),(0,a.jsxs)("div",{className:"text-xs",children:["Table ",e]})]}),s?(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl mb-1",children:s.avatar}),(0,a.jsx)("div",{className:"text-xs font-medium",children:s.name}),(0,a.jsx)("div",{className:"text-xs opacity-75",children:s.order}),(0,a.jsx)("div",{className:"mt-2",children:(0,a.jsx)("div",{className:"w-full h-1 rounded ".concat(s.satisfaction>70?"bg-green-400":s.satisfaction>40?"bg-yellow-400":"bg-red-400"),style:{width:"".concat(s.satisfaction,"%")}})})]}):(0,a.jsx)("div",{className:"text-center text-gray-400",children:(0,a.jsx)("div",{className:"text-xs",children:"Empty"})})]},e)})}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 border border-gray-300",children:[(0,a.jsxs)("h4",{className:"font-semibold text-gray-800 mb-2",children:["\uD83C\uDF7D️ ",n("bakery.dining.stats","Dining Stats")]}),(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-4 text-sm",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-green-600",children:o.length}),(0,a.jsx)("div",{className:"text-gray-600",children:n("bakery.current.customers","Current Customers")})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-blue-600",children:o.filter(e=>"waiting"===e.status).length}),(0,a.jsx)("div",{className:"text-gray-600",children:n("bakery.waiting.customers","Waiting")})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-bold text-purple-600",children:o.filter(e=>"eating"===e.status).length}),(0,a.jsx)("div",{className:"text-gray-600",children:n("bakery.eating.customers","Eating")})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("div",{className:"text-lg font-bold text-orange-600",children:[o.length>0?Math.round(o.reduce((e,s)=>e+s.satisfaction,0)/o.length):100,"%"]}),(0,a.jsx)("div",{className:"text-gray-600",children:n("bakery.avg.satisfaction","Avg Satisfaction")})]})]})]})]}),"counter"===x&&(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"bg-gradient-to-r from-yellow-100 to-orange-100 rounded-lg p-6 border-2 border-orange-300",children:[(0,a.jsxs)("h3",{className:"text-xl font-bold text-orange-800 mb-4 text-center",children:["\uD83D\uDED2 ",n("bakery.service.counter","Service Counter")]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 border-2 border-gray-300 mb-4",children:[(0,a.jsxs)("h4",{className:"font-semibold text-gray-800 mb-3",children:["\uD83E\uDDC1 ",n("bakery.display.case","Display Case")]}),(0,a.jsx)("div",{className:"grid grid-cols-4 gap-3",children:["\uD83C\uDF6A","\uD83E\uDDC1","\uD83E\uDD50","\uD83C\uDF5E","\uD83E\uDD67","\uD83C\uDF70","\uD83E\uDD68","\uD83E\uDDC7"].map((e,s)=>(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3 text-center border",children:[(0,a.jsx)("div",{className:"text-2xl mb-1",children:e}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:"Fresh"})]},s))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg p-4 border-2 border-gray-300",children:[(0,a.jsxs)("h4",{className:"font-semibold text-gray-800 mb-3",children:["\uD83D\uDCCB ",n("bakery.order.queue","Order Queue")]}),(0,a.jsx)("div",{className:"space-y-2",children:c.slice(0,3).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-lg",children:h(e.customerName)}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-sm",children:e.customerName}),(0,a.jsx)("div",{className:"text-xs text-gray-600",children:e.items[0]})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-sm font-bold text-green-600",children:["$",e.reward]}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:[Math.floor(e.timeLimit/60),"m left"]})]})]},e.id))})]})]})})]}),(0,a.jsx)("div",{className:"mt-4 bg-gray-100 rounded-lg p-3",children:(0,a.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsxs)("span",{children:["\uD83D\uDCB0 $",d.money]}),(0,a.jsxs)("span",{children:["⭐ Level ",d.level]}),(0,a.jsxs)("span",{children:["\uD83D\uDCE6 ",c.length," Orders"]})]}),(0,a.jsxs)("div",{className:"text-gray-600",children:[n("bakery.last.updated","Last updated"),": ",new Date().toLocaleTimeString()]})]})})]})}function L(e){let{isOpen:s,onClose:t}=e,{t:n}=(0,r.o)(),{orders:c,player:d,completeOrder:o}=(0,l.I)(),[m,x]=(0,i.useState)([]),[u,h]=(0,i.useState)(null);(0,i.useEffect)(()=>{x(c.map((e,s)=>{let t=e.timeLimit,a=Math.max(0,Math.min(100,t/300*100));return{id:e.id,name:e.customerName,avatar:g(e.customerName),orderItems:e.items,patience:t,maxPatience:300,satisfaction:a,status:"pending"===e.status?"waiting":"in_progress"===e.status?"ordering":"served",tableNumber:s+1,orderValue:e.reward,preferences:p(e.customerName),mood:v(a)}}))},[c]);let g=e=>{let s=["\uD83D\uDC68‍\uD83D\uDCBC","\uD83D\uDC69‍\uD83D\uDCBC","\uD83D\uDC68‍\uD83C\uDF93","\uD83D\uDC69‍\uD83C\uDF93","\uD83D\uDC68‍\uD83C\uDF73","\uD83D\uDC69‍\uD83C\uDF73","\uD83D\uDC68‍⚕️","\uD83D\uDC69‍⚕️","\uD83D\uDC68‍\uD83C\uDFA8","\uD83D\uDC69‍\uD83C\uDFA8","\uD83D\uDC68‍\uD83D\uDCBB","\uD83D\uDC69‍\uD83D\uDCBB","\uD83D\uDC68‍\uD83D\uDD2C","\uD83D\uDC69‍\uD83D\uDD2C","\uD83D\uDC68‍\uD83C\uDFEB","\uD83D\uDC69‍\uD83C\uDFEB","\uD83D\uDC68‍\uD83C\uDFA4","\uD83D\uDC69‍\uD83C\uDFA4"];return s[e.length%s.length]},p=e=>["sweet","savory","healthy","indulgent","traditional","exotic"].slice(0,2+e.split("").reduce((e,s)=>e+s.charCodeAt(0),0)%3),v=e=>e>80?"happy":e>50?"neutral":e>20?"impatient":"angry",b=e=>{switch(e){case"happy":return"\uD83D\uDE0A";case"neutral":return"\uD83D\uDE10";case"impatient":return"\uD83D\uDE24";case"angry":return"\uD83D\uDE20"}},j=e=>{switch(e){case"happy":return"text-green-600 bg-green-100";case"neutral":return"text-yellow-600 bg-yellow-100";case"impatient":return"text-orange-600 bg-orange-100";case"angry":return"text-red-600 bg-red-100"}},y=e=>{switch(e){case"entering":return"\uD83D\uDEB6";case"waiting":return"⏰";case"ordering":return"\uD83D\uDCDD";case"served":return"\uD83C\uDF7D️";case"eating":return"\uD83D\uDE0B";case"leaving":return"\uD83D\uDC4B"}};return s?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-500 p-6 text-white",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold",children:["\uD83D\uDC65 ",n("customers.manager.title","Customer Manager")]}),(0,a.jsx)("p",{className:"text-blue-100 text-sm",children:n("customers.manager.subtitle","Monitor and serve your customers")})]}),(0,a.jsxs)("button",{onClick:t,className:"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors",children:["✕ ",n("common.close","Close")]})]})}),(0,a.jsxs)("div",{className:"flex h-[70vh]",children:[(0,a.jsxs)("div",{className:"w-1/2 p-6 border-r border-gray-200 overflow-y-auto",children:[(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:["\uD83D\uDCCB ",n("customers.current.list","Current Customers")," (",m.length,")"]}),(0,a.jsxs)("div",{className:"space-y-3",children:[m.map(e=>(0,a.jsxs)("div",{onClick:()=>h(e),className:"p-4 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md ".concat((null==u?void 0:u.id)===e.id?"border-blue-400 bg-blue-50":"border-gray-300 bg-white hover:border-gray-400"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-2xl",children:e.avatar}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[n("customers.table","Table")," ",e.tableNumber]})]})]}),(0,a.jsx)("div",{className:"text-right",children:(0,a.jsxs)("div",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat(j(e.mood)),children:[b(e.mood)," ",e.mood]})})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-lg",children:y(e.status)}),(0,a.jsx)("span",{className:"text-sm text-gray-600 capitalize",children:e.status})]}),(0,a.jsxs)("div",{className:"text-sm font-bold text-green-600",children:["$",e.orderValue]})]}),(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mb-1",children:[(0,a.jsx)("span",{children:n("customers.patience","Patience")}),(0,a.jsxs)("span",{children:[Math.round(e.satisfaction),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat(e.satisfaction>60?"bg-green-500":e.satisfaction>30?"bg-yellow-500":"bg-red-500"),style:{width:"".concat(e.satisfaction,"%")}})})]})]},e.id)),0===m.length&&(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFEA"}),(0,a.jsx)("p",{children:n("customers.no.customers","No customers currently")}),(0,a.jsx)("p",{className:"text-sm",children:n("customers.waiting.for.orders","Waiting for new orders...")})]})]})]}),(0,a.jsx)("div",{className:"w-1/2 p-6 overflow-y-auto",children:u?(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("div",{className:"text-6xl mb-2",children:u.avatar}),(0,a.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:u.name}),(0,a.jsxs)("p",{className:"text-gray-600",children:[n("customers.table","Table")," ",u.tableNumber]})]}),(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:[(0,a.jsxs)("h4",{className:"font-semibold text-gray-800 mb-3",children:["\uD83D\uDCDD ",n("customers.order.details","Order Details")]}),(0,a.jsx)("div",{className:"space-y-2",children:u.orderItems.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-2 bg-white rounded border",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{children:"\uD83E\uDDC1"}),(0,a.jsx)("span",{className:"font-medium",children:e})]}),(0,a.jsxs)("span",{className:"text-green-600 font-bold",children:["$",Math.round(u.orderValue/u.orderItems.length)]})]},s))}),(0,a.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"flex justify-between font-bold",children:[(0,a.jsxs)("span",{children:[n("customers.total","Total"),":"]}),(0,a.jsxs)("span",{className:"text-green-600",children:["$",u.orderValue]})]})})]}),(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 mb-4",children:[(0,a.jsxs)("h4",{className:"font-semibold text-blue-800 mb-3",children:["ℹ️ ",n("customers.info","Customer Info")]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{children:[n("customers.mood","Mood"),":"]}),(0,a.jsxs)("span",{className:"font-medium ".concat(j(u.mood)),children:[b(u.mood)," ",u.mood]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{children:[n("customers.status","Status"),":"]}),(0,a.jsxs)("span",{className:"font-medium capitalize",children:[y(u.status)," ",u.status]})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsxs)("span",{children:[n("customers.patience","Patience"),":"]}),(0,a.jsxs)("span",{className:"font-medium",children:[Math.round(u.satisfaction),"%"]})]})]})]}),(0,a.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4 mb-4",children:[(0,a.jsxs)("h4",{className:"font-semibold text-purple-800 mb-3",children:["❤️ ",n("customers.preferences","Preferences")]}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:u.preferences.map((e,s)=>(0,a.jsx)("span",{className:"px-3 py-1 bg-purple-200 text-purple-800 rounded-full text-sm font-medium",children:e},s))})]}),"waiting"===u.status&&(0,a.jsx)("div",{className:"space-y-2",children:(0,a.jsxs)("button",{onClick:()=>{o(u.id),x(e=>e.map(e=>e.id===u.id?{...e,status:"served"}:e))},className:"w-full bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-4 rounded-lg transition-colors",children:["\uD83C\uDF7D️ ",n("customers.serve.order","Serve Order")]})})]}):(0,a.jsxs)("div",{className:"text-center py-12 text-gray-500",children:[(0,a.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDC46"}),(0,a.jsx)("p",{className:"text-lg",children:n("customers.select.customer","Select a customer")}),(0,a.jsx)("p",{className:"text-sm",children:n("customers.select.to.view.details","Select a customer to view details")})]})})]})]})}):null}function A(e){let{onCustomerClick:s}=e,{t}=(0,r.o)(),{orders:n}=(0,l.I)(),[c,d]=(0,i.useState)([]),[o,m]=(0,i.useState)(!0);(0,i.useEffect)(()=>{d([{id:1,position:{x:20,y:20},seats:2,isOccupied:!1},{id:2,position:{x:60,y:20},seats:4,isOccupied:!1},{id:3,position:{x:100,y:20},seats:2,isOccupied:!1},{id:4,position:{x:20,y:60},seats:2,isOccupied:!1},{id:5,position:{x:60,y:60},seats:6,isOccupied:!1},{id:6,position:{x:100,y:60},seats:2,isOccupied:!1},{id:7,position:{x:20,y:100},seats:4,isOccupied:!1},{id:8,position:{x:60,y:100},seats:2,isOccupied:!1},{id:9,position:{x:100,y:100},seats:2,isOccupied:!1}])},[]),(0,i.useEffect)(()=>{d(e=>{let s=[...e];return s.forEach(e=>{e.isOccupied=!1,e.customer=void 0}),n.forEach((e,t)=>{let a=t%s.length,i=s[a];i&&(i.isOccupied=!0,i.customer={id:e.id,name:e.customerName,avatar:x(e.customerName),order:e.items[0],satisfaction:Math.max(0,Math.min(100,e.timeLimit/300*100)),timeSeated:300-e.timeLimit})}),s})},[n]);let x=e=>{let s=["\uD83D\uDC68‍\uD83D\uDCBC","\uD83D\uDC69‍\uD83D\uDCBC","\uD83D\uDC68‍\uD83C\uDF93","\uD83D\uDC69‍\uD83C\uDF93","\uD83D\uDC68‍\uD83C\uDF73","\uD83D\uDC69‍\uD83C\uDF73","\uD83D\uDC68‍⚕️","\uD83D\uDC69‍⚕️","\uD83D\uDC68‍\uD83C\uDFA8","\uD83D\uDC69‍\uD83C\uDFA8"];return s[e.length%s.length]};return(0,a.jsxs)("div",{className:"bg-gradient-to-br from-amber-50 to-orange-50 rounded-lg p-6 min-h-[600px] relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-orange-800",children:["\uD83C\uDF7D️ ",t("dining.room.title","Dining Room")]}),(0,a.jsx)("p",{className:"text-orange-600",children:t("dining.room.subtitle","Watch your customers enjoy their meals")})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg px-4 py-2 border border-orange-200",children:[(0,a.jsx)("div",{className:"text-sm text-gray-600",children:t("dining.occupied.tables","Occupied Tables")}),(0,a.jsxs)("div",{className:"text-xl font-bold text-orange-600",children:[c.filter(e=>e.isOccupied).length,"/",c.length]})]}),(0,a.jsx)("button",{onClick:()=>m(!o),className:"p-2 rounded-lg transition-colors ".concat(o?"bg-green-100 text-green-600":"bg-gray-100 text-gray-600"),title:t("dining.ambient.sounds","Toggle ambient sounds"),children:o?"\uD83D\uDD0A":"\uD83D\uDD07"})]})]}),(0,a.jsxs)("div",{className:"relative bg-white rounded-lg border-2 border-orange-200 h-96 overflow-hidden",children:[(0,a.jsx)("div",{className:"absolute inset-0 opacity-10",children:(0,a.jsx)("div",{className:"grid grid-cols-8 grid-rows-6 h-full",children:Array.from({length:48}).map((e,s)=>(0,a.jsx)("div",{className:"border border-gray-300"},s))})}),c.map(e=>{var t;return(0,a.jsxs)("div",{className:(e=>{let s=e.seats<=2?"w-16 h-16":e.seats<=4?"w-20 h-20":"w-24 h-24",t=e.isOccupied?"bg-orange-100 border-orange-300":"bg-gray-100 border-gray-300";return"".concat("absolute transition-all duration-300 hover:scale-105 cursor-pointer"," ").concat(s," ").concat(t," border-2 rounded-lg flex flex-col items-center justify-center")})(e),style:{left:"".concat(e.position.x,"px"),top:"".concat(e.position.y,"px")},onClick:()=>e.customer&&(null==s?void 0:s(e.customer.id)),children:[(0,a.jsx)("div",{className:"text-2xl mb-1",children:(t=e.seats)<=2?"\uD83E\uDE91":t<=4?"\uD83C\uDF7D️":"\uD83C\uDFDB️"}),(0,a.jsxs)("div",{className:"text-xs font-bold text-gray-600",children:["#",e.id]}),e.customer&&(0,a.jsxs)("div",{className:"absolute -top-8 left-1/2 transform -translate-x-1/2",children:[(0,a.jsx)("div",{className:"bg-white rounded-full p-1 border-2 border-orange-300 shadow-lg",children:(0,a.jsx)("span",{className:"text-lg",children:e.customer.avatar})}),(0,a.jsx)("div",{className:"absolute -bottom-1 -right-1",children:(0,a.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(e.customer.satisfaction>80?"bg-green-500":e.customer.satisfaction>50?"bg-yellow-500":e.customer.satisfaction>20?"bg-orange-500":"bg-red-500")})})]}),e.customer&&(0,a.jsx)("div",{className:"absolute -bottom-6 left-1/2 transform -translate-x-1/2",children:(0,a.jsx)("div",{className:"animate-bounce text-xs",children:"\uD83C\uDF7D️"})})]},e.id)}),(0,a.jsx)("div",{className:"absolute top-4 left-4 text-2xl",children:"\uD83E\uDEB4"}),(0,a.jsx)("div",{className:"absolute top-4 right-4 text-2xl",children:"\uD83E\uDEB4"}),(0,a.jsx)("div",{className:"absolute bottom-4 left-4 text-2xl",children:"\uD83D\uDD6F️"}),(0,a.jsx)("div",{className:"absolute bottom-4 right-4 text-2xl",children:"\uD83D\uDD6F️"}),(0,a.jsx)("div",{className:"absolute bottom-0 left-1/2 transform -translate-x-1/2 bg-brown-200 rounded-t-lg p-2 border-2 border-brown-300",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg",children:"\uD83D\uDECE️"}),(0,a.jsx)("div",{className:"text-xs font-bold",children:t("dining.service.counter","Service")})]})})]}),(0,a.jsxs)("div",{className:"mt-6 bg-white rounded-lg p-4 border border-orange-200",children:[(0,a.jsxs)("h3",{className:"font-semibold text-gray-800 mb-3",children:["\uD83D\uDC65 ",t("dining.customer.status","Customer Status")]}),c.filter(e=>e.isOccupied).length>0?(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:c.filter(e=>e.isOccupied).map(e=>{var i,r,l,n,c,d,o,m,x,u,h;return(0,a.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3 border cursor-pointer hover:bg-gray-100 transition-colors",onClick:()=>e.customer&&(null==s?void 0:s(e.customer.id)),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"text-lg",children:null==(i=e.customer)?void 0:i.avatar}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-sm",children:null==(r=e.customer)?void 0:r.name}),(0,a.jsxs)("div",{className:"text-xs text-gray-600",children:["Table ",e.id]})]})]}),(0,a.jsx)("div",{className:"text-lg ".concat((h=(null==(l=e.customer)?void 0:l.satisfaction)||0)>80?"text-green-500":h>50?"text-yellow-500":h>20?"text-orange-500":"text-red-500"),children:(null==(n=e.customer)?void 0:n.satisfaction)&&e.customer.satisfaction>80?"\uD83D\uDE0A":(null==(c=e.customer)?void 0:c.satisfaction)&&e.customer.satisfaction>50?"\uD83D\uDE10":(null==(d=e.customer)?void 0:d.satisfaction)&&e.customer.satisfaction>20?"\uD83D\uDE24":"\uD83D\uDE20"})]}),(0,a.jsxs)("div",{className:"text-xs text-gray-600 mb-2",children:[t("dining.enjoying","Enjoying"),": ",null==(o=e.customer)?void 0:o.order]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-1",children:(0,a.jsx)("div",{className:"h-1 rounded-full transition-all duration-300 ".concat(((null==(m=e.customer)?void 0:m.satisfaction)||0)>60?"bg-green-500":((null==(x=e.customer)?void 0:x.satisfaction)||0)>30?"bg-yellow-500":"bg-red-500"),style:{width:"".concat((null==(u=e.customer)?void 0:u.satisfaction)||0,"%")}})})]},e.id)})}):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFEA"}),(0,a.jsx)("p",{children:t("dining.no.customers","No customers dining currently")}),(0,a.jsx)("p",{className:"text-sm",children:t("dining.waiting.for.customers","Complete orders to see customers dining")})]})]}),o&&(0,a.jsxs)("div",{className:"absolute top-2 right-2 text-xs text-gray-500 animate-pulse",children:["\uD83C\uDFB5 ",t("dining.ambient.playing","Ambient sounds playing")]})]})}function F(e){let{player:s,onOpenMenu:t,onOpenAchievements:l,onOpenSkills:c,onOpenBakeries:d,onOpenSettings:o}=e,{t:m}=(0,r.o)(),[x,u]=(0,i.useState)(!1),h=s.experience/s.maxExperience*100;return(0,a.jsx)("div",{className:"bg-white shadow-lg border-b border-gray-200 relative",children:(0,a.jsxs)("div",{className:"px-6 py-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)(n.$,{variant:"secondary",size:"sm",className:"bg-orange-100 hover:bg-orange-200 text-orange-800",onClick:t,children:["☰ ",m("toolbar.menu","Menu")]}),(0,a.jsx)("div",{className:"hidden md:block",children:(0,a.jsx)("h1",{className:"text-xl font-bold text-orange-800",children:"\uD83E\uDD56 Bake It Out"})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-800",children:s.name}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["Level ",s.level]})]}),(0,a.jsxs)("div",{className:"hidden sm:block",children:[(0,a.jsx)("div",{className:"w-32 bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(h,"%")}})}),(0,a.jsxs)("div",{className:"text-xs text-center text-gray-500 mt-1",children:[s.experience,"/",s.maxExperience," XP"]})]}),(0,a.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,a.jsxs)("span",{className:"text-green-800 font-medium",children:["$",s.money]})}),s.skillPoints>0&&(0,a.jsxs)("div",{className:"bg-yellow-100 px-3 py-1 rounded-full relative",children:[(0,a.jsxs)("span",{className:"text-yellow-800 font-medium",children:["⭐ ",s.skillPoints]}),(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n.$,{variant:"secondary",size:"sm",className:"md:hidden",onClick:()=>u(!x),children:"⚡"}),(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-2",children:[(0,a.jsx)(n.$,{variant:"secondary",size:"sm",onClick:d,children:"\uD83C\uDFEA"}),(0,a.jsx)(n.$,{variant:"secondary",size:"sm",onClick:l,className:"relative",children:"\uD83C\uDFC6"}),(0,a.jsxs)(n.$,{variant:"secondary",size:"sm",onClick:c,className:s.skillPoints>0?"bg-yellow-100 hover:bg-yellow-200":"",children:["\uD83C\uDF1F",s.skillPoints>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]}),(0,a.jsx)(n.$,{variant:"secondary",size:"sm",onClick:o,children:"⚙️"})]})]})]}),x&&(0,a.jsxs)("div",{className:"md:hidden mt-4 pt-4 border-t border-gray-200",children:[(0,a.jsxs)("div",{className:"grid grid-cols-4 gap-2",children:[(0,a.jsxs)(n.$,{variant:"secondary",size:"sm",className:"flex flex-col items-center py-3",onClick:()=>{d(),u(!1)},children:[(0,a.jsx)("span",{className:"text-lg",children:"\uD83C\uDFEA"}),(0,a.jsx)("span",{className:"text-xs",children:m("toolbar.bakeries","Bakeries")})]}),(0,a.jsxs)(n.$,{variant:"secondary",size:"sm",className:"flex flex-col items-center py-3",onClick:()=>{l(),u(!1)},children:[(0,a.jsx)("span",{className:"text-lg",children:"\uD83C\uDFC6"}),(0,a.jsx)("span",{className:"text-xs",children:m("toolbar.achievements","Achievements")})]}),(0,a.jsxs)(n.$,{variant:"secondary",size:"sm",className:"flex flex-col items-center py-3 relative ".concat(s.skillPoints>0?"bg-yellow-100 hover:bg-yellow-200":""),onClick:()=>{c(),u(!1)},children:[(0,a.jsx)("span",{className:"text-lg",children:"\uD83C\uDF1F"}),(0,a.jsx)("span",{className:"text-xs",children:m("toolbar.skills","Skills")}),s.skillPoints>0&&(0,a.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]}),(0,a.jsxs)(n.$,{variant:"secondary",size:"sm",className:"flex flex-col items-center py-3",onClick:()=>{o(),u(!1)},children:[(0,a.jsx)("span",{className:"text-lg",children:"⚙️"}),(0,a.jsx)("span",{className:"text-xs",children:m("toolbar.settings","Settings")})]})]}),(0,a.jsxs)("div",{className:"mt-3",children:[(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(h,"%")}})}),(0,a.jsxs)("div",{className:"text-xs text-center text-gray-500 mt-1",children:[s.experience,"/",s.maxExperience," XP"]})]})]})]})})}function z(e){let{isOpen:s,onClose:t,onSaveGame:i,onLoadGame:l,onSettings:c,onMainMenu:d,onExit:o}=e,{t:m}=(0,r.o)();return s?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-orange-500 to-yellow-500 p-6 text-white",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold",children:["\uD83C\uDFAE ",m("gameMenu.title","Game Menu")]}),(0,a.jsx)("p",{className:"text-orange-100 text-sm",children:m("gameMenu.subtitle","Manage your game")})]}),(0,a.jsx)(n.$,{variant:"secondary",size:"sm",className:"bg-white/20 hover:bg-white/30 text-white border-white/30",onClick:t,children:"✕"})]})}),(0,a.jsxs)("div",{className:"p-6 space-y-3",children:[(0,a.jsxs)(n.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-orange-50",onClick:()=>{t()},children:[(0,a.jsx)("span",{className:"text-2xl mr-3",children:"▶️"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-semibold",children:m("gameMenu.resume","Resume Game")}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:m("gameMenu.resumeDesc","Continue playing")})]})]}),(0,a.jsxs)(n.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-green-50",onClick:()=>{i(),t()},children:[(0,a.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDCBE"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-semibold",children:m("gameMenu.save","Save Game")}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:m("gameMenu.saveDesc","Save your progress")})]})]}),(0,a.jsxs)(n.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-blue-50",onClick:()=>{l(),t()},children:[(0,a.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDCC1"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-semibold",children:m("gameMenu.load","Load Game")}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:m("gameMenu.loadDesc","Load saved progress")})]})]}),(0,a.jsxs)(n.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-purple-50",onClick:()=>{c(),t()},children:[(0,a.jsx)("span",{className:"text-2xl mr-3",children:"⚙️"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-semibold",children:m("gameMenu.settings","Settings")}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:m("gameMenu.settingsDesc","Game preferences")})]})]}),(0,a.jsxs)("div",{className:"border-t pt-3 mt-4",children:[(0,a.jsxs)(n.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-yellow-50",onClick:()=>{d(),t()},children:[(0,a.jsx)("span",{className:"text-2xl mr-3",children:"\uD83C\uDFE0"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-semibold",children:m("gameMenu.mainMenu","Main Menu")}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:m("gameMenu.mainMenuDesc","Return to main menu")})]})]}),o&&(0,a.jsxs)(n.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-red-50 text-red-600",onClick:()=>{o(),t()},children:[(0,a.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDEAA"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-semibold",children:m("gameMenu.exit","Exit Game")}),(0,a.jsx)("div",{className:"text-sm text-red-400",children:m("gameMenu.exitDesc","Close the application")})]})]})]})]}),(0,a.jsx)("div",{className:"bg-gray-50 px-6 py-3 text-center text-sm text-gray-500",children:m("gameMenu.tip","Press ESC to open this menu anytime")})]})}):null}var O=t(2148);function q(){var e;let{t:s}=(0,r.o)(),{setGameActivity:t,setBakingActivity:o}=(0,O.l)(),{player:b,equipment:y,inventory:w,orders:D,achievements:C,skills:q,levelUpRewards:$,showLevelUp:P,updateEquipment:I,acceptOrder:M,completeOrder:B,declineOrder:T,generateNewOrder:_,upgradeSkill:R,checkAchievements:J,dismissLevelUp:U,spendMoney:G}=(0,l.I)(),[Y,W]=(0,i.useState)(!1),[V,H]=(0,i.useState)(!1),[Q,K]=(0,i.useState)(!1),[X,Z]=(0,i.useState)(!1),[ee,es]=(0,i.useState)(!1),[et,ea]=(0,i.useState)(!1),[ei,er]=(0,i.useState)(!1),[el,en]=(0,i.useState)(!1),[ec,ed]=(0,i.useState)(!1),[eo,em]=(0,i.useState)(!1),[ex,eu]=(0,i.useState)(!1),[eh,eg]=(0,i.useState)(!1),[ep,ev]=(0,i.useState)("save"),[eb,ej]=(0,i.useState)(null),[ey,ef]=(0,i.useState)("traditional"),[eN,ew]=(0,i.useState)({language:"en",soundEnabled:!0,musicEnabled:!0,notificationsEnabled:!0,autoSaveEnabled:!0,graphicsQuality:"medium",animationSpeed:1,showTutorials:!0}),[ek,eD]=(0,i.useState)([{id:"main",name:"Downtown Delights",location:"City Center",specialization:"general",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],unlocked:!0,purchaseCost:0}]),[eC,eS]=(0,i.useState)("main"),{notifications:eE,removeNotification:eL,showSuccess:eA,showError:eF,showInfo:ez}=function(){let[e,s]=(0,i.useState)([]),t=e=>{let t=Date.now().toString()+Math.random().toString(36).substr(2,9),a={...e,id:t,duration:e.duration||5e3};s(e=>[...e,a])};return{notifications:e,addNotification:t,removeNotification:e=>{s(s=>s.filter(s=>s.id!==e))},showSuccess:(e,s)=>{t({type:"success",title:e,message:s})},showError:(e,s)=>{t({type:"error",title:e,message:s})},showWarning:(e,s)=>{t({type:"warning",title:e,message:s})},showInfo:(e,s)=>{t({type:"info",title:e,message:s})}}}(),eO=(e,s)=>{ej({id:e,name:s}),K(!0)},eq=e=>{M(e),ez("Order Accepted","You have accepted a new order!")},e$=e=>{let s=D.find(s=>s.id===e);s&&(B(e),J(),eA("Order Completed!","You earned $".concat(s.reward," and gained experience!")))},eP=e=>{T(e),ez("Order Declined","Order has been removed from your queue.")};return window.addEventListener("keydown",e=>{"Escape"===e.key&&em(!eo)}),(0,i.useEffect)(()=>{(async()=>{if(D&&D.length>0){var e;let s=(null==(e=D[0].items[0])?void 0:e.name)||"Unknown item";await o(b.level,s)}else await t(b.level,b.money,"Managing bakery")})()},[b.level,b.money,D,t,o]),(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-orange-50 to-yellow-50",children:[(0,a.jsx)(F,{player:b,onOpenMenu:()=>em(!0),onOpenAchievements:()=>Z(!0),onOpenSkills:()=>es(!0),onOpenBakeries:()=>ed(!0),onOpenSettings:()=>en(!0)}),(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-6 pb-4",children:(0,a.jsxs)("div",{className:"flex justify-center space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>ef("traditional"),className:"px-4 py-2 rounded-lg font-medium transition-colors ".concat("traditional"===ey?"bg-orange-500 text-white":"bg-white text-gray-700 hover:bg-gray-100 border border-gray-300"),children:["\uD83D\uDCCA ",s("game.view.traditional","Traditional View")]}),(0,a.jsxs)("button",{onClick:()=>ef("layout"),className:"px-4 py-2 rounded-lg font-medium transition-colors ".concat("layout"===ey?"bg-orange-500 text-white":"bg-white text-gray-700 hover:bg-gray-100 border border-gray-300"),children:["\uD83C\uDFEA ",s("game.view.layout","Bakery Layout")]}),(0,a.jsxs)("button",{onClick:()=>ef("dining"),className:"px-4 py-2 rounded-lg font-medium transition-colors ".concat("dining"===ey?"bg-orange-500 text-white":"bg-white text-gray-700 hover:bg-gray-100 border border-gray-300"),children:["\uD83C\uDF7D️ ",s("game.view.dining","Dining Room")]}),(0,a.jsxs)("button",{onClick:()=>eg(!0),className:"px-4 py-2 rounded-lg font-medium bg-blue-500 text-white hover:bg-blue-600 transition-colors",children:["\uD83D\uDC65 ",s("game.view.customers","Customer Manager")]})]})}),(0,a.jsxs)("div",{className:"max-w-7xl mx-auto p-6 grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-3 space-y-6",children:["layout"===ey&&(0,a.jsx)(E,{equipment:y,onEquipmentClick:eO}),"dining"===ey&&(0,a.jsx)(A,{onCustomerClick:e=>{eg(!0)}}),"traditional"===ey&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-orange-800",children:s("kitchen.title")}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Current: ",null==(e=ek.find(e=>e.id===eC))?void 0:e.name]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:y.map(e=>(0,a.jsx)(c.$,{equipment:e,onClick:eO},e.id))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-orange-800 mb-4",children:s("inventory.title")}),(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:w.map(e=>(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,a.jsx)("div",{className:"text-2xl mb-1",children:e.icon}),(0,a.jsx)("div",{className:"font-medium text-gray-800",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:s("inventory.quantity",{qty:e.quantity.toString()})}),(0,a.jsx)("div",{className:"text-xs text-green-600",children:s("inventory.cost",{cost:e.cost.toString()})})]},e.name))})]})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-orange-800",children:s("orders.title")}),(0,a.jsx)(n.$,{size:"sm",variant:"primary",onClick:_,children:s("orders.newOrder")})]}),(0,a.jsx)("div",{className:"space-y-4",children:D.map(e=>(0,a.jsx)(d.p,{order:e,onAccept:eq,onDecline:eP,onComplete:e$},e.id))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-orange-800 mb-4",children:s("actions.title")}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(n.$,{variant:"secondary",size:"sm",className:"w-full",onClick:()=>H(!0),children:s("actions.buyIngredients")}),(0,a.jsx)(n.$,{variant:"secondary",size:"sm",className:"w-full",onClick:()=>W(!0),children:s("actions.viewRecipes")}),(0,a.jsx)(n.$,{variant:"secondary",size:"sm",className:"w-full",onClick:()=>er(!0),children:s("actions.equipmentShop")})]})]})]})]}),(0,a.jsx)(m,{isOpen:Y,onClose:()=>W(!1)}),(0,a.jsx)(x,{isOpen:V,onClose:()=>H(!1)}),(0,a.jsx)(u,{isOpen:Q,onClose:()=>K(!1),equipmentId:(null==eb?void 0:eb.id)||"",equipmentName:(null==eb?void 0:eb.name)||""}),(0,a.jsx)(p,{isOpen:X,onClose:()=>Z(!1),achievements:C}),(0,a.jsx)(v,{isOpen:ee,onClose:()=>es(!1),skills:q,skillPoints:b.skillPoints,playerLevel:b.level,onUpgradeSkill:R}),(0,a.jsx)(g,{isOpen:P,onClose:U,newLevel:b.level,rewards:$}),(0,a.jsx)(j,{isOpen:et,onClose:()=>ea(!1)}),(0,a.jsx)(f,{isOpen:ei,onClose:()=>er(!1),onShowSuccess:eA}),(0,a.jsx)(N.b,{isOpen:el,onClose:()=>en(!1),settings:eN,onSettingsChange:e=>{ew(s=>({...s,...e}))}}),(0,a.jsx)(k,{isOpen:ec,onClose:()=>ed(!1),bakeries:ek,currentBakeryId:eC,onSwitchBakery:e=>{var s;eS(e),ez("Bakery Switched","Switched to ".concat(null==(s=ek.find(s=>s.id===e))?void 0:s.name))},onPurchaseBakery:e=>{if(G(e.purchaseCost)){let s={id:Date.now().toString(),name:e.name,location:"Downtown",specialization:"general",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],unlocked:!0,purchaseCost:e.purchaseCost};eD(e=>[...e,s]),eA("Bakery Purchased!","You now own ".concat(e.name,"!"))}},playerMoney:b.money}),(0,a.jsx)(L,{isOpen:eh,onClose:()=>eg(!1)}),(0,a.jsx)(z,{isOpen:eo,onClose:()=>em(!1),onSaveGame:()=>{ev("save"),eu(!0)},onLoadGame:()=>{ev("load"),eu(!0)},onSettings:()=>en(!0),onMainMenu:()=>{window.location.href="/"},onExit:window.electronAPI?()=>{window.electronAPI&&window.electronAPI.quit()}:void 0}),(0,a.jsx)(S,{isOpen:ex,onClose:()=>eu(!1),mode:ep,onSaveSuccess:()=>{eA("Game Saved!","Your progress has been saved successfully."),eu(!1)},onLoadSuccess:()=>{eA("Game Loaded!","Your saved progress has been loaded."),eu(!1)}}),(0,a.jsx)(h,{notifications:eE,onRemove:eL})]})}function $(){return(0,a.jsx)(l.S,{children:(0,a.jsx)(q,{})})}}}]);