{"name": "mime-db", "description": "Media Type Database", "version": "1.33.0", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (http://jongleberry.com)", "<PERSON> <<EMAIL>> (http://github.com/broofa)"], "license": "MIT", "keywords": ["mime", "db", "type", "types", "database", "charset", "charsets"], "repository": "jshttp/mime-db", "devDependencies": {"bluebird": "3.5.1", "co": "4.6.0", "cogent": "1.0.1", "csv-parse": "1.3.1", "eslint": "3.19.0", "eslint-config-standard": "10.2.1", "eslint-plugin-import": "2.8.0", "eslint-plugin-node": "5.2.1", "eslint-plugin-promise": "3.6.0", "eslint-plugin-standard": "3.0.1", "gnode": "0.1.2", "mocha": "1.21.5", "nyc": "11.4.1", "raw-body": "2.3.2", "stream-to-array": "2.3.0"}, "files": ["HISTORY.md", "LICENSE", "README.md", "db.json", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"build": "node scripts/build", "fetch": "gnode scripts/fetch-apache && gnode scripts/fetch-iana && gnode scripts/fetch-nginx", "lint": "eslint .", "test": "mocha --reporter spec --bail --check-leaks test/", "test-cov": "nyc --reporter=html --reporter=text npm test", "test-travis": "nyc --reporter=text npm test", "update": "npm run fetch && npm run build"}}