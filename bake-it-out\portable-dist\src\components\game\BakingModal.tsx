'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { Recipe, getAvailableRecipes, canCraftRecipe } from '@/lib/gameLogic'
import { useGame } from '@/contexts/GameContext'
import { useLanguage } from '@/contexts/LanguageContext'
import { getLocalizedRecipeName, getLocalizedIngredientName } from '@/lib/localization'

interface BakingModalProps {
  isOpen: boolean
  onClose: () => void
  equipmentId: string
  equipmentName: string
}

export function BakingModal({ isOpen, onClose, equipmentId, equipmentName }: BakingModalProps) {
  const { player, inventory, updateEquipment, useIngredient } = useGame()
  const { t } = useLanguage()
  const [selectedRecipe, setSelectedRecipe] = useState<Recipe | null>(null)
  
  if (!isOpen) return null

  const availableRecipes = getAvailableRecipes(player.level)
  const craftableRecipes = availableRecipes.filter(recipe => 
    canCraftRecipe(recipe, inventory)
  )

  const startBaking = (recipe: Recipe) => {
    // Check if we can craft the recipe
    if (!canCraftRecipe(recipe, inventory)) {
      return
    }

    // Consume ingredients - check if we have enough first
    const hasEnoughIngredients = recipe.ingredients.every(ingredient => {
      const inventoryItem = inventory.find(item => item.name === ingredient.name)
      return inventoryItem && inventoryItem.quantity >= ingredient.quantity
    })

    if (!hasEnoughIngredients) {
      return
    }

    // Consume the ingredients
    recipe.ingredients.forEach(ingredient => {
      useIngredient(ingredient.name, ingredient.quantity)
    })

    // Start the equipment
    updateEquipment(equipmentId, {
      isActive: true,
      timeRemaining: recipe.bakingTime,
      currentRecipe: recipe.name
    })

    onClose()
  }

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins}:${secs.toString().padStart(2, '0')}`
  }

  const getDifficultyStars = (difficulty: number) => {
    return '⭐'.repeat(difficulty) + '☆'.repeat(5 - difficulty)
  }

  const getRecipeIcon = (category: string) => {
    switch (category) {
      case 'cookies': return '🍪'
      case 'cakes': return '🧁'
      case 'bread': return '🍞'
      case 'pastries': return '🥐'
      default: return '🍽️'
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold text-orange-800">
              🔥 {equipmentName} - Select Recipe
            </h2>
            <Button variant="secondary" onClick={onClose}>
              ✕ Close
            </Button>
          </div>
        </div>

        <div className="p-6">
          {craftableRecipes.length === 0 ? (
            <div className="text-center py-8">
              <div className="text-4xl mb-4">😔</div>
              <h3 className="text-lg font-medium text-gray-800 mb-2">
                No recipes available
              </h3>
              <p className="text-gray-600 mb-4">
                You don&apos;t have enough ingredients to craft any recipes.
              </p>
              <Button variant="primary" onClick={onClose}>
                Buy Ingredients
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[60vh] overflow-y-auto">
              {craftableRecipes.map(recipe => (
                <div
                  key={recipe.id}
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all ${
                    selectedRecipe?.id === recipe.id
                      ? 'border-orange-400 bg-orange-50'
                      : 'border-gray-300 bg-gray-50 hover:border-orange-300'
                  }`}
                  onClick={() => setSelectedRecipe(recipe)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      <span className="text-2xl">{getRecipeIcon(recipe.category)}</span>
                      <h3 className="font-semibold text-gray-800">{getLocalizedRecipeName(recipe, t)}</h3>
                    </div>
                    <span className="text-sm text-green-600">${recipe.basePrice}</span>
                  </div>

                  <div className="text-xs text-gray-500 mb-2">
                    {getDifficultyStars(recipe.difficulty)} • ⏱️ {formatTime(recipe.bakingTime)}
                  </div>

                  <div className="space-y-1 mb-3">
                    <div className="text-sm font-medium text-gray-700">Ingredients:</div>
                    {recipe.ingredients.map((ingredient, index) => {
                      const inventoryItem = inventory.find(item => item.name === ingredient.name)

                      return (
                        <div
                          key={index}
                          className="text-xs flex justify-between text-green-600"
                        >
                          <span>{getLocalizedIngredientName(ingredient, t)}</span>
                          <span>
                            {ingredient.quantity}
                            <span className="ml-1">
                              ({inventoryItem?.quantity || 0} available)
                            </span>
                          </span>
                        </div>
                      )
                    })}
                  </div>

                  {selectedRecipe?.id === recipe.id && (
                    <Button
                      variant="success"
                      size="sm"
                      className="w-full"
                      onClick={() => startBaking(recipe)}
                    >
                      🔥 Start Baking
                    </Button>
                  )}
                </div>
              ))}
            </div>
          )}

          {selectedRecipe && craftableRecipes.length > 0 && (
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="font-medium text-blue-800 mb-2">
                📋 Baking Instructions for {selectedRecipe.name}
              </h3>
              <div className="text-sm text-blue-700 space-y-1">
                <p>• Baking time: {formatTime(selectedRecipe.bakingTime)}</p>
                <p>• Difficulty: {getDifficultyStars(selectedRecipe.difficulty)}</p>
                <p>• Expected reward: ${selectedRecipe.basePrice}</p>
                <p>• Make sure you have all ingredients before starting!</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
