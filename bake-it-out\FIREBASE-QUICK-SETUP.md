# 🔥 Firebase Quick Setup Checklist

## ✅ **Step 1: Project Created** ✅
Your Firebase project "bake-it-out" is ready!

## 🔧 **Step 2: Enable Authentication**

1. **Go to your Firebase Console**: https://console.firebase.google.com/project/bake-it-out
2. **Click "Authentication"** in the left sidebar
3. **Click "Get started"**
4. **Go to "Sign-in method" tab**
5. **Enable "Email/Password"**:
   - Click on "Email/Password" provider
   - Toggle "Enable" to ON
   - Click "Save"

## 📊 **Step 3: Create Firestore Database**

1. **Click "Firestore Database"** in the left sidebar
2. **Click "Create database"**
3. **Choose "Start in test mode"** (we'll add security rules next)
4. **Select location**: Choose closest to your users (e.g., us-central1)
5. **Click "Done"**

## 🔒 **Step 4: Deploy Security Rules**

1. **Go to Firestore Database → Rules tab**
2. **Replace the existing rules** with this content:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Users collection - users can only access their own profile
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Game saves collection - users can only access their own saves
    match /game_saves/{saveId} {
      allow read, write, delete: if request.auth != null && 
        request.auth.uid == resource.data.userId;
      allow create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
    }
    
    // User stats collection - users can only access their own stats
    match /user_stats/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Sync logs collection - users can only access their own logs
    match /sync_logs/{logId} {
      allow read, create: if request.auth != null && 
        request.auth.uid == request.resource.data.userId;
      allow read: if request.auth != null && 
        request.auth.uid == resource.data.userId;
    }
    
    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
```

3. **Click "Publish"**

## 🚀 **Step 5: Test the Implementation**

1. **Start your development server**:
   ```bash
   npm run dev
   ```

2. **Open the game** at http://localhost:3000

3. **Test cloud saves**:
   - Click the Settings button (⚙️)
   - Go to "Save & Data" tab
   - You should see "Cloud Sync" section (no more "Coming Soon"!)
   - Click "Login / Register"
   - Create a new account
   - Save your game to the cloud
   - Try loading the save

## 🎯 **Expected Results**

✅ **Authentication works** - You can create accounts and login  
✅ **Cloud saves work** - You can save and load game data  
✅ **Cross-device sync** - Same account works on different devices  
✅ **Security** - Users can only access their own data  
✅ **Real-time status** - Sync indicators show progress  

## 🆘 **Troubleshooting**

### **"Firebase not configured"**
- Make sure you completed all steps above
- Check that `.env.local` file exists with correct values
- Restart your development server (`npm run dev`)

### **"Permission denied"**
- Make sure you deployed the security rules (Step 4)
- Verify the user is logged in
- Check browser console for detailed errors

### **"Authentication failed"**
- Make sure Email/Password is enabled in Firebase console
- Check that the email format is valid
- Try with a different email address

## 🎉 **Success!**

Once all steps are complete, your players will have:

🔥 **Firebase-powered cloud saves**  
🔐 **Secure user authentication**  
☁️ **Cross-device synchronization**  
📱 **Real-time sync status**  
🎮 **Seamless gaming experience**  

**Your cloud save system is ready! 🧁**
