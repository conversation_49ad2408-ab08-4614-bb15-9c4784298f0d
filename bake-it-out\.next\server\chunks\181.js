"use strict";exports.id=181,exports.ids=[181],exports.modules={2643:(a,b,c)=>{c.d(b,{$:()=>e});var d=c(687);c(3210);let e=({variant:a="primary",size:b="md",className:c="",children:e,...f})=>{let g=["font-medium rounded-lg transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2",{primary:"bg-blue-600 hover:bg-blue-700 text-white focus:ring-blue-500",secondary:"bg-gray-200 hover:bg-gray-300 text-gray-900 focus:ring-gray-500",danger:"bg-red-600 hover:bg-red-700 text-white focus:ring-red-500",success:"bg-green-600 hover:bg-green-700 text-white focus:ring-green-500"}[a],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"}[b],c].join(" ");return(0,d.jsx)("button",{className:g,...f,children:e})}},3211:(a,b,c)=>{c.d(b,{$:()=>f});var d=c(687),e=c(4393);function f({equipment:a,onClick:b}){let{t:c}=(0,e.o)();return(0,d.jsx)("div",{className:`p-4 rounded-lg border-2 cursor-pointer transition-all ${a.isActive?"border-green-400 bg-green-50":"border-gray-200 bg-gray-50 hover:border-orange-300 hover:bg-orange-50"}`,onClick:()=>!a.isActive&&b(a.id,a.name),children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-3xl mb-2",children:(a=>{switch(a){case"oven":return"\uD83D\uDD25";case"mixer":return"\uD83E\uDD44";case"counter":return"\uD83C\uDF7D️";default:return"⚙️"}})(a.type)}),(0,d.jsx)("h3",{className:"font-medium text-gray-800",children:c(`equipment.${a.name}`,a.name)}),(0,d.jsxs)("div",{className:"text-xs text-gray-500",children:[c("game.level","Level")," ",a.level]}),a.isActive&&a.timeRemaining?(0,d.jsxs)("div",{className:"mt-2",children:[(0,d.jsx)("div",{className:"text-sm text-green-600",children:c("kitchen.making",{recipe:a.currentRecipe||""})}),(0,d.jsx)("div",{className:"text-lg font-mono text-green-700",children:(a=>{let b=Math.floor(a/60);return`${b}:${(a%60).toString().padStart(2,"0")}`})(a.timeRemaining)}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 mt-2",children:(0,d.jsx)("div",{className:"bg-green-500 h-2 rounded-full transition-all duration-1000",style:{width:`${100-a.timeRemaining/60*100}%`}})})]}):(0,d.jsx)("div",{className:"text-sm text-gray-500 mt-2",children:a.isActive?c("equipment.status.active","Busy"):c("click_to_use","Click to use")})]})})}},4579:(a,b,c)=>{c.d(b,{p:()=>g});var d=c(687),e=c(2643),f=c(4393);function g({order:a,onAccept:b,onDecline:c,onComplete:g}){let{t:h}=(0,f.o)();return(0,d.jsxs)("div",{className:`p-4 rounded-lg border ${(()=>{switch(a.status){case"pending":return"border-yellow-300 bg-yellow-50";case"accepted":case"in_progress":return"border-blue-300 bg-blue-50";case"completed":return"border-green-300 bg-green-50";case"failed":return"border-red-300 bg-red-50";default:return"border-gray-300 bg-gray-50"}})()}`,children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-lg",children:(()=>{let b=["\uD83D\uDC69","\uD83D\uDC68","\uD83D\uDC75","\uD83D\uDC74","\uD83D\uDC67","\uD83D\uDC66"],c=a.customerName.length%b.length;return b[c]})()}),(0,d.jsx)("h3",{className:"font-medium text-gray-800",children:a.customerName})]}),(0,d.jsx)("span",{className:"text-sm font-semibold text-green-600",children:h("orders.reward",{amount:a.reward.toString()})})]}),(0,d.jsx)("div",{className:"text-sm text-gray-600 mb-2",children:a.items.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,d.jsx)("span",{children:"\uD83E\uDDC1"}),(0,d.jsx)("span",{children:a})]},b))}),(0,d.jsxs)("div",{className:"flex justify-between items-center mb-3",children:[(0,d.jsxs)("div",{className:"text-xs text-gray-500",children:["⏰ ",h("orders.time","Time"),": ",(a=>{let b=Math.floor(a/60);return`${b}:${(a%60).toString().padStart(2,"0")}`})(a.timeLimit)]}),(0,d.jsx)("div",{className:"text-xs",title:`Difficulty: ${a.difficulty}/5`,children:"⭐".repeat(a.difficulty)+"☆".repeat(5-a.difficulty)})]}),"pending"===a.status&&(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsxs)(e.$,{size:"sm",variant:"success",onClick:()=>b(a.id),className:"flex-1",children:["✅ ",h("orders.accept","Accept")]}),(0,d.jsxs)(e.$,{size:"sm",variant:"danger",onClick:()=>c(a.id),className:"flex-1",children:["❌ ",h("orders.reject","Reject")]})]}),"accepted"===a.status&&(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("div",{className:"text-blue-600 text-sm font-medium mb-2",children:["\uD83D\uDCCB ",h("orders.accepted","Order Accepted")]}),(0,d.jsxs)(e.$,{size:"sm",variant:"primary",onClick:()=>g&&g(a.id),className:"w-full",children:["\uD83C\uDFAF ",h("orders.complete")]})]}),"in_progress"===a.status&&(0,d.jsxs)("div",{className:"text-center text-orange-600 text-sm font-medium",children:["\uD83D\uDD04 ",h("orders.inProgress")]}),"completed"===a.status&&(0,d.jsx)("div",{className:"text-center text-green-600 text-sm font-medium",children:"✅ Completed!"}),"failed"===a.status&&(0,d.jsx)("div",{className:"text-center text-red-600 text-sm font-medium",children:"❌ Failed"})]})}},5745:(a,b,c)=>{c.d(b,{b:()=>j});var d=c(687),e=c(3210),f=c(2643),g=c(4393),h=c(1100),i=c(6005);function j({isOpen:a,onClose:b,settings:c,onSettingsChange:j}){let{language:k,setLanguage:l,t:m}=(0,g.o)(),{isEnabled:n,isConnected:o,setEnabled:p}=(0,h.l)(),[q,r]=(0,e.useState)("general");if(!a)return null;let s=(a,b)=>{j({[a]:b})},t=a=>{l(a),s("language",a)},u=[{id:"general",name:m("settings.general")||"General",icon:"⚙️"},{id:"audio",name:m("settings.audio")||"Audio",icon:"\uD83D\uDD0A"},{id:"graphics",name:m("settings.graphics")||"Graphics",icon:"\uD83C\uDFA8"},{id:"save",name:m("settings.save")||"Save & Data",icon:"\uD83D\uDCBE"},{id:"discord",name:m("settings.discord")||"Discord",icon:"\uD83C\uDFAE"}];return(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:m("settings.title")||"⚙️ Settings"}),(0,d.jsx)(f.$,{variant:"secondary",onClick:b,children:m("game.close")||"✕ Close"})]})}),(0,d.jsx)("div",{className:"border-b border-gray-200",children:(0,d.jsx)("div",{className:"flex space-x-0",children:u.map(a=>(0,d.jsxs)("button",{onClick:()=>r(a.id),className:`px-4 py-3 font-medium text-sm border-b-2 transition-colors ${q===a.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"}`,children:[a.icon," ",a.name]},a.id))})}),(0,d.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["general"===q&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:m("settings.language")||"\uD83C\uDF0D Language"}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsx)(f.$,{variant:"en"===k?"primary":"secondary",size:"sm",onClick:()=>t("en"),children:"\uD83C\uDDFA\uD83C\uDDF8 English"}),(0,d.jsx)(f.$,{variant:"cs"===k?"primary":"secondary",size:"sm",onClick:()=>t("cs"),children:"\uD83C\uDDE8\uD83C\uDDFF Čeština"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:m("settings.gameplay")||"\uD83C\uDFAE Gameplay"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("label",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{children:m("settings.notifications")||"Enable Notifications"}),(0,d.jsx)("input",{type:"checkbox",checked:c.notificationsEnabled,onChange:a=>s("notificationsEnabled",a.target.checked),className:"rounded"})]}),(0,d.jsxs)("label",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{children:m("settings.tutorials")||"Show Tutorials"}),(0,d.jsx)("input",{type:"checkbox",checked:c.showTutorials,onChange:a=>s("showTutorials",a.target.checked),className:"rounded"})]}),(0,d.jsxs)("div",{children:[(0,d.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:[m("settings.animationSpeed")||"Animation Speed",": ",c.animationSpeed,"x"]}),(0,d.jsx)("input",{type:"range",min:"0.5",max:"2",step:"0.1",value:c.animationSpeed,onChange:a=>s("animationSpeed",parseFloat(a.target.value)),className:"w-full"})]})]})]})]}),"audio"===q&&(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("label",{className:"flex items-center justify-between",children:[(0,d.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{children:"\uD83D\uDD0A"}),(0,d.jsx)("span",{children:m("settings.sound")||"Sound Effects"})]}),(0,d.jsx)("input",{type:"checkbox",checked:c.soundEnabled,onChange:a=>s("soundEnabled",a.target.checked),className:"rounded"})]}),(0,d.jsxs)("label",{className:"flex items-center justify-between",children:[(0,d.jsxs)("span",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{children:"\uD83C\uDFB5"}),(0,d.jsx)("span",{children:m("settings.music")||"Background Music"})]}),(0,d.jsx)("input",{type:"checkbox",checked:c.musicEnabled,onChange:a=>s("musicEnabled",a.target.checked),className:"rounded"})]})]})}),"graphics"===q&&(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:m("settings.quality")||"\uD83C\uDFA8 Graphics Quality"}),(0,d.jsx)("div",{className:"space-x-2",children:["low","medium","high"].map(a=>(0,d.jsx)(f.$,{variant:c.graphicsQuality===a?"primary":"secondary",size:"sm",onClick:()=>s("graphicsQuality",a),children:a.charAt(0).toUpperCase()+a.slice(1)},a))})]})}),"save"===q&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:m("settings.autoSave")||"\uD83D\uDCBE Auto-Save"}),(0,d.jsxs)("label",{className:"flex items-center justify-between",children:[(0,d.jsx)("span",{children:m("settings.enableAutoSave")||"Enable Auto-Save"}),(0,d.jsx)("input",{type:"checkbox",checked:c.autoSaveEnabled,onChange:a=>s("autoSaveEnabled",a.target.checked),className:"rounded"})]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800 mb-3",children:m("settings.dataManagement")||"\uD83D\uDCC1 Data Management"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsx)(f.$,{variant:"secondary",onClick:()=>{let a={version:"1.0.0",timestamp:Date.now(),player:{},equipment:[],inventory:[],achievements:[],skills:[],automationSettings:{},gameSettings:c,bakeries:[],currentBakeryId:"main"},b=new Blob([i.B.exportSave(a)],{type:"application/json"}),d=URL.createObjectURL(b),e=document.createElement("a");e.href=d,e.download=`bake-it-out-save-${Date.now()}.json`,e.click(),URL.revokeObjectURL(d)},className:"w-full",children:m("settings.exportSave")||"\uD83D\uDCE4 Export Save"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("input",{type:"file",accept:".json",onChange:a=>{let b=a.target.files?.[0];if(!b)return;let c=new FileReader;c.onload=a=>{try{let b=a.target?.result,c=i.B.importSave(b);c?(console.log("Save imported successfully:",c),alert("Save imported successfully!")):alert("Failed to import save file")}catch(a){alert("Invalid save file")}},c.readAsText(b)},className:"hidden",id:"import-save"}),(0,d.jsx)(f.$,{variant:"secondary",onClick:()=>document.getElementById("import-save")?.click(),className:"w-full",children:m("settings.importSave")||"\uD83D\uDCE5 Import Save"})]})]})]}),(0,d.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,d.jsx)("h4",{className:"font-medium text-yellow-800 mb-2",children:m("settings.cloudSync")||"☁️ Cloud Sync"}),(0,d.jsx)("p",{className:"text-sm text-yellow-700 mb-3",children:m("settings.cloudSyncDescription")||"Cloud sync allows you to save your progress online and play across multiple devices."}),(0,d.jsx)(f.$,{variant:"secondary",size:"sm",disabled:!0,children:m("settings.comingSoon")||"Coming Soon"})]})]}),"discord"===q&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h3",{className:"font-semibold text-gray-800 mb-3",children:["\uD83C\uDFAE ",m("settings.discordRichPresence")||"Discord Rich Presence"]}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-4",children:m("settings.discordDescription")||"Show your current game status and activity in Discord."}),(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium text-gray-800",children:m("settings.enableDiscordRPC")||"Enable Discord Rich Presence"}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:o?m("settings.discordConnected")||"✅ Connected to Discord":m("settings.discordDisconnected")||"❌ Not connected to Discord"})]}),(0,d.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,d.jsx)("input",{type:"checkbox",checked:n,onChange:a=>p(a.target.checked),className:"sr-only peer"}),(0,d.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"})]})]})]}),(0,d.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,d.jsxs)("h4",{className:"font-medium text-blue-800 mb-2",children:["ℹ️ ",m("settings.discordInfo")||"What is Discord Rich Presence?"]}),(0,d.jsxs)("div",{className:"text-sm text-blue-700 space-y-2",children:[(0,d.jsx)("p",{children:m("settings.discordInfoDesc1")||"Discord Rich Presence shows your friends what you're doing in Bake It Out:"}),(0,d.jsxs)("ul",{className:"list-disc list-inside ml-4 space-y-1",children:[(0,d.jsx)("li",{children:m("settings.discordFeature1")||"Your current level and money"}),(0,d.jsx)("li",{children:m("settings.discordFeature2")||"What you're currently baking"}),(0,d.jsx)("li",{children:m("settings.discordFeature3")||"Multiplayer room information"}),(0,d.jsx)("li",{children:m("settings.discordFeature4")||"How long you've been playing"})]}),(0,d.jsx)("p",{className:"mt-2",children:m("settings.discordInfoDesc2")||"Your friends can even join your multiplayer games directly from Discord!"})]})]}),n&&!o&&(0,d.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,d.jsxs)("h4",{className:"font-medium text-yellow-800 mb-2",children:["⚠️ ",m("settings.discordTroubleshooting")||"Discord Not Connected"]}),(0,d.jsxs)("div",{className:"text-sm text-yellow-700 space-y-2",children:[(0,d.jsx)("p",{children:m("settings.discordTrouble1")||"Make sure Discord is running on your computer."}),(0,d.jsx)("p",{children:m("settings.discordTrouble2")||"Discord Rich Presence only works in the desktop version of the game."}),(0,d.jsx)("p",{children:m("settings.discordTrouble3")||"Try restarting both Discord and the game if the connection fails."})]})]}),(0,d.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,d.jsxs)("h4",{className:"font-medium text-green-800 mb-2",children:["\uD83D\uDD12 ",m("settings.discordPrivacy")||"Privacy Information"]}),(0,d.jsxs)("div",{className:"text-sm text-green-700 space-y-2",children:[(0,d.jsx)("p",{children:m("settings.discordPrivacyDesc1")||"Discord Rich Presence only shares:"}),(0,d.jsxs)("ul",{className:"list-disc list-inside ml-4 space-y-1",children:[(0,d.jsx)("li",{children:m("settings.discordPrivacy1")||"Your current game activity (public)"}),(0,d.jsx)("li",{children:m("settings.discordPrivacy2")||"Your player level and progress (public)"}),(0,d.jsx)("li",{children:m("settings.discordPrivacy3")||"Multiplayer room codes (for joining)"})]}),(0,d.jsx)("p",{className:"mt-2",children:m("settings.discordPrivacyDesc2")||"No personal information or save data is shared with Discord."})]})]})]})]})]})})}}};