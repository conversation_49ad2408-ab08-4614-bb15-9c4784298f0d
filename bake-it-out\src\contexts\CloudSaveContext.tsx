'use client'

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react'
import { useLanguage } from './LanguageContext'

interface CloudSave {
  id: string
  saveName: string
  description: string
  saveType: 'manual' | 'auto' | 'checkpoint'
  metadata: {
    version: number
    gameVersion: string
    platform: string
    level: number
    money: number
    playTime: number
  }
  createdAt: string
  updatedAt: string
  size: number
}

interface User {
  id: string
  username: string
  email: string
  profile: {
    displayName: string
    avatar: string
    preferredLanguage: string
  }
  gameStats: {
    totalPlayTime: number
    highestLevel: number
    totalMoney: number
    achievementsUnlocked: number
    gamesPlayed: number
  }
}

interface CloudSaveContextType {
  // Authentication
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  
  // Auth methods
  login: (username: string, password: string) => Promise<boolean>
  register: (username: string, email: string, password: string) => Promise<boolean>
  logout: () => Promise<void>
  
  // Cloud saves
  cloudSaves: CloudSave[]
  isLoadingSaves: boolean
  
  // Save methods
  saveToCloud: (saveName: string, gameData: any, saveType?: string) => Promise<string | null>
  loadFromCloud: (saveId: string) => Promise<any | null>
  deleteCloudSave: (saveId: string) => Promise<boolean>
  refreshCloudSaves: () => Promise<void>
  
  // Sync
  autoSyncEnabled: boolean
  setAutoSyncEnabled: (enabled: boolean) => void
  lastSyncTime: Date | null
  syncStatus: 'idle' | 'syncing' | 'error' | 'success'
  
  // Error handling
  error: string | null
  clearError: () => void
}

const CloudSaveContext = createContext<CloudSaveContextType | undefined>(undefined)

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api'

export function CloudSaveProvider({ children }: { children: React.ReactNode }) {
  const { t } = useLanguage()
  
  // State
  const [user, setUser] = useState<User | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [cloudSaves, setCloudSaves] = useState<CloudSave[]>([])
  const [isLoadingSaves, setIsLoadingSaves] = useState(false)
  const [autoSyncEnabled, setAutoSyncEnabled] = useState(true)
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null)
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'error' | 'success'>('idle')
  const [error, setError] = useState<string | null>(null)

  // Helper function to get auth headers
  const getAuthHeaders = () => {
    const token = localStorage.getItem('accessToken')
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    }
  }

  // API call wrapper with error handling
  const apiCall = async (endpoint: string, options: RequestInit = {}) => {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        ...options,
        headers: {
          ...getAuthHeaders(),
          ...options.headers
        }
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'API request failed')
      }

      return data
    } catch (error) {
      console.error('API call failed:', error)
      throw error
    }
  }

  // Authentication methods
  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true)
      setError(null)

      const data = await apiCall('/auth/login', {
        method: 'POST',
        body: JSON.stringify({ username, password })
      })

      // Store tokens
      localStorage.setItem('accessToken', data.tokens.accessToken)
      localStorage.setItem('refreshToken', data.tokens.refreshToken)

      setUser(data.user)
      setIsAuthenticated(true)
      
      // Load cloud saves
      await refreshCloudSaves()

      return true
    } catch (error: any) {
      setError(error.message)
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (username: string, email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true)
      setError(null)

      const data = await apiCall('/auth/register', {
        method: 'POST',
        body: JSON.stringify({ username, email, password })
      })

      // Store tokens
      localStorage.setItem('accessToken', data.tokens.accessToken)
      localStorage.setItem('refreshToken', data.tokens.refreshToken)

      setUser(data.user)
      setIsAuthenticated(true)

      return true
    } catch (error: any) {
      setError(error.message)
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async (): Promise<void> => {
    try {
      await apiCall('/auth/logout', { method: 'POST' })
    } catch (error) {
      // Continue with logout even if API call fails
      console.error('Logout API call failed:', error)
    } finally {
      // Clear local state
      localStorage.removeItem('accessToken')
      localStorage.removeItem('refreshToken')
      setUser(null)
      setIsAuthenticated(false)
      setCloudSaves([])
      setError(null)
    }
  }

  // Cloud save methods
  const saveToCloud = async (saveName: string, gameData: any, saveType: string = 'manual'): Promise<string | null> => {
    try {
      setSyncStatus('syncing')
      setError(null)

      const data = await apiCall('/saves', {
        method: 'POST',
        body: JSON.stringify({
          saveName,
          gameData,
          saveType,
          metadata: {
            gameVersion: '1.0.0',
            platform: 'web',
            deviceInfo: navigator.userAgent
          }
        })
      })

      // Refresh saves list
      await refreshCloudSaves()
      
      setSyncStatus('success')
      setLastSyncTime(new Date())
      
      return data.save.id
    } catch (error: any) {
      setError(error.message)
      setSyncStatus('error')
      return null
    }
  }

  const loadFromCloud = async (saveId: string): Promise<any | null> => {
    try {
      setSyncStatus('syncing')
      setError(null)

      const data = await apiCall(`/saves/${saveId}`)
      
      setSyncStatus('success')
      setLastSyncTime(new Date())
      
      return data.save.gameData
    } catch (error: any) {
      setError(error.message)
      setSyncStatus('error')
      return null
    }
  }

  const deleteCloudSave = async (saveId: string): Promise<boolean> => {
    try {
      setError(null)

      await apiCall(`/saves/${saveId}`, { method: 'DELETE' })
      
      // Remove from local list
      setCloudSaves(prev => prev.filter(save => save.id !== saveId))
      
      return true
    } catch (error: any) {
      setError(error.message)
      return false
    }
  }

  const refreshCloudSaves = async (): Promise<void> => {
    if (!isAuthenticated) return

    try {
      setIsLoadingSaves(true)
      setError(null)

      const data = await apiCall('/saves')
      setCloudSaves(data.saves)
    } catch (error: any) {
      setError(error.message)
    } finally {
      setIsLoadingSaves(false)
    }
  }

  // Auto-sync functionality
  useEffect(() => {
    if (!autoSyncEnabled || !isAuthenticated) return

    const interval = setInterval(() => {
      refreshCloudSaves()
    }, 5 * 60 * 1000) // Sync every 5 minutes

    return () => clearInterval(interval)
  }, [autoSyncEnabled, isAuthenticated])

  // Initialize authentication on mount
  useEffect(() => {
    const initAuth = async () => {
      const token = localStorage.getItem('accessToken')
      
      if (token) {
        try {
          const data = await apiCall('/auth/verify')
          setUser(data.user)
          setIsAuthenticated(true)
          await refreshCloudSaves()
        } catch (error) {
          // Token is invalid, clear it
          localStorage.removeItem('accessToken')
          localStorage.removeItem('refreshToken')
        }
      }
      
      setIsLoading(false)
    }

    initAuth()
  }, [])

  const clearError = () => setError(null)

  const value: CloudSaveContextType = {
    // Authentication
    user,
    isAuthenticated,
    isLoading,
    
    // Auth methods
    login,
    register,
    logout,
    
    // Cloud saves
    cloudSaves,
    isLoadingSaves,
    
    // Save methods
    saveToCloud,
    loadFromCloud,
    deleteCloudSave,
    refreshCloudSaves,
    
    // Sync
    autoSyncEnabled,
    setAutoSyncEnabled,
    lastSyncTime,
    syncStatus,
    
    // Error handling
    error,
    clearError
  }

  return (
    <CloudSaveContext.Provider value={value}>
      {children}
    </CloudSaveContext.Provider>
  )
}

export function useCloudSave() {
  const context = useContext(CloudSaveContext)
  if (context === undefined) {
    throw new Error('useCloudSave must be used within a CloudSaveProvider')
  }
  return context
}
