'use client'

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react'
import { useLanguage } from './LanguageContext'
import { supabase } from '@/lib/supabase'
import type { User as SupabaseUser } from '@supabase/supabase-js'

interface CloudSave {
  id: string
  saveName: string
  description: string
  saveType: 'manual' | 'auto' | 'checkpoint'
  metadata: {
    version: number
    gameVersion: string
    platform: string
    level: number
    money: number
    playTime: number
  }
  createdAt: string
  updatedAt: string
  size: number
}

interface User {
  id: string
  username: string
  email: string
  profile: {
    displayName: string
    avatar: string
    preferredLanguage: string
  }
  gameStats: {
    totalPlayTime: number
    highestLevel: number
    totalMoney: number
    achievementsUnlocked: number
    gamesPlayed: number
  }
}

interface CloudSaveContextType {
  // Authentication
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  
  // Auth methods
  login: (username: string, password: string) => Promise<boolean>
  register: (username: string, email: string, password: string) => Promise<boolean>
  logout: () => Promise<void>
  
  // Cloud saves
  cloudSaves: CloudSave[]
  isLoadingSaves: boolean
  
  // Save methods
  saveToCloud: (saveName: string, gameData: any, saveType?: string) => Promise<string | null>
  loadFromCloud: (saveId: string) => Promise<any | null>
  deleteCloudSave: (saveId: string) => Promise<boolean>
  refreshCloudSaves: () => Promise<void>
  
  // Sync
  autoSyncEnabled: boolean
  setAutoSyncEnabled: (enabled: boolean) => void
  lastSyncTime: Date | null
  syncStatus: 'idle' | 'syncing' | 'error' | 'success'
  
  // Error handling
  error: string | null
  clearError: () => void
}

const CloudSaveContext = createContext<CloudSaveContextType | undefined>(undefined)

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api'

export function CloudSaveProvider({ children }: { children: React.ReactNode }) {
  const { t } = useLanguage()
  
  // State
  const [user, setUser] = useState<User | null>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [cloudSaves, setCloudSaves] = useState<CloudSave[]>([])
  const [isLoadingSaves, setIsLoadingSaves] = useState(false)
  const [autoSyncEnabled, setAutoSyncEnabled] = useState(true)
  const [lastSyncTime, setLastSyncTime] = useState<Date | null>(null)
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'error' | 'success'>('idle')
  const [error, setError] = useState<string | null>(null)

  // Helper function to get auth headers
  const getAuthHeaders = () => {
    const token = localStorage.getItem('accessToken')
    return {
      'Content-Type': 'application/json',
      ...(token && { Authorization: `Bearer ${token}` })
    }
  }

  // API call wrapper with error handling
  const apiCall = async (endpoint: string, options: RequestInit = {}) => {
    try {
      const response = await fetch(`${API_BASE_URL}${endpoint}`, {
        ...options,
        headers: {
          ...getAuthHeaders(),
          ...options.headers
        }
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || 'API request failed')
      }

      return data
    } catch (error) {
      console.error('API call failed:', error)
      throw error
    }
  }

  // Authentication methods
  const login = async (username: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true)
      setError(null)

      // Try to sign in with email/password
      const { data, error } = await supabase.auth.signInWithPassword({
        email: username.includes('@') ? username : `${username}@bakeitout.local`,
        password: password
      })

      if (error) {
        throw new Error(error.message)
      }

      if (data.user) {
        // Fetch user profile
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', data.user.id)
          .single()

        if (profileError && profileError.code !== 'PGRST116') {
          throw new Error(profileError.message)
        }

        // Fetch user stats
        const { data: stats, error: statsError } = await supabase
          .from('game_stats')
          .select('*')
          .eq('user_id', data.user.id)
          .single()

        const user: User = {
          id: data.user.id,
          username: profile?.username || data.user.email?.split('@')[0] || 'Player',
          email: data.user.email || '',
          profile: {
            displayName: profile?.display_name || profile?.username || 'Player',
            avatar: profile?.avatar_url || '',
            preferredLanguage: profile?.preferred_language || 'en'
          },
          gameStats: {
            totalPlayTime: stats?.total_play_time || 0,
            highestLevel: stats?.highest_level || 1,
            totalMoney: stats?.total_money || 0,
            achievementsUnlocked: stats?.achievements_unlocked || 0,
            gamesPlayed: stats?.games_played || 0
          }
        }

        setUser(user)
        setIsAuthenticated(true)

        // Load cloud saves
        await refreshCloudSaves()

        return true
      }

      return false
    } catch (error: any) {
      setError(error.message)
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const register = async (username: string, email: string, password: string): Promise<boolean> => {
    try {
      setIsLoading(true)
      setError(null)

      // Sign up with Supabase
      const { data, error } = await supabase.auth.signUp({
        email: email,
        password: password,
        options: {
          data: {
            username: username,
            display_name: username
          }
        }
      })

      if (error) {
        throw new Error(error.message)
      }

      if (data.user) {
        // User created successfully
        // Note: User will need to confirm email if email confirmation is enabled
        setIsAuthenticated(false) // Will be true after email confirmation
        return true
      }

      return false
    } catch (error: any) {
      setError(error.message)
      return false
    } finally {
      setIsLoading(false)
    }
  }

  const logout = async (): Promise<void> => {
    try {
      await supabase.auth.signOut()
    } catch (error) {
      // Continue with logout even if API call fails
      console.error('Logout failed:', error)
    } finally {
      // Clear local state
      setUser(null)
      setIsAuthenticated(false)
      setCloudSaves([])
      setSyncStatus('idle')
      setError(null)
    }
  }

  // Cloud save methods
  const saveToCloud = async (saveName: string, gameData: any, saveType: string = 'manual'): Promise<string | null> => {
    try {
      setSyncStatus('syncing')
      setError(null)

      if (!user) {
        throw new Error('User not authenticated')
      }

      const saveData = {
        user_id: user.id,
        save_name: saveName,
        description: `${saveType} save - Level ${gameData.player?.level || 1}`,
        save_type: saveType,
        game_data: gameData,
        metadata: {
          gameVersion: '1.1.0',
          platform: 'web',
          deviceInfo: navigator.userAgent,
          level: gameData.player?.level || 1,
          money: gameData.player?.money || 0,
          playTime: gameData.player?.playTime || 0
        },
        game_version: '1.1.0',
        platform: 'web',
        device_info: navigator.userAgent
      }

      const { data, error } = await supabase
        .from('game_saves')
        .upsert(saveData, {
          onConflict: 'user_id,save_name',
          ignoreDuplicates: false
        })
        .select()
        .single()

      if (error) {
        throw new Error(error.message)
      }

      // Log the sync operation
      await supabase.from('sync_logs').insert({
        user_id: user.id,
        save_id: data.id,
        action: 'upload',
        status: 'success',
        device_info: navigator.userAgent
      })

      // Refresh saves list
      await refreshCloudSaves()

      setSyncStatus('success')
      setLastSyncTime(new Date())

      return data.id
    } catch (error: any) {
      setError(error.message)
      setSyncStatus('error')

      // Log the error
      if (user) {
        await supabase.from('sync_logs').insert({
          user_id: user.id,
          action: 'upload',
          status: 'error',
          error_message: error.message,
          device_info: navigator.userAgent
        })
      }

      return null
    }
  }

  const loadFromCloud = async (saveId: string): Promise<any | null> => {
    try {
      setSyncStatus('syncing')
      setError(null)

      if (!user) {
        throw new Error('User not authenticated')
      }

      const { data, error } = await supabase
        .from('game_saves')
        .select('game_data')
        .eq('id', saveId)
        .eq('user_id', user.id)
        .single()

      if (error) {
        throw new Error(error.message)
      }

      // Log the sync operation
      await supabase.from('sync_logs').insert({
        user_id: user.id,
        save_id: saveId,
        action: 'download',
        status: 'success',
        device_info: navigator.userAgent
      })

      setSyncStatus('success')
      setLastSyncTime(new Date())

      return data.game_data
    } catch (error: any) {
      setError(error.message)
      setSyncStatus('error')

      // Log the error
      if (user) {
        await supabase.from('sync_logs').insert({
          user_id: user.id,
          save_id: saveId,
          action: 'download',
          status: 'error',
          error_message: error.message,
          device_info: navigator.userAgent
        })
      }

      return null
    }
  }

  const deleteCloudSave = async (saveId: string): Promise<boolean> => {
    try {
      setError(null)

      if (!user) {
        throw new Error('User not authenticated')
      }

      const { error } = await supabase
        .from('game_saves')
        .delete()
        .eq('id', saveId)
        .eq('user_id', user.id)

      if (error) {
        throw new Error(error.message)
      }

      // Log the sync operation
      await supabase.from('sync_logs').insert({
        user_id: user.id,
        save_id: saveId,
        action: 'delete',
        status: 'success',
        device_info: navigator.userAgent
      })

      // Remove from local list
      setCloudSaves(prev => prev.filter(save => save.id !== saveId))

      return true
    } catch (error: any) {
      setError(error.message)

      // Log the error
      if (user) {
        await supabase.from('sync_logs').insert({
          user_id: user.id,
          save_id: saveId,
          action: 'delete',
          status: 'error',
          error_message: error.message,
          device_info: navigator.userAgent
        })
      }

      return false
    }
  }

  const refreshCloudSaves = async (): Promise<void> => {
    if (!isAuthenticated || !user) return

    try {
      setIsLoadingSaves(true)
      setError(null)

      const { data, error } = await supabase
        .from('save_metadata')
        .select('*')
        .eq('user_id', user.id)
        .order('updated_at', { ascending: false })

      if (error) {
        throw new Error(error.message)
      }

      // Transform Supabase data to CloudSave format
      const cloudSaves: CloudSave[] = data.map(save => ({
        id: save.id,
        saveName: save.save_name,
        description: save.description || '',
        saveType: save.save_type as 'manual' | 'auto' | 'checkpoint',
        metadata: {
          version: save.version,
          gameVersion: save.game_version,
          platform: save.platform,
          level: save.metadata?.level || 1,
          money: save.metadata?.money || 0,
          playTime: save.metadata?.playTime || 0
        },
        createdAt: save.created_at,
        updatedAt: save.updated_at,
        size: save.size_bytes
      }))

      setCloudSaves(cloudSaves)
    } catch (error: any) {
      setError(error.message)
      setCloudSaves([])
    } finally {
      setIsLoadingSaves(false)
    }
  }

  // Auto-sync functionality
  useEffect(() => {
    if (!autoSyncEnabled || !isAuthenticated) return

    const interval = setInterval(() => {
      refreshCloudSaves()
    }, 5 * 60 * 1000) // Sync every 5 minutes

    return () => clearInterval(interval)
  }, [autoSyncEnabled, isAuthenticated])

  // Initialize Supabase authentication on mount
  useEffect(() => {
    const initAuth = async () => {
      try {
        // Get initial session
        const { data: { session } } = await supabase.auth.getSession()

        if (session?.user) {
          // Fetch user profile
          const { data: profile } = await supabase
            .from('profiles')
            .select('*')
            .eq('id', session.user.id)
            .single()

          // Fetch user stats
          const { data: stats } = await supabase
            .from('game_stats')
            .select('*')
            .eq('user_id', session.user.id)
            .single()

          const user: User = {
            id: session.user.id,
            username: profile?.username || session.user.email?.split('@')[0] || 'Player',
            email: session.user.email || '',
            profile: {
              displayName: profile?.display_name || profile?.username || 'Player',
              avatar: profile?.avatar_url || '',
              preferredLanguage: profile?.preferred_language || 'en'
            },
            gameStats: {
              totalPlayTime: stats?.total_play_time || 0,
              highestLevel: stats?.highest_level || 1,
              totalMoney: stats?.total_money || 0,
              achievementsUnlocked: stats?.achievements_unlocked || 0,
              gamesPlayed: stats?.games_played || 0
            }
          }

          setUser(user)
          setIsAuthenticated(true)
          await refreshCloudSaves()
        }
      } catch (error) {
        console.error('Auth initialization failed:', error)
      } finally {
        setIsLoading(false)
      }
    }

    initAuth()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' && session?.user) {
          // User signed in - handled by login method
        } else if (event === 'SIGNED_OUT') {
          // User signed out
          setUser(null)
          setIsAuthenticated(false)
          setCloudSaves([])
          setSyncStatus('idle')
          setError(null)
        }
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const clearError = () => setError(null)

  const value: CloudSaveContextType = {
    // Authentication
    user,
    isAuthenticated,
    isLoading,
    
    // Auth methods
    login,
    register,
    logout,
    
    // Cloud saves
    cloudSaves,
    isLoadingSaves,
    
    // Save methods
    saveToCloud,
    loadFromCloud,
    deleteCloudSave,
    refreshCloudSaves,
    
    // Sync
    autoSyncEnabled,
    setAutoSyncEnabled,
    lastSyncTime,
    syncStatus,
    
    // Error handling
    error,
    clearError
  }

  return (
    <CloudSaveContext.Provider value={value}>
      {children}
    </CloudSaveContext.Provider>
  )
}

export function useCloudSave() {
  const context = useContext(CloudSaveContext)
  if (context === undefined) {
    throw new Error('useCloudSave must be used within a CloudSaveProvider')
  }
  return context
}
