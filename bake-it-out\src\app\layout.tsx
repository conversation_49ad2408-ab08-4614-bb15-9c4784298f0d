import type { Metada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { LanguageProvider } from "@/contexts/LanguageContext";
import { MultiplayerProvider } from "@/contexts/MultiplayerContext";
import { DiscordRPCProvider } from "@/contexts/DiscordRPCContext";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Bake It Out - Bakery Management Game",
  description: "Master the art of bakery management in this engaging multiplayer game with Czech and English support",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>🧁</text></svg>" />
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Prevent hydration mismatches by ensuring consistent initial state
              if (typeof window !== 'undefined') {
                window.__HYDRATION_SAFE__ = true;
              }
            `,
          }}
        />
      </head>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
        suppressHydrationWarning
      >
        <LanguageProvider>
          <MultiplayerProvider>
            <DiscordRPCProvider>
              {children}
            </DiscordRPCProvider>
          </MultiplayerProvider>
        </LanguageProvider>
      </body>
    </html>
  );
}
