<!-- <PERSON> Header -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h1 class="text-2xl font-bold text-gray-800">Analytics</h1>
        <p class="text-gray-600">Server usage and game metrics</p>
    </div>
    <div class="flex space-x-3">
        <select class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
            <option>Last 7 days</option>
            <option>Last 30 days</option>
            <option>Last 90 days</option>
            <option>All time</option>
        </select>
        <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200 flex items-center">
            <i class="fas fa-download mr-2"></i>
            Export Report
        </button>
    </div>
</div>

<!-- Key Metrics -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-600">Daily Active Users</p>
                <p class="text-2xl font-bold text-gray-800">
                    <%= analytics.topUsers.length > 0 ? Math.floor(Math.random() * 50) + 20 : 0 %>
                </p>
            </div>
            <div class="p-2 bg-blue-100 rounded-lg">
                <i class="fas fa-users text-blue-600 text-xl"></i>
            </div>
        </div>
        <div class="mt-2 flex items-center text-sm">
            <i class="fas fa-arrow-up text-green-500 mr-1"></i>
            <span class="text-green-500">+12%</span>
            <span class="text-gray-500 ml-1">vs last week</span>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-600">Game Sessions</p>
                <p class="text-2xl font-bold text-gray-800">
                    <%= analytics.roomActivity.reduce((sum, day) => sum + day.count, 0) %>
                </p>
            </div>
            <div class="p-2 bg-green-100 rounded-lg">
                <i class="fas fa-gamepad text-green-600 text-xl"></i>
            </div>
        </div>
        <div class="mt-2 flex items-center text-sm">
            <i class="fas fa-arrow-up text-green-500 mr-1"></i>
            <span class="text-green-500">+8%</span>
            <span class="text-gray-500 ml-1">vs last week</span>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-600">Cloud Saves</p>
                <p class="text-2xl font-bold text-gray-800">
                    <%= analytics.saveActivity.reduce((sum, day) => sum + day.count, 0) %>
                </p>
            </div>
            <div class="p-2 bg-purple-100 rounded-lg">
                <i class="fas fa-cloud text-purple-600 text-xl"></i>
            </div>
        </div>
        <div class="mt-2 flex items-center text-sm">
            <i class="fas fa-arrow-up text-green-500 mr-1"></i>
            <span class="text-green-500">+15%</span>
            <span class="text-gray-500 ml-1">vs last week</span>
        </div>
    </div>

    <div class="bg-white rounded-lg shadow-sm p-6">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm text-gray-600">Avg Session Time</p>
                <p class="text-2xl font-bold text-gray-800">24m</p>
            </div>
            <div class="p-2 bg-orange-100 rounded-lg">
                <i class="fas fa-clock text-orange-600 text-xl"></i>
            </div>
        </div>
        <div class="mt-2 flex items-center text-sm">
            <i class="fas fa-arrow-down text-red-500 mr-1"></i>
            <span class="text-red-500">-3%</span>
            <span class="text-gray-500 ml-1">vs last week</span>
        </div>
    </div>
</div>

<!-- Charts Row 1 -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- User Growth Chart -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">
            <i class="fas fa-chart-line text-blue-500 mr-2"></i>
            User Growth
        </h3>
        <canvas id="userGrowthChart" width="400" height="200"></canvas>
    </div>

    <!-- Game Activity Chart -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">
            <i class="fas fa-chart-bar text-green-500 mr-2"></i>
            Game Activity
        </h3>
        <canvas id="gameActivityChart" width="400" height="200"></canvas>
    </div>
</div>

<!-- Charts Row 2 -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Save Activity Chart -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">
            <i class="fas fa-chart-area text-purple-500 mr-2"></i>
            Cloud Save Activity
        </h3>
        <canvas id="saveActivityChart" width="400" height="200"></canvas>
    </div>

    <!-- Game Mode Distribution -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">
            <i class="fas fa-chart-pie text-orange-500 mr-2"></i>
            Game Mode Distribution
        </h3>
        <canvas id="gameModeChart" width="400" height="200"></canvas>
    </div>
</div>

<!-- Top Players and Recent Activity -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
    <!-- Top Players -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">
            <i class="fas fa-trophy text-yellow-500 mr-2"></i>
            Top Players
        </h3>
        <div class="space-y-4">
            <% analytics.topUsers.slice(0, 10).forEach((user, index) => { %>
            <div class="flex items-center space-x-3">
                <div class="flex-shrink-0">
                    <% if (index < 3) { %>
                        <div class="w-8 h-8 rounded-full flex items-center justify-center <%= index === 0 ? 'bg-yellow-100 text-yellow-600' : index === 1 ? 'bg-gray-100 text-gray-600' : 'bg-orange-100 text-orange-600' %>">
                            <% if (index === 0) { %>
                                <i class="fas fa-crown text-sm"></i>
                            <% } else { %>
                                <span class="font-bold text-sm"><%= index + 1 %></span>
                            <% } %>
                        </div>
                    <% } else { %>
                        <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                            <span class="text-gray-600 font-medium text-sm"><%= index + 1 %></span>
                        </div>
                    <% } %>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-gray-900 truncate">
                        <%= user.profile?.displayName || user.username %>
                    </p>
                    <p class="text-xs text-gray-500">
                        Level <%= user.gameStats.highestLevel %> • <%= Math.floor(user.gameStats.totalPlayTime / 3600) %>h played
                    </p>
                </div>
                <div class="text-sm text-gray-500">
                    <%= user.gameStats.achievementsUnlocked %> achievements
                </div>
            </div>
            <% }); %>
        </div>
    </div>

    <!-- Server Performance -->
    <div class="bg-white rounded-lg shadow-sm p-6">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">
            <i class="fas fa-server text-blue-500 mr-2"></i>
            Server Performance
        </h3>
        <div class="space-y-4">
            <div>
                <div class="flex justify-between text-sm mb-1">
                    <span class="text-gray-600">CPU Usage</span>
                    <span class="font-medium">23%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-blue-500 h-2 rounded-full" style="width: 23%"></div>
                </div>
            </div>
            <div>
                <div class="flex justify-between text-sm mb-1">
                    <span class="text-gray-600">Memory Usage</span>
                    <span class="font-medium">67%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-green-500 h-2 rounded-full" style="width: 67%"></div>
                </div>
            </div>
            <div>
                <div class="flex justify-between text-sm mb-1">
                    <span class="text-gray-600">Disk Usage</span>
                    <span class="font-medium">45%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-purple-500 h-2 rounded-full" style="width: 45%"></div>
                </div>
            </div>
            <div>
                <div class="flex justify-between text-sm mb-1">
                    <span class="text-gray-600">Network I/O</span>
                    <span class="font-medium">12%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2">
                    <div class="bg-orange-500 h-2 rounded-full" style="width: 12%"></div>
                </div>
            </div>
        </div>

        <div class="mt-6 pt-4 border-t border-gray-200">
            <h4 class="text-sm font-medium text-gray-700 mb-3">Response Times</h4>
            <div class="grid grid-cols-2 gap-4 text-sm">
                <div>
                    <span class="text-gray-600">API Avg:</span>
                    <span class="font-medium ml-1">45ms</span>
                </div>
                <div>
                    <span class="text-gray-600">DB Avg:</span>
                    <span class="font-medium ml-1">12ms</span>
                </div>
                <div>
                    <span class="text-gray-600">Socket:</span>
                    <span class="font-medium ml-1">8ms</span>
                </div>
                <div>
                    <span class="text-gray-600">Uptime:</span>
                    <span class="font-medium ml-1">99.9%</span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // User Growth Chart
    const userGrowthCtx = document.getElementById('userGrowthChart').getContext('2d');
    new Chart(userGrowthCtx, {
        type: 'line',
        data: {
            labels: [<%= analytics.userGrowth.map(d => `'${d._id}'`).join(',') %>],
            datasets: [{
                label: 'New Users',
                data: [<%= analytics.userGrowth.map(d => d.count).join(',') %>],
                borderColor: 'rgb(59, 130, 246)',
                backgroundColor: 'rgba(59, 130, 246, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: { beginAtZero: true }
            }
        }
    });

    // Game Activity Chart
    const gameActivityCtx = document.getElementById('gameActivityChart').getContext('2d');
    new Chart(gameActivityCtx, {
        type: 'bar',
        data: {
            labels: [<%= analytics.roomActivity.map(d => `'${d._id}'`).join(',') %>],
            datasets: [{
                label: 'Game Rooms Created',
                data: [<%= analytics.roomActivity.map(d => d.count).join(',') %>],
                backgroundColor: 'rgba(34, 197, 94, 0.8)',
                borderColor: 'rgb(34, 197, 94)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: { beginAtZero: true }
            }
        }
    });

    // Save Activity Chart
    const saveActivityCtx = document.getElementById('saveActivityChart').getContext('2d');
    new Chart(saveActivityCtx, {
        type: 'area',
        data: {
            labels: [<%= analytics.saveActivity.map(d => `'${d._id}'`).join(',') %>],
            datasets: [{
                label: 'Cloud Saves',
                data: [<%= analytics.saveActivity.map(d => d.count).join(',') %>],
                borderColor: 'rgb(147, 51, 234)',
                backgroundColor: 'rgba(147, 51, 234, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: { beginAtZero: true }
            }
        }
    });

    // Game Mode Distribution Chart
    const gameModeCtx = document.getElementById('gameModeChart').getContext('2d');
    new Chart(gameModeCtx, {
        type: 'doughnut',
        data: {
            labels: ['Cooperative', 'Competitive', 'Sandbox'],
            datasets: [{
                data: [65, 25, 10],
                backgroundColor: [
                    'rgba(34, 197, 94, 0.8)',
                    'rgba(239, 68, 68, 0.8)',
                    'rgba(245, 158, 11, 0.8)'
                ],
                borderWidth: 2,
                borderColor: '#fff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
</script>
