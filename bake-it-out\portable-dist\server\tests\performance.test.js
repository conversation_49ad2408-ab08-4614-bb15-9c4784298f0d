const request = require('supertest');
const app = require('../src/index');
const User = require('../src/models/User');
const CloudSave = require('../src/models/CloudSave');

describe('Performance Tests', () => {
  let authTokens = [];
  let userIds = [];

  // Create test users and auth tokens
  beforeAll(async () => {
    // Clean up
    await User.deleteMany({ username: /^perftest/ });
    await CloudSave.deleteMany({});

    // Create multiple test users
    for (let i = 0; i < 5; i++) {
      const response = await request(app)
        .post('/api/auth/register')
        .send({
          username: `perftest${i}`,
          email: `perftest${i}@example.com`,
          password: 'password123'
        });

      authTokens.push(response.body.tokens.accessToken);
      userIds.push(response.body.user.id);
    }
  });

  afterAll(async () => {
    // Clean up
    await User.deleteMany({ username: /^perftest/ });
    await CloudSave.deleteMany({});
  });

  describe('Save Creation Performance', () => {
    test('should handle multiple concurrent save creations', async () => {
      const startTime = Date.now();
      
      // Create large game data
      const largeGameData = {
        player: {
          name: 'Performance Test Player',
          level: 50,
          money: 100000,
          playTime: 36000,
          achievements: Array(100).fill().map((_, i) => ({
            id: `achievement_${i}`,
            name: `Achievement ${i}`,
            completed: true,
            completedAt: new Date()
          }))
        },
        equipment: Array(50).fill().map((_, i) => ({
          id: `equipment_${i}`,
          name: `Equipment ${i}`,
          level: Math.floor(Math.random() * 10) + 1,
          type: 'oven',
          isActive: false
        })),
        inventory: Array(20).fill().map((_, i) => ({
          name: `Ingredient ${i}`,
          quantity: Math.floor(Math.random() * 100),
          cost: Math.floor(Math.random() * 50) + 1,
          icon: '🧄'
        })),
        orders: Array(10).fill().map((_, i) => ({
          id: `order_${i}`,
          customerName: `Customer ${i}`,
          items: [`Item ${i}`],
          timeLimit: 300,
          reward: 100,
          status: 'completed'
        })),
        bakeries: Array(3).fill().map((_, i) => ({
          id: `bakery_${i}`,
          name: `Bakery ${i}`,
          location: `Location ${i}`,
          level: Math.floor(Math.random() * 5) + 1
        }))
      };

      // Create saves concurrently
      const promises = authTokens.map((token, index) =>
        request(app)
          .post('/api/saves')
          .set('Authorization', `Bearer ${token}`)
          .send({
            saveName: `Performance Test Save ${index}`,
            description: 'Large save file for performance testing',
            gameData: largeGameData,
            saveType: 'manual'
          })
      );

      const responses = await Promise.all(promises);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(201);
        expect(response.body.save).toBeDefined();
      });

      // Should complete within reasonable time (5 seconds for 5 concurrent saves)
      expect(duration).toBeLessThan(5000);
      
      console.log(`Created ${responses.length} large saves in ${duration}ms`);
    });

    test('should handle rapid sequential saves', async () => {
      const token = authTokens[0];
      const startTime = Date.now();
      
      const gameData = {
        player: { name: 'Rapid Test', level: 1, money: 100, playTime: 0 },
        equipment: [],
        inventory: [],
        orders: []
      };

      // Create 10 saves rapidly
      const promises = [];
      for (let i = 0; i < 10; i++) {
        promises.push(
          request(app)
            .post('/api/saves')
            .set('Authorization', `Bearer ${token}`)
            .send({
              saveName: `Rapid Save ${i}`,
              gameData: gameData,
              saveType: 'auto'
            })
        );
      }

      const responses = await Promise.all(promises);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // All should succeed
      responses.forEach(response => {
        expect(response.status).toBe(201);
      });

      // Should be fast
      expect(duration).toBeLessThan(3000);
      
      console.log(`Created 10 rapid saves in ${duration}ms`);
    });
  });

  describe('Save Retrieval Performance', () => {
    test('should quickly retrieve save lists', async () => {
      const startTime = Date.now();
      
      // Get saves for all users concurrently
      const promises = authTokens.map(token =>
        request(app)
          .get('/api/saves')
          .set('Authorization', `Bearer ${token}`)
      );

      const responses = await Promise.all(promises);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // All should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.saves).toBeDefined();
      });

      // Should be very fast
      expect(duration).toBeLessThan(1000);
      
      console.log(`Retrieved save lists for ${responses.length} users in ${duration}ms`);
    });

    test('should handle large save data retrieval', async () => {
      const token = authTokens[0];
      
      // Get the first save (which should be large from previous test)
      const listResponse = await request(app)
        .get('/api/saves')
        .set('Authorization', `Bearer ${token}`);
      
      const saveId = listResponse.body.saves[0].id;
      
      const startTime = Date.now();
      
      // Retrieve full save data
      const response = await request(app)
        .get(`/api/saves/${saveId}`)
        .set('Authorization', `Bearer ${token}`);
      
      const endTime = Date.now();
      const duration = endTime - startTime;

      expect(response.status).toBe(200);
      expect(response.body.save.gameData).toBeDefined();
      
      // Should retrieve large save quickly
      expect(duration).toBeLessThan(500);
      
      console.log(`Retrieved large save data in ${duration}ms`);
    });
  });

  describe('Authentication Performance', () => {
    test('should handle concurrent login attempts', async () => {
      const startTime = Date.now();
      
      // Attempt concurrent logins
      const promises = Array(10).fill().map((_, i) =>
        request(app)
          .post('/api/auth/login')
          .send({
            username: `perftest${i % 5}`, // Use existing users
            password: 'password123'
          })
      );

      const responses = await Promise.all(promises);
      const endTime = Date.now();
      const duration = endTime - startTime;

      // All should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
        expect(response.body.tokens.accessToken).toBeDefined();
      });

      // Should handle concurrent auth quickly
      expect(duration).toBeLessThan(2000);
      
      console.log(`Handled ${responses.length} concurrent logins in ${duration}ms`);
    });
  });

  describe('Memory Usage', () => {
    test('should not leak memory during operations', async () => {
      const initialMemory = process.memoryUsage();
      
      // Perform many operations
      for (let i = 0; i < 50; i++) {
        const token = authTokens[i % authTokens.length];
        
        // Create save
        await request(app)
          .post('/api/saves')
          .set('Authorization', `Bearer ${token}`)
          .send({
            saveName: `Memory Test ${i}`,
            gameData: {
              player: { name: 'Memory Test', level: 1, money: 100, playTime: 0 },
              equipment: [],
              inventory: [],
              orders: []
            },
            saveType: 'auto'
          });
        
        // Get saves list
        await request(app)
          .get('/api/saves')
          .set('Authorization', `Bearer ${token}`);
      }
      
      // Force garbage collection if available
      if (global.gc) {
        global.gc();
      }
      
      const finalMemory = process.memoryUsage();
      const memoryIncrease = finalMemory.heapUsed - initialMemory.heapUsed;
      
      // Memory increase should be reasonable (less than 50MB)
      expect(memoryIncrease).toBeLessThan(50 * 1024 * 1024);
      
      console.log(`Memory increase: ${Math.round(memoryIncrease / 1024 / 1024)}MB`);
    });
  });

  describe('Database Performance', () => {
    test('should handle database queries efficiently', async () => {
      const token = authTokens[0];
      
      // Test pagination performance
      const startTime = Date.now();
      
      const promises = [];
      for (let page = 0; page < 5; page++) {
        promises.push(
          request(app)
            .get('/api/saves')
            .query({ skip: page * 10, limit: 10 })
            .set('Authorization', `Bearer ${token}`)
        );
      }
      
      const responses = await Promise.all(promises);
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // All should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });
      
      // Pagination should be fast
      expect(duration).toBeLessThan(1000);
      
      console.log(`Handled paginated queries in ${duration}ms`);
    });
  });

  describe('Error Handling Performance', () => {
    test('should handle errors efficiently', async () => {
      const startTime = Date.now();
      
      // Generate various error conditions
      const promises = [
        // Invalid auth
        request(app).get('/api/saves'),
        // Invalid save ID
        request(app)
          .get('/api/saves/invalid-id')
          .set('Authorization', `Bearer ${authTokens[0]}`),
        // Invalid data
        request(app)
          .post('/api/saves')
          .set('Authorization', `Bearer ${authTokens[0]}`)
          .send({ invalid: 'data' }),
        // Non-existent endpoint
        request(app)
          .get('/api/nonexistent')
          .set('Authorization', `Bearer ${authTokens[0]}`)
      ];
      
      const responses = await Promise.all(promises);
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      // Should handle errors quickly
      expect(duration).toBeLessThan(1000);
      
      // Should return appropriate error codes
      expect(responses[0].status).toBe(401); // Unauthorized
      expect(responses[1].status).toBe(400); // Bad request
      expect(responses[2].status).toBe(400); // Validation error
      expect(responses[3].status).toBe(404); // Not found
      
      console.log(`Handled error conditions in ${duration}ms`);
    });
  });
});
