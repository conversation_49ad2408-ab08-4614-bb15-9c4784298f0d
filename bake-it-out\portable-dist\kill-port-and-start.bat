@echo off
echo Bake It Out - Port Cleanup and Start
echo.

echo Checking for processes using port 3002...
netstat -ano | findstr :3002 > nul
if %errorlevel% == 0 (
    echo Found process using port 3002, attempting to kill it...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3002') do (
        echo Killing process %%a
        taskkill /PID %%a /F > nul 2>&1
    )
    echo Port 3002 cleared.
) else (
    echo Port 3002 is available.
)

echo.
echo Checking for processes using port 3001...
netstat -ano | findstr :3001 > nul
if %errorlevel% == 0 (
    echo Found process using port 3001, attempting to kill it...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :3001') do (
        echo Killing process %%a
        taskkill /PID %%a /F > nul 2>&1
    )
    echo Port 3001 cleared.
) else (
    echo Port 3001 is available.
)

echo.
echo Starting Bake It Out...
start.bat
