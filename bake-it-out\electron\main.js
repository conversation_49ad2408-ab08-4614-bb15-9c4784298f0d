const { app, <PERSON><PERSON>er<PERSON><PERSON>ow, <PERSON>u, shell, ipc<PERSON>ain, dialog } = require('electron')
const path = require('path')
const isDev = require('electron-is-dev')
const { spawn } = require('child_process')
const { createServer } = require('http')
const { Server } = require('socket.io')
const AppUpdater = require('./updater')

let mainWindow
let socketServer
let nextServer
let appUpdater

// Socket.IO server setup
function startSocketServer() {
  const httpServer = createServer()
  const io = new Server(httpServer, {
    cors: {
      origin: ["http://localhost:3000", "http://127.0.0.1:3000"],
      methods: ["GET", "POST"],
      credentials: true
    },
    transports: ['websocket', 'polling']
  })

  // In-memory storage for development
  const rooms = new Map()
  const players = new Map()

  // Utility functions
  function generateRoomId() {
    return Math.random().toString(36).substring(2, 8).toUpperCase()
  }

  function generatePlayerId() {
    return 'player_' + Math.random().toString(36).substring(2, 15)
  }

  function createRoom(roomData, hostPlayer) {
    const roomId = generateRoomId()
    const room = {
      id: roomId,
      name: roomData.name || `Room ${roomId}`,
      mode: roomData.mode || 'cooperative',
      maxPlayers: roomData.maxPlayers || 4,
      currentPlayers: 1,
      players: [hostPlayer],
      gameState: 'waiting',
      settings: {
        gameMode: roomData.gameMode || 'cooperative',
        timeLimit: roomData.timeLimit,
        targetScore: roomData.targetScore,
        difficulty: roomData.difficulty || 'medium',
        allowSpectators: roomData.allowSpectators || true
      },
      createdAt: Date.now(),
      gameData: {
        sharedResources: {
          inventory: {},
          orders: [],
          equipment: [],
          automationJobs: []
        },
        gameStats: {
          totalOrders: 0,
          totalRevenue: 0,
          totalExperience: 0,
          gameStartTime: Date.now()
        },
        events: []
      }
    }
    
    rooms.set(roomId, room)
    return room
  }

  function addPlayerToRoom(roomId, player) {
    const room = rooms.get(roomId)
    if (!room) return null
    
    if (room.currentPlayers >= room.maxPlayers) {
      return null
    }
    
    room.players.push(player)
    room.currentPlayers++
    rooms.set(roomId, room)
    return room
  }

  function removePlayerFromRoom(roomId, playerId) {
    const room = rooms.get(roomId)
    if (!room) return null
    
    room.players = room.players.filter(p => p.id !== playerId)
    room.currentPlayers--
    
    if (room.currentPlayers === 0) {
      rooms.delete(roomId)
      return null
    }
    
    if (!room.players.find(p => p.isHost)) {
      room.players[0].isHost = true
    }
    
    rooms.set(roomId, room)
    return room
  }

  function broadcastToRoom(roomId, event, data, excludeSocketId = null) {
    const room = rooms.get(roomId)
    if (!room) return
    
    room.players.forEach(player => {
      const playerSocket = players.get(player.id)
      if (playerSocket && playerSocket.id !== excludeSocketId) {
        playerSocket.emit(event, data)
      }
    })
  }

  // Socket.IO event handlers
  io.on('connection', (socket) => {
    console.log(`Player connected: ${socket.id}`)
    
    // Room management
    socket.on('create_room', (roomData) => {
      try {
        const playerId = generatePlayerId()
        const hostPlayer = {
          id: playerId,
          name: roomData.hostName || 'Host',
          avatar: roomData.hostAvatar || '👨‍🍳',
          level: roomData.hostLevel || 1,
          isHost: true,
          isReady: false,
          socketId: socket.id
        }
        
        const room = createRoom(roomData, hostPlayer)
        players.set(playerId, socket)
        socket.playerId = playerId
        socket.roomId = room.id
        socket.join(room.id)
        
        socket.emit('room_created', room)
        console.log(`Room created: ${room.id} by ${hostPlayer.name}`)
      } catch (error) {
        socket.emit('error', { code: 'CREATE_ROOM_ERROR', message: error.message })
      }
    })
    
    socket.on('join_room', (roomId, playerData) => {
      try {
        const room = rooms.get(roomId)
        if (!room) {
          socket.emit('error', { code: 'ROOM_NOT_FOUND', message: 'Room not found' })
          return
        }
        
        if (room.currentPlayers >= room.maxPlayers) {
          socket.emit('error', { code: 'ROOM_FULL', message: 'Room is full' })
          return
        }
        
        const playerId = generatePlayerId()
        const player = {
          id: playerId,
          name: playerData.name || 'Player',
          avatar: playerData.avatar || '👨‍🍳',
          level: playerData.level || 1,
          isHost: false,
          isReady: false,
          socketId: socket.id
        }
        
        const updatedRoom = addPlayerToRoom(roomId, player)
        if (!updatedRoom) {
          socket.emit('error', { code: 'JOIN_ROOM_ERROR', message: 'Failed to join room' })
          return
        }
        
        players.set(playerId, socket)
        socket.playerId = playerId
        socket.roomId = roomId
        socket.join(roomId)
        
        socket.emit('room_joined', updatedRoom, player)
        broadcastToRoom(roomId, 'player_joined', player, socket.id)
        broadcastToRoom(roomId, 'room_updated', updatedRoom, socket.id)
        
        console.log(`Player ${player.name} joined room ${roomId}`)
      } catch (error) {
        socket.emit('error', { code: 'JOIN_ROOM_ERROR', message: error.message })
      }
    })
    
    socket.on('leave_room', (roomId) => {
      try {
        if (socket.playerId && socket.roomId) {
          const updatedRoom = removePlayerFromRoom(socket.roomId, socket.playerId)
          
          socket.leave(socket.roomId)
          players.delete(socket.playerId)
          
          if (updatedRoom) {
            broadcastToRoom(socket.roomId, 'player_left', socket.playerId)
            broadcastToRoom(socket.roomId, 'room_updated', updatedRoom)
          }
          
          socket.emit('room_left', socket.roomId)
          console.log(`Player ${socket.playerId} left room ${socket.roomId}`)
          
          socket.playerId = null
          socket.roomId = null
        }
      } catch (error) {
        socket.emit('error', { code: 'LEAVE_ROOM_ERROR', message: error.message })
      }
    })
    
    // Game state synchronization
    socket.on('player_action', (action) => {
      try {
        if (!socket.roomId || !socket.playerId) return
        
        const room = rooms.get(socket.roomId)
        if (!room) return
        
        const event = {
          id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
          type: action.type,
          playerId: socket.playerId,
          data: action.data,
          timestamp: action.timestamp || Date.now()
        }
        
        room.gameData.events.push(event)
        
        if (room.gameData.events.length > 100) {
          room.gameData.events = room.gameData.events.slice(-100)
        }
        
        rooms.set(socket.roomId, room)
        broadcastToRoom(socket.roomId, 'player_action', action, socket.id)
        
        console.log(`Player action in room ${socket.roomId}:`, action.type)
      } catch (error) {
        socket.emit('error', { code: 'PLAYER_ACTION_ERROR', message: error.message })
      }
    })
    
    socket.on('send_message', (message) => {
      try {
        if (!socket.roomId) return
        broadcastToRoom(socket.roomId, 'message_received', message)
        console.log(`Message in room ${socket.roomId}: ${message.content}`)
      } catch (error) {
        socket.emit('error', { code: 'MESSAGE_ERROR', message: error.message })
      }
    })
    
    socket.on('disconnect', (reason) => {
      console.log(`Player disconnected: ${socket.id}, reason: ${reason}`)
      
      if (socket.playerId && socket.roomId) {
        const updatedRoom = removePlayerFromRoom(socket.roomId, socket.playerId)
        players.delete(socket.playerId)
        
        if (updatedRoom) {
          broadcastToRoom(socket.roomId, 'player_left', socket.playerId)
          broadcastToRoom(socket.roomId, 'room_updated', updatedRoom)
        }
      }
    })
  })

  const PORT = 3001
  httpServer.listen(PORT, () => {
    console.log(`🚀 Multiplayer server running on port ${PORT}`)
  })

  return httpServer
}

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1200,
    minHeight: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets', 'icon.png'),
    show: false,
    titleBarStyle: 'default'
  })

  // Load the app
  const startUrl = isDev 
    ? 'http://localhost:3000' 
    : `file://${path.join(__dirname, '../out/index.html')}`
  
  mainWindow.loadURL(startUrl)

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show()

    if (isDev) {
      mainWindow.webContents.openDevTools()
    }

    // Initialize auto-updater in production
    if (!isDev) {
      appUpdater = new AppUpdater(mainWindow)
    }
  })

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null
  })

  // Handle external links
  mainWindow.webContents.setWindowOpenHandler(({ url }) => {
    shell.openExternal(url)
    return { action: 'deny' }
  })
}

// App event handlers
app.whenReady().then(() => {
  // Start Socket.IO server
  socketServer = startSocketServer()
  
  // Start Next.js server in development
  if (isDev) {
    console.log('Starting Next.js development server...')
    nextServer = spawn('npm', ['run', 'dev'], {
      cwd: path.join(__dirname, '..'),
      stdio: 'inherit'
    })
  }
  
  // Create main window
  createWindow()

  // Create application menu
  const template = [
    {
      label: 'File',
      submenu: [
        {
          label: 'New Game',
          accelerator: 'CmdOrCtrl+N',
          click: () => {
            mainWindow.webContents.send('new-game')
          }
        },
        { type: 'separator' },
        {
          label: 'Exit',
          accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
          click: () => {
            app.quit()
          }
        }
      ]
    },
    {
      label: 'Game',
      submenu: [
        {
          label: 'Single Player',
          click: () => {
            mainWindow.webContents.send('single-player')
          }
        },
        {
          label: 'Multiplayer',
          click: () => {
            mainWindow.webContents.send('multiplayer')
          }
        },
        { type: 'separator' },
        {
          label: 'Settings',
          click: () => {
            mainWindow.webContents.send('settings')
          }
        }
      ]
    },
    {
      label: 'Language',
      submenu: [
        {
          label: 'English',
          type: 'radio',
          checked: true,
          click: () => {
            mainWindow.webContents.send('language-change', 'en')
          }
        },
        {
          label: 'Čeština',
          type: 'radio',
          click: () => {
            mainWindow.webContents.send('language-change', 'cs')
          }
        }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'About Bake It Out',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About Bake It Out',
              message: 'Bake It Out',
              detail: 'A multiplayer bakery management game\nVersion 1.0.0\n\nDeveloped with ❤️ using Electron, Next.js, and Socket.IO'
            })
          }
        },
        {
          label: 'Learn More',
          click: () => {
            shell.openExternal('https://github.com/your-repo/bake-it-out')
          }
        }
      ]
    }
  ]

  const menu = Menu.buildFromTemplate(template)
  Menu.setApplicationMenu(menu)

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow()
    }
  })
})

// IPC Handlers for file operations
const fs = require('fs').promises
const os = require('os')

// Get save directory path
ipcMain.handle('get-save-directory', async () => {
  const userDataPath = app.getPath('userData')
  return path.join(userDataPath, 'saves')
})

// Ensure directory exists
ipcMain.handle('ensure-directory', async (event, dirPath) => {
  try {
    await fs.mkdir(dirPath, { recursive: true })
    return true
  } catch (error) {
    console.error('Failed to create directory:', error)
    throw error
  }
})

// Write file
ipcMain.handle('write-file', async (event, filePath, content) => {
  try {
    await fs.writeFile(filePath, content, 'utf8')
    return true
  } catch (error) {
    console.error('Failed to write file:', error)
    throw error
  }
})

// Read file
ipcMain.handle('read-file', async (event, filePath) => {
  try {
    const content = await fs.readFile(filePath, 'utf8')
    return content
  } catch (error) {
    console.error('Failed to read file:', error)
    return null
  }
})

// Delete file
ipcMain.handle('delete-file', async (event, filePath) => {
  try {
    await fs.unlink(filePath)
    return true
  } catch (error) {
    console.error('Failed to delete file:', error)
    throw error
  }
})

// Copy file
ipcMain.handle('copy-file', async (event, source, destination) => {
  try {
    await fs.copyFile(source, destination)
    return true
  } catch (error) {
    console.error('Failed to copy file:', error)
    throw error
  }
})

// List files in directory
ipcMain.handle('list-files', async (event, directory, extension) => {
  try {
    const files = await fs.readdir(directory)
    const filteredFiles = extension
      ? files.filter(file => file.endsWith(extension))
      : files

    const fileStats = await Promise.all(
      filteredFiles.map(async (file) => {
        try {
          const filePath = path.join(directory, file)
          const stats = await fs.stat(filePath)
          return {
            name: file,
            size: stats.size,
            modified: stats.mtime
          }
        } catch (error) {
          return { name: file, size: 0, modified: new Date() }
        }
      })
    )

    return fileStats
  } catch (error) {
    console.error('Failed to list files:', error)
    return []
  }
})

// Show save dialog
ipcMain.handle('show-save-dialog', async (event, defaultName) => {
  try {
    const result = await dialog.showSaveDialog(mainWindow, {
      defaultPath: defaultName,
      filters: [
        { name: 'JSON Files', extensions: ['json'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    })

    return result.canceled ? null : result.filePath
  } catch (error) {
    console.error('Failed to show save dialog:', error)
    return null
  }
})

// Show open dialog
ipcMain.handle('show-open-dialog', async (event, extensions) => {
  try {
    const result = await dialog.showOpenDialog(mainWindow, {
      properties: ['openFile'],
      filters: [
        { name: 'JSON Files', extensions: extensions || ['json'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    })

    return result.canceled ? null : result.filePaths[0]
  } catch (error) {
    console.error('Failed to show open dialog:', error)
    return null
  }
})

// Quit application
ipcMain.handle('quit', () => {
  app.quit()
})

// Discord Rich Presence handlers
let discordClient = null

// Initialize Discord RPC
ipcMain.handle('init-discord-rpc', async (event, clientId) => {
  try {
    // Dynamic import of discord-rpc
    const DiscordRPC = require('discord-rpc')

    discordClient = new DiscordRPC.Client({ transport: 'ipc' })

    discordClient.on('ready', () => {
      console.log('Discord RPC connected successfully')
    })

    await discordClient.login({ clientId })
    return true
  } catch (error) {
    console.error('Failed to initialize Discord RPC:', error)
    discordClient = null
    return false
  }
})

// Update Discord RPC activity
ipcMain.handle('update-discord-rpc', async (event, activity) => {
  try {
    if (discordClient) {
      await discordClient.setActivity(activity)
      return true
    }
    return false
  } catch (error) {
    console.error('Failed to update Discord RPC:', error)
    return false
  }
})

// Clear Discord RPC activity
ipcMain.handle('clear-discord-rpc', async () => {
  try {
    if (discordClient) {
      await discordClient.clearActivity()
      return true
    }
    return false
  } catch (error) {
    console.error('Failed to clear Discord RPC:', error)
    return false
  }
})

// Disconnect Discord RPC
ipcMain.handle('disconnect-discord-rpc', async () => {
  try {
    if (discordClient) {
      await discordClient.destroy()
      discordClient = null
      return true
    }
    return false
  } catch (error) {
    console.error('Failed to disconnect Discord RPC:', error)
    return false
  }
})

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

app.on('before-quit', () => {
  // Clean up servers
  if (socketServer) {
    socketServer.close()
  }
  if (nextServer) {
    nextServer.kill()
  }
})

// Security: Prevent new window creation
app.on('web-contents-created', (event, contents) => {
  contents.on('new-window', (event, navigationUrl) => {
    event.preventDefault()
    shell.openExternal(navigationUrl)
  })
})
