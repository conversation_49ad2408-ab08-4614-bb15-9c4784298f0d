'use client'

import { But<PERSON> } from '@/components/ui/Button'
import { useLanguage } from '@/contexts/LanguageContext'

interface CreditsModalProps {
  isOpen: boolean
  onClose: () => void
}

export function CreditsModal({ isOpen, onClose }: CreditsModalProps) {
  const { t } = useLanguage()

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-orange-500 to-yellow-500 p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">ℹ️ {t('credits.title', 'About Bake It Out')}</h2>
              <p className="text-orange-100 text-sm">
                {t('credits.subtitle', 'Game Information & Credits')}
              </p>
            </div>
            <Button
              variant="secondary"
              size="sm"
              className="bg-white/20 hover:bg-white/30 text-white border-white/30"
              onClick={onClose}
            >
              ✕
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[70vh]">
          {/* Game Info */}
          <div className="text-center mb-8">
            <div className="text-6xl mb-4">🥖</div>
            <h3 className="text-3xl font-bold text-orange-800 mb-2">Bake It Out</h3>
            <p className="text-lg text-gray-600 mb-4">
              {t('credits.description', 'A multiplayer bakery management game with real-time collaboration and localization support')}
            </p>
            <div className="bg-orange-100 rounded-lg p-4 inline-block">
              <div className="text-sm text-orange-800">
                <div><strong>{t('credits.version', 'Version')}:</strong> 1.1.0</div>
                <div><strong>{t('credits.release', 'Release')}:</strong> {t('credits.releaseDate', 'January 2025')}</div>
                <div><strong>{t('credits.platform', 'Platform')}:</strong> {t('credits.platforms', 'Windows, macOS, Linux')}</div>
                <div><strong>{t('credits.contact', 'Contact')}:</strong> .avariss on Discord</div>
                <div><strong>{t('credits.facebook', 'Facebook')}:</strong> facebook.com/tazzisthebset</div>
              </div>
            </div>
          </div>

          {/* Features */}
          <div className="mb-8">
            <h4 className="text-xl font-semibold text-gray-800 mb-4">
              🌟 {t('credits.features', 'Key Features')}
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="bg-blue-50 rounded-lg p-4">
                <div className="text-2xl mb-2">👥</div>
                <h5 className="font-semibold text-blue-800">
                  {t('credits.multiplayer', 'Real-time Multiplayer')}
                </h5>
                <p className="text-sm text-blue-600">
                  {t('credits.multiplayerDesc', 'Collaborate with friends in real-time bakery management')}
                </p>
              </div>
              
              <div className="bg-green-50 rounded-lg p-4">
                <div className="text-2xl mb-2">🌍</div>
                <h5 className="font-semibold text-green-800">
                  {t('credits.localization', 'Localization')}
                </h5>
                <p className="text-sm text-green-600">
                  {t('credits.localizationDesc', 'Full support for English and Czech languages')}
                </p>
              </div>
              
              <div className="bg-purple-50 rounded-lg p-4">
                <div className="text-2xl mb-2">🏆</div>
                <h5 className="font-semibold text-purple-800">
                  {t('credits.progression', 'Progression System')}
                </h5>
                <p className="text-sm text-purple-600">
                  {t('credits.progressionDesc', 'Achievements, skills, and equipment upgrades')}
                </p>
              </div>
              
              <div className="bg-orange-50 rounded-lg p-4">
                <div className="text-2xl mb-2">🤖</div>
                <h5 className="font-semibold text-orange-800">
                  {t('credits.automation', 'Automation')}
                </h5>
                <p className="text-sm text-orange-600">
                  {t('credits.automationDesc', 'Advanced automation and efficiency systems')}
                </p>
              </div>
            </div>
          </div>

          {/* Technology Stack */}
          <div className="mb-8">
            <h4 className="text-xl font-semibold text-gray-800 mb-4">
              🔧 {t('credits.technology', 'Technology Stack')}
            </h4>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <strong className="text-gray-700">Frontend:</strong>
                  <div className="text-gray-600">Next.js, React, TypeScript</div>
                </div>
                <div>
                  <strong className="text-gray-700">Styling:</strong>
                  <div className="text-gray-600">Tailwind CSS</div>
                </div>
                <div>
                  <strong className="text-gray-700">Desktop:</strong>
                  <div className="text-gray-600">Electron</div>
                </div>
                <div>
                  <strong className="text-gray-700">Multiplayer:</strong>
                  <div className="text-gray-600">Socket.IO</div>
                </div>
                <div>
                  <strong className="text-gray-700">Database:</strong>
                  <div className="text-gray-600">Supabase</div>
                </div>
                <div>
                  <strong className="text-gray-700">i18n:</strong>
                  <div className="text-gray-600">Custom Context</div>
                </div>
              </div>
            </div>
          </div>

          {/* Development Team */}
          <div className="mb-8">
            <h4 className="text-xl font-semibold text-gray-800 mb-4">
              👨‍💻 {t('credits.team', 'Development Team')}
            </h4>
            <div className="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg p-4">
              <div className="text-center">
                <div className="text-2xl mb-2">🎮</div>
                <div className="font-semibold text-gray-800">
                  {t('credits.developedBy', 'Developed by the Bake It Out Team')}
                </div>
                <div className="text-sm text-gray-600 mt-2">
                  {t('credits.teamDesc', 'Built with passion for gaming and baking!')}
                </div>
              </div>
            </div>
          </div>

          {/* Special Thanks */}
          <div className="mb-6">
            <h4 className="text-xl font-semibold text-gray-800 mb-4">
              🙏 {t('credits.thanks', 'Special Thanks')}
            </h4>
            <div className="text-sm text-gray-600 space-y-2">
              <p>• {t('credits.thanksPlayers', 'All beta testers and players for their feedback')}</p>
              <p>• {t('credits.thanksTranslators', 'Czech language translators and cultural consultants')}</p>
              <p>• {t('credits.thanksOpenSource', 'Open source community for amazing tools and libraries')}</p>
              <p>• {t('credits.thanksBakers', 'Real bakers who inspired the game mechanics')}</p>
            </div>
          </div>

          {/* Contact & Links */}
          <div className="border-t pt-6">
            <h4 className="text-lg font-semibold text-gray-800 mb-3">
              📞 {t('credits.contact', 'Contact & Support')}
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <strong className="text-gray-700">{t('credits.website', 'Website')}:</strong>
                <div className="text-blue-600">www.bakeitout.game</div>
              </div>
              <div>
                <strong className="text-gray-700">{t('credits.support', 'Support')}:</strong>
                <div className="text-blue-600"><EMAIL></div>
              </div>
              <div>
                <strong className="text-gray-700">{t('credits.github', 'GitHub')}:</strong>
                <div className="text-blue-600">github.com/bakeitout/game</div>
              </div>
              <div>
                <strong className="text-gray-700">{t('credits.discord', 'Discord')}:</strong>
                <div className="text-blue-600">discord.gg/bakeitout</div>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 text-center">
          <Button
            variant="primary"
            onClick={onClose}
            className="bg-gradient-to-r from-orange-500 to-yellow-500 hover:from-orange-600 hover:to-yellow-600"
          >
            {t('credits.close', 'Close')}
          </Button>
        </div>
      </div>
    </div>
  )
}
