{"name": "@types/events", "version": "3.0.3", "description": "TypeScript definitions for events", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/events", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/yasupeke"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>man", "url": "https://github.com/weareoutman"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/events"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "5f4b7f7dad95eb65e392f898c2cf1af7ca4f51ada625ed70ea1a4a5ab1086b21", "typeScriptVersion": "4.5"}