@echo off
title Bake It Out - Browser Game

echo.
echo ===============================================
echo        BAKE IT OUT - Browser Game
echo ===============================================
echo.

REM Check if build exists
if not exist "out\index.html" (
    echo ERROR: Game not built yet!
    echo.
    echo Please run first:
    echo   npm install
    echo   npm run build
    echo.
    echo Or use: EASY-SETUP.bat
    echo.
    pause
    exit /b 1
)

echo Starting browser game...
echo.

REM Start server using Node.js (no npm needed)
echo Server starting at: http://localhost:3000
echo.

REM Start our custom server
start /B node serve-browser.js

REM Wait for server to start
timeout /t 3 >nul

REM Open browser
start http://localhost:3000

echo ===============================================
echo                GAME STARTED!
echo ===============================================
echo.
echo Your game is running at: http://localhost:3000
echo.
echo If browser didn't open, copy the URL above.
echo.
echo Press any key to stop the server...
pause >nul

REM Stop the server
taskkill /F /IM node.exe >nul 2>&1

echo.
echo Game server stopped. Thanks for playing!
heapause
