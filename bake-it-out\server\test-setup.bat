@echo off
title Bake It Out - Setup Test
color 0A

echo.
echo  ╔══════════════════════════════════════════════════════════════╗
echo  ║                    🧁 BAKE IT OUT 🧁                         ║
echo  ║                    Setup Validator                           ║
echo  ║                                                              ║
echo  ║  Testing if your server setup is working correctly...       ║
echo  ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 Running setup validation...
echo.

REM Run the validation script
node scripts/validate-setup.js

echo.
echo 🧪 Testing server startup...
echo.

REM Start server in background
echo Starting server for testing...
start /B node src/index.js >test-output.log 2>&1

REM Wait for server to start
echo Waiting for server to initialize...
timeout /t 8 >nul

REM Test endpoints
echo Testing server endpoints...

REM Test health endpoint
curl -s http://localhost:3001/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Health endpoint: OK
) else (
    echo ❌ Health endpoint: Failed
)

REM Test API root
curl -s http://localhost:3001/ >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ API root: OK
) else (
    echo ❌ API root: Failed
)

REM Test dashboard
curl -s http://localhost:3001/dashboard >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Dashboard: OK
) else (
    echo ❌ Dashboard: Failed
)

echo.
echo 🛑 Stopping test server...
taskkill /F /IM node.exe >nul 2>&1
timeout /t 2 >nul

echo.
echo 📋 Test Results Summary:
echo.

REM Check if test log has errors
if exist "test-output.log" (
    findstr /i "error" test-output.log >nul
    if %errorlevel% equ 0 (
        echo ⚠️ Errors found in server log:
        echo.
        type test-output.log | findstr /i "error"
        echo.
    ) else (
        echo ✅ No errors found in server startup
    )
    
    REM Clean up
    del test-output.log >nul 2>&1
)

echo.
echo 💡 If tests passed, your server is ready!
echo    Start with: npm start
echo    Dashboard: http://localhost:3001/dashboard
echo.
echo 🆘 If tests failed, try:
echo    • npm run setup-easy (automatic setup)
echo    • npm run validate (detailed validation)
echo    • Check README.md for troubleshooting
echo.

pause
