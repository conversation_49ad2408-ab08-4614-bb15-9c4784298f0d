'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'

type Language = 'en' | 'cs'

interface LanguageContextType {
  language: Language
  setLanguage: (lang: Language) => void
  t: (key: string, params?: Record<string, string>) => string
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined)

// Comprehensive translations object
const translations = {
  en: {
    // Main game
    'game.title': 'Bake It Out',
    'game.subtitle': 'Master the art of bakery management in this engaging multiplayer game. Complete orders, unlock recipes, automate your processes, and compete with friends!',
    'game.play': '🎮 Start Playing',
    'game.singlePlayer': '🎮 Single Player',
    'game.singlePlayerDesc': 'Play solo and master your bakery skills',
    'game.multiplayer': '👥 Multiplayer',
    'game.multiplayerDesc': 'Play with friends in cooperative or competitive modes',
    'game.english': '🇺🇸 English',
    'game.czech': '🇨🇿 Čeština',
    'game.home': '🏠 Home',
    'game.close': '✕ Close',
    'game.continue': '🚀 Continue Playing',

    // Menu options
    'menu.singlePlayer': 'Single Player',
    'menu.multiplayer': 'Multiplayer',
    'menu.settings': 'Settings',
    'menu.credits': 'Credits',
    'menu.exit': 'Exit',

    // Credits
    'credits.title': 'About Bake It Out',
    'credits.subtitle': 'Game Information & Credits',
    'credits.description': 'A multiplayer bakery management game with real-time collaboration and localization support.',
    'credits.version': 'Version',
    'credits.release': 'Release Date',
    'credits.releaseDate': 'January 2025',
    'credits.platform': 'Platform',
    'credits.platforms': 'Windows, macOS, Linux',
    'credits.close': 'Close',
    'credits.features': 'Features',
    'credits.multiplayer': 'Multiplayer Support',
    'credits.multiplayerDesc': 'Real-time collaboration with friends in cooperative or competitive modes.',
    'credits.localization': 'Localization',
    'credits.localizationDesc': 'Full support for English and Czech languages with easy switching.',
    'credits.progression': 'Progression System',
    'credits.progressionDesc': 'Level up your bakery, unlock recipes, and master automation.',
    'credits.automation': 'Automation',
    'credits.automationDesc': 'Advanced automation systems to optimize your bakery operations.',
    'credits.technology': 'Technology Stack',
    'credits.team': 'Development Team',
    'credits.developedBy': 'Developed by',
    'credits.teamDesc': 'Created with passion by the Bake It Out development team.',
    'credits.thanks': 'Special Thanks',
    'credits.thanksPlayers': 'All our amazing players and beta testers',
    'credits.thanksTranslators': 'Community translators for localization support',
    'credits.thanksOpenSource': 'Open source community for incredible tools and libraries',
    'credits.thanksBakers': 'Real bakers who inspired this game',
    'credits.contact': 'Contact & Support',
    'credits.website': 'Website',
    'credits.support': 'Support',
    'credits.github': 'GitHub',
    'credits.discord': 'Discord',
    'menu.newGame': 'New Game',
    'menu.continueGame': 'Continue Game',
    'menu.loadGame': 'Load Game',
    'menu.selectLanguage': 'Select Language',
    'menu.about': 'About',
    'menu.help': 'Help',
    'menu.quit': 'Quit',

    // Features
    'features.manage.title': 'Manage Your Bakery',
    'features.manage.description': 'Take orders, bake delicious goods, and serve happy customers',
    'features.levelup.title': 'Level Up & Automate',
    'features.levelup.description': 'Unlock new recipes, buy equipment, and automate your processes',
    'features.multiplayer.title': 'Play Together',
    'features.multiplayer.description': 'Cooperative and competitive multiplayer modes with friends',
    'status.development': '🚧 Game in Development - Phase 5: Multilayer Support! 🚧',

    // Game interface
    'ui.level': 'Level {{level}}',
    'ui.money': '${{amount}}',
    'ui.experience': 'XP: {{current}}/{{max}}',
    'ui.skillPoints': 'SP: {{points}}',
    'ui.achievements': '🏆 Achievements',
    'ui.skills': '🌟 Skills',
    'ui.automation': '🤖 Automation',

    // Kitchen
    'kitchen.title': '🏪 Kitchen',
    'kitchen.clickToUse': 'Click to use',
    'kitchen.making': 'Making: {{recipe}}',
    'kitchen.timeRemaining': 'Time: {{time}}',

    // Inventory
    'inventory.title': '📦 Inventory',
    'inventory.quantity': 'Qty: {{qty}}',
    'inventory.cost': '${{cost}} each',

    // Orders
    'orders.title': '📋 Orders',
    'orders.newOrder': '+ New Order',
    'orders.accept': 'Accept',
    'orders.decline': 'Decline',
    'orders.complete': 'Complete',
    'orders.inProgress': 'In Progress',
    'orders.timeLimit': 'Time: {{time}}',
    'orders.reward': '${{amount}}',
    'orders.customer': 'Customer: {{name}}',

    // Quick Actions
    'actions.title': '⚡ Quick Actions',
    'actions.buyIngredients': '🛒 Buy Ingredients',
    'actions.viewRecipes': '📖 View Recipes',
    'actions.equipmentShop': '🔧 Equipment Shop',

    // Modals
    'modal.recipes.title': '📖 Recipe Book',
    'modal.shop.title': '🛒 Ingredient Shop',
    'modal.baking.title': '🔥 {{equipment}} - Select Recipe',
    'modal.achievements.title': '🏆 Achievements',
    'modal.skills.title': '🌟 Skill Tree',
    'modal.automation.title': '🤖 Automation Control',
    'modal.equipmentShop.title': '🏪 Equipment Shop',
    'modal.settings.title': '⚙️ Settings',
    'modal.bakeries.title': '🏪 Bakery Manager',
    'modal.levelUp.title': 'Level Up!',
    'modal.levelUp.subtitle': 'You reached Level {{level}}!',

    // Recipe Modal
    'recipes.all': 'All',
    'recipes.cookies': 'Cookies',
    'recipes.cakes': 'Cakes',
    'recipes.bread': 'Bread',
    'recipes.pastries': 'Pastries',
    'recipes.ingredients': 'Ingredients:',
    'recipes.difficulty': 'Difficulty:',
    'recipes.time': 'Time:',
    'recipes.canCraft': '✅ Can Craft',
    'recipes.unlockLevel': 'Unlocked at Level {{level}}',
    'recipes.noRecipes': 'No recipes available in this category.',
    'recipes.levelUpToUnlock': 'Level up to unlock more recipes!',

    // Shop Modal
    'shop.currentStock': 'Current stock: {{quantity}}',
    'shop.buy': 'Buy',
    'shop.tooExpensive': 'Too Expensive',
    'shop.tips.title': '💡 Shopping Tips',
    'shop.tips.bulk': '• Buy ingredients in bulk to save time',
    'shop.tips.stock': '• Keep an eye on your stock levels',
    'shop.tips.rare': '• Some recipes require rare ingredients',
    'shop.tips.prices': '• Prices may vary based on availability',

    // Baking Modal
    'baking.selectRecipe': 'Select Recipe',
    'baking.noRecipes': 'No recipes available',
    'baking.noIngredients': 'You don\'t have enough ingredients to craft any recipes.',
    'baking.buyIngredients': 'Buy Ingredients',
    'baking.startBaking': '🔥 Start Baking',
    'baking.instructions': '📋 Baking Instructions for {{recipe}}',
    'baking.expectedReward': 'Expected reward: ${{amount}}',
    'baking.makesSure': 'Make sure you have all ingredients before starting!',
    'baking.inProgress': 'Baking in progress...',
    'baking.completed': 'Baking completed!',
    'baking.cancelled': 'Baking cancelled',
    'baking.timeRemaining': 'Time remaining: {{time}}',
    'baking.clickToCollect': 'Click to collect',

    // Achievements Modal
    'achievements.completed': '{{completed}} of {{total}} achievements completed',
    'achievements.overallProgress': 'Overall Progress',
    'achievements.progress': 'Progress',
    'achievements.reward': 'Reward:',
    'achievements.noAchievements': 'No achievements in this category.',

    // Skills Modal
    'skills.availablePoints': 'Available Skill Points: {{points}}',
    'skills.efficiency': 'Efficiency',
    'skills.automation': 'Automation',
    'skills.quality': 'Quality',
    'skills.business': 'Business',
    'skills.effects': 'Effects:',
    'skills.requires': 'Requires: {{requirements}}',
    'skills.requiresLevel': 'Requires Level {{level}}',
    'skills.maxed': '✅ Maxed',
    'skills.upgrade': '⬆️ Upgrade ({{cost}} SP)',
    'skills.locked': '🔒 Locked',
    'skills.noSkills': 'No skills in this category.',
    'skills.tips.title': '💡 Skill Tips',
    'skills.tips.earnPoints': '• Earn skill points by leveling up (1 point every 2 levels)',
    'skills.tips.prerequisites': '• Some skills require other skills to be unlocked first',
    'skills.tips.playstyle': '• Focus on skills that match your playstyle',
    'skills.tips.efficiency': '• Efficiency skills help with resource management',

    // Automation Modal
    'automation.masterControl': '🎛️ Master Control',
    'automation.enableAutomation': 'Enable Automation',
    'automation.autoStart': 'Auto-start Equipment',
    'automation.priorityMode': '🎯 Priority Mode',
    'automation.efficiency': 'Efficiency (Orders First)',
    'automation.profit': 'Profit (Highest Value)',
    'automation.speed': 'Speed (Fastest Recipes)',
    'automation.priorityDescription': 'How automation chooses what to bake',
    'automation.performance': '⚡ Performance',
    'automation.maxJobs': 'Max Concurrent Jobs: {{jobs}}',
    'automation.safety': '🛡️ Safety',
    'automation.stopWhenLow': 'Stop when ingredients below: {{threshold}}',
    'automation.upgrades': '💡 Automation Upgrades',
    'automation.upgradesDescription': 'Improve your automation efficiency, speed, and intelligence with these upgrades.',
    'automation.purchase': 'Purchase',
    'automation.noUpgrades': 'No upgrades available at your current level.',
    'automation.levelUpForUpgrades': 'Level up to unlock more automation upgrades!',
    'automation.automatedEquipment': 'Automated Equipment',
    'automation.activeUpgrades': 'Active Upgrades',
    'automation.automationStatus': 'Automation Status',
    'automation.equipmentStatus': '🏭 Equipment Status',
    'automation.running': 'Running',
    'automation.idle': 'Idle',
    'automation.noAutomatedEquipment': 'No automated equipment available.',
    'automation.purchaseAutoEquipment': 'Purchase auto-equipment from the shop to get started!',

    // Equipment Shop Modal
    'equipmentShop.upgradeYourBakery': 'Upgrade your bakery with professional equipment',
    'equipmentShop.basic': 'Basic',
    'equipmentShop.automated': 'Automated',
    'equipmentShop.advanced': 'Advanced',
    'equipmentShop.efficiency': 'Efficiency: {{efficiency}}x',
    'equipmentShop.automation': 'Automation:',
    'equipmentShop.unlockLevel': 'Unlock Level: {{level}}',
    'equipmentShop.purchase': '💰 Purchase',
    'equipmentShop.noEquipment': 'No equipment available in this category.',
    'equipmentShop.levelUpForEquipment': 'Level up to unlock more equipment!',
    'equipmentShop.tips.title': '💡 Equipment Tips',
    'equipmentShop.tips.automated': '• Automated equipment can run without your supervision',
    'equipmentShop.tips.efficiency': '• Higher efficiency means faster production and better quality',
    'equipmentShop.tips.conveyor': '• Conveyor belts connect equipment for seamless workflow',
    'equipmentShop.tips.advanced': '• Advanced equipment unlocks at higher levels',

    // Level Up Modal
    'levelUp.levelRewards': '🎁 Level Rewards',
    'levelUp.whatsNext': '💡 What\'s Next?',
    'levelUp.checkRecipes': '• Check out new recipes in your recipe book',
    'levelUp.visitShop': '• Visit the shop for new equipment',
    'levelUp.challengingOrders': '• Take on more challenging orders',
    'levelUp.investSkills': '• Invest in skill upgrades',

    // Settings Modal
    'settings.title': '⚙️ Settings',
    'settings.general': 'General',
    'settings.audio': 'Audio',
    'settings.graphics': 'Graphics',
    'settings.save': 'Save & Data',
    'settings.language': '🌍 Language',
    'settings.gameplay': '🎮 Gameplay',
    'settings.notifications': 'Enable Notifications',
    'settings.tutorials': 'Show Tutorials',
    'settings.animationSpeed': 'Animation Speed',
    'settings.sound': 'Sound Effects',
    'settings.music': 'Background Music',
    'settings.quality': '🎨 Graphics Quality',
    'settings.autoSave': '💾 Auto-Save',
    'settings.enableAutoSave': 'Enable Auto-Save',
    'settings.dataManagement': '📁 Data Management',
    'settings.exportSave': '📤 Export Save',
    'settings.importSave': '📥 Import Save',
    'settings.cloudSync': '☁️ Cloud Sync',
    'settings.cloudSyncDescription': 'Cloud sync allows you to save your progress online and play across multiple devices.',
    'settings.comingSoon': 'Coming Soon',

    // Bakery Manager Modal
    'bakeries.title': '🏪 Bakery Manager',
    'bakeries.subtitle': 'Manage your bakery empire',
    'bakeries.owned': 'My Bakeries',
    'bakeries.available': 'Available',
    'bakeries.current': 'Current',
    'bakeries.level': 'Level',
    'bakeries.specialization': 'Specialization',
    'bakeries.equipment': 'Equipment',
    'bakeries.orders': 'Active Orders',
    'bakeries.switchTo': 'Switch To',
    'bakeries.noOwned': 'You don\'t own any bakeries yet.',
    'bakeries.purchase': '💰 Purchase',
    'bakeries.tooExpensive': '💸 Too Expensive',
    'bakeries.allOwned': 'You own all available bakeries!',
    'bakeries.tips': '💡 Bakery Tips',
    'bakeries.tip1': 'Each bakery specializes in different products for bonus efficiency',
    'bakeries.tip2': 'Switch between bakeries to manage multiple locations',
    'bakeries.tip3': 'Specialized bakeries attract customers looking for specific items',
    'bakeries.tip4': 'Upgrade each bakery independently for maximum profit',

    // Notifications
    'notifications.orderAccepted': 'Order Accepted',
    'notifications.orderAcceptedMessage': 'You have accepted a new order!',
    'notifications.orderCompleted': 'Order Completed!',
    'notifications.orderCompletedMessage': 'You earned ${{reward}} and gained experience!',
    'notifications.orderDeclined': 'Order Declined',
    'notifications.orderDeclinedMessage': 'Order has been removed from your queue.',
    'notifications.bakeryPurchased': 'Bakery Purchased!',
    'notifications.bakeryPurchasedMessage': 'You now own {{name}}!',
    'notifications.bakerySwitched': 'Bakery Switched',
    'notifications.bakerySwitchedMessage': 'Switched to {{name}}',

    // Common buttons and actions
    'common.accept': 'Accept',
    'common.decline': 'Decline',
    'common.complete': 'Complete',
    'common.purchase': 'Purchase',
    'common.upgrade': 'Upgrade',
    'common.cancel': 'Cancel',
    'common.confirm': 'Confirm',
    'common.save': 'Save',
    'common.load': 'Load',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.yes': 'Yes',
    'common.no': 'No',
    'common.create': 'Create',
    'common.join': 'Join',
    'common.leave': 'Leave',
    'common.start': 'Start',
    'common.ready': 'Ready',
    'common.notReady': 'Not Ready',
    'common.send': 'Send',
    'common.refresh': 'Refresh',
    'common.retry': 'Retry',
    'common.reset': 'Reset',
    'common.clear': 'Clear',
    'common.apply': 'Apply',
    'common.warning': 'Warning',
    'common.info': 'Information',
    'common.success': 'Success',
    'common.error': 'Error',

    // Save/Load System
    'saveLoad.saveDesc': 'Choose a slot to save your progress',
    'saveLoad.loadDesc': 'Select a save file to load',
    'saveLoad.saveName': 'Save Name',
    'saveLoad.emptySlot': 'Empty Slot',
    'saveLoad.selectedSaveSlot': 'Selected: Slot {{slot}}',
    'saveLoad.selectedLoadSlot': 'Selected: Slot {{slot}}',
    'saveLoad.confirmOverwrite': 'Overwrite Save?',
    'saveLoad.overwriteWarning': 'This will overwrite the existing save. This action cannot be undone.',
    'saveLoad.overwrite': 'Overwrite',
    'saveLoad.fileSlots': 'File Slots',
    'saveLoad.gameSlots': 'Game Slots',
    'saveLoad.exportSave': 'Export Save',
    'saveLoad.importSave': 'Import Save',
    'saveLoad.deleteConfirm': 'Delete Save?',
    'saveLoad.deleteWarning': 'This will permanently delete this save file. This action cannot be undone.',
    'saveLoad.delete': 'Delete',

    // Game Menu
    'gameMenu.title': 'Game Menu',
    'gameMenu.subtitle': 'Manage your game',
    'gameMenu.resume': 'Resume Game',
    'gameMenu.resumeDesc': 'Continue playing',
    'gameMenu.save': 'Save Game',
    'gameMenu.saveDesc': 'Save your progress',
    'gameMenu.load': 'Load Game',
    'gameMenu.loadDesc': 'Load saved progress',
    'gameMenu.settings': 'Settings',
    'gameMenu.settingsDesc': 'Game preferences',
    'gameMenu.mainMenu': 'Main Menu',
    'gameMenu.mainMenuDesc': 'Return to main menu',
    'gameMenu.exit': 'Exit Game',
    'gameMenu.exitDesc': 'Close the application',
    'gameMenu.tip': 'Press ESC to open this menu anytime',

    // Discord Rich Presence
    'settings.discord': 'Discord',
    'settings.discordRichPresence': 'Discord Rich Presence',
    'settings.discordDescription': 'Show your current game status and activity in Discord.',
    'settings.enableDiscordRPC': 'Enable Discord Rich Presence',
    'settings.discordConnected': '✅ Connected to Discord',
    'settings.discordDisconnected': '❌ Not connected to Discord',
    'settings.discordInfo': 'What is Discord Rich Presence?',
    'settings.discordInfoDesc1': 'Discord Rich Presence shows your friends what you\'re doing in Bake It Out:',
    'settings.discordFeature1': 'Your current level and money',
    'settings.discordFeature2': 'What you\'re currently baking',
    'settings.discordFeature3': 'Multiplayer room information',
    'settings.discordFeature4': 'How long you\'ve been playing',
    'settings.discordInfoDesc2': 'Your friends can even join your multiplayer games directly from Discord!',
    'settings.discordTroubleshooting': 'Discord Not Connected',
    'settings.discordTrouble1': 'Make sure Discord is running on your computer.',
    'settings.discordTrouble2': 'Discord Rich Presence only works in the desktop version of the game.',
    'settings.discordTrouble3': 'Try restarting both Discord and the game if the connection fails.',
    'settings.discordPrivacy': 'Privacy Information',
    'settings.discordPrivacyDesc1': 'Discord Rich Presence only shares:',
    'settings.discordPrivacy1': 'Your current game activity (public)',
    'settings.discordPrivacy2': 'Your player level and progress (public)',
    'settings.discordPrivacy3': 'Multiplayer room codes (for joining)',
    'settings.discordPrivacyDesc2': 'No personal information or save data is shared with Discord.',
    'settings.discordStatus': 'Discord Status',
    'settings.discordInitializing': '🔄 Initializing Discord RPC...',
    'settings.discordRetrying': '🔄 Retrying connection...',
    'settings.discordUnavailable': '❌ Discord not available',
    'settings.discordDesktopOnly': 'ℹ️ Discord RPC only available in desktop version',

    // Error messages and status
    'error.general': 'An error occurred',
    'error.saveLoad': 'Failed to save/load game',
    'error.connection': 'Connection error',
    'error.fileNotFound': 'File not found',
    'error.invalidData': 'Invalid data format',
    'error.permissionDenied': 'Permission denied',
    'status.loading': 'Loading...',
    'status.saving': 'Saving...',
    'status.connecting': 'Connecting...',
    'status.ready': 'Ready',
    'status.success': 'Success!',
    'status.failed': 'Failed',
    'status.offline': 'Offline',
    'status.online': 'Online',

    // UI Elements
    'ui.placeholder': 'Enter text...',
    'ui.search': 'Search',
    'ui.filter': 'Filter',
    'ui.sort': 'Sort',
    'ui.ascending': 'Ascending',
    'ui.descending': 'Descending',
    'ui.selectAll': 'Select All',
    'ui.deselectAll': 'Deselect All',
    'ui.noResults': 'No results found',
    'ui.noData': 'No data available',
    'ui.loading': 'Loading...',
    'ui.saving': 'Saving...',
    'ui.saved': 'Saved!',
    'ui.failed': 'Failed',
    'ui.retry': 'Retry',
    'ui.back': 'Back',
    'ui.forward': 'Forward',
    'ui.home': 'Home',
    'ui.menu': 'Menu',
    'ui.options': 'Options',
    'ui.preferences': 'Preferences',

    // Multiplayer
    'multiplayer.lobby': '👥 Multiplayer Lobby',
    'multiplayer.connected': '🟢 Connected',
    'multiplayer.disconnected': '🔴 Disconnected',
    'multiplayer.createRoom': 'Create Room',
    'multiplayer.joinRoom': 'Join Room',
    'multiplayer.room': 'Room',
    'multiplayer.yourName': 'Your Name',
    'multiplayer.enterName': 'Enter your name',
    'multiplayer.roomName': 'Room Name',
    'multiplayer.enterRoomName': 'Enter room name',
    'multiplayer.gameMode': 'Game Mode',
    'multiplayer.cooperative': '🤝 Cooperative',
    'multiplayer.competitive': '⚔️ Competitive',
    'multiplayer.maxPlayers': 'Max Players: {{count}}',
    'multiplayer.roomId': 'Room ID',
    'multiplayer.enterRoomId': 'Enter room ID',
    'multiplayer.players': 'Players ({{count}})',
    'multiplayer.host': 'HOST',
    'multiplayer.level': 'Level {{level}}',
    'multiplayer.chat': 'Chat',
    'multiplayer.typeMessage': 'Type a message...',
    'multiplayer.gameTime': 'Game Time: {{time}}',
    'multiplayer.teamStats': '📊 Team Stats',
    'multiplayer.ordersCompleted': 'Orders Completed:',
    'multiplayer.totalRevenue': 'Total Revenue:',
    'multiplayer.teamExperience': 'Team Experience:',
    'multiplayer.sharedKitchen': '🏪 Shared Kitchen',
    'multiplayer.sharedOrders': '📋 Shared Orders',
    'multiplayer.sharedInventory': '📦 Shared Inventory',
    'multiplayer.contribution': 'Contribution:',
    'multiplayer.online': '🟢 Online',
    'multiplayer.status': 'Status:',
    'multiplayer.you': '(You)',
    'multiplayer.teamChat': '💬 Team Chat',
    'multiplayer.chatPlaceholder': 'Chat messages will appear here...',

    // Multiplayer game modes
    'multiplayer.mode.cooperative.description': '🤝 Cooperative Mode: Work together to complete orders and grow your shared bakery!',
    'multiplayer.mode.competitive.description': '⚔️ Competitive Mode: Compete against other players to complete the most orders!',

    // Multiplayer game interface
    'multiplayer.game.title': '🎮 Multiplayer Game - {{roomName}}',
    'multiplayer.game.mode': 'Mode: {{mode}}',
    'multiplayer.game.playersCount': 'Players: {{count}}',
    'multiplayer.game.playing': '🟢 Playing',
    'multiplayer.game.leaveGame': '🚪 Leave Game',
    'multiplayer.game.tabs.game': 'Game',
    'multiplayer.game.tabs.players': 'Players',
    'multiplayer.game.tabs.chat': 'Chat',

    // Room creation and joining
    'multiplayer.create.title': '🏗️ Create Room',
    'multiplayer.join.title': '🚪 Join Room',
    'multiplayer.room.info': 'Mode: {{mode}} • Players: {{current}}/{{max}}',
    'multiplayer.room.readyUp': '✅ Ready',
    'multiplayer.room.notReady': '⏳ Not Ready',
    'multiplayer.room.startGame': '🚀 Start Game',
    'multiplayer.room.leaveRoom': '🚪 Leave',

    // Connection states
    'multiplayer.connection.connecting': 'Connecting...',
    'multiplayer.connection.reconnecting': 'Reconnecting...',
    'multiplayer.connection.failed': 'Connection failed',
    'multiplayer.connection.error': '⚠️ {{error}}',

    // System messages
    'multiplayer.system.playerJoined': '{{name}} joined the room',
    'multiplayer.system.playerLeft': '{{name}} left the room',
    'multiplayer.system.gameStarted': 'Game started!',
    'multiplayer.system.gameEnded': 'Game ended!',
    'multiplayer.system.roomCreated': 'Room created successfully',
    'multiplayer.system.roomJoined': 'Joined room successfully'
  },
  cs: {
    // Main game
    'game.title': 'Bake It Out',
    'game.subtitle': 'Ovládněte umění řízení pekárny v této poutavé multiplayerové hře. Plňte objednávky, odemykejte recepty, automatizujte procesy a soutěžte s přáteli!',
    'game.play': '🎮 Začít hrát',
    'game.singlePlayer': '🎮 Jeden hráč',
    'game.singlePlayerDesc': 'Hrajte sólo a zdokonalte své pekařské dovednosti',
    'game.multiplayer': '👥 Multiplayer',
    'game.multiplayerDesc': 'Hrajte s přáteli v kooperativních nebo soutěžních režimech',
    'game.english': '🇺🇸 English',
    'game.czech': '🇨🇿 Čeština',
    'game.home': '🏠 Domů',
    'game.close': '✕ Zavřít',
    'game.continue': '🚀 Pokračovat ve hře',

    // Menu options
    'menu.singlePlayer': 'Jeden hráč',
    'menu.multiplayer': 'Multiplayer',
    'menu.settings': 'Nastavení',
    'menu.credits': 'Titulky',
    'menu.exit': 'Ukončit',

    // Credits
    'credits.title': 'O hře Bake It Out',
    'credits.subtitle': 'Informace o hře a titulky',
    'credits.description': 'Multiplayerová hra na správu pekárny s real-time spoluprací a podporou lokalizace.',
    'credits.version': 'Verze',
    'credits.release': 'Datum vydání',
    'credits.releaseDate': 'Leden 2025',
    'credits.platform': 'Platforma',
    'credits.platforms': 'Windows, macOS, Linux',
    'credits.close': 'Zavřít',
    'credits.features': 'Funkce',
    'credits.multiplayer': 'Podpora multiplayeru',
    'credits.multiplayerDesc': 'Real-time spolupráce s přáteli v kooperativních nebo soutěžních režimech.',
    'credits.localization': 'Lokalizace',
    'credits.localizationDesc': 'Plná podpora angličtiny a češtiny s jednoduchým přepínáním.',
    'credits.progression': 'Systém postupu',
    'credits.progressionDesc': 'Vylepšujte svou pekárnu, odemykejte recepty a ovládněte automatizaci.',
    'credits.automation': 'Automatizace',
    'credits.automationDesc': 'Pokročilé automatizační systémy pro optimalizaci provozu pekárny.',
    'credits.technology': 'Technologický stack',
    'credits.team': 'Vývojový tým',
    'credits.developedBy': 'Vyvinuto týmem',
    'credits.teamDesc': 'Vytvořeno s láskou vývojovým týmem Bake It Out.',
    'credits.thanks': 'Speciální poděkování',
    'credits.thanksPlayers': 'Všem našim úžasným hráčům a beta testerům',
    'credits.thanksTranslators': 'Komunitním překladatelům za podporu lokalizace',
    'credits.thanksOpenSource': 'Open source komunitě za neuvěřitelné nástroje a knihovny',
    'credits.thanksBakers': 'Skutečným pekařům, kteří inspirovali tuto hru',
    'credits.contact': 'Kontakt a podpora',
    'credits.website': 'Webové stránky',
    'credits.support': 'Podpora',
    'credits.github': 'GitHub',
    'credits.discord': 'Discord',
    'menu.newGame': 'Nová hra',
    'menu.continueGame': 'Pokračovat ve hře',
    'menu.loadGame': 'Načíst hru',
    'menu.selectLanguage': 'Vybrat jazyk',
    'menu.about': 'O hře',
    'menu.help': 'Nápověda',
    'menu.quit': 'Ukončit',

    // Features
    'features.manage.title': 'Spravujte svou pekárnu',
    'features.manage.description': 'Přijímejte objednávky, pečte lahodné výrobky a obsluhujte spokojené zákazníky',
    'features.levelup.title': 'Postupujte a automatizujte',
    'features.levelup.description': 'Odemykejte nové recepty, kupujte vybavení a automatizujte své procesy',
    'features.multiplayer.title': 'Hrajte společně',
    'features.multiplayer.description': 'Kooperativní a soutěžní multiplayerové režimy s přáteli',
    'status.development': '🚧 Hra ve vývoji - Fáze 5: Vícevrstvá podpora! 🚧',

    // Game interface
    'ui.level': 'Úroveň {{level}}',
    'ui.money': '{{amount}} Kč',
    'ui.experience': 'XP: {{current}}/{{max}}',
    'ui.skillPoints': 'SP: {{points}}',
    'ui.achievements': '🏆 Úspěchy',
    'ui.skills': '🌟 Dovednosti',
    'ui.automation': '🤖 Automatizace',

    // Kitchen
    'kitchen.title': '🏪 Kuchyně',
    'kitchen.clickToUse': 'Klikněte pro použití',
    'kitchen.making': 'Připravuje: {{recipe}}',
    'kitchen.timeRemaining': 'Čas: {{time}}',

    // Inventory
    'inventory.title': '📦 Sklad',
    'inventory.quantity': 'Množství: {{qty}}',
    'inventory.cost': '{{cost}} Kč za kus',

    // Orders
    'orders.title': '📋 Objednávky',
    'orders.newOrder': '+ Nová objednávka',
    'orders.accept': 'Přijmout',
    'orders.decline': 'Odmítnout',
    'orders.complete': 'Dokončit',
    'orders.inProgress': 'Probíhá',
    'orders.timeLimit': 'Čas: {{time}}',
    'orders.reward': '{{amount}} Kč',
    'orders.customer': 'Zákazník: {{name}}',

    // Quick Actions
    'actions.title': '⚡ Rychlé akce',
    'actions.buyIngredients': '🛒 Koupit suroviny',
    'actions.viewRecipes': '📖 Zobrazit recepty',
    'actions.equipmentShop': '🔧 Obchod s vybavením',

    // Modals
    'modal.recipes.title': '📖 Kniha receptů',
    'modal.shop.title': '🛒 Obchod se surovinami',
    'modal.baking.title': '🔥 {{equipment}} - Vyberte recept',
    'modal.achievements.title': '🏆 Úspěchy',
    'modal.skills.title': '🌟 Strom dovedností',
    'modal.automation.title': '🤖 Ovládání automatizace',
    'modal.equipmentShop.title': '🏪 Obchod s vybavením',
    'modal.settings.title': '⚙️ Nastavení',
    'modal.bakeries.title': '🏪 Správce pekáren',
    'modal.levelUp.title': 'Postup na vyšší úroveň!',
    'modal.levelUp.subtitle': 'Dosáhli jste úrovně {{level}}!',

    // Recipe Modal
    'recipes.all': 'Vše',
    'recipes.cookies': 'Sušenky',
    'recipes.cakes': 'Dorty',
    'recipes.bread': 'Chléb',
    'recipes.pastries': 'Pečivo',
    'recipes.ingredients': 'Suroviny:',
    'recipes.difficulty': 'Obtížnost:',
    'recipes.time': 'Čas:',
    'recipes.canCraft': '✅ Lze vyrobit',
    'recipes.unlockLevel': 'Odemčeno na úrovni {{level}}',
    'recipes.noRecipes': 'V této kategorii nejsou k dispozici žádné recepty.',
    'recipes.levelUpToUnlock': 'Postupte na vyšší úroveň pro odemčení dalších receptů!',

    // Shop Modal
    'shop.currentStock': 'Aktuální zásoba: {{quantity}}',
    'shop.buy': 'Koupit',
    'shop.tooExpensive': 'Příliš drahé',
    'shop.tips.title': '💡 Tipy pro nakupování',
    'shop.tips.bulk': '• Kupujte suroviny ve velkém množství pro úsporu času',
    'shop.tips.stock': '• Sledujte úroveň svých zásob',
    'shop.tips.rare': '• Některé recepty vyžadují vzácné suroviny',
    'shop.tips.prices': '• Ceny se mohou lišit podle dostupnosti',

    // Baking Modal
    'baking.selectRecipe': 'Vyberte recept',
    'baking.noRecipes': 'Žádné recepty k dispozici',
    'baking.noIngredients': 'Nemáte dostatek surovin pro výrobu jakéhokoli receptu.',
    'baking.buyIngredients': 'Koupit suroviny',
    'baking.startBaking': '🔥 Začít péct',
    'baking.instructions': '📋 Pokyny pro pečení {{recipe}}',
    'baking.expectedReward': 'Očekávaná odměna: {{amount}} Kč',
    'baking.makesSure': 'Ujistěte se, že máte všechny suroviny před začátkem!',
    'baking.inProgress': 'Pečení probíhá...',
    'baking.completed': 'Pečení dokončeno!',
    'baking.cancelled': 'Pečení zrušeno',
    'baking.timeRemaining': 'Zbývající čas: {{time}}',
    'baking.clickToCollect': 'Klikněte pro vyzvednutí',

    // Achievements Modal
    'achievements.completed': '{{completed}} z {{total}} úspěchů dokončeno',
    'achievements.overallProgress': 'Celkový pokrok',
    'achievements.progress': 'Pokrok',
    'achievements.reward': 'Odměna:',
    'achievements.noAchievements': 'V této kategorii nejsou žádné úspěchy.',

    // Skills Modal
    'skills.availablePoints': 'Dostupné body dovedností: {{points}}',
    'skills.efficiency': 'Efektivita',
    'skills.automation': 'Automatizace',
    'skills.quality': 'Kvalita',
    'skills.business': 'Podnikání',
    'skills.effects': 'Efekty:',
    'skills.requires': 'Vyžaduje: {{requirements}}',
    'skills.requiresLevel': 'Vyžaduje úroveň {{level}}',
    'skills.maxed': '✅ Maximální',
    'skills.upgrade': '⬆️ Vylepšit ({{cost}} SP)',
    'skills.locked': '🔒 Uzamčeno',
    'skills.noSkills': 'V této kategorii nejsou žádné dovednosti.',
    'skills.tips.title': '💡 Tipy pro dovednosti',
    'skills.tips.earnPoints': '• Získávejte body dovedností postupem na vyšší úroveň (1 bod každé 2 úrovně)',
    'skills.tips.prerequisites': '• Některé dovednosti vyžadují nejprve odemčení jiných dovedností',
    'skills.tips.playstyle': '• Zaměřte se na dovednosti, které odpovídají vašemu stylu hry',
    'skills.tips.efficiency': '• Dovednosti efektivity pomáhají se správou zdrojů',

    // Automation Modal
    'automation.masterControl': '🎛️ Hlavní ovládání',
    'automation.enableAutomation': 'Povolit automatizaci',
    'automation.autoStart': 'Automatické spuštění vybavení',
    'automation.priorityMode': '🎯 Režim priority',
    'automation.efficiency': 'Efektivita (objednávky první)',
    'automation.profit': 'Zisk (nejvyšší hodnota)',
    'automation.speed': 'Rychlost (nejrychlejší recepty)',
    'automation.priorityDescription': 'Jak automatizace vybírá, co péct',
    'automation.performance': '⚡ Výkon',
    'automation.maxJobs': 'Max současných úloh: {{jobs}}',
    'automation.safety': '🛡️ Bezpečnost',
    'automation.stopWhenLow': 'Zastavit, když suroviny klesnou pod: {{threshold}}',
    'automation.upgrades': '💡 Vylepšení automatizace',
    'automation.upgradesDescription': 'Vylepšete efektivitu, rychlost a inteligenci vaší automatizace.',
    'automation.purchase': 'Koupit',
    'automation.noUpgrades': 'Na vaší současné úrovni nejsou k dispozici žádná vylepšení.',
    'automation.levelUpForUpgrades': 'Postupte na vyšší úroveň pro odemčení dalších vylepšení automatizace!',
    'automation.automatedEquipment': 'Automatizované vybavení',
    'automation.activeUpgrades': 'Aktivní vylepšení',
    'automation.automationStatus': 'Stav automatizace',
    'automation.equipmentStatus': '🏭 Stav vybavení',
    'automation.running': 'Běží',
    'automation.idle': 'Nečinné',
    'automation.noAutomatedEquipment': 'Žádné automatizované vybavení k dispozici.',
    'automation.purchaseAutoEquipment': 'Kupte si auto-vybavení z obchodu pro začátek!',

    // Equipment Shop Modal
    'equipmentShop.upgradeYourBakery': 'Vylepšete svou pekárnu profesionálním vybavením',
    'equipmentShop.basic': 'Základní',
    'equipmentShop.automated': 'Automatizované',
    'equipmentShop.advanced': 'Pokročilé',
    'equipmentShop.efficiency': 'Efektivita: {{efficiency}}x',
    'equipmentShop.automation': 'Automatizace:',
    'equipmentShop.unlockLevel': 'Úroveň odemčení: {{level}}',
    'equipmentShop.purchase': '💰 Koupit',
    'equipmentShop.noEquipment': 'V této kategorii není k dispozici žádné vybavení.',
    'equipmentShop.levelUpForEquipment': 'Postupte na vyšší úroveň pro odemčení dalšího vybavení!',
    'equipmentShop.tips.title': '💡 Tipy pro vybavení',
    'equipmentShop.tips.automated': '• Automatizované vybavení může běžet bez vašeho dohledu',
    'equipmentShop.tips.efficiency': '• Vyšší efektivita znamená rychlejší výrobu a lepší kvalitu',
    'equipmentShop.tips.conveyor': '• Dopravní pásy spojují vybavení pro bezproblémový pracovní tok',
    'equipmentShop.tips.advanced': '• Pokročilé vybavení se odemyká na vyšších úrovních',

    // Level Up Modal
    'levelUp.levelRewards': '🎁 Odměny za úroveň',
    'levelUp.whatsNext': '💡 Co dál?',
    'levelUp.checkRecipes': '• Podívejte se na nové recepty ve své knize receptů',
    'levelUp.visitShop': '• Navštivte obchod pro nové vybavení',
    'levelUp.challengingOrders': '• Přijměte náročnější objednávky',
    'levelUp.investSkills': '• Investujte do vylepšení dovedností',

    // Settings Modal
    'settings.title': '⚙️ Nastavení',
    'settings.general': 'Obecné',
    'settings.audio': 'Zvuk',
    'settings.graphics': 'Grafika',
    'settings.save': 'Uložení a data',
    'settings.language': '🌍 Jazyk',
    'settings.gameplay': '🎮 Hratelnost',
    'settings.notifications': 'Povolit oznámení',
    'settings.tutorials': 'Zobrazit návody',
    'settings.animationSpeed': 'Rychlost animace',
    'settings.sound': 'Zvukové efekty',
    'settings.music': 'Hudba na pozadí',
    'settings.quality': '🎨 Kvalita grafiky',
    'settings.autoSave': '💾 Automatické ukládání',
    'settings.enableAutoSave': 'Povolit automatické ukládání',
    'settings.dataManagement': '📁 Správa dat',
    'settings.exportSave': '📤 Exportovat uložení',
    'settings.importSave': '📥 Importovat uložení',
    'settings.cloudSync': '☁️ Cloudová synchronizace',
    'settings.cloudSyncDescription': 'Cloudová synchronizace vám umožňuje uložit pokrok online a hrát na více zařízeních.',
    'settings.comingSoon': 'Již brzy',

    // Bakery Manager Modal
    'bakeries.title': '🏪 Správce pekáren',
    'bakeries.subtitle': 'Spravujte své pekárenské impérium',
    'bakeries.owned': 'Moje pekárny',
    'bakeries.available': 'Dostupné',
    'bakeries.current': 'Aktuální',
    'bakeries.level': 'Úroveň',
    'bakeries.specialization': 'Specializace',
    'bakeries.equipment': 'Vybavení',
    'bakeries.orders': 'Aktivní objednávky',
    'bakeries.switchTo': 'Přepnout na',
    'bakeries.noOwned': 'Ještě nevlastníte žádné pekárny.',
    'bakeries.purchase': '💰 Koupit',
    'bakeries.tooExpensive': '💸 Příliš drahé',
    'bakeries.allOwned': 'Vlastníte všechny dostupné pekárny!',
    'bakeries.tips': '💡 Tipy pro pekárny',
    'bakeries.tip1': 'Každá pekárna se specializuje na různé produkty pro bonusovou efektivitu',
    'bakeries.tip2': 'Přepínejte mezi pekárnami pro správu více lokalit',
    'bakeries.tip3': 'Specializované pekárny přitahují zákazníky hledající konkrétní položky',
    'bakeries.tip4': 'Vylepšujte každou pekárnu nezávisle pro maximální zisk',

    // Notifications
    'notifications.orderAccepted': 'Objednávka přijata',
    'notifications.orderAcceptedMessage': 'Přijali jste novou objednávku!',
    'notifications.orderCompleted': 'Objednávka dokončena!',
    'notifications.orderCompletedMessage': 'Získali jste {{reward}} Kč a zkušenosti!',
    'notifications.orderDeclined': 'Objednávka odmítnuta',
    'notifications.orderDeclinedMessage': 'Objednávka byla odstraněna z vaší fronty.',
    'notifications.bakeryPurchased': 'Pekárna zakoupena!',
    'notifications.bakeryPurchasedMessage': 'Nyní vlastníte {{name}}!',
    'notifications.bakerySwitched': 'Pekárna přepnuta',
    'notifications.bakerySwitchedMessage': 'Přepnuto na {{name}}',

    // Common buttons and actions
    'common.accept': 'Přijmout',
    'common.decline': 'Odmítnout',
    'common.complete': 'Dokončit',
    'common.purchase': 'Koupit',
    'common.upgrade': 'Vylepšit',
    'common.cancel': 'Zrušit',
    'common.confirm': 'Potvrdit',
    'common.save': 'Uložit',
    'common.load': 'Načíst',
    'common.delete': 'Smazat',
    'common.edit': 'Upravit',
    'common.back': 'Zpět',
    'common.next': 'Další',
    'common.previous': 'Předchozí',
    'common.yes': 'Ano',
    'common.no': 'Ne',
    'common.create': 'Vytvořit',
    'common.join': 'Připojit se',
    'common.leave': 'Odejít',
    'common.start': 'Začít',
    'common.ready': 'Připraven',
    'common.notReady': 'Nepřipraven',
    'common.send': 'Odeslat',
    'common.refresh': 'Obnovit',
    'common.retry': 'Zkusit znovu',
    'common.reset': 'Resetovat',
    'common.clear': 'Vymazat',
    'common.apply': 'Použít',
    'common.warning': 'Varování',
    'common.info': 'Informace',
    'common.success': 'Úspěch',
    'common.error': 'Chyba',

    // Save/Load System
    'saveLoad.saveDesc': 'Vyberte slot pro uložení vašeho postupu',
    'saveLoad.loadDesc': 'Vyberte soubor uložení k načtení',
    'saveLoad.saveName': 'Název uložení',
    'saveLoad.emptySlot': 'Prázdný slot',
    'saveLoad.selectedSaveSlot': 'Vybrán: Slot {{slot}}',
    'saveLoad.selectedLoadSlot': 'Vybrán: Slot {{slot}}',
    'saveLoad.confirmOverwrite': 'Přepsat uložení?',
    'saveLoad.overwriteWarning': 'Toto přepíše existující uložení. Tuto akci nelze vrátit zpět.',
    'saveLoad.overwrite': 'Přepsat',
    'saveLoad.fileSlots': 'Souborové sloty',
    'saveLoad.gameSlots': 'Herní sloty',
    'saveLoad.exportSave': 'Exportovat uložení',
    'saveLoad.importSave': 'Importovat uložení',
    'saveLoad.deleteConfirm': 'Smazat uložení?',
    'saveLoad.deleteWarning': 'Toto trvale smaže tento soubor uložení. Tuto akci nelze vrátit zpět.',
    'saveLoad.delete': 'Smazat',

    // Game Menu
    'gameMenu.title': 'Herní menu',
    'gameMenu.subtitle': 'Spravujte svou hru',
    'gameMenu.resume': 'Pokračovat ve hře',
    'gameMenu.resumeDesc': 'Pokračovat v hraní',
    'gameMenu.save': 'Uložit hru',
    'gameMenu.saveDesc': 'Uložit váš postup',
    'gameMenu.load': 'Načíst hru',
    'gameMenu.loadDesc': 'Načíst uložený postup',
    'gameMenu.settings': 'Nastavení',
    'gameMenu.settingsDesc': 'Herní předvolby',
    'gameMenu.mainMenu': 'Hlavní menu',
    'gameMenu.mainMenuDesc': 'Návrat do hlavního menu',
    'gameMenu.exit': 'Ukončit hru',
    'gameMenu.exitDesc': 'Zavřít aplikaci',
    'gameMenu.tip': 'Stiskněte ESC pro otevření tohoto menu kdykoli',

    // Discord Rich Presence
    'settings.discord': 'Discord',
    'settings.discordRichPresence': 'Discord Rich Presence',
    'settings.discordDescription': 'Zobrazit váš aktuální herní stav a aktivitu v Discordu.',
    'settings.enableDiscordRPC': 'Povolit Discord Rich Presence',
    'settings.discordConnected': '✅ Připojeno k Discordu',
    'settings.discordDisconnected': '❌ Nepřipojeno k Discordu',
    'settings.discordInfo': 'Co je Discord Rich Presence?',
    'settings.discordInfoDesc1': 'Discord Rich Presence ukazuje vašim přátelům, co děláte v Bake It Out:',
    'settings.discordFeature1': 'Vaši aktuální úroveň a peníze',
    'settings.discordFeature2': 'Co právě pečete',
    'settings.discordFeature3': 'Informace o multiplayer místnosti',
    'settings.discordFeature4': 'Jak dlouho hrajete',
    'settings.discordInfoDesc2': 'Vaši přátelé se mohou připojit k vašim multiplayer hrám přímo z Discordu!',
    'settings.discordTroubleshooting': 'Discord není připojen',
    'settings.discordTrouble1': 'Ujistěte se, že Discord běží na vašem počítači.',
    'settings.discordTrouble2': 'Discord Rich Presence funguje pouze v desktopové verzi hry.',
    'settings.discordTrouble3': 'Zkuste restartovat Discord i hru, pokud se připojení nezdaří.',
    'settings.discordPrivacy': 'Informace o soukromí',
    'settings.discordPrivacyDesc1': 'Discord Rich Presence sdílí pouze:',
    'settings.discordPrivacy1': 'Vaši aktuální herní aktivitu (veřejné)',
    'settings.discordPrivacy2': 'Vaši úroveň hráče a postup (veřejné)',
    'settings.discordPrivacy3': 'Kódy multiplayer místností (pro připojení)',
    'settings.discordPrivacyDesc2': 'Žádné osobní informace nebo uložená data nejsou sdílena s Discordem.',
    'settings.discordStatus': 'Stav Discordu',
    'settings.discordInitializing': '🔄 Inicializace Discord RPC...',
    'settings.discordRetrying': '🔄 Opakování připojení...',
    'settings.discordUnavailable': '❌ Discord není dostupný',
    'settings.discordDesktopOnly': 'ℹ️ Discord RPC dostupný pouze v desktopové verzi',

    // Error messages and status
    'error.general': 'Došlo k chybě',
    'error.saveLoad': 'Nepodařilo se uložit/načíst hru',
    'error.connection': 'Chyba připojení',
    'error.fileNotFound': 'Soubor nenalezen',
    'error.invalidData': 'Neplatný formát dat',
    'error.permissionDenied': 'Přístup odepřen',
    'status.loading': 'Načítání...',
    'status.saving': 'Ukládání...',
    'status.connecting': 'Připojování...',
    'status.ready': 'Připraven',
    'status.success': 'Úspěch!',
    'status.failed': 'Neúspěšné',
    'status.offline': 'Offline',
    'status.online': 'Online',

    // UI Elements
    'ui.placeholder': 'Zadejte text...',
    'ui.search': 'Hledat',
    'ui.filter': 'Filtrovat',
    'ui.sort': 'Seřadit',
    'ui.ascending': 'Vzestupně',
    'ui.descending': 'Sestupně',
    'ui.selectAll': 'Vybrat vše',
    'ui.deselectAll': 'Zrušit výběr',
    'ui.noResults': 'Žádné výsledky',
    'ui.noData': 'Žádná data k dispozici',
    'ui.loading': 'Načítání...',
    'ui.saving': 'Ukládání...',
    'ui.saved': 'Uloženo!',
    'ui.failed': 'Neúspěšné',
    'ui.retry': 'Zkusit znovu',
    'ui.back': 'Zpět',
    'ui.forward': 'Vpřed',
    'ui.home': 'Domů',
    'ui.menu': 'Menu',
    'ui.options': 'Možnosti',
    'ui.preferences': 'Předvolby',

    // Multiplayer
    'multiplayer.lobby': '👥 Multiplayerová lobby',
    'multiplayer.connected': '🟢 Připojeno',
    'multiplayer.disconnected': '🔴 Odpojeno',
    'multiplayer.createRoom': 'Vytvořit místnost',
    'multiplayer.joinRoom': 'Připojit se k místnosti',
    'multiplayer.room': 'Místnost',
    'multiplayer.yourName': 'Vaše jméno',
    'multiplayer.enterName': 'Zadejte své jméno',
    'multiplayer.roomName': 'Název místnosti',
    'multiplayer.enterRoomName': 'Zadejte název místnosti',
    'multiplayer.gameMode': 'Herní režim',
    'multiplayer.cooperative': '🤝 Kooperativní',
    'multiplayer.competitive': '⚔️ Soutěžní',
    'multiplayer.maxPlayers': 'Max hráčů: {{count}}',
    'multiplayer.roomId': 'ID místnosti',
    'multiplayer.enterRoomId': 'Zadejte ID místnosti',
    'multiplayer.players': 'Hráči ({{count}})',
    'multiplayer.host': 'HOSTITEL',
    'multiplayer.level': 'Úroveň {{level}}',
    'multiplayer.chat': 'Chat',
    'multiplayer.typeMessage': 'Napište zprávu...',
    'multiplayer.gameTime': 'Herní čas: {{time}}',
    'multiplayer.teamStats': '📊 Týmové statistiky',
    'multiplayer.ordersCompleted': 'Dokončené objednávky:',
    'multiplayer.totalRevenue': 'Celkový příjem:',
    'multiplayer.teamExperience': 'Týmové zkušenosti:',
    'multiplayer.sharedKitchen': '🏪 Sdílená kuchyně',
    'multiplayer.sharedOrders': '📋 Sdílené objednávky',
    'multiplayer.sharedInventory': '📦 Sdílený sklad',
    'multiplayer.contribution': 'Příspěvek:',
    'multiplayer.online': '🟢 Online',
    'multiplayer.status': 'Stav:',
    'multiplayer.you': '(Vy)',
    'multiplayer.teamChat': '💬 Týmový chat',
    'multiplayer.chatPlaceholder': 'Zde se zobrazí zprávy chatu...',

    // Multiplayer game modes
    'multiplayer.mode.cooperative.description': '🤝 Kooperativní režim: Spolupracujte na dokončování objednávek a rozvoji sdílené pekárny!',
    'multiplayer.mode.competitive.description': '⚔️ Soutěžní režim: Soutěžte s ostatními hráči o dokončení nejvíce objednávek!',

    // Multiplayer game interface
    'multiplayer.game.title': '🎮 Multiplayerová hra - {{roomName}}',
    'multiplayer.game.mode': 'Režim: {{mode}}',
    'multiplayer.game.playersCount': 'Hráči: {{count}}',
    'multiplayer.game.playing': '🟢 Hraje se',
    'multiplayer.game.leaveGame': '🚪 Opustit hru',
    'multiplayer.game.tabs.game': 'Hra',
    'multiplayer.game.tabs.players': 'Hráči',
    'multiplayer.game.tabs.chat': 'Chat',

    // Room creation and joining
    'multiplayer.create.title': '🏗️ Vytvořit místnost',
    'multiplayer.join.title': '🚪 Připojit se k místnosti',
    'multiplayer.room.info': 'Režim: {{mode}} • Hráči: {{current}}/{{max}}',
    'multiplayer.room.readyUp': '✅ Připraven',
    'multiplayer.room.notReady': '⏳ Nepřipraven',
    'multiplayer.room.startGame': '🚀 Začít hru',
    'multiplayer.room.leaveRoom': '🚪 Opustit',

    // Connection states
    'multiplayer.connection.connecting': 'Připojování...',
    'multiplayer.connection.reconnecting': 'Znovu se připojuje...',
    'multiplayer.connection.failed': 'Připojení selhalo',
    'multiplayer.connection.error': '⚠️ {{error}}',

    // System messages
    'multiplayer.system.playerJoined': '{{name}} se připojil do místnosti',
    'multiplayer.system.playerLeft': '{{name}} opustil místnost',
    'multiplayer.system.gameStarted': 'Hra začala!',
    'multiplayer.system.gameEnded': 'Hra skončila!',
    'multiplayer.system.roomCreated': 'Místnost byla úspěšně vytvořena',
    'multiplayer.system.roomJoined': 'Úspěšně jste se připojili do místnosti'
  }
}

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<Language>('en')
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    // Mark as mounted to prevent hydration mismatch
    setMounted(true)
  }, [])

  useEffect(() => {
    // Only access localStorage after component is mounted on client side
    if (!mounted) return

    const savedLanguage = localStorage.getItem('language') as Language
    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'cs')) {
      setLanguage(savedLanguage)
    }
  }, [mounted])

  const handleSetLanguage = (lang: Language) => {
    setLanguage(lang)
    // Only save to localStorage if we're on the client side
    if (mounted && typeof window !== 'undefined') {
      localStorage.setItem('language', lang)
    }
  }

  const t = (key: string, params?: Record<string, string>) => {
    let translation = translations[language][key as keyof typeof translations[typeof language]] || key
    
    if (params) {
      Object.entries(params).forEach(([param, value]) => {
        translation = translation.replace(`{{${param}}}`, value)
      })
    }
    
    return translation
  }

  return (
    <LanguageContext.Provider value={{ language, setLanguage: handleSetLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  )
}

export function useLanguage() {
  const context = useContext(LanguageContext)
  if (context === undefined) {
    // Fallback for when context is not available
    console.warn('useLanguage called outside of LanguageProvider, using fallback')
    return {
      language: 'en' as Language,
      setLanguage: () => {},
      t: (key: string) => key
    }
  }
  return context
}
