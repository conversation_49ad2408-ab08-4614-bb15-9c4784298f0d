module.exports = {

"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[project]/src/contexts/LanguageContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "LanguageProvider": ()=>LanguageProvider,
    "useLanguage": ()=>useLanguage
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
'use client';
;
;
const LanguageContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
// Comprehensive translations object
const translations = {
    en: {
        // Main game
        'game.title': 'Bake It Out',
        'game.subtitle': 'Master the art of bakery management in this engaging multiplayer game. Complete orders, unlock recipes, automate your processes, and compete with friends!',
        'game.play': '🎮 Start Playing',
        'game.singlePlayer': '🎮 Single Player',
        'game.singlePlayerDesc': 'Play solo and master your bakery skills',
        'game.multiplayer': '👥 Multiplayer',
        'game.multiplayerDesc': 'Play with friends in cooperative or competitive modes',
        'game.english': '🇺🇸 English',
        'game.czech': '🇨🇿 Čeština',
        'game.home': '🏠 Home',
        'game.close': '✕ Close',
        'game.continue': '🚀 Continue Playing',
        // Menu options
        'menu.singlePlayer': 'Single Player',
        'menu.multiplayer': 'Multiplayer',
        'menu.settings': 'Settings',
        'menu.credits': 'Credits',
        'menu.exit': 'Exit',
        'menu.newGame': 'New Game',
        'menu.continueGame': 'Continue Game',
        'menu.loadGame': 'Load Game',
        'menu.selectLanguage': 'Select Language',
        'menu.about': 'About',
        'menu.help': 'Help',
        'menu.quit': 'Quit',
        // Features
        'features.manage.title': 'Manage Your Bakery',
        'features.manage.description': 'Take orders, bake delicious goods, and serve happy customers',
        'features.levelup.title': 'Level Up & Automate',
        'features.levelup.description': 'Unlock new recipes, buy equipment, and automate your processes',
        'features.multiplayer.title': 'Play Together',
        'features.multiplayer.description': 'Cooperative and competitive multiplayer modes with friends',
        'status.development': '🚧 Game in Development - Phase 5: Multilayer Support! 🚧',
        // Game interface
        'ui.level': 'Level {{level}}',
        'ui.money': '${{amount}}',
        'ui.experience': 'XP: {{current}}/{{max}}',
        'ui.skillPoints': 'SP: {{points}}',
        'ui.achievements': '🏆 Achievements',
        'ui.skills': '🌟 Skills',
        'ui.automation': '🤖 Automation',
        // Kitchen
        'kitchen.title': '🏪 Kitchen',
        'kitchen.clickToUse': 'Click to use',
        'kitchen.making': 'Making: {{recipe}}',
        'kitchen.timeRemaining': 'Time: {{time}}',
        // Inventory
        'inventory.title': '📦 Inventory',
        'inventory.quantity': 'Qty: {{qty}}',
        'inventory.cost': '${{cost}} each',
        // Orders
        'orders.title': '📋 Orders',
        'orders.newOrder': '+ New Order',
        'orders.accept': 'Accept',
        'orders.decline': 'Decline',
        'orders.complete': 'Complete',
        'orders.inProgress': 'In Progress',
        'orders.timeLimit': 'Time: {{time}}',
        'orders.reward': '${{amount}}',
        'orders.customer': 'Customer: {{name}}',
        // Quick Actions
        'actions.title': '⚡ Quick Actions',
        'actions.buyIngredients': '🛒 Buy Ingredients',
        'actions.viewRecipes': '📖 View Recipes',
        'actions.equipmentShop': '🔧 Equipment Shop',
        // Modals
        'modal.recipes.title': '📖 Recipe Book',
        'modal.shop.title': '🛒 Ingredient Shop',
        'modal.baking.title': '🔥 {{equipment}} - Select Recipe',
        'modal.achievements.title': '🏆 Achievements',
        'modal.skills.title': '🌟 Skill Tree',
        'modal.automation.title': '🤖 Automation Control',
        'modal.equipmentShop.title': '🏪 Equipment Shop',
        'modal.settings.title': '⚙️ Settings',
        'modal.bakeries.title': '🏪 Bakery Manager',
        'modal.levelUp.title': 'Level Up!',
        'modal.levelUp.subtitle': 'You reached Level {{level}}!',
        // Recipe Modal
        'recipes.all': 'All',
        'recipes.cookies': 'Cookies',
        'recipes.cakes': 'Cakes',
        'recipes.bread': 'Bread',
        'recipes.pastries': 'Pastries',
        'recipes.ingredients': 'Ingredients:',
        'recipes.difficulty': 'Difficulty:',
        'recipes.time': 'Time:',
        'recipes.canCraft': '✅ Can Craft',
        'recipes.unlockLevel': 'Unlocked at Level {{level}}',
        'recipes.noRecipes': 'No recipes available in this category.',
        'recipes.levelUpToUnlock': 'Level up to unlock more recipes!',
        // Shop Modal
        'shop.currentStock': 'Current stock: {{quantity}}',
        'shop.buy': 'Buy',
        'shop.tooExpensive': 'Too Expensive',
        'shop.tips.title': '💡 Shopping Tips',
        'shop.tips.bulk': '• Buy ingredients in bulk to save time',
        'shop.tips.stock': '• Keep an eye on your stock levels',
        'shop.tips.rare': '• Some recipes require rare ingredients',
        'shop.tips.prices': '• Prices may vary based on availability',
        // Baking Modal
        'baking.selectRecipe': 'Select Recipe',
        'baking.noRecipes': 'No recipes available',
        'baking.noIngredients': 'You don\'t have enough ingredients to craft any recipes.',
        'baking.buyIngredients': 'Buy Ingredients',
        'baking.startBaking': '🔥 Start Baking',
        'baking.instructions': '📋 Baking Instructions for {{recipe}}',
        'baking.expectedReward': 'Expected reward: ${{amount}}',
        'baking.makesSure': 'Make sure you have all ingredients before starting!',
        'baking.inProgress': 'Baking in progress...',
        'baking.completed': 'Baking completed!',
        'baking.cancelled': 'Baking cancelled',
        'baking.timeRemaining': 'Time remaining: {{time}}',
        'baking.clickToCollect': 'Click to collect',
        // Achievements Modal
        'achievements.completed': '{{completed}} of {{total}} achievements completed',
        'achievements.overallProgress': 'Overall Progress',
        'achievements.progress': 'Progress',
        'achievements.reward': 'Reward:',
        'achievements.noAchievements': 'No achievements in this category.',
        // Skills Modal
        'skills.availablePoints': 'Available Skill Points: {{points}}',
        'skills.efficiency': 'Efficiency',
        'skills.automation': 'Automation',
        'skills.quality': 'Quality',
        'skills.business': 'Business',
        'skills.effects': 'Effects:',
        'skills.requires': 'Requires: {{requirements}}',
        'skills.requiresLevel': 'Requires Level {{level}}',
        'skills.maxed': '✅ Maxed',
        'skills.upgrade': '⬆️ Upgrade ({{cost}} SP)',
        'skills.locked': '🔒 Locked',
        'skills.noSkills': 'No skills in this category.',
        'skills.tips.title': '💡 Skill Tips',
        'skills.tips.earnPoints': '• Earn skill points by leveling up (1 point every 2 levels)',
        'skills.tips.prerequisites': '• Some skills require other skills to be unlocked first',
        'skills.tips.playstyle': '• Focus on skills that match your playstyle',
        'skills.tips.efficiency': '• Efficiency skills help with resource management',
        // Automation Modal
        'automation.masterControl': '🎛️ Master Control',
        'automation.enableAutomation': 'Enable Automation',
        'automation.autoStart': 'Auto-start Equipment',
        'automation.priorityMode': '🎯 Priority Mode',
        'automation.efficiency': 'Efficiency (Orders First)',
        'automation.profit': 'Profit (Highest Value)',
        'automation.speed': 'Speed (Fastest Recipes)',
        'automation.priorityDescription': 'How automation chooses what to bake',
        'automation.performance': '⚡ Performance',
        'automation.maxJobs': 'Max Concurrent Jobs: {{jobs}}',
        'automation.safety': '🛡️ Safety',
        'automation.stopWhenLow': 'Stop when ingredients below: {{threshold}}',
        'automation.upgrades': '💡 Automation Upgrades',
        'automation.upgradesDescription': 'Improve your automation efficiency, speed, and intelligence with these upgrades.',
        'automation.purchase': 'Purchase',
        'automation.noUpgrades': 'No upgrades available at your current level.',
        'automation.levelUpForUpgrades': 'Level up to unlock more automation upgrades!',
        'automation.automatedEquipment': 'Automated Equipment',
        'automation.activeUpgrades': 'Active Upgrades',
        'automation.automationStatus': 'Automation Status',
        'automation.equipmentStatus': '🏭 Equipment Status',
        'automation.running': 'Running',
        'automation.idle': 'Idle',
        'automation.noAutomatedEquipment': 'No automated equipment available.',
        'automation.purchaseAutoEquipment': 'Purchase auto-equipment from the shop to get started!',
        // Equipment Shop Modal
        'equipmentShop.upgradeYourBakery': 'Upgrade your bakery with professional equipment',
        'equipmentShop.basic': 'Basic',
        'equipmentShop.automated': 'Automated',
        'equipmentShop.advanced': 'Advanced',
        'equipmentShop.efficiency': 'Efficiency: {{efficiency}}x',
        'equipmentShop.automation': 'Automation:',
        'equipmentShop.unlockLevel': 'Unlock Level: {{level}}',
        'equipmentShop.purchase': '💰 Purchase',
        'equipmentShop.noEquipment': 'No equipment available in this category.',
        'equipmentShop.levelUpForEquipment': 'Level up to unlock more equipment!',
        'equipmentShop.tips.title': '💡 Equipment Tips',
        'equipmentShop.tips.automated': '• Automated equipment can run without your supervision',
        'equipmentShop.tips.efficiency': '• Higher efficiency means faster production and better quality',
        'equipmentShop.tips.conveyor': '• Conveyor belts connect equipment for seamless workflow',
        'equipmentShop.tips.advanced': '• Advanced equipment unlocks at higher levels',
        // Level Up Modal
        'levelUp.levelRewards': '🎁 Level Rewards',
        'levelUp.whatsNext': '💡 What\'s Next?',
        'levelUp.checkRecipes': '• Check out new recipes in your recipe book',
        'levelUp.visitShop': '• Visit the shop for new equipment',
        'levelUp.challengingOrders': '• Take on more challenging orders',
        'levelUp.investSkills': '• Invest in skill upgrades',
        // Settings Modal
        'settings.title': '⚙️ Settings',
        'settings.general': 'General',
        'settings.audio': 'Audio',
        'settings.graphics': 'Graphics',
        'settings.save': 'Save & Data',
        'settings.language': '🌍 Language',
        'settings.gameplay': '🎮 Gameplay',
        'settings.notifications': 'Enable Notifications',
        'settings.tutorials': 'Show Tutorials',
        'settings.animationSpeed': 'Animation Speed',
        'settings.sound': 'Sound Effects',
        'settings.music': 'Background Music',
        'settings.quality': '🎨 Graphics Quality',
        'settings.autoSave': '💾 Auto-Save',
        'settings.enableAutoSave': 'Enable Auto-Save',
        'settings.dataManagement': '📁 Data Management',
        'settings.exportSave': '📤 Export Save',
        'settings.importSave': '📥 Import Save',
        'settings.cloudSync': '☁️ Cloud Sync',
        'settings.cloudSyncDescription': 'Cloud sync allows you to save your progress online and play across multiple devices.',
        'settings.comingSoon': 'Coming Soon',
        // Bakery Manager Modal
        'bakeries.title': '🏪 Bakery Manager',
        'bakeries.subtitle': 'Manage your bakery empire',
        'bakeries.owned': 'My Bakeries',
        'bakeries.available': 'Available',
        'bakeries.current': 'Current',
        'bakeries.level': 'Level',
        'bakeries.specialization': 'Specialization',
        'bakeries.equipment': 'Equipment',
        'bakeries.orders': 'Active Orders',
        'bakeries.switchTo': 'Switch To',
        'bakeries.noOwned': 'You don\'t own any bakeries yet.',
        'bakeries.purchase': '💰 Purchase',
        'bakeries.tooExpensive': '💸 Too Expensive',
        'bakeries.allOwned': 'You own all available bakeries!',
        'bakeries.tips': '💡 Bakery Tips',
        'bakeries.tip1': 'Each bakery specializes in different products for bonus efficiency',
        'bakeries.tip2': 'Switch between bakeries to manage multiple locations',
        'bakeries.tip3': 'Specialized bakeries attract customers looking for specific items',
        'bakeries.tip4': 'Upgrade each bakery independently for maximum profit',
        // Notifications
        'notifications.orderAccepted': 'Order Accepted',
        'notifications.orderAcceptedMessage': 'You have accepted a new order!',
        'notifications.orderCompleted': 'Order Completed!',
        'notifications.orderCompletedMessage': 'You earned ${{reward}} and gained experience!',
        'notifications.orderDeclined': 'Order Declined',
        'notifications.orderDeclinedMessage': 'Order has been removed from your queue.',
        'notifications.bakeryPurchased': 'Bakery Purchased!',
        'notifications.bakeryPurchasedMessage': 'You now own {{name}}!',
        'notifications.bakerySwitched': 'Bakery Switched',
        'notifications.bakerySwitchedMessage': 'Switched to {{name}}',
        // Common buttons and actions
        'common.accept': 'Accept',
        'common.decline': 'Decline',
        'common.complete': 'Complete',
        'common.purchase': 'Purchase',
        'common.upgrade': 'Upgrade',
        'common.cancel': 'Cancel',
        'common.confirm': 'Confirm',
        'common.save': 'Save',
        'common.load': 'Load',
        'common.delete': 'Delete',
        'common.edit': 'Edit',
        'common.back': 'Back',
        'common.next': 'Next',
        'common.previous': 'Previous',
        'common.yes': 'Yes',
        'common.no': 'No',
        'common.create': 'Create',
        'common.join': 'Join',
        'common.leave': 'Leave',
        'common.start': 'Start',
        'common.ready': 'Ready',
        'common.notReady': 'Not Ready',
        'common.send': 'Send',
        'common.refresh': 'Refresh',
        'common.retry': 'Retry',
        'common.reset': 'Reset',
        'common.clear': 'Clear',
        'common.apply': 'Apply',
        'common.warning': 'Warning',
        'common.info': 'Information',
        'common.success': 'Success',
        'common.error': 'Error',
        // Save/Load System
        'saveLoad.saveDesc': 'Choose a slot to save your progress',
        'saveLoad.loadDesc': 'Select a save file to load',
        'saveLoad.saveName': 'Save Name',
        'saveLoad.emptySlot': 'Empty Slot',
        'saveLoad.selectedSaveSlot': 'Selected: Slot {{slot}}',
        'saveLoad.selectedLoadSlot': 'Selected: Slot {{slot}}',
        'saveLoad.confirmOverwrite': 'Overwrite Save?',
        'saveLoad.overwriteWarning': 'This will overwrite the existing save. This action cannot be undone.',
        'saveLoad.overwrite': 'Overwrite',
        'saveLoad.fileSlots': 'File Slots',
        'saveLoad.gameSlots': 'Game Slots',
        'saveLoad.exportSave': 'Export Save',
        'saveLoad.importSave': 'Import Save',
        'saveLoad.deleteConfirm': 'Delete Save?',
        'saveLoad.deleteWarning': 'This will permanently delete this save file. This action cannot be undone.',
        'saveLoad.delete': 'Delete',
        // Game Menu
        'gameMenu.title': 'Game Menu',
        'gameMenu.subtitle': 'Manage your game',
        'gameMenu.resume': 'Resume Game',
        'gameMenu.resumeDesc': 'Continue playing',
        'gameMenu.save': 'Save Game',
        'gameMenu.saveDesc': 'Save your progress',
        'gameMenu.load': 'Load Game',
        'gameMenu.loadDesc': 'Load saved progress',
        'gameMenu.settings': 'Settings',
        'gameMenu.settingsDesc': 'Game preferences',
        'gameMenu.mainMenu': 'Main Menu',
        'gameMenu.mainMenuDesc': 'Return to main menu',
        'gameMenu.exit': 'Exit Game',
        'gameMenu.exitDesc': 'Close the application',
        'gameMenu.tip': 'Press ESC to open this menu anytime',
        // Discord Rich Presence
        'settings.discord': 'Discord',
        'settings.discordRichPresence': 'Discord Rich Presence',
        'settings.discordDescription': 'Show your current game status and activity in Discord.',
        'settings.enableDiscordRPC': 'Enable Discord Rich Presence',
        'settings.discordConnected': '✅ Connected to Discord',
        'settings.discordDisconnected': '❌ Not connected to Discord',
        'settings.discordInfo': 'What is Discord Rich Presence?',
        'settings.discordInfoDesc1': 'Discord Rich Presence shows your friends what you\'re doing in Bake It Out:',
        'settings.discordFeature1': 'Your current level and money',
        'settings.discordFeature2': 'What you\'re currently baking',
        'settings.discordFeature3': 'Multiplayer room information',
        'settings.discordFeature4': 'How long you\'ve been playing',
        'settings.discordInfoDesc2': 'Your friends can even join your multiplayer games directly from Discord!',
        'settings.discordTroubleshooting': 'Discord Not Connected',
        'settings.discordTrouble1': 'Make sure Discord is running on your computer.',
        'settings.discordTrouble2': 'Discord Rich Presence only works in the desktop version of the game.',
        'settings.discordTrouble3': 'Try restarting both Discord and the game if the connection fails.',
        'settings.discordPrivacy': 'Privacy Information',
        'settings.discordPrivacyDesc1': 'Discord Rich Presence only shares:',
        'settings.discordPrivacy1': 'Your current game activity (public)',
        'settings.discordPrivacy2': 'Your player level and progress (public)',
        'settings.discordPrivacy3': 'Multiplayer room codes (for joining)',
        'settings.discordPrivacyDesc2': 'No personal information or save data is shared with Discord.',
        'settings.discordStatus': 'Discord Status',
        'settings.discordInitializing': '🔄 Initializing Discord RPC...',
        'settings.discordRetrying': '🔄 Retrying connection...',
        'settings.discordUnavailable': '❌ Discord not available',
        'settings.discordDesktopOnly': 'ℹ️ Discord RPC only available in desktop version',
        // Error messages and status
        'error.general': 'An error occurred',
        'error.saveLoad': 'Failed to save/load game',
        'error.connection': 'Connection error',
        'error.fileNotFound': 'File not found',
        'error.invalidData': 'Invalid data format',
        'error.permissionDenied': 'Permission denied',
        'status.loading': 'Loading...',
        'status.saving': 'Saving...',
        'status.connecting': 'Connecting...',
        'status.ready': 'Ready',
        'status.success': 'Success!',
        'status.failed': 'Failed',
        'status.offline': 'Offline',
        'status.online': 'Online',
        // UI Elements
        'ui.placeholder': 'Enter text...',
        'ui.search': 'Search',
        'ui.filter': 'Filter',
        'ui.sort': 'Sort',
        'ui.ascending': 'Ascending',
        'ui.descending': 'Descending',
        'ui.selectAll': 'Select All',
        'ui.deselectAll': 'Deselect All',
        'ui.noResults': 'No results found',
        'ui.noData': 'No data available',
        'ui.loading': 'Loading...',
        'ui.saving': 'Saving...',
        'ui.saved': 'Saved!',
        'ui.failed': 'Failed',
        'ui.retry': 'Retry',
        'ui.back': 'Back',
        'ui.forward': 'Forward',
        'ui.home': 'Home',
        'ui.menu': 'Menu',
        'ui.options': 'Options',
        'ui.preferences': 'Preferences',
        // Multiplayer
        'multiplayer.lobby': '👥 Multiplayer Lobby',
        'multiplayer.connected': '🟢 Connected',
        'multiplayer.disconnected': '🔴 Disconnected',
        'multiplayer.createRoom': 'Create Room',
        'multiplayer.joinRoom': 'Join Room',
        'multiplayer.room': 'Room',
        'multiplayer.yourName': 'Your Name',
        'multiplayer.enterName': 'Enter your name',
        'multiplayer.roomName': 'Room Name',
        'multiplayer.enterRoomName': 'Enter room name',
        'multiplayer.gameMode': 'Game Mode',
        'multiplayer.cooperative': '🤝 Cooperative',
        'multiplayer.competitive': '⚔️ Competitive',
        'multiplayer.maxPlayers': 'Max Players: {{count}}',
        'multiplayer.roomId': 'Room ID',
        'multiplayer.enterRoomId': 'Enter room ID',
        'multiplayer.players': 'Players ({{count}})',
        'multiplayer.host': 'HOST',
        'multiplayer.level': 'Level {{level}}',
        'multiplayer.chat': 'Chat',
        'multiplayer.typeMessage': 'Type a message...',
        'multiplayer.gameTime': 'Game Time: {{time}}',
        'multiplayer.teamStats': '📊 Team Stats',
        'multiplayer.ordersCompleted': 'Orders Completed:',
        'multiplayer.totalRevenue': 'Total Revenue:',
        'multiplayer.teamExperience': 'Team Experience:',
        'multiplayer.sharedKitchen': '🏪 Shared Kitchen',
        'multiplayer.sharedOrders': '📋 Shared Orders',
        'multiplayer.sharedInventory': '📦 Shared Inventory',
        'multiplayer.contribution': 'Contribution:',
        'multiplayer.online': '🟢 Online',
        'multiplayer.status': 'Status:',
        'multiplayer.you': '(You)',
        'multiplayer.teamChat': '💬 Team Chat',
        'multiplayer.chatPlaceholder': 'Chat messages will appear here...',
        // Multiplayer game modes
        'multiplayer.mode.cooperative.description': '🤝 Cooperative Mode: Work together to complete orders and grow your shared bakery!',
        'multiplayer.mode.competitive.description': '⚔️ Competitive Mode: Compete against other players to complete the most orders!',
        // Multiplayer game interface
        'multiplayer.game.title': '🎮 Multiplayer Game - {{roomName}}',
        'multiplayer.game.mode': 'Mode: {{mode}}',
        'multiplayer.game.playersCount': 'Players: {{count}}',
        'multiplayer.game.playing': '🟢 Playing',
        'multiplayer.game.leaveGame': '🚪 Leave Game',
        'multiplayer.game.tabs.game': 'Game',
        'multiplayer.game.tabs.players': 'Players',
        'multiplayer.game.tabs.chat': 'Chat',
        // Room creation and joining
        'multiplayer.create.title': '🏗️ Create Room',
        'multiplayer.join.title': '🚪 Join Room',
        'multiplayer.room.info': 'Mode: {{mode}} • Players: {{current}}/{{max}}',
        'multiplayer.room.readyUp': '✅ Ready',
        'multiplayer.room.notReady': '⏳ Not Ready',
        'multiplayer.room.startGame': '🚀 Start Game',
        'multiplayer.room.leaveRoom': '🚪 Leave',
        // Connection states
        'multiplayer.connection.connecting': 'Connecting...',
        'multiplayer.connection.reconnecting': 'Reconnecting...',
        'multiplayer.connection.failed': 'Connection failed',
        'multiplayer.connection.error': '⚠️ {{error}}',
        // System messages
        'multiplayer.system.playerJoined': '{{name}} joined the room',
        'multiplayer.system.playerLeft': '{{name}} left the room',
        'multiplayer.system.gameStarted': 'Game started!',
        'multiplayer.system.gameEnded': 'Game ended!',
        'multiplayer.system.roomCreated': 'Room created successfully',
        'multiplayer.system.roomJoined': 'Joined room successfully'
    },
    cs: {
        // Main game
        'game.title': 'Bake It Out',
        'game.subtitle': 'Ovládněte umění řízení pekárny v této poutavé multiplayerové hře. Plňte objednávky, odemykejte recepty, automatizujte procesy a soutěžte s přáteli!',
        'game.play': '🎮 Začít hrát',
        'game.singlePlayer': '🎮 Jeden hráč',
        'game.singlePlayerDesc': 'Hrajte sólo a zdokonalte své pekařské dovednosti',
        'game.multiplayer': '👥 Multiplayer',
        'game.multiplayerDesc': 'Hrajte s přáteli v kooperativních nebo soutěžních režimech',
        'game.english': '🇺🇸 English',
        'game.czech': '🇨🇿 Čeština',
        'game.home': '🏠 Domů',
        'game.close': '✕ Zavřít',
        'game.continue': '🚀 Pokračovat ve hře',
        // Menu options
        'menu.singlePlayer': 'Jeden hráč',
        'menu.multiplayer': 'Multiplayer',
        'menu.settings': 'Nastavení',
        'menu.credits': 'Titulky',
        'menu.exit': 'Ukončit',
        'menu.newGame': 'Nová hra',
        'menu.continueGame': 'Pokračovat ve hře',
        'menu.loadGame': 'Načíst hru',
        'menu.selectLanguage': 'Vybrat jazyk',
        'menu.about': 'O hře',
        'menu.help': 'Nápověda',
        'menu.quit': 'Ukončit',
        // Features
        'features.manage.title': 'Spravujte svou pekárnu',
        'features.manage.description': 'Přijímejte objednávky, pečte lahodné výrobky a obsluhujte spokojené zákazníky',
        'features.levelup.title': 'Postupujte a automatizujte',
        'features.levelup.description': 'Odemykejte nové recepty, kupujte vybavení a automatizujte své procesy',
        'features.multiplayer.title': 'Hrajte společně',
        'features.multiplayer.description': 'Kooperativní a soutěžní multiplayerové režimy s přáteli',
        'status.development': '🚧 Hra ve vývoji - Fáze 5: Vícevrstvá podpora! 🚧',
        // Game interface
        'ui.level': 'Úroveň {{level}}',
        'ui.money': '{{amount}} Kč',
        'ui.experience': 'XP: {{current}}/{{max}}',
        'ui.skillPoints': 'SP: {{points}}',
        'ui.achievements': '🏆 Úspěchy',
        'ui.skills': '🌟 Dovednosti',
        'ui.automation': '🤖 Automatizace',
        // Kitchen
        'kitchen.title': '🏪 Kuchyně',
        'kitchen.clickToUse': 'Klikněte pro použití',
        'kitchen.making': 'Připravuje: {{recipe}}',
        'kitchen.timeRemaining': 'Čas: {{time}}',
        // Inventory
        'inventory.title': '📦 Sklad',
        'inventory.quantity': 'Množství: {{qty}}',
        'inventory.cost': '{{cost}} Kč za kus',
        // Orders
        'orders.title': '📋 Objednávky',
        'orders.newOrder': '+ Nová objednávka',
        'orders.accept': 'Přijmout',
        'orders.decline': 'Odmítnout',
        'orders.complete': 'Dokončit',
        'orders.inProgress': 'Probíhá',
        'orders.timeLimit': 'Čas: {{time}}',
        'orders.reward': '{{amount}} Kč',
        'orders.customer': 'Zákazník: {{name}}',
        // Quick Actions
        'actions.title': '⚡ Rychlé akce',
        'actions.buyIngredients': '🛒 Koupit suroviny',
        'actions.viewRecipes': '📖 Zobrazit recepty',
        'actions.equipmentShop': '🔧 Obchod s vybavením',
        // Modals
        'modal.recipes.title': '📖 Kniha receptů',
        'modal.shop.title': '🛒 Obchod se surovinami',
        'modal.baking.title': '🔥 {{equipment}} - Vyberte recept',
        'modal.achievements.title': '🏆 Úspěchy',
        'modal.skills.title': '🌟 Strom dovedností',
        'modal.automation.title': '🤖 Ovládání automatizace',
        'modal.equipmentShop.title': '🏪 Obchod s vybavením',
        'modal.settings.title': '⚙️ Nastavení',
        'modal.bakeries.title': '🏪 Správce pekáren',
        'modal.levelUp.title': 'Postup na vyšší úroveň!',
        'modal.levelUp.subtitle': 'Dosáhli jste úrovně {{level}}!',
        // Recipe Modal
        'recipes.all': 'Vše',
        'recipes.cookies': 'Sušenky',
        'recipes.cakes': 'Dorty',
        'recipes.bread': 'Chléb',
        'recipes.pastries': 'Pečivo',
        'recipes.ingredients': 'Suroviny:',
        'recipes.difficulty': 'Obtížnost:',
        'recipes.time': 'Čas:',
        'recipes.canCraft': '✅ Lze vyrobit',
        'recipes.unlockLevel': 'Odemčeno na úrovni {{level}}',
        'recipes.noRecipes': 'V této kategorii nejsou k dispozici žádné recepty.',
        'recipes.levelUpToUnlock': 'Postupte na vyšší úroveň pro odemčení dalších receptů!',
        // Shop Modal
        'shop.currentStock': 'Aktuální zásoba: {{quantity}}',
        'shop.buy': 'Koupit',
        'shop.tooExpensive': 'Příliš drahé',
        'shop.tips.title': '💡 Tipy pro nakupování',
        'shop.tips.bulk': '• Kupujte suroviny ve velkém množství pro úsporu času',
        'shop.tips.stock': '• Sledujte úroveň svých zásob',
        'shop.tips.rare': '• Některé recepty vyžadují vzácné suroviny',
        'shop.tips.prices': '• Ceny se mohou lišit podle dostupnosti',
        // Baking Modal
        'baking.selectRecipe': 'Vyberte recept',
        'baking.noRecipes': 'Žádné recepty k dispozici',
        'baking.noIngredients': 'Nemáte dostatek surovin pro výrobu jakéhokoli receptu.',
        'baking.buyIngredients': 'Koupit suroviny',
        'baking.startBaking': '🔥 Začít péct',
        'baking.instructions': '📋 Pokyny pro pečení {{recipe}}',
        'baking.expectedReward': 'Očekávaná odměna: {{amount}} Kč',
        'baking.makesSure': 'Ujistěte se, že máte všechny suroviny před začátkem!',
        'baking.inProgress': 'Pečení probíhá...',
        'baking.completed': 'Pečení dokončeno!',
        'baking.cancelled': 'Pečení zrušeno',
        'baking.timeRemaining': 'Zbývající čas: {{time}}',
        'baking.clickToCollect': 'Klikněte pro vyzvednutí',
        // Achievements Modal
        'achievements.completed': '{{completed}} z {{total}} úspěchů dokončeno',
        'achievements.overallProgress': 'Celkový pokrok',
        'achievements.progress': 'Pokrok',
        'achievements.reward': 'Odměna:',
        'achievements.noAchievements': 'V této kategorii nejsou žádné úspěchy.',
        // Skills Modal
        'skills.availablePoints': 'Dostupné body dovedností: {{points}}',
        'skills.efficiency': 'Efektivita',
        'skills.automation': 'Automatizace',
        'skills.quality': 'Kvalita',
        'skills.business': 'Podnikání',
        'skills.effects': 'Efekty:',
        'skills.requires': 'Vyžaduje: {{requirements}}',
        'skills.requiresLevel': 'Vyžaduje úroveň {{level}}',
        'skills.maxed': '✅ Maximální',
        'skills.upgrade': '⬆️ Vylepšit ({{cost}} SP)',
        'skills.locked': '🔒 Uzamčeno',
        'skills.noSkills': 'V této kategorii nejsou žádné dovednosti.',
        'skills.tips.title': '💡 Tipy pro dovednosti',
        'skills.tips.earnPoints': '• Získávejte body dovedností postupem na vyšší úroveň (1 bod každé 2 úrovně)',
        'skills.tips.prerequisites': '• Některé dovednosti vyžadují nejprve odemčení jiných dovedností',
        'skills.tips.playstyle': '• Zaměřte se na dovednosti, které odpovídají vašemu stylu hry',
        'skills.tips.efficiency': '• Dovednosti efektivity pomáhají se správou zdrojů',
        // Automation Modal
        'automation.masterControl': '🎛️ Hlavní ovládání',
        'automation.enableAutomation': 'Povolit automatizaci',
        'automation.autoStart': 'Automatické spuštění vybavení',
        'automation.priorityMode': '🎯 Režim priority',
        'automation.efficiency': 'Efektivita (objednávky první)',
        'automation.profit': 'Zisk (nejvyšší hodnota)',
        'automation.speed': 'Rychlost (nejrychlejší recepty)',
        'automation.priorityDescription': 'Jak automatizace vybírá, co péct',
        'automation.performance': '⚡ Výkon',
        'automation.maxJobs': 'Max současných úloh: {{jobs}}',
        'automation.safety': '🛡️ Bezpečnost',
        'automation.stopWhenLow': 'Zastavit, když suroviny klesnou pod: {{threshold}}',
        'automation.upgrades': '💡 Vylepšení automatizace',
        'automation.upgradesDescription': 'Vylepšete efektivitu, rychlost a inteligenci vaší automatizace.',
        'automation.purchase': 'Koupit',
        'automation.noUpgrades': 'Na vaší současné úrovni nejsou k dispozici žádná vylepšení.',
        'automation.levelUpForUpgrades': 'Postupte na vyšší úroveň pro odemčení dalších vylepšení automatizace!',
        'automation.automatedEquipment': 'Automatizované vybavení',
        'automation.activeUpgrades': 'Aktivní vylepšení',
        'automation.automationStatus': 'Stav automatizace',
        'automation.equipmentStatus': '🏭 Stav vybavení',
        'automation.running': 'Běží',
        'automation.idle': 'Nečinné',
        'automation.noAutomatedEquipment': 'Žádné automatizované vybavení k dispozici.',
        'automation.purchaseAutoEquipment': 'Kupte si auto-vybavení z obchodu pro začátek!',
        // Equipment Shop Modal
        'equipmentShop.upgradeYourBakery': 'Vylepšete svou pekárnu profesionálním vybavením',
        'equipmentShop.basic': 'Základní',
        'equipmentShop.automated': 'Automatizované',
        'equipmentShop.advanced': 'Pokročilé',
        'equipmentShop.efficiency': 'Efektivita: {{efficiency}}x',
        'equipmentShop.automation': 'Automatizace:',
        'equipmentShop.unlockLevel': 'Úroveň odemčení: {{level}}',
        'equipmentShop.purchase': '💰 Koupit',
        'equipmentShop.noEquipment': 'V této kategorii není k dispozici žádné vybavení.',
        'equipmentShop.levelUpForEquipment': 'Postupte na vyšší úroveň pro odemčení dalšího vybavení!',
        'equipmentShop.tips.title': '💡 Tipy pro vybavení',
        'equipmentShop.tips.automated': '• Automatizované vybavení může běžet bez vašeho dohledu',
        'equipmentShop.tips.efficiency': '• Vyšší efektivita znamená rychlejší výrobu a lepší kvalitu',
        'equipmentShop.tips.conveyor': '• Dopravní pásy spojují vybavení pro bezproblémový pracovní tok',
        'equipmentShop.tips.advanced': '• Pokročilé vybavení se odemyká na vyšších úrovních',
        // Level Up Modal
        'levelUp.levelRewards': '🎁 Odměny za úroveň',
        'levelUp.whatsNext': '💡 Co dál?',
        'levelUp.checkRecipes': '• Podívejte se na nové recepty ve své knize receptů',
        'levelUp.visitShop': '• Navštivte obchod pro nové vybavení',
        'levelUp.challengingOrders': '• Přijměte náročnější objednávky',
        'levelUp.investSkills': '• Investujte do vylepšení dovedností',
        // Settings Modal
        'settings.title': '⚙️ Nastavení',
        'settings.general': 'Obecné',
        'settings.audio': 'Zvuk',
        'settings.graphics': 'Grafika',
        'settings.save': 'Uložení a data',
        'settings.language': '🌍 Jazyk',
        'settings.gameplay': '🎮 Hratelnost',
        'settings.notifications': 'Povolit oznámení',
        'settings.tutorials': 'Zobrazit návody',
        'settings.animationSpeed': 'Rychlost animace',
        'settings.sound': 'Zvukové efekty',
        'settings.music': 'Hudba na pozadí',
        'settings.quality': '🎨 Kvalita grafiky',
        'settings.autoSave': '💾 Automatické ukládání',
        'settings.enableAutoSave': 'Povolit automatické ukládání',
        'settings.dataManagement': '📁 Správa dat',
        'settings.exportSave': '📤 Exportovat uložení',
        'settings.importSave': '📥 Importovat uložení',
        'settings.cloudSync': '☁️ Cloudová synchronizace',
        'settings.cloudSyncDescription': 'Cloudová synchronizace vám umožňuje uložit pokrok online a hrát na více zařízeních.',
        'settings.comingSoon': 'Již brzy',
        // Bakery Manager Modal
        'bakeries.title': '🏪 Správce pekáren',
        'bakeries.subtitle': 'Spravujte své pekárenské impérium',
        'bakeries.owned': 'Moje pekárny',
        'bakeries.available': 'Dostupné',
        'bakeries.current': 'Aktuální',
        'bakeries.level': 'Úroveň',
        'bakeries.specialization': 'Specializace',
        'bakeries.equipment': 'Vybavení',
        'bakeries.orders': 'Aktivní objednávky',
        'bakeries.switchTo': 'Přepnout na',
        'bakeries.noOwned': 'Ještě nevlastníte žádné pekárny.',
        'bakeries.purchase': '💰 Koupit',
        'bakeries.tooExpensive': '💸 Příliš drahé',
        'bakeries.allOwned': 'Vlastníte všechny dostupné pekárny!',
        'bakeries.tips': '💡 Tipy pro pekárny',
        'bakeries.tip1': 'Každá pekárna se specializuje na různé produkty pro bonusovou efektivitu',
        'bakeries.tip2': 'Přepínejte mezi pekárnami pro správu více lokalit',
        'bakeries.tip3': 'Specializované pekárny přitahují zákazníky hledající konkrétní položky',
        'bakeries.tip4': 'Vylepšujte každou pekárnu nezávisle pro maximální zisk',
        // Notifications
        'notifications.orderAccepted': 'Objednávka přijata',
        'notifications.orderAcceptedMessage': 'Přijali jste novou objednávku!',
        'notifications.orderCompleted': 'Objednávka dokončena!',
        'notifications.orderCompletedMessage': 'Získali jste {{reward}} Kč a zkušenosti!',
        'notifications.orderDeclined': 'Objednávka odmítnuta',
        'notifications.orderDeclinedMessage': 'Objednávka byla odstraněna z vaší fronty.',
        'notifications.bakeryPurchased': 'Pekárna zakoupena!',
        'notifications.bakeryPurchasedMessage': 'Nyní vlastníte {{name}}!',
        'notifications.bakerySwitched': 'Pekárna přepnuta',
        'notifications.bakerySwitchedMessage': 'Přepnuto na {{name}}',
        // Common buttons and actions
        'common.accept': 'Přijmout',
        'common.decline': 'Odmítnout',
        'common.complete': 'Dokončit',
        'common.purchase': 'Koupit',
        'common.upgrade': 'Vylepšit',
        'common.cancel': 'Zrušit',
        'common.confirm': 'Potvrdit',
        'common.save': 'Uložit',
        'common.load': 'Načíst',
        'common.delete': 'Smazat',
        'common.edit': 'Upravit',
        'common.back': 'Zpět',
        'common.next': 'Další',
        'common.previous': 'Předchozí',
        'common.yes': 'Ano',
        'common.no': 'Ne',
        'common.create': 'Vytvořit',
        'common.join': 'Připojit se',
        'common.leave': 'Odejít',
        'common.start': 'Začít',
        'common.ready': 'Připraven',
        'common.notReady': 'Nepřipraven',
        'common.send': 'Odeslat',
        'common.refresh': 'Obnovit',
        'common.retry': 'Zkusit znovu',
        'common.reset': 'Resetovat',
        'common.clear': 'Vymazat',
        'common.apply': 'Použít',
        'common.warning': 'Varování',
        'common.info': 'Informace',
        'common.success': 'Úspěch',
        'common.error': 'Chyba',
        // Save/Load System
        'saveLoad.saveDesc': 'Vyberte slot pro uložení vašeho postupu',
        'saveLoad.loadDesc': 'Vyberte soubor uložení k načtení',
        'saveLoad.saveName': 'Název uložení',
        'saveLoad.emptySlot': 'Prázdný slot',
        'saveLoad.selectedSaveSlot': 'Vybrán: Slot {{slot}}',
        'saveLoad.selectedLoadSlot': 'Vybrán: Slot {{slot}}',
        'saveLoad.confirmOverwrite': 'Přepsat uložení?',
        'saveLoad.overwriteWarning': 'Toto přepíše existující uložení. Tuto akci nelze vrátit zpět.',
        'saveLoad.overwrite': 'Přepsat',
        'saveLoad.fileSlots': 'Souborové sloty',
        'saveLoad.gameSlots': 'Herní sloty',
        'saveLoad.exportSave': 'Exportovat uložení',
        'saveLoad.importSave': 'Importovat uložení',
        'saveLoad.deleteConfirm': 'Smazat uložení?',
        'saveLoad.deleteWarning': 'Toto trvale smaže tento soubor uložení. Tuto akci nelze vrátit zpět.',
        'saveLoad.delete': 'Smazat',
        // Game Menu
        'gameMenu.title': 'Herní menu',
        'gameMenu.subtitle': 'Spravujte svou hru',
        'gameMenu.resume': 'Pokračovat ve hře',
        'gameMenu.resumeDesc': 'Pokračovat v hraní',
        'gameMenu.save': 'Uložit hru',
        'gameMenu.saveDesc': 'Uložit váš postup',
        'gameMenu.load': 'Načíst hru',
        'gameMenu.loadDesc': 'Načíst uložený postup',
        'gameMenu.settings': 'Nastavení',
        'gameMenu.settingsDesc': 'Herní předvolby',
        'gameMenu.mainMenu': 'Hlavní menu',
        'gameMenu.mainMenuDesc': 'Návrat do hlavního menu',
        'gameMenu.exit': 'Ukončit hru',
        'gameMenu.exitDesc': 'Zavřít aplikaci',
        'gameMenu.tip': 'Stiskněte ESC pro otevření tohoto menu kdykoli',
        // Discord Rich Presence
        'settings.discord': 'Discord',
        'settings.discordRichPresence': 'Discord Rich Presence',
        'settings.discordDescription': 'Zobrazit váš aktuální herní stav a aktivitu v Discordu.',
        'settings.enableDiscordRPC': 'Povolit Discord Rich Presence',
        'settings.discordConnected': '✅ Připojeno k Discordu',
        'settings.discordDisconnected': '❌ Nepřipojeno k Discordu',
        'settings.discordInfo': 'Co je Discord Rich Presence?',
        'settings.discordInfoDesc1': 'Discord Rich Presence ukazuje vašim přátelům, co děláte v Bake It Out:',
        'settings.discordFeature1': 'Vaši aktuální úroveň a peníze',
        'settings.discordFeature2': 'Co právě pečete',
        'settings.discordFeature3': 'Informace o multiplayer místnosti',
        'settings.discordFeature4': 'Jak dlouho hrajete',
        'settings.discordInfoDesc2': 'Vaši přátelé se mohou připojit k vašim multiplayer hrám přímo z Discordu!',
        'settings.discordTroubleshooting': 'Discord není připojen',
        'settings.discordTrouble1': 'Ujistěte se, že Discord běží na vašem počítači.',
        'settings.discordTrouble2': 'Discord Rich Presence funguje pouze v desktopové verzi hry.',
        'settings.discordTrouble3': 'Zkuste restartovat Discord i hru, pokud se připojení nezdaří.',
        'settings.discordPrivacy': 'Informace o soukromí',
        'settings.discordPrivacyDesc1': 'Discord Rich Presence sdílí pouze:',
        'settings.discordPrivacy1': 'Vaši aktuální herní aktivitu (veřejné)',
        'settings.discordPrivacy2': 'Vaši úroveň hráče a postup (veřejné)',
        'settings.discordPrivacy3': 'Kódy multiplayer místností (pro připojení)',
        'settings.discordPrivacyDesc2': 'Žádné osobní informace nebo uložená data nejsou sdílena s Discordem.',
        'settings.discordStatus': 'Stav Discordu',
        'settings.discordInitializing': '🔄 Inicializace Discord RPC...',
        'settings.discordRetrying': '🔄 Opakování připojení...',
        'settings.discordUnavailable': '❌ Discord není dostupný',
        'settings.discordDesktopOnly': 'ℹ️ Discord RPC dostupný pouze v desktopové verzi',
        // Error messages and status
        'error.general': 'Došlo k chybě',
        'error.saveLoad': 'Nepodařilo se uložit/načíst hru',
        'error.connection': 'Chyba připojení',
        'error.fileNotFound': 'Soubor nenalezen',
        'error.invalidData': 'Neplatný formát dat',
        'error.permissionDenied': 'Přístup odepřen',
        'status.loading': 'Načítání...',
        'status.saving': 'Ukládání...',
        'status.connecting': 'Připojování...',
        'status.ready': 'Připraven',
        'status.success': 'Úspěch!',
        'status.failed': 'Neúspěšné',
        'status.offline': 'Offline',
        'status.online': 'Online',
        // UI Elements
        'ui.placeholder': 'Zadejte text...',
        'ui.search': 'Hledat',
        'ui.filter': 'Filtrovat',
        'ui.sort': 'Seřadit',
        'ui.ascending': 'Vzestupně',
        'ui.descending': 'Sestupně',
        'ui.selectAll': 'Vybrat vše',
        'ui.deselectAll': 'Zrušit výběr',
        'ui.noResults': 'Žádné výsledky',
        'ui.noData': 'Žádná data k dispozici',
        'ui.loading': 'Načítání...',
        'ui.saving': 'Ukládání...',
        'ui.saved': 'Uloženo!',
        'ui.failed': 'Neúspěšné',
        'ui.retry': 'Zkusit znovu',
        'ui.back': 'Zpět',
        'ui.forward': 'Vpřed',
        'ui.home': 'Domů',
        'ui.menu': 'Menu',
        'ui.options': 'Možnosti',
        'ui.preferences': 'Předvolby',
        // Multiplayer
        'multiplayer.lobby': '👥 Multiplayerová lobby',
        'multiplayer.connected': '🟢 Připojeno',
        'multiplayer.disconnected': '🔴 Odpojeno',
        'multiplayer.createRoom': 'Vytvořit místnost',
        'multiplayer.joinRoom': 'Připojit se k místnosti',
        'multiplayer.room': 'Místnost',
        'multiplayer.yourName': 'Vaše jméno',
        'multiplayer.enterName': 'Zadejte své jméno',
        'multiplayer.roomName': 'Název místnosti',
        'multiplayer.enterRoomName': 'Zadejte název místnosti',
        'multiplayer.gameMode': 'Herní režim',
        'multiplayer.cooperative': '🤝 Kooperativní',
        'multiplayer.competitive': '⚔️ Soutěžní',
        'multiplayer.maxPlayers': 'Max hráčů: {{count}}',
        'multiplayer.roomId': 'ID místnosti',
        'multiplayer.enterRoomId': 'Zadejte ID místnosti',
        'multiplayer.players': 'Hráči ({{count}})',
        'multiplayer.host': 'HOSTITEL',
        'multiplayer.level': 'Úroveň {{level}}',
        'multiplayer.chat': 'Chat',
        'multiplayer.typeMessage': 'Napište zprávu...',
        'multiplayer.gameTime': 'Herní čas: {{time}}',
        'multiplayer.teamStats': '📊 Týmové statistiky',
        'multiplayer.ordersCompleted': 'Dokončené objednávky:',
        'multiplayer.totalRevenue': 'Celkový příjem:',
        'multiplayer.teamExperience': 'Týmové zkušenosti:',
        'multiplayer.sharedKitchen': '🏪 Sdílená kuchyně',
        'multiplayer.sharedOrders': '📋 Sdílené objednávky',
        'multiplayer.sharedInventory': '📦 Sdílený sklad',
        'multiplayer.contribution': 'Příspěvek:',
        'multiplayer.online': '🟢 Online',
        'multiplayer.status': 'Stav:',
        'multiplayer.you': '(Vy)',
        'multiplayer.teamChat': '💬 Týmový chat',
        'multiplayer.chatPlaceholder': 'Zde se zobrazí zprávy chatu...',
        // Multiplayer game modes
        'multiplayer.mode.cooperative.description': '🤝 Kooperativní režim: Spolupracujte na dokončování objednávek a rozvoji sdílené pekárny!',
        'multiplayer.mode.competitive.description': '⚔️ Soutěžní režim: Soutěžte s ostatními hráči o dokončení nejvíce objednávek!',
        // Multiplayer game interface
        'multiplayer.game.title': '🎮 Multiplayerová hra - {{roomName}}',
        'multiplayer.game.mode': 'Režim: {{mode}}',
        'multiplayer.game.playersCount': 'Hráči: {{count}}',
        'multiplayer.game.playing': '🟢 Hraje se',
        'multiplayer.game.leaveGame': '🚪 Opustit hru',
        'multiplayer.game.tabs.game': 'Hra',
        'multiplayer.game.tabs.players': 'Hráči',
        'multiplayer.game.tabs.chat': 'Chat',
        // Room creation and joining
        'multiplayer.create.title': '🏗️ Vytvořit místnost',
        'multiplayer.join.title': '🚪 Připojit se k místnosti',
        'multiplayer.room.info': 'Režim: {{mode}} • Hráči: {{current}}/{{max}}',
        'multiplayer.room.readyUp': '✅ Připraven',
        'multiplayer.room.notReady': '⏳ Nepřipraven',
        'multiplayer.room.startGame': '🚀 Začít hru',
        'multiplayer.room.leaveRoom': '🚪 Opustit',
        // Connection states
        'multiplayer.connection.connecting': 'Připojování...',
        'multiplayer.connection.reconnecting': 'Znovu se připojuje...',
        'multiplayer.connection.failed': 'Připojení selhalo',
        'multiplayer.connection.error': '⚠️ {{error}}',
        // System messages
        'multiplayer.system.playerJoined': '{{name}} se připojil do místnosti',
        'multiplayer.system.playerLeft': '{{name}} opustil místnost',
        'multiplayer.system.gameStarted': 'Hra začala!',
        'multiplayer.system.gameEnded': 'Hra skončila!',
        'multiplayer.system.roomCreated': 'Místnost byla úspěšně vytvořena',
        'multiplayer.system.roomJoined': 'Úspěšně jste se připojili do místnosti'
    }
};
function LanguageProvider({ children }) {
    const [language, setLanguage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('en');
    const [mounted, setMounted] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Mark as mounted to prevent hydration mismatch
        setMounted(true);
    }, []);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Only access localStorage after component is mounted on client side
        if (!mounted) return;
        const savedLanguage = localStorage.getItem('language');
        if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'cs')) {
            setLanguage(savedLanguage);
        }
    }, [
        mounted
    ]);
    const handleSetLanguage = (lang)=>{
        setLanguage(lang);
        // Only save to localStorage if we're on the client side
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    };
    const t = (key, params)=>{
        let translation = translations[language][key] || key;
        if (params) {
            Object.entries(params).forEach(([param, value])=>{
                translation = translation.replace(`{{${param}}}`, value);
            });
        }
        return translation;
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(LanguageContext.Provider, {
        value: {
            language,
            setLanguage: handleSetLanguage,
            t
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/LanguageContext.tsx",
        lineNumber: 1005,
        columnNumber: 5
    }, this);
}
function useLanguage() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(LanguageContext);
    if (context === undefined) {
        // Fallback for when context is not available
        console.warn('useLanguage called outside of LanguageProvider, using fallback');
        return {
            language: 'en',
            setLanguage: ()=>{},
            t: (key)=>key
        };
    }
    return context;
}
}),
"[externals]/fs [external] (fs, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("fs", () => require("fs"));

module.exports = mod;
}}),
"[externals]/url [external] (url, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("url", () => require("url"));

module.exports = mod;
}}),
"[externals]/child_process [external] (child_process, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("child_process", () => require("child_process"));

module.exports = mod;
}}),
"[externals]/http [external] (http, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("http", () => require("http"));

module.exports = mod;
}}),
"[externals]/https [external] (https, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("https", () => require("https"));

module.exports = mod;
}}),
"[externals]/tty [external] (tty, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tty", () => require("tty"));

module.exports = mod;
}}),
"[externals]/util [external] (util, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("util", () => require("util"));

module.exports = mod;
}}),
"[externals]/os [external] (os, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("os", () => require("os"));

module.exports = mod;
}}),
"[externals]/stream [external] (stream, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("stream", () => require("stream"));

module.exports = mod;
}}),
"[externals]/zlib [external] (zlib, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("zlib", () => require("zlib"));

module.exports = mod;
}}),
"[externals]/buffer [external] (buffer, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("buffer", () => require("buffer"));

module.exports = mod;
}}),
"[externals]/crypto [external] (crypto, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("crypto", () => require("crypto"));

module.exports = mod;
}}),
"[externals]/events [external] (events, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("events", () => require("events"));

module.exports = mod;
}}),
"[externals]/net [external] (net, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("net", () => require("net"));

module.exports = mod;
}}),
"[externals]/tls [external] (tls, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("tls", () => require("tls"));

module.exports = mod;
}}),
"[project]/src/lib/socket.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Socket.IO client setup for multiplayer functionality
__turbopack_context__.s({
    "socketManager": ()=>socketManager,
    "useSocket": ()=>useSocket
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm-debug/index.js [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/socket.io-client/build/esm-debug/index.js [app-ssr] (ecmascript) <locals>");
;
class SocketManager {
    socket = null;
    isConnected = false;
    reconnectAttempts = 0;
    maxReconnectAttempts = 5;
    currentRoom = null;
    currentPlayer = null;
    constructor(){
    // Don't auto-connect to prevent errors when server isn't running
    // Connection will be initiated when multiplayer features are used
    }
    connect() {
        if (this.socket?.connected) return;
        try {
            this.socket = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$socket$2e$io$2d$client$2f$build$2f$esm$2d$debug$2f$index$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["io"])(process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3001', {
                transports: [
                    'websocket',
                    'polling'
                ],
                timeout: 10000,
                forceNew: true,
                autoConnect: false // Don't auto-connect to prevent immediate errors
            });
            this.setupEventListeners();
            this.socket.connect(); // Manually connect after setting up listeners
        } catch (error) {
            console.warn('Failed to initialize socket connection:', error);
        }
    }
    setupEventListeners() {
        if (!this.socket) return;
        this.socket.on('connect', ()=>{
            console.log('Connected to multiplayer server');
            this.isConnected = true;
            this.reconnectAttempts = 0;
        });
        this.socket.on('disconnect', (reason)=>{
            console.log('Disconnected from multiplayer server:', reason);
            this.isConnected = false;
            if (reason === 'io server disconnect') {
                // Server disconnected, try to reconnect
                this.handleReconnect();
            }
        });
        this.socket.on('connect_error', (error)=>{
            console.warn('Multiplayer server not available:', error.message);
            this.isConnected = false;
        // Don't attempt reconnection immediately to avoid spam
        });
        this.socket.on('error', (error)=>{
            console.warn('Socket error:', error.message);
        });
    }
    handleReconnect() {
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.reconnectAttempts++;
            console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`);
            setTimeout(()=>{
                this.connect();
            }, Math.pow(2, this.reconnectAttempts) * 1000); // Exponential backoff
        } else {
            console.error('Max reconnection attempts reached');
        }
    }
    // Public method to check connection status
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            socket: this.socket
        };
    }
    // Room management methods
    createRoom(roomData) {
        return new Promise((resolve, reject)=>{
            if (!this.socket?.connected) {
                reject(new Error('Not connected to server'));
                return;
            }
            this.socket.emit('create_room', roomData);
            this.socket.once('room_created', (room)=>{
                this.currentRoom = room;
                resolve(room);
            });
            this.socket.once('error', (error)=>{
                reject(new Error(error.message));
            });
        });
    }
    joinRoom(roomId, playerData) {
        return new Promise((resolve, reject)=>{
            if (!this.socket?.connected) {
                reject(new Error('Not connected to server'));
                return;
            }
            this.socket.emit('join_room', roomId, playerData);
            this.socket.once('room_joined', (room, player)=>{
                this.currentRoom = room;
                this.currentPlayer = player;
                resolve({
                    room,
                    player
                });
            });
            this.socket.once('error', (error)=>{
                reject(new Error(error.message));
            });
        });
    }
    leaveRoom() {
        if (this.socket?.connected && this.currentRoom) {
            this.socket.emit('leave_room', this.currentRoom.id);
            this.currentRoom = null;
            this.currentPlayer = null;
        }
    }
    // Game state methods
    sendPlayerAction(action) {
        if (this.socket?.connected && this.currentPlayer) {
            this.socket.emit('player_action', {
                ...action,
                playerId: this.currentPlayer.id,
                timestamp: Date.now()
            });
        }
    }
    sendMessage(content) {
        if (this.socket?.connected && this.currentPlayer) {
            this.socket.emit('send_message', {
                playerId: this.currentPlayer.id,
                playerName: this.currentPlayer.name,
                content,
                timestamp: Date.now()
            });
        }
    }
    // Event subscription methods
    on(event, callback) {
        this.socket?.on(event, callback);
    }
    off(event, callback) {
        this.socket?.off(event, callback);
    }
    once(event, callback) {
        this.socket?.once(event, callback);
    }
    // Utility methods
    isSocketConnected() {
        return this.isConnected && this.socket?.connected === true;
    }
    getCurrentRoom() {
        return this.currentRoom;
    }
    getCurrentPlayer() {
        return this.currentPlayer;
    }
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
            this.isConnected = false;
            this.currentRoom = null;
            this.currentPlayer = null;
        }
    }
}
const socketManager = new SocketManager();
function useSocket() {
    return socketManager;
}
}),
"[project]/src/contexts/MultiplayerContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "MultiplayerProvider": ()=>MultiplayerProvider,
    "useMultiplayer": ()=>useMultiplayer
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$socket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/socket.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
const MultiplayerContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function MultiplayerProvider({ children }) {
    // Connection state
    const [isConnected, setIsConnected] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isInRoom, setIsInRoom] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [connectionError, setConnectionError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // Room and player data
    const [currentRoom, setCurrentRoom] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [currentPlayer, setCurrentPlayer] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [players, setPlayers] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    // Game state
    const [gameState, setGameState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])('waiting');
    const [sharedGameState, setSharedGameState] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    // Chat
    const [messages, setMessages] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    // Initialize socket connection and event listeners only when needed
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        // Don't auto-connect to prevent errors when server isn't running
        // Connection will be initiated when multiplayer features are used
        const socket = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$socket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socketManager"];
        // Connection events
        const handleConnect = ()=>{
            setIsConnected(true);
            setConnectionError(null);
        };
        const handleDisconnect = ()=>{
            setIsConnected(false);
            setIsInRoom(false);
            setCurrentRoom(null);
            setCurrentPlayer(null);
            setPlayers([]);
        };
        const handleError = (error)=>{
            setConnectionError(error.message || 'Connection error');
            console.error('Multiplayer error:', error);
        };
        // Room events
        const handleRoomCreated = (room)=>{
            setCurrentRoom(room);
            setPlayers(room.players);
            setIsInRoom(true);
            setGameState(room.gameState);
            // Set current player as host
            const hostPlayer = room.players.find((p)=>p.isHost);
            if (hostPlayer) {
                setCurrentPlayer(hostPlayer);
            }
        };
        const handleRoomJoined = (room, player)=>{
            setCurrentRoom(room);
            setCurrentPlayer(player);
            setPlayers(room.players);
            setIsInRoom(true);
            setGameState(room.gameState);
        };
        const handleRoomLeft = ()=>{
            setCurrentRoom(null);
            setCurrentPlayer(null);
            setPlayers([]);
            setIsInRoom(false);
            setGameState('waiting');
            setSharedGameState(null);
            setMessages([]);
        };
        const handleRoomUpdated = (room)=>{
            setCurrentRoom(room);
            setPlayers(room.players);
            setGameState(room.gameState);
        };
        const handlePlayerJoined = (player)=>{
            setPlayers((prev)=>[
                    ...prev,
                    player
                ]);
            addSystemMessage(`${player.name} joined the room`);
        };
        const handlePlayerLeft = (playerId)=>{
            setPlayers((prev)=>{
                const leftPlayer = prev.find((p)=>p.id === playerId);
                if (leftPlayer) {
                    addSystemMessage(`${leftPlayer.name} left the room`);
                }
                return prev.filter((p)=>p.id !== playerId);
            });
        };
        // Game events
        const handleGameStarted = (gameState)=>{
            setGameState('playing');
            setSharedGameState(gameState);
            addSystemMessage('Game started!');
        };
        const handleGameStateUpdate = (update)=>{
            setSharedGameState((prev)=>prev ? {
                    ...prev,
                    ...update
                } : null);
        };
        const handlePlayerAction = (action)=>{
            // Handle player actions from other players
            console.log('Player action received:', action);
        };
        // Chat events
        const handleMessageReceived = (message)=>{
            const chatMessage = {
                id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
                playerId: message.playerId,
                playerName: message.playerName,
                content: message.content,
                timestamp: message.timestamp
            };
            setMessages((prev)=>[
                    ...prev,
                    chatMessage
                ]);
        };
        // Register event listeners
        socket.on('connect', handleConnect);
        socket.on('disconnect', handleDisconnect);
        socket.on('error', handleError);
        socket.on('room_created', handleRoomCreated);
        socket.on('room_joined', handleRoomJoined);
        socket.on('room_left', handleRoomLeft);
        socket.on('room_updated', handleRoomUpdated);
        socket.on('player_joined', handlePlayerJoined);
        socket.on('player_left', handlePlayerLeft);
        socket.on('game_started', handleGameStarted);
        socket.on('game_state_update', handleGameStateUpdate);
        socket.on('player_action', handlePlayerAction);
        socket.on('message_received', handleMessageReceived);
        // Check initial connection state
        setIsConnected(socket.isSocketConnected());
        // Cleanup
        return ()=>{
            socket.off('connect', handleConnect);
            socket.off('disconnect', handleDisconnect);
            socket.off('error', handleError);
            socket.off('room_created', handleRoomCreated);
            socket.off('room_joined', handleRoomJoined);
            socket.off('room_left', handleRoomLeft);
            socket.off('room_updated', handleRoomUpdated);
            socket.off('player_joined', handlePlayerJoined);
            socket.off('player_left', handlePlayerLeft);
            socket.off('game_started', handleGameStarted);
            socket.off('game_state_update', handleGameStateUpdate);
            socket.off('player_action', handlePlayerAction);
            socket.off('message_received', handleMessageReceived);
        };
    }, []);
    // Helper function to add system messages
    const addSystemMessage = (content)=>{
        const systemMessage = {
            id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
            playerId: 'system',
            playerName: 'System',
            content,
            timestamp: Date.now()
        };
        setMessages((prev)=>[
                ...prev,
                systemMessage
            ]);
    };
    // Action functions
    const createRoom = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (roomData, playerData)=>{
        try {
            setConnectionError(null);
            // Connect to socket server when multiplayer is actually used
            const { isConnected } = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$socket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socketManager"].getConnectionStatus();
            if (!isConnected) {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$socket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socketManager"].connect();
                // Wait a moment for connection
                await new Promise((resolve)=>setTimeout(resolve, 1000));
            }
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$socket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socketManager"].createRoom({
                ...roomData,
                hostName: playerData.name,
                hostAvatar: playerData.avatar,
                hostLevel: playerData.level
            });
        // Room created event will be handled by the event listener
        } catch (error) {
            setConnectionError(error.message);
            throw error;
        }
    }, []);
    const joinRoom = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(async (roomId, playerData)=>{
        try {
            setConnectionError(null);
            // Connect to socket server when multiplayer is actually used
            const { isConnected } = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$socket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socketManager"].getConnectionStatus();
            if (!isConnected) {
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$socket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socketManager"].connect();
                // Wait a moment for connection
                await new Promise((resolve)=>setTimeout(resolve, 1000));
            }
            await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$socket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socketManager"].joinRoom(roomId, playerData);
        // Room joined event will be handled by the event listener
        } catch (error) {
            setConnectionError(error.message);
            throw error;
        }
    }, []);
    const leaveRoom = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$socket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socketManager"].leaveRoom();
    }, []);
    const startGame = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])(()=>{
        if (currentRoom && currentPlayer?.isHost) {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$socket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socketManager"].sendPlayerAction({
                type: 'start_game',
                data: {
                    roomId: currentRoom.id
                }
            });
        }
    }, [
        currentRoom,
        currentPlayer
    ]);
    const sendMessage = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((content)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$socket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socketManager"].sendMessage(content);
    }, []);
    const sendPlayerAction = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((action)=>{
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$socket$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["socketManager"].sendPlayerAction(action);
    }, []);
    const setPlayerReady = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((ready)=>{
        if (currentPlayer) {
            sendPlayerAction({
                type: 'player_ready',
                data: {
                    ready
                }
            });
        }
    }, [
        currentPlayer,
        sendPlayerAction
    ]);
    const kickPlayer = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((playerId)=>{
        if (currentPlayer?.isHost) {
            sendPlayerAction({
                type: 'kick_player',
                data: {
                    playerId
                }
            });
        }
    }, [
        currentPlayer,
        sendPlayerAction
    ]);
    const updateRoomSettings = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useCallback"])((settings)=>{
        if (currentPlayer?.isHost) {
            sendPlayerAction({
                type: 'update_room_settings',
                data: {
                    settings
                }
            });
        }
    }, [
        currentPlayer,
        sendPlayerAction
    ]);
    const value = {
        // Connection state
        isConnected,
        isInRoom,
        connectionError,
        // Room and player data
        currentRoom,
        currentPlayer,
        players,
        // Game state
        gameState,
        sharedGameState,
        // Chat
        messages,
        // Actions
        createRoom,
        joinRoom,
        leaveRoom,
        startGame,
        sendMessage,
        sendPlayerAction,
        // Room management
        setPlayerReady,
        kickPlayer,
        updateRoomSettings
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(MultiplayerContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/MultiplayerContext.tsx",
        lineNumber: 343,
        columnNumber: 5
    }, this);
}
function useMultiplayer() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(MultiplayerContext);
    if (context === undefined) {
        // Fallback for when context is not available
        console.warn('useMultiplayer called outside of MultiplayerProvider, using fallback');
        return {
            isConnected: false,
            connectionStatus: 'disconnected',
            currentRoom: null,
            gameState: null,
            createRoom: async ()=>{},
            joinRoom: async ()=>{},
            leaveRoom: ()=>{},
            sendChatMessage: ()=>{},
            updateGameState: ()=>{},
            setPlayerReady: ()=>{}
        };
    }
    return context;
}
}),
"[project]/src/lib/discordRPC.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Discord Rich Presence integration for Bake It Out
// Shows current game status, activity, and multiplayer information
// For Electron, we'll use IPC to communicate with the main process
// which will handle the actual Discord RPC connection
__turbopack_context__.s({
    "default": ()=>__TURBOPACK__default__export__,
    "discordRPC": ()=>discordRPC
});
class DiscordRPCService {
    isConnected = false;
    isEnabled = true;
    startTime = Date.now();
    currentActivity = null;
    isElectron = false;
    // Discord Application ID for Bake It Out
    CLIENT_ID = '1234567890123456789' // This would be your actual Discord app ID
    ;
    constructor(){
        // Check if we're in Electron environment
        this.isElectron = "undefined" !== 'undefined' && window.electronAPI !== undefined;
        if (this.isElectron) {
            this.initializeRPC();
        } else {
            console.log('Discord RPC only available in desktop version');
        }
    }
    async initializeRPC() {
        if (!this.isEnabled || !this.isElectron) return;
        try {
            // Use Electron IPC to initialize Discord RPC in main process
            if (window.electronAPI && window.electronAPI.initDiscordRPC) {
                const success = await window.electronAPI.initDiscordRPC(this.CLIENT_ID);
                if (success) {
                    console.log('Discord RPC initialized successfully');
                    this.isConnected = true;
                    // Set initial activity with a delay to ensure connection is stable
                    setTimeout(async ()=>{
                        await this.updateActivity({
                            state: 'In Main Menu',
                            details: 'Starting the bakery adventure',
                            largeImageKey: 'bake_it_out_logo',
                            largeImageText: 'Bake It Out - Bakery Management Game',
                            startTimestamp: this.startTime
                        });
                    }, 1000);
                } else {
                    console.log('Discord RPC initialization failed');
                    this.isConnected = false;
                }
            } else {
                console.log('Discord RPC not available - missing Electron API');
                this.isConnected = false;
            }
        } catch (error) {
            console.log('Discord RPC initialization error:', error.message || error);
            this.isConnected = false;
        }
    }
    async updatePlayerStatus(playerStatus) {
        if (!this.isConnected || !this.client) return;
        const activity = this.createActivityFromPlayerStatus(playerStatus);
        await this.updateActivity(activity);
    }
    createActivityFromPlayerStatus(status) {
        const baseActivity = {
            state: '',
            details: '',
            largeImageKey: 'bake_it_out_logo',
            largeImageText: 'Bake It Out - Bakery Management Game',
            startTimestamp: this.startTime
        };
        switch(status.currentActivity){
            case 'menu':
                return {
                    ...baseActivity,
                    state: 'In Main Menu',
                    details: 'Choosing game mode',
                    smallImageKey: 'menu_icon',
                    smallImageText: 'Main Menu'
                };
            case 'baking':
                return {
                    ...baseActivity,
                    state: `Level ${status.level} Baker`,
                    details: status.currentOrder ? `Baking: ${status.currentOrder}` : 'Managing the bakery',
                    smallImageKey: 'baking_icon',
                    smallImageText: 'Baking',
                    buttons: [
                        {
                            label: 'Play Bake It Out',
                            url: 'https://bakeitout.game'
                        }
                    ]
                };
            case 'managing':
                return {
                    ...baseActivity,
                    state: `Level ${status.level} - $${status.money}`,
                    details: status.bakeryName ? `Managing ${status.bakeryName}` : 'Managing bakery',
                    smallImageKey: 'management_icon',
                    smallImageText: 'Bakery Management'
                };
            case 'multiplayer':
                const multiplayerActivity = {
                    ...baseActivity,
                    state: `Level ${status.level} Baker`,
                    details: 'Playing with friends',
                    smallImageKey: 'multiplayer_icon',
                    smallImageText: 'Multiplayer',
                    buttons: [
                        {
                            label: 'Join Game',
                            url: 'https://bakeitout.game/join'
                        }
                    ]
                };
                if (status.multiplayerRoom) {
                    multiplayerActivity.partyId = status.multiplayerRoom.id;
                    multiplayerActivity.partySize = status.multiplayerRoom.playerCount;
                    multiplayerActivity.partyMax = status.multiplayerRoom.maxPlayers;
                    multiplayerActivity.details = `Multiplayer Bakery (${status.multiplayerRoom.playerCount}/${status.multiplayerRoom.maxPlayers})`;
                }
                return multiplayerActivity;
            case 'idle':
                return {
                    ...baseActivity,
                    state: `Level ${status.level} Baker`,
                    details: 'Taking a break',
                    smallImageKey: 'idle_icon',
                    smallImageText: 'Idle'
                };
            default:
                return {
                    ...baseActivity,
                    state: 'Playing Bake It Out',
                    details: 'Bakery Management Game'
                };
        }
    }
    async updateActivity(activity) {
        if (!this.isConnected || !this.isElectron) return;
        try {
            if (window.electronAPI && window.electronAPI.updateDiscordRPC) {
                await window.electronAPI.updateDiscordRPC(activity);
                this.currentActivity = activity;
                console.log('Discord RPC activity updated:', activity.details);
            }
        } catch (error) {
            console.error('Failed to update Discord RPC activity:', error);
        }
    }
    async setMenuActivity() {
        await this.updateActivity({
            state: 'In Main Menu',
            details: 'Choosing game mode',
            largeImageKey: 'bake_it_out_logo',
            largeImageText: 'Bake It Out - Bakery Management Game',
            smallImageKey: 'menu_icon',
            smallImageText: 'Main Menu',
            startTimestamp: this.startTime
        });
    }
    async setGameActivity(level, money, activity) {
        await this.updateActivity({
            state: `Level ${level} - $${money}`,
            details: activity || 'Managing bakery',
            largeImageKey: 'bake_it_out_logo',
            largeImageText: 'Bake It Out - Bakery Management Game',
            smallImageKey: 'baking_icon',
            smallImageText: 'In Game',
            startTimestamp: this.startTime,
            buttons: [
                {
                    label: 'Play Bake It Out',
                    url: 'https://bakeitout.game'
                }
            ]
        });
    }
    async setMultiplayerActivity(roomId, playerCount, maxPlayers) {
        await this.updateActivity({
            state: 'Multiplayer Bakery',
            details: `Playing with friends (${playerCount}/${maxPlayers})`,
            largeImageKey: 'bake_it_out_logo',
            largeImageText: 'Bake It Out - Bakery Management Game',
            smallImageKey: 'multiplayer_icon',
            smallImageText: 'Multiplayer',
            startTimestamp: this.startTime,
            partyId: roomId,
            partySize: playerCount,
            partyMax: maxPlayers,
            buttons: [
                {
                    label: 'Join Game',
                    url: `https://bakeitout.game/join/${roomId}`
                }
            ]
        });
    }
    async setBakingActivity(level, currentOrder) {
        await this.updateActivity({
            state: `Level ${level} Baker`,
            details: `Baking: ${currentOrder}`,
            largeImageKey: 'bake_it_out_logo',
            largeImageText: 'Bake It Out - Bakery Management Game',
            smallImageKey: 'baking_icon',
            smallImageText: 'Baking',
            startTimestamp: this.startTime
        });
    }
    async clearActivity() {
        if (!this.isConnected || !this.isElectron) return;
        try {
            if (window.electronAPI && window.electronAPI.clearDiscordRPC) {
                await window.electronAPI.clearDiscordRPC();
                this.currentActivity = null;
                console.log('Discord RPC activity cleared');
            }
        } catch (error) {
            console.error('Failed to clear Discord RPC activity:', error);
        }
    }
    setEnabled(enabled) {
        this.isEnabled = enabled;
        if (!enabled && this.isConnected) {
            this.clearActivity();
            this.disconnect();
        } else if (enabled && !this.isConnected) {
            this.initializeRPC();
        }
    }
    isRPCEnabled() {
        return this.isEnabled;
    }
    isRPCConnected() {
        return this.isConnected;
    }
    getCurrentActivity() {
        return this.currentActivity;
    }
    async disconnect() {
        if (this.isConnected && this.isElectron) {
            try {
                if (window.electronAPI && window.electronAPI.disconnectDiscordRPC) {
                    await window.electronAPI.disconnectDiscordRPC();
                    console.log('Discord RPC disconnected');
                }
            } catch (error) {
                console.error('Error disconnecting Discord RPC:', error);
            }
        }
        this.isConnected = false;
        this.currentActivity = null;
    }
    // Cleanup method for when the app closes
    async cleanup() {
        await this.clearActivity();
        await this.disconnect();
    }
}
const discordRPC = new DiscordRPCService();
const __TURBOPACK__default__export__ = discordRPC;
}),
"[project]/src/lib/gameLogic.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Game logic and data structures
__turbopack_context__.s({
    "CUSTOMER_NAMES": ()=>CUSTOMER_NAMES,
    "RECIPES": ()=>RECIPES,
    "calculateExperienceReward": ()=>calculateExperienceReward,
    "calculateLevelRequirement": ()=>calculateLevelRequirement,
    "canCraftRecipe": ()=>canCraftRecipe,
    "generateRandomOrder": ()=>generateRandomOrder,
    "getAvailableRecipes": ()=>getAvailableRecipes,
    "getRecipeById": ()=>getRecipeById
});
const RECIPES = [
    {
        id: 'chocolate_chip_cookies',
        name: 'Chocolate Chip Cookies',
        ingredients: [
            {
                name: 'Flour',
                quantity: 2
            },
            {
                name: 'Sugar',
                quantity: 1
            },
            {
                name: 'Butter',
                quantity: 1
            },
            {
                name: 'Chocolate Chips',
                quantity: 1
            }
        ],
        bakingTime: 45,
        difficulty: 1,
        unlockLevel: 1,
        basePrice: 25,
        category: 'cookies'
    },
    {
        id: 'vanilla_muffins',
        name: 'Vanilla Muffins',
        ingredients: [
            {
                name: 'Flour',
                quantity: 2
            },
            {
                name: 'Sugar',
                quantity: 1
            },
            {
                name: 'Eggs',
                quantity: 1
            },
            {
                name: 'Vanilla',
                quantity: 1
            }
        ],
        bakingTime: 60,
        difficulty: 1,
        unlockLevel: 1,
        basePrice: 20,
        category: 'cakes'
    },
    {
        id: 'cinnamon_rolls',
        name: 'Cinnamon Rolls',
        ingredients: [
            {
                name: 'Flour',
                quantity: 3
            },
            {
                name: 'Sugar',
                quantity: 2
            },
            {
                name: 'Butter',
                quantity: 2
            },
            {
                name: 'Eggs',
                quantity: 1
            }
        ],
        bakingTime: 90,
        difficulty: 2,
        unlockLevel: 2,
        basePrice: 35,
        category: 'pastries'
    },
    {
        id: 'chocolate_brownies',
        name: 'Chocolate Brownies',
        ingredients: [
            {
                name: 'Flour',
                quantity: 2
            },
            {
                name: 'Sugar',
                quantity: 2
            },
            {
                name: 'Butter',
                quantity: 1
            },
            {
                name: 'Chocolate Chips',
                quantity: 2
            }
        ],
        bakingTime: 75,
        difficulty: 2,
        unlockLevel: 2,
        basePrice: 30,
        category: 'cakes'
    },
    {
        id: 'sourdough_bread',
        name: 'Sourdough Bread',
        ingredients: [
            {
                name: 'Flour',
                quantity: 4
            },
            {
                name: 'Salt',
                quantity: 1
            }
        ],
        bakingTime: 180,
        difficulty: 3,
        unlockLevel: 3,
        basePrice: 45,
        category: 'bread'
    }
];
const CUSTOMER_NAMES = [
    'Alice Johnson',
    'Bob Smith',
    'Carol Davis',
    'David Wilson',
    'Emma Brown',
    'Frank Miller',
    'Grace Taylor',
    'Henry Anderson',
    'Ivy Thomas',
    'Jack Martinez',
    'Kate Garcia',
    'Liam Rodriguez',
    'Mia Lopez',
    'Noah Gonzalez',
    'Olivia Hernandez',
    'Paul Perez',
    'Quinn Turner',
    'Ruby Phillips',
    'Sam Campbell',
    'Tina Parker'
];
function generateRandomOrder(playerLevel) {
    // Filter recipes based on player level
    const availableRecipes = RECIPES.filter((recipe)=>recipe.unlockLevel <= playerLevel);
    if (availableRecipes.length === 0) {
        // Fallback to basic recipe
        availableRecipes.push(RECIPES[0]);
    }
    // Select random recipe(s)
    const numItems = Math.random() < 0.7 ? 1 : Math.random() < 0.9 ? 2 : 3;
    const selectedRecipes = [];
    for(let i = 0; i < numItems; i++){
        const recipe = availableRecipes[Math.floor(Math.random() * availableRecipes.length)];
        selectedRecipes.push(recipe);
    }
    // Calculate order properties
    const totalDifficulty = selectedRecipes.reduce((sum, recipe)=>sum + recipe.difficulty, 0);
    const avgDifficulty = Math.ceil(totalDifficulty / selectedRecipes.length);
    const totalBasePrice = selectedRecipes.reduce((sum, recipe)=>sum + recipe.basePrice, 0);
    // Add some randomness to price and time
    const priceMultiplier = 0.8 + Math.random() * 0.4 // 0.8 to 1.2
    ;
    const timeMultiplier = 1.5 + Math.random() * 1.0 // 1.5 to 2.5
    ;
    const reward = Math.floor(totalBasePrice * priceMultiplier);
    const baseTime = selectedRecipes.reduce((sum, recipe)=>sum + recipe.bakingTime, 0);
    const timeLimit = Math.floor(baseTime * timeMultiplier);
    return {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        customerName: CUSTOMER_NAMES[Math.floor(Math.random() * CUSTOMER_NAMES.length)],
        items: selectedRecipes.map((recipe)=>recipe.name),
        timeLimit,
        reward,
        status: 'pending',
        difficulty: Math.min(5, avgDifficulty)
    };
}
function calculateExperienceReward(difficulty, timeBonus = false) {
    const baseExp = difficulty * 10;
    const bonus = timeBonus ? Math.floor(baseExp * 0.5) : 0;
    return baseExp + bonus;
}
function calculateLevelRequirement(level) {
    return level * 100 + (level - 1) * 50;
}
function canCraftRecipe(recipe, inventory) {
    return recipe.ingredients.every((ingredient)=>{
        const inventoryItem = inventory.find((item)=>item.name === ingredient.name);
        return inventoryItem && inventoryItem.quantity >= ingredient.quantity;
    });
}
function getRecipeById(id) {
    return RECIPES.find((recipe)=>recipe.id === id);
}
function getAvailableRecipes(playerLevel) {
    return RECIPES.filter((recipe)=>recipe.unlockLevel <= playerLevel);
}
}),
"[project]/src/lib/progressionSystem.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Advanced progression system for Bake It Out
__turbopack_context__.s({
    "ACHIEVEMENTS": ()=>ACHIEVEMENTS,
    "SKILL_TREE": ()=>SKILL_TREE,
    "calculateExperienceRequired": ()=>calculateExperienceRequired,
    "calculateTotalExperienceForLevel": ()=>calculateTotalExperienceForLevel,
    "getLevelFromExperience": ()=>getLevelFromExperience,
    "getLevelRewards": ()=>getLevelRewards
});
function calculateExperienceRequired(level) {
    if (level <= 1) return 0;
    return Math.floor(100 * Math.pow(1.15, level - 1));
}
function calculateTotalExperienceForLevel(level) {
    let total = 0;
    for(let i = 1; i <= level; i++){
        total += calculateExperienceRequired(i);
    }
    return total;
}
function getLevelFromExperience(experience) {
    let level = 1;
    let totalExp = 0;
    while(true){
        const expRequired = calculateExperienceRequired(level + 1);
        if (totalExp + expRequired > experience) {
            break;
        }
        totalExp += expRequired;
        level++;
    }
    const expRequired = calculateExperienceRequired(level + 1);
    const currentLevelExp = experience - totalExp;
    return {
        level,
        experience: currentLevelExp,
        experienceRequired: expRequired,
        totalExperience: experience,
        rewards: getLevelRewards(level)
    };
}
function getLevelRewards(level) {
    const rewards = [];
    // Money rewards every level
    rewards.push({
        type: 'money',
        id: `money_${level}`,
        name: 'Level Bonus',
        description: `Bonus money for reaching level ${level}`,
        value: level * 25
    });
    // Recipe unlocks at specific levels
    const recipeUnlocks = {
        2: [
            'cinnamon_rolls'
        ],
        3: [
            'chocolate_brownies',
            'sourdough_bread'
        ],
        4: [
            'croissants'
        ],
        5: [
            'cheesecake'
        ],
        6: [
            'macarons'
        ],
        7: [
            'honey_glazed_donuts'
        ],
        8: [
            'sourdough_bread'
        ],
        9: [
            'chocolate_souffle'
        ],
        10: [
            'croquembouche'
        ],
        12: [
            'opera_cake'
        ],
        15: [
            'artisan_pizza_dough'
        ]
    };
    if (recipeUnlocks[level]) {
        recipeUnlocks[level].forEach((recipeId)=>{
            rewards.push({
                type: 'recipe',
                id: recipeId,
                name: 'New Recipe Unlocked',
                description: `You can now bake ${recipeId.replace(/_/g, ' ')}`
            });
        });
    }
    // Equipment unlocks
    const equipmentUnlocks = {
        3: [
            'professional_oven'
        ],
        4: [
            'auto_mixer'
        ],
        5: [
            'stand_mixer'
        ],
        6: [
            'auto_oven'
        ],
        7: [
            'conveyor_belt'
        ],
        8: [
            'advanced_auto_mixer'
        ],
        10: [
            'industrial_oven'
        ],
        12: [
            'smart_conveyor_system'
        ]
    };
    if (equipmentUnlocks[level]) {
        equipmentUnlocks[level].forEach((equipmentId)=>{
            rewards.push({
                type: 'equipment',
                id: equipmentId,
                name: 'New Equipment Available',
                description: `${equipmentId.replace(/_/g, ' ')} is now available for purchase`
            });
        });
    }
    // Skill points every 2 levels
    if (level % 2 === 0) {
        rewards.push({
            type: 'skill_point',
            id: `skill_point_${level}`,
            name: 'Skill Point',
            description: 'Use this to upgrade your skills in the technology tree',
            value: 1
        });
    }
    return rewards;
}
const ACHIEVEMENTS = [
    // Baking Achievements
    {
        id: 'first_order',
        name: 'First Customer',
        description: 'Complete your first order',
        icon: '🎯',
        category: 'baking',
        requirements: [
            {
                type: 'orders_completed',
                target: 1
            }
        ],
        reward: {
            type: 'money',
            id: 'first_order_bonus',
            name: 'First Order Bonus',
            description: 'Bonus for first order',
            value: 50
        },
        unlocked: true,
        completed: false
    },
    {
        id: 'baker_apprentice',
        name: 'Baker Apprentice',
        description: 'Complete 10 orders',
        icon: '👨‍🍳',
        category: 'baking',
        requirements: [
            {
                type: 'orders_completed',
                target: 10
            }
        ],
        reward: {
            type: 'skill_point',
            id: 'apprentice_skill',
            name: 'Skill Point',
            description: 'Gain 1 skill point',
            value: 1
        },
        unlocked: true,
        completed: false
    },
    {
        id: 'baker_journeyman',
        name: 'Baker Journeyman',
        description: 'Complete 50 orders',
        icon: '👨‍🍳',
        category: 'baking',
        requirements: [
            {
                type: 'orders_completed',
                target: 50
            }
        ],
        reward: {
            type: 'money',
            id: 'journeyman_bonus',
            name: 'Journeyman Bonus',
            description: 'Large money bonus',
            value: 500
        },
        unlocked: true,
        completed: false
    },
    {
        id: 'master_baker',
        name: 'Master Baker',
        description: 'Complete 100 orders',
        icon: '🏆',
        category: 'baking',
        requirements: [
            {
                type: 'orders_completed',
                target: 100
            }
        ],
        reward: {
            type: 'skill_point',
            id: 'master_skill',
            name: 'Master Skill Points',
            description: 'Gain 3 skill points',
            value: 3
        },
        unlocked: true,
        completed: false
    },
    {
        id: 'speed_baker',
        name: 'Speed Baker',
        description: 'Bake 100 items',
        icon: '⚡',
        category: 'efficiency',
        requirements: [
            {
                type: 'items_baked',
                target: 100
            }
        ],
        reward: {
            type: 'skill_point',
            id: 'speed_skill',
            name: 'Speed Skill Point',
            description: 'Gain 1 skill point',
            value: 1
        },
        unlocked: true,
        completed: false
    },
    {
        id: 'money_maker',
        name: 'Money Maker',
        description: 'Earn $1000 total',
        icon: '💰',
        category: 'business',
        requirements: [
            {
                type: 'money_earned',
                target: 1000
            }
        ],
        reward: {
            type: 'skill_point',
            id: 'money_maker_skill',
            name: 'Business Skill Point',
            description: 'Extra skill point for business success',
            value: 1
        },
        unlocked: true,
        completed: false
    },
    {
        id: 'recipe_collector',
        name: 'Recipe Collector',
        description: 'Unlock 5 different recipes',
        icon: '📚',
        category: 'collection',
        requirements: [
            {
                type: 'recipes_unlocked',
                target: 5
            }
        ],
        reward: {
            type: 'money',
            id: 'recipe_bonus',
            name: 'Recipe Collection Bonus',
            description: 'Bonus for collecting recipes',
            value: 200
        },
        unlocked: true,
        completed: false
    },
    {
        id: 'level_master',
        name: 'Level Master',
        description: 'Reach level 10',
        icon: '⭐',
        category: 'special',
        requirements: [
            {
                type: 'level_reached',
                target: 10
            }
        ],
        reward: {
            type: 'skill_point',
            id: 'level_master_skill',
            name: 'Master Level Bonus',
            description: 'Gain 2 skill points',
            value: 2
        },
        unlocked: true,
        completed: false
    },
    // Additional Business Achievements
    {
        id: 'first_hundred',
        name: 'First Hundred',
        description: 'Earn $100 total',
        icon: '💵',
        category: 'business',
        requirements: [
            {
                type: 'money_earned',
                target: 100
            }
        ],
        reward: {
            type: 'money',
            id: 'first_hundred_bonus',
            name: 'Business Bonus',
            description: 'Small business milestone bonus',
            value: 25
        },
        unlocked: true,
        completed: false
    },
    {
        id: 'entrepreneur',
        name: 'Entrepreneur',
        description: 'Earn $5000 total',
        icon: '🏢',
        category: 'business',
        requirements: [
            {
                type: 'money_earned',
                target: 5000
            }
        ],
        reward: {
            type: 'skill_point',
            id: 'entrepreneur_skill',
            name: 'Business Skill Points',
            description: 'Gain 2 skill points',
            value: 2
        },
        unlocked: true,
        completed: false
    },
    // Equipment Achievements
    {
        id: 'equipment_enthusiast',
        name: 'Equipment Enthusiast',
        description: 'Own 3 pieces of equipment',
        icon: '⚙️',
        category: 'collection',
        requirements: [
            {
                type: 'equipment_owned',
                target: 3
            }
        ],
        reward: {
            type: 'money',
            id: 'equipment_bonus',
            name: 'Equipment Bonus',
            description: 'Equipment investment bonus',
            value: 300
        },
        unlocked: true,
        completed: false
    },
    // Level Achievements
    {
        id: 'rising_star',
        name: 'Rising Star',
        description: 'Reach level 5',
        icon: '🌟',
        category: 'special',
        requirements: [
            {
                type: 'level_reached',
                target: 5
            }
        ],
        reward: {
            type: 'skill_point',
            id: 'rising_star_skill',
            name: 'Rising Star Bonus',
            description: 'Gain 1 skill point',
            value: 1
        },
        unlocked: true,
        completed: false
    },
    {
        id: 'legendary_baker',
        name: 'Legendary Baker',
        description: 'Reach level 20',
        icon: '👑',
        category: 'special',
        requirements: [
            {
                type: 'level_reached',
                target: 20
            }
        ],
        reward: {
            type: 'skill_point',
            id: 'legendary_skill',
            name: 'Legendary Bonus',
            description: 'Gain 5 skill points',
            value: 5
        },
        unlocked: true,
        completed: false
    }
];
const SKILL_TREE = [
    {
        id: 'baking_speed_1',
        name: 'Quick Hands',
        description: 'Increase baking speed by 10%',
        icon: '⚡',
        category: 'efficiency',
        level: 0,
        maxLevel: 3,
        cost: 1,
        requirements: {
            playerLevel: 2
        },
        effects: [
            {
                type: 'baking_speed',
                value: 0.1
            }
        ]
    },
    {
        id: 'money_bonus_1',
        name: 'Business Sense',
        description: 'Increase money earned by 15%',
        icon: '💼',
        category: 'business',
        level: 0,
        maxLevel: 3,
        cost: 1,
        requirements: {
            playerLevel: 3
        },
        effects: [
            {
                type: 'money_multiplier',
                value: 0.15
            }
        ]
    },
    {
        id: 'xp_bonus_1',
        name: 'Fast Learner',
        description: 'Increase experience gained by 20%',
        icon: '📈',
        category: 'efficiency',
        level: 0,
        maxLevel: 2,
        cost: 2,
        requirements: {
            playerLevel: 4
        },
        effects: [
            {
                type: 'xp_multiplier',
                value: 0.2
            }
        ]
    },
    {
        id: 'ingredient_efficiency_1',
        name: 'Efficient Baker',
        description: 'Use 10% fewer ingredients',
        icon: '🌾',
        category: 'efficiency',
        level: 0,
        maxLevel: 2,
        cost: 2,
        requirements: {
            playerLevel: 5,
            skills: [
                'baking_speed_1'
            ]
        },
        effects: [
            {
                type: 'ingredient_efficiency',
                value: 0.1
            }
        ]
    },
    {
        id: 'automation_unlock_1',
        name: 'Automation Expert',
        description: 'Unlock advanced automation features',
        icon: '🤖',
        category: 'automation',
        level: 0,
        maxLevel: 1,
        cost: 3,
        requirements: {
            playerLevel: 8,
            achievements: [
                'baker_apprentice'
            ]
        },
        effects: [
            {
                type: 'automation_unlock',
                value: 1
            }
        ]
    }
];
}),
"[project]/src/lib/automationSystem.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Automation system for Bake It Out
__turbopack_context__.s({
    "AUTOMATION_UPGRADES": ()=>AUTOMATION_UPGRADES,
    "addItemToConveyor": ()=>addItemToConveyor,
    "calculateAutomationEfficiency": ()=>calculateAutomationEfficiency,
    "calculateAutomationSpeed": ()=>calculateAutomationSpeed,
    "canAutomate": ()=>canAutomate,
    "generateAutomationJob": ()=>generateAutomationJob,
    "selectOptimalRecipe": ()=>selectOptimalRecipe,
    "updateConveyorBelt": ()=>updateConveyorBelt
});
const AUTOMATION_UPGRADES = [
    {
        id: 'auto_queue_basic',
        name: 'Basic Auto-Queue',
        description: 'Equipment automatically starts the next recipe when finished',
        type: 'intelligence',
        cost: 500,
        unlockLevel: 4,
        effects: {
            autoQueueing: true
        }
    },
    {
        id: 'efficiency_boost_1',
        name: 'Efficiency Boost I',
        description: 'Automated equipment uses 10% fewer ingredients',
        type: 'efficiency',
        cost: 750,
        unlockLevel: 5,
        effects: {
            efficiencyBonus: 0.1
        }
    },
    {
        id: 'speed_boost_1',
        name: 'Speed Boost I',
        description: 'Automated equipment works 15% faster',
        type: 'speed',
        cost: 1000,
        unlockLevel: 6,
        effects: {
            speedMultiplier: 1.15
        }
    },
    {
        id: 'smart_prioritization',
        name: 'Smart Prioritization',
        description: 'Automation prioritizes orders based on profit and urgency',
        type: 'intelligence',
        cost: 1500,
        unlockLevel: 8,
        effects: {
            smartPrioritization: true
        }
    },
    {
        id: 'efficiency_boost_2',
        name: 'Efficiency Boost II',
        description: 'Automated equipment uses 20% fewer ingredients',
        type: 'efficiency',
        cost: 2000,
        unlockLevel: 10,
        effects: {
            efficiencyBonus: 0.2
        }
    },
    {
        id: 'speed_boost_2',
        name: 'Speed Boost II',
        description: 'Automated equipment works 30% faster',
        type: 'speed',
        cost: 2500,
        unlockLevel: 12,
        effects: {
            speedMultiplier: 1.3
        }
    }
];
function calculateAutomationEfficiency(baseEfficiency, automationLevel, upgrades, skillBonuses = 0) {
    let efficiency = baseEfficiency;
    // Automation level bonus
    efficiency *= 1 + automationLevel * 0.1;
    // Upgrade bonuses
    upgrades.forEach((upgradeId)=>{
        const upgrade = AUTOMATION_UPGRADES.find((u)=>u.id === upgradeId);
        if (upgrade?.effects.efficiencyBonus) {
            efficiency *= 1 + upgrade.effects.efficiencyBonus;
        }
    });
    // Skill bonuses
    efficiency *= 1 + skillBonuses;
    return Math.min(efficiency, 2.0) // Cap at 200% efficiency
    ;
}
function calculateAutomationSpeed(baseSpeed, automationLevel, upgrades, skillBonuses = 0) {
    let speed = baseSpeed;
    // Automation level bonus
    speed *= 1 + automationLevel * 0.05;
    // Upgrade bonuses
    upgrades.forEach((upgradeId)=>{
        const upgrade = AUTOMATION_UPGRADES.find((u)=>u.id === upgradeId);
        if (upgrade?.effects.speedMultiplier) {
            speed *= upgrade.effects.speedMultiplier;
        }
    });
    // Skill bonuses
    speed *= 1 + skillBonuses;
    return speed;
}
function canAutomate(equipmentType, automationLevel) {
    const automationRequirements = {
        'oven': 1,
        'mixer': 1,
        'counter': 2,
        'auto_oven': 0,
        'auto_mixer': 0,
        'conveyor': 0
    };
    return automationLevel >= (automationRequirements[equipmentType] || 999);
}
function generateAutomationJob(equipmentId, recipeId, recipe, efficiency) {
    const adjustedDuration = Math.floor(recipe.bakingTime / efficiency);
    const adjustedIngredients = recipe.ingredients.map((ing)=>({
            ...ing,
            quantity: Math.ceil(ing.quantity * (1 - (efficiency - 1) * 0.1))
        }));
    return {
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
        equipmentId,
        recipeId,
        startTime: Date.now(),
        duration: adjustedDuration,
        status: 'queued',
        ingredients: adjustedIngredients,
        efficiency
    };
}
function selectOptimalRecipe(availableRecipes, inventory, priorityMode, currentOrders) {
    const craftableRecipes = availableRecipes.filter((recipe)=>recipe.ingredients.every((ingredient)=>{
            const inventoryItem = inventory.find((item)=>item.name === ingredient.name);
            return inventoryItem && inventoryItem.quantity >= ingredient.quantity;
        }));
    if (craftableRecipes.length === 0) return null;
    switch(priorityMode){
        case 'speed':
            return craftableRecipes.reduce((fastest, recipe)=>recipe.bakingTime < fastest.bakingTime ? recipe : fastest).id;
        case 'profit':
            return craftableRecipes.reduce((mostProfitable, recipe)=>recipe.basePrice > mostProfitable.basePrice ? recipe : mostProfitable).id;
        case 'efficiency':
            // Prioritize recipes needed for current orders
            const neededRecipes = currentOrders.flatMap((order)=>order.items);
            const neededCraftable = craftableRecipes.filter((recipe)=>neededRecipes.includes(recipe.name));
            if (neededCraftable.length > 0) {
                return neededCraftable[0].id;
            }
            return craftableRecipes[0].id;
        default:
            return craftableRecipes[0].id;
    }
}
function updateConveyorBelt(belt, deltaTime) {
    const speed = belt.speed / 60 // convert to items per second
    ;
    const moveDistance = speed * deltaTime;
    const updatedItems = belt.items.map((item)=>({
            ...item,
            position: Math.min(1, item.position + moveDistance)
        }));
    // Remove items that reached the end
    const activeItems = updatedItems.filter((item)=>item.position < 1);
    return {
        ...belt,
        items: activeItems
    };
}
function addItemToConveyor(belt, item) {
    if (belt.items.length >= belt.capacity) {
        return belt // Belt is full
        ;
    }
    const newItem = {
        ...item,
        position: 0
    };
    return {
        ...belt,
        items: [
            ...belt.items,
            newItem
        ]
    };
}
}),
"[project]/src/lib/saveSystem.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

// Save/Load system for Bake It Out
__turbopack_context__.s({
    "SaveSystem": ()=>SaveSystem,
    "saveSystem": ()=>saveSystem
});
const SAVE_VERSION = '1.0.0';
const LOCAL_STORAGE_KEY = 'bakeItOut_gameSave';
const AUTO_SAVE_INTERVAL = 30000 // 30 seconds
;
class SaveSystem {
    autoSaveInterval = null;
    cloudSyncEnabled = false;
    constructor(){
        this.initializeAutoSave();
    }
    // Local Storage Operations
    saveToLocal(gameData) {
        try {
            const save = {
                version: SAVE_VERSION,
                timestamp: Date.now(),
                ...gameData
            };
            const saveString = JSON.stringify(save);
            localStorage.setItem(LOCAL_STORAGE_KEY, saveString);
            console.log('Game saved to local storage');
            return true;
        } catch (error) {
            console.error('Failed to save game to local storage:', error);
            return false;
        }
    }
    loadFromLocal() {
        try {
            const saveString = localStorage.getItem(LOCAL_STORAGE_KEY);
            if (!saveString) return null;
            const save = JSON.parse(saveString);
            // Version compatibility check
            if (save.version !== SAVE_VERSION) {
                console.warn('Save version mismatch, attempting migration');
                return this.migrateSave(save);
            }
            console.log('Game loaded from local storage');
            return save;
        } catch (error) {
            console.error('Failed to load game from local storage:', error);
            return null;
        }
    }
    deleteLocalSave() {
        try {
            localStorage.removeItem(LOCAL_STORAGE_KEY);
            console.log('Local save deleted');
            return true;
        } catch (error) {
            console.error('Failed to delete local save:', error);
            return false;
        }
    }
    // Auto-save functionality
    initializeAutoSave() {
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
    }
    triggerAutoSave() {
        // This would be called by the game context to auto-save
        const event = new CustomEvent('autoSave');
        window.dispatchEvent(event);
    }
    stopAutoSave() {
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
            this.autoSaveInterval = null;
        }
    }
    // Cloud Save Operations (placeholder for future implementation)
    async saveToCloud(gameData, userId) {
        try {
            // This would integrate with a cloud service like Firebase, Supabase, etc.
            const metadata = {
                id: `${userId}_${Date.now()}`,
                userId,
                deviceId: this.getDeviceId(),
                lastModified: Date.now(),
                gameVersion: SAVE_VERSION,
                bakeryCount: gameData.bakeries?.length || 1,
                playerLevel: gameData.player.level
            };
            // Placeholder for cloud save implementation
            console.log('Cloud save would be implemented here', {
                gameData,
                metadata
            });
            return true;
        } catch (error) {
            console.error('Failed to save to cloud:', error);
            return false;
        }
    }
    async loadFromCloud(userId) {
        try {
            // Placeholder for cloud load implementation
            console.log('Cloud load would be implemented here', {
                userId
            });
            return null;
        } catch (error) {
            console.error('Failed to load from cloud:', error);
            return null;
        }
    }
    async syncWithCloud(localSave, userId) {
        try {
            const cloudSave = await this.loadFromCloud(userId);
            if (!cloudSave) {
                // No cloud save exists, upload local save
                await this.saveToCloud(localSave, userId);
                return localSave;
            }
            // Compare timestamps and merge
            if (cloudSave.timestamp > localSave.timestamp) {
                console.log('Cloud save is newer, using cloud data');
                return cloudSave;
            } else {
                console.log('Local save is newer, uploading to cloud');
                await this.saveToCloud(localSave, userId);
                return localSave;
            }
        } catch (error) {
            console.error('Failed to sync with cloud:', error);
            return localSave;
        }
    }
    // Save migration for version compatibility
    migrateSave(oldSave) {
        try {
            // Handle migration from older save versions
            const migratedSave = {
                version: SAVE_VERSION,
                timestamp: oldSave.timestamp || Date.now(),
                player: {
                    level: oldSave.player?.level || 1,
                    experience: oldSave.player?.experience || 0,
                    money: oldSave.player?.money || 100,
                    skillPoints: oldSave.player?.skillPoints || 0,
                    totalMoneyEarned: oldSave.player?.totalMoneyEarned || 0,
                    totalOrdersCompleted: oldSave.player?.totalOrdersCompleted || 0,
                    totalItemsBaked: oldSave.player?.totalItemsBaked || 0,
                    unlockedRecipes: oldSave.player?.unlockedRecipes || [
                        'chocolate_chip_cookies',
                        'vanilla_muffins'
                    ],
                    automationUpgrades: oldSave.player?.automationUpgrades || []
                },
                equipment: oldSave.equipment || [],
                inventory: oldSave.inventory || [],
                achievements: oldSave.achievements || [],
                skills: oldSave.skills || [],
                automationSettings: oldSave.automationSettings || {},
                gameSettings: {
                    language: oldSave.gameSettings?.language || 'en',
                    soundEnabled: oldSave.gameSettings?.soundEnabled ?? true,
                    musicEnabled: oldSave.gameSettings?.musicEnabled ?? true,
                    notificationsEnabled: oldSave.gameSettings?.notificationsEnabled ?? true,
                    autoSaveEnabled: oldSave.gameSettings?.autoSaveEnabled ?? true
                },
                bakeries: oldSave.bakeries || [],
                currentBakeryId: oldSave.currentBakeryId || 'main'
            };
            console.log('Save migrated successfully');
            return migratedSave;
        } catch (error) {
            console.error('Failed to migrate save:', error);
            return null;
        }
    }
    // Utility functions
    getDeviceId() {
        let deviceId = localStorage.getItem('deviceId');
        if (!deviceId) {
            deviceId = 'device_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
            localStorage.setItem('deviceId', deviceId);
        }
        return deviceId;
    }
    exportSave(gameData) {
        return JSON.stringify(gameData, null, 2);
    }
    importSave(saveString) {
        try {
            const save = JSON.parse(saveString);
            return this.migrateSave(save);
        } catch (error) {
            console.error('Failed to import save:', error);
            return null;
        }
    }
    // Backup management
    createBackup(gameData) {
        try {
            const backupKey = `${LOCAL_STORAGE_KEY}_backup_${Date.now()}`;
            localStorage.setItem(backupKey, JSON.stringify(gameData));
            // Keep only the 5 most recent backups
            this.cleanupOldBackups();
            return true;
        } catch (error) {
            console.error('Failed to create backup:', error);
            return false;
        }
    }
    cleanupOldBackups() {
        const backupKeys = Object.keys(localStorage).filter((key)=>key.startsWith(`${LOCAL_STORAGE_KEY}_backup_`)).sort();
        while(backupKeys.length > 5){
            const oldestKey = backupKeys.shift();
            if (oldestKey) {
                localStorage.removeItem(oldestKey);
            }
        }
    }
    getBackups() {
        const backups = [];
        Object.keys(localStorage).forEach((key)=>{
            if (key.startsWith(`${LOCAL_STORAGE_KEY}_backup_`)) {
                try {
                    const data = JSON.parse(localStorage.getItem(key) || '{}');
                    const timestamp = parseInt(key.split('_').pop() || '0');
                    backups.push({
                        key,
                        timestamp,
                        data
                    });
                } catch (error) {
                    console.error('Failed to parse backup:', error);
                }
            }
        });
        return backups.sort((a, b)=>b.timestamp - a.timestamp);
    }
}
const saveSystem = new SaveSystem();
}),
"[project]/src/contexts/GameContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "GameProvider": ()=>GameProvider,
    "useGame": ()=>useGame
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$gameLogic$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/gameLogic.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$progressionSystem$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/progressionSystem.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automationSystem$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/automationSystem.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$saveSystem$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/saveSystem.ts [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
;
const GameContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
const RECIPES = [
    'Chocolate Chip Cookies',
    'Vanilla Muffins',
    'Cinnamon Rolls',
    'Brownies',
    'Croissants',
    'Bread Loaf',
    'Cupcakes',
    'Apple Pie'
];
const CUSTOMER_NAMES = [
    'Alice Johnson',
    'Bob Smith',
    'Carol Davis',
    'David Wilson',
    'Emma Brown',
    'Frank Miller',
    'Grace Taylor',
    'Henry Anderson',
    'Ivy Thomas',
    'Jack Martinez',
    'Kate Garcia',
    'Liam Rodriguez'
];
function GameProvider({ children }) {
    const [player, setPlayer] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        level: 1,
        experience: 0,
        money: 100,
        maxExperience: 100,
        skillPoints: 0,
        totalMoneyEarned: 0,
        totalOrdersCompleted: 0,
        totalItemsBaked: 0,
        unlockedRecipes: [
            'chocolate_chip_cookies',
            'vanilla_muffins'
        ],
        automationUpgrades: []
    });
    const [equipment, setEquipment] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([
        {
            id: 'oven1',
            name: 'Basic Oven',
            type: 'oven',
            isActive: false,
            level: 1,
            efficiency: 1.0,
            automationLevel: 0
        },
        {
            id: 'mixer1',
            name: 'Hand Mixer',
            type: 'mixer',
            isActive: false,
            level: 1,
            efficiency: 1.0,
            automationLevel: 0
        },
        {
            id: 'counter1',
            name: 'Work Counter',
            type: 'counter',
            isActive: false,
            level: 1,
            efficiency: 1.0,
            automationLevel: 0
        }
    ]);
    const [inventory, setInventory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([
        {
            name: 'Flour',
            quantity: 15,
            cost: 5,
            icon: '🌾'
        },
        {
            name: 'Sugar',
            quantity: 12,
            cost: 8,
            icon: '🍯'
        },
        {
            name: 'Eggs',
            quantity: 10,
            cost: 12,
            icon: '🥚'
        },
        {
            name: 'Butter',
            quantity: 8,
            cost: 15,
            icon: '🧈'
        },
        {
            name: 'Chocolate Chips',
            quantity: 6,
            cost: 20,
            icon: '🍫'
        },
        {
            name: 'Vanilla',
            quantity: 5,
            cost: 25,
            icon: '🌿'
        },
        {
            name: 'Salt',
            quantity: 10,
            cost: 3,
            icon: '🧂'
        }
    ]);
    const [orders, setOrders] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([
        {
            id: '1',
            customerName: 'Alice Johnson',
            items: [
                'Chocolate Chip Cookies'
            ],
            timeLimit: 300,
            reward: 25,
            status: 'pending',
            difficulty: 1
        }
    ]);
    const [achievements, setAchievements] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$progressionSystem$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["ACHIEVEMENTS"]);
    const [skills, setSkills] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$progressionSystem$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SKILL_TREE"]);
    const [levelUpRewards, setLevelUpRewards] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [showLevelUp, setShowLevelUp] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [automationSettings, setAutomationSettings] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])({
        enabled: false,
        autoStart: false,
        preferredRecipes: [],
        maxConcurrentJobs: 2,
        priorityMode: 'efficiency',
        ingredientThreshold: 5
    });
    const [automationJobs, setAutomationJobs] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    const [conveyorBelts, setConveyorBelts] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])([]);
    // Equipment timer effect
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const interval = setInterval(()=>{
            setEquipment((prev)=>prev.map((eq)=>{
                    if (eq.isActive && eq.timeRemaining && eq.timeRemaining > 0) {
                        return {
                            ...eq,
                            timeRemaining: eq.timeRemaining - 1
                        };
                    } else if (eq.isActive && eq.timeRemaining === 0) {
                        // Baking completed - could add notification here
                        return {
                            ...eq,
                            isActive: false,
                            timeRemaining: undefined,
                            currentRecipe: undefined
                        };
                    }
                    return eq;
                }));
        }, 1000);
        return ()=>clearInterval(interval);
    }, []);
    // Order timer effect
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const interval = setInterval(()=>{
            setOrders((prev)=>prev.map((order)=>{
                    if ((order.status === 'accepted' || order.status === 'in_progress') && order.timeLimit > 0) {
                        const newTimeLimit = order.timeLimit - 1;
                        if (newTimeLimit === 0) {
                            return {
                                ...order,
                                status: 'failed',
                                timeLimit: 0
                            };
                        }
                        return {
                            ...order,
                            timeLimit: newTimeLimit
                        };
                    }
                    return order;
                }));
        }, 1000);
        return ()=>clearInterval(interval);
    }, []);
    const updatePlayer = (updates)=>{
        setPlayer((prev)=>({
                ...prev,
                ...updates
            }));
    };
    const updateEquipment = (equipmentId, updates)=>{
        setEquipment((prev)=>prev.map((eq)=>eq.id === equipmentId ? {
                    ...eq,
                    ...updates
                } : eq));
    };
    const addEquipment = (equipmentData)=>{
        const newEquipment = {
            ...equipmentData,
            id: Date.now().toString() + Math.random().toString(36).substring(2, 11)
        };
        setEquipment((prev)=>[
                ...prev,
                newEquipment
            ]);
        // Check achievements after equipment purchase
        setTimeout(()=>checkAchievements(), 100);
    };
    const addExperience = (amount)=>{
        setPlayer((prev)=>{
            const newTotalExp = prev.experience + amount;
            const levelData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$progressionSystem$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getLevelFromExperience"])(newTotalExp);
            const leveledUp = levelData.level > prev.level;
            if (leveledUp) {
                setLevelUpRewards(levelData.rewards);
                setShowLevelUp(true);
                // Add skill points for level up
                const skillPointsGained = levelData.level % 2 === 0 ? 1 : 0;
                // Check achievements after level up
                setTimeout(()=>checkAchievements(), 100);
                return {
                    ...prev,
                    level: levelData.level,
                    experience: newTotalExp,
                    maxExperience: levelData.experienceRequired,
                    skillPoints: prev.skillPoints + skillPointsGained
                };
            }
            return {
                ...prev,
                experience: newTotalExp,
                maxExperience: levelData.experienceRequired
            };
        });
    };
    const addMoney = (amount)=>{
        setPlayer((prev)=>({
                ...prev,
                money: prev.money + amount,
                totalMoneyEarned: prev.totalMoneyEarned + amount
            }));
        // Check achievements after money update
        setTimeout(()=>checkAchievements(), 100);
    };
    const spendMoney = (amount)=>{
        if (player.money >= amount) {
            setPlayer((prev)=>({
                    ...prev,
                    money: prev.money - amount
                }));
            return true;
        }
        return false;
    };
    const useIngredient = (name, quantity)=>{
        const ingredient = inventory.find((ing)=>ing.name === name);
        if (ingredient && ingredient.quantity >= quantity) {
            setInventory((prev)=>prev.map((ing)=>ing.name === name ? {
                        ...ing,
                        quantity: ing.quantity - quantity
                    } : ing));
            return true;
        }
        return false;
    };
    const addIngredient = (name, quantity)=>{
        setInventory((prev)=>prev.map((ing)=>ing.name === name ? {
                    ...ing,
                    quantity: ing.quantity + quantity
                } : ing));
    };
    const acceptOrder = (orderId)=>{
        setOrders((prev)=>prev.map((order)=>order.id === orderId ? {
                    ...order,
                    status: 'accepted'
                } : order));
    };
    const completeOrder = (orderId)=>{
        const order = orders.find((o)=>o.id === orderId);
        if (order) {
            // Check if player has required ingredients
            const canComplete = order.items.every((itemName)=>{
                const recipe = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$gameLogic$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getRecipeById"])(itemName.toLowerCase().replace(/\s+/g, '_'));
                return recipe ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$gameLogic$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["canCraftRecipe"])(recipe, inventory) : false;
            });
            if (canComplete) {
                // Consume ingredients
                order.items.forEach((itemName)=>{
                    const recipe = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$gameLogic$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getRecipeById"])(itemName.toLowerCase().replace(/\s+/g, '_'));
                    if (recipe) {
                        recipe.ingredients.forEach((ingredient)=>{
                            useIngredient(ingredient.name, ingredient.quantity);
                        });
                    }
                });
                // Complete order
                setOrders((prev)=>prev.map((o)=>o.id === orderId ? {
                            ...o,
                            status: 'completed'
                        } : o));
                // Update player stats
                setPlayer((prev)=>({
                        ...prev,
                        totalOrdersCompleted: prev.totalOrdersCompleted + 1,
                        totalItemsBaked: prev.totalItemsBaked + order.items.length
                    }));
                // Calculate rewards
                const timeBonus = order.timeLimit > 60 // Bonus if completed with time to spare
                ;
                const expReward = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$gameLogic$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["calculateExperienceReward"])(order.difficulty, timeBonus);
                addMoney(order.reward);
                addExperience(expReward);
                // Check for achievement progress
                setTimeout(()=>checkAchievements(), 100);
            }
        }
    };
    const declineOrder = (orderId)=>{
        setOrders((prev)=>prev.filter((order)=>order.id !== orderId));
    };
    const generateNewOrder = ()=>{
        const newOrder = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$gameLogic$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generateRandomOrder"])(player.level);
        setOrders((prev)=>[
                ...prev,
                newOrder
            ]);
    };
    const upgradeSkill = (skillId)=>{
        const skill = skills.find((s)=>s.id === skillId);
        if (!skill || skill.level >= skill.maxLevel || player.skillPoints < skill.cost) {
            return;
        }
        setSkills((prev)=>prev.map((s)=>s.id === skillId ? {
                    ...s,
                    level: s.level + 1
                } : s));
        setPlayer((prev)=>({
                ...prev,
                skillPoints: prev.skillPoints - skill.cost
            }));
    };
    const checkAchievements = ()=>{
        setAchievements((prev)=>{
            const newAchievements = prev.map((achievement)=>{
                if (achievement.completed) return achievement;
                // Check all requirements
                const updatedRequirements = achievement.requirements.map((requirement)=>{
                    let current = 0;
                    switch(requirement.type){
                        case 'orders_completed':
                            current = player.totalOrdersCompleted;
                            break;
                        case 'money_earned':
                            current = player.totalMoneyEarned;
                            break;
                        case 'recipes_unlocked':
                            current = player.unlockedRecipes.length;
                            break;
                        case 'level_reached':
                            current = player.level;
                            break;
                        case 'items_baked':
                            current = player.totalItemsBaked;
                            break;
                        case 'equipment_owned':
                            current = equipment.length;
                            break;
                    }
                    return {
                        ...requirement,
                        current
                    };
                });
                // Check if all requirements are met
                const completed = updatedRequirements.every((req)=>req.current >= req.target);
                // If newly completed, show notification and apply reward
                if (completed && !achievement.completed) {
                    showSuccess('Achievement Unlocked!', `🏆 ${achievement.name}`);
                    // Apply achievement reward
                    if (achievement.reward.type === 'money' && achievement.reward.value) {
                        addMoney(achievement.reward.value);
                    } else if (achievement.reward.type === 'skill_point' && achievement.reward.value) {
                        setPlayer((prev)=>({
                                ...prev,
                                skillPoints: prev.skillPoints + achievement.reward.value
                            }));
                    }
                }
                return {
                    ...achievement,
                    requirements: updatedRequirements,
                    completed
                };
            });
            return newAchievements;
        });
    };
    const dismissLevelUp = ()=>{
        setShowLevelUp(false);
        setLevelUpRewards([]);
    };
    const updateAutomationSettings = (updates)=>{
        setAutomationSettings((prev)=>({
                ...prev,
                ...updates
            }));
    };
    const purchaseAutomationUpgrade = (upgradeId)=>{
        const upgrade = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automationSystem$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["AUTOMATION_UPGRADES"].find((u)=>u.id === upgradeId);
        if (!upgrade || player.money < upgrade.cost) return;
        if (spendMoney(upgrade.cost)) {
            setPlayer((prev)=>({
                    ...prev,
                    automationUpgrades: [
                        ...prev.automationUpgrades,
                        upgradeId
                    ]
                }));
        }
    };
    const startAutomationJob = (equipmentId)=>{
        if (!automationSettings.enabled) return;
        const targetEquipment = equipment.find((eq)=>eq.id === equipmentId);
        if (!targetEquipment || targetEquipment.isActive || targetEquipment.automationLevel === 0) return;
        const availableRecipes = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$gameLogic$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getAvailableRecipes"])(player.level);
        const optimalRecipeId = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automationSystem$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["selectOptimalRecipe"])(availableRecipes, inventory, automationSettings.priorityMode, orders);
        if (!optimalRecipeId) return;
        const recipe = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$gameLogic$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getRecipeById"])(optimalRecipeId);
        if (!recipe || !(0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$gameLogic$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["canCraftRecipe"])(recipe, inventory)) return;
        const efficiency = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automationSystem$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["calculateAutomationEfficiency"])(targetEquipment.efficiency, targetEquipment.automationLevel, player.automationUpgrades);
        const job = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$automationSystem$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["generateAutomationJob"])(equipmentId, optimalRecipeId, recipe, efficiency);
        // Start the job
        setAutomationJobs((prev)=>[
                ...prev,
                {
                    ...job,
                    status: 'running'
                }
            ]);
        updateEquipment(equipmentId, {
            isActive: true,
            timeRemaining: job.duration,
            currentRecipe: recipe.name
        });
        // Consume ingredients
        job.ingredients.forEach((ingredient)=>{
            useIngredient(ingredient.name, ingredient.quantity);
        });
    };
    // Save/Load functionality
    const saveGameState = async (slotNumber, saveName)=>{
        try {
            const gameData = {
                version: '1.0.0',
                timestamp: Date.now(),
                player: {
                    level: player.level,
                    experience: player.experience,
                    money: player.money,
                    skillPoints: player.skillPoints,
                    totalMoneyEarned: player.totalMoneyEarned || 0,
                    totalOrdersCompleted: player.totalOrdersCompleted || 0,
                    totalItemsBaked: player.totalItemsBaked || 0,
                    unlockedRecipes: player.unlockedRecipes || [],
                    automationUpgrades: player.automationUpgrades || [],
                    name: saveName || `Save ${slotNumber || 1}`,
                    playTime: player.playTime || 0
                },
                equipment,
                inventory,
                achievements,
                skills,
                automationSettings,
                gameSettings: {
                    language: 'en',
                    soundEnabled: true,
                    musicEnabled: true,
                    notificationsEnabled: true,
                    autoSaveEnabled: true
                },
                bakeries: [],
                currentBakeryId: 'main'
            };
            if (slotNumber) {
                // Save to specific slot
                const slotKey = `bakeItOut_save_slot_${slotNumber}`;
                localStorage.setItem(slotKey, JSON.stringify(gameData));
            } else {
                // Save to default location
                __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$saveSystem$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveSystem"].saveToLocal(gameData);
            }
            return true;
        } catch (error) {
            console.error('Failed to save game:', error);
            return false;
        }
    };
    const loadGameState = async (slotNumber)=>{
        try {
            const slotKey = `bakeItOut_save_slot_${slotNumber}`;
            const saveData = localStorage.getItem(slotKey);
            if (!saveData) return false;
            const gameData = JSON.parse(saveData);
            // Load player data
            setPlayer((prev)=>({
                    ...prev,
                    level: gameData.player.level,
                    experience: gameData.player.experience,
                    money: gameData.player.money,
                    skillPoints: gameData.player.skillPoints,
                    totalMoneyEarned: gameData.player.totalMoneyEarned || 0,
                    totalOrdersCompleted: gameData.player.totalOrdersCompleted || 0,
                    totalItemsBaked: gameData.player.totalItemsBaked || 0,
                    unlockedRecipes: gameData.player.unlockedRecipes || [],
                    automationUpgrades: gameData.player.automationUpgrades || [],
                    playTime: gameData.player.playTime || 0
                }));
            // Load other game state
            setEquipment(gameData.equipment || []);
            setInventory(gameData.inventory || []);
            setAchievements(gameData.achievements || []);
            setSkills(gameData.skills || []);
            setAutomationSettings(gameData.automationSettings || {
                enabled: false,
                efficiency: 1,
                speed: 1,
                qualityBonus: 0
            });
            return true;
        } catch (error) {
            console.error('Failed to load game:', error);
            return false;
        }
    };
    const quickSave = async ()=>{
        return await saveGameState(0, 'Quick Save');
    };
    const quickLoad = async ()=>{
        return await loadGameState(0);
    };
    const autoSave = async ()=>{
        return await saveGameState(-1, 'Auto Save');
    };
    // Auto-save every 2 minutes
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const autoSaveInterval = setInterval(()=>{
            autoSave();
        }, 120000) // 2 minutes
        ;
        return ()=>clearInterval(autoSaveInterval);
    }, []);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(GameContext.Provider, {
        value: {
            player,
            equipment,
            inventory,
            orders,
            achievements,
            skills,
            levelUpRewards,
            showLevelUp,
            automationSettings,
            automationJobs,
            conveyorBelts,
            updatePlayer,
            updateEquipment,
            addEquipment,
            addExperience,
            addMoney,
            spendMoney,
            useIngredient,
            addIngredient,
            acceptOrder,
            completeOrder,
            declineOrder,
            generateNewOrder,
            upgradeSkill,
            checkAchievements,
            dismissLevelUp,
            updateAutomationSettings,
            purchaseAutomationUpgrade,
            startAutomationJob,
            saveGameState,
            loadGameState,
            quickSave,
            quickLoad,
            autoSave
        },
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/GameContext.tsx",
        lineNumber: 615,
        columnNumber: 5
    }, this);
}
function useGame() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(GameContext);
    if (context === undefined) {
        // Return a fallback object for SSR/build compatibility
        return {
            player: {
                level: 1,
                experience: 0,
                money: 100,
                maxExperience: 100,
                skillPoints: 0,
                totalMoneyEarned: 0,
                totalOrdersCompleted: 0,
                totalItemsBaked: 0,
                unlockedRecipes: [],
                automationUpgrades: []
            },
            equipment: [],
            inventory: [],
            orders: [],
            achievements: [],
            skills: [],
            levelUpRewards: [],
            showLevelUp: false,
            updateEquipment: ()=>{},
            acceptOrder: ()=>{},
            completeOrder: ()=>{},
            declineOrder: ()=>{},
            generateNewOrder: ()=>{},
            upgradeSkill: ()=>{},
            checkAchievements: ()=>{},
            dismissLevelUp: ()=>{},
            spendMoney: ()=>false,
            addMoney: ()=>{},
            addExperience: ()=>{},
            addEquipment: ()=>{},
            removeEquipment: ()=>{},
            addInventoryItem: ()=>{},
            removeInventoryItem: ()=>{},
            updateAutomationSettings: ()=>{},
            purchaseAutomationUpgrade: ()=>{},
            startAutomationJob: ()=>{},
            saveGameState: async ()=>false,
            loadGameState: async ()=>false,
            quickSave: async ()=>false,
            quickLoad: async ()=>false,
            autoSave: async ()=>false
        };
    }
    return context;
}
}),
"[project]/src/contexts/DiscordRPCContext.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

__turbopack_context__.s({
    "DiscordRPCProvider": ()=>DiscordRPCProvider,
    "useDiscordRPC": ()=>useDiscordRPC
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$discordRPC$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/discordRPC.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$GameContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/GameContext.tsx [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$MultiplayerContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/MultiplayerContext.tsx [app-ssr] (ecmascript)");
'use client';
;
;
;
;
;
const DiscordRPCContext = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createContext"])(undefined);
function useDiscordRPC() {
    const context = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useContext"])(DiscordRPCContext);
    if (context === undefined) {
        // Return a fallback object instead of throwing an error
        return {
            isEnabled: false,
            isConnected: false,
            currentActivity: null,
            setEnabled: ()=>{},
            updateActivity: async ()=>{},
            setMenuActivity: async ()=>{},
            setGameActivity: async ()=>{},
            setMultiplayerActivity: async ()=>{},
            setBakingActivity: async ()=>{},
            clearActivity: async ()=>{}
        };
    }
    return context;
}
function DiscordRPCProvider({ children }) {
    const [isEnabled, setIsEnabled] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false) // Start disabled to prevent SSR issues
    ;
    const [isConnected, setIsConnected] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [currentActivity, setCurrentActivity] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [isClient, setIsClient] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    // Get game state for automatic updates
    const gameContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$GameContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useGame"])();
    const multiplayerContext = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$MultiplayerContext$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useMultiplayer"])();
    // Initialize client-side state
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        setIsClient(true);
        // Load saved preference
        const saved = localStorage.getItem('discordRPCEnabled');
        if (saved !== null) {
            const enabled = saved === 'true';
            setIsEnabled(enabled);
        } else {
            // Default to enabled if in Electron environment
            const shouldEnable = window.electronAPI !== undefined;
            setIsEnabled(shouldEnable);
        }
    }, []);
    // Initialize Discord RPC
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isClient) return;
        // Check if Discord RPC should be enabled (only in Electron environment)
        const shouldEnable = "undefined" !== 'undefined' && window.electronAPI !== undefined && isEnabled;
        if ("TURBOPACK compile-time falsy", 0) //TURBOPACK unreachable
        ;
        else {
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$discordRPC$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["discordRPC"].setEnabled(false);
        }
    }, [
        isClient,
        isEnabled
    ]);
    // Auto-update Discord RPC based on game state
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isClient || !isEnabled || !isConnected || !gameContext) return;
        const updateRPCFromGameState = async ()=>{
            try {
                const { player, currentOrders } = gameContext;
                // Determine current activity
                let activity = 'managing';
                let currentOrder;
                if (currentOrders && currentOrders.length > 0) {
                    activity = 'baking';
                    currentOrder = currentOrders[0].items[0]?.name || 'Unknown item';
                }
                // Check if in multiplayer
                if (multiplayerContext?.gameState === 'playing') {
                    activity = 'multiplayer';
                }
                const playerStatus = {
                    level: player.level,
                    money: player.money,
                    currentActivity: activity,
                    currentOrder,
                    playTime: player.playTime
                };
                // Update Discord RPC
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$discordRPC$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["discordRPC"].updatePlayerStatus(playerStatus);
            } catch (error) {
                console.error('Failed to update Discord RPC from game state:', error);
            }
        };
        // Update immediately and then every 30 seconds
        updateRPCFromGameState();
        const interval = setInterval(updateRPCFromGameState, 30000);
        return ()=>clearInterval(interval);
    }, [
        gameContext?.player,
        gameContext?.currentOrders,
        multiplayerContext?.gameState,
        isClient,
        isEnabled,
        isConnected
    ]);
    // Auto-update for multiplayer state
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        if (!isClient || !isEnabled || !isConnected || !multiplayerContext) return;
        const updateMultiplayerRPC = async ()=>{
            try {
                const { gameState, currentRoom, players } = multiplayerContext;
                if (gameState === 'playing' && currentRoom) {
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$discordRPC$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["discordRPC"].setMultiplayerActivity(currentRoom.id, players.length, currentRoom.maxPlayers || 4);
                }
            } catch (error) {
                console.error('Failed to update multiplayer Discord RPC:', error);
            }
        };
        updateMultiplayerRPC();
    }, [
        multiplayerContext?.gameState,
        multiplayerContext?.currentRoom,
        multiplayerContext?.players,
        isClient,
        isEnabled,
        isConnected
    ]);
    const handleSetEnabled = (enabled)=>{
        setIsEnabled(enabled);
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$discordRPC$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["discordRPC"].setEnabled(enabled);
        // Save preference to localStorage
        if (isClient) {
            localStorage.setItem('discordRPCEnabled', enabled.toString());
        }
    };
    const updateActivity = async (activity)=>{
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$discordRPC$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["discordRPC"].updateActivity(activity);
        setCurrentActivity(activity);
    };
    const setMenuActivity = async ()=>{
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$discordRPC$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["discordRPC"].setMenuActivity();
        setCurrentActivity(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$discordRPC$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["discordRPC"].getCurrentActivity());
    };
    const setGameActivity = async (level, money, activity)=>{
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$discordRPC$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["discordRPC"].setGameActivity(level, money, activity);
        setCurrentActivity(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$discordRPC$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["discordRPC"].getCurrentActivity());
    };
    const setMultiplayerActivity = async (roomId, playerCount, maxPlayers)=>{
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$discordRPC$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["discordRPC"].setMultiplayerActivity(roomId, playerCount, maxPlayers);
        setCurrentActivity(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$discordRPC$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["discordRPC"].getCurrentActivity());
    };
    const setBakingActivity = async (level, currentOrder)=>{
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$discordRPC$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["discordRPC"].setBakingActivity(level, currentOrder);
        setCurrentActivity(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$discordRPC$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["discordRPC"].getCurrentActivity());
    };
    const clearActivity = async ()=>{
        await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$discordRPC$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["discordRPC"].clearActivity();
        setCurrentActivity(null);
    };
    // This effect is now handled in the client-side initialization above
    // Cleanup on unmount
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        return ()=>{
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$discordRPC$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["discordRPC"].cleanup();
        };
    }, []);
    const value = {
        isEnabled,
        isConnected,
        currentActivity,
        setEnabled: handleSetEnabled,
        updateActivity,
        setMenuActivity,
        setGameActivity,
        setMultiplayerActivity,
        setBakingActivity,
        clearActivity
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(DiscordRPCContext.Provider, {
        value: value,
        children: children
    }, void 0, false, {
        fileName: "[project]/src/contexts/DiscordRPCContext.tsx",
        lineNumber: 233,
        columnNumber: 5
    }, this);
}
}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__281f48d2._.js.map