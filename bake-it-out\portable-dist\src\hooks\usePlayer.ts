import { useState, useEffect } from 'react'
import { supabase, Player } from '@/lib/supabase'
import { useAuth } from './useAuth'

export function usePlayer() {
  const { user } = useAuth()
  const [player, setPlayer] = useState<Player | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!user) {
      setPlayer(null)
      setLoading(false)
      return
    }

    fetchPlayer()
  }, [user])

  const fetchPlayer = async () => {
    if (!user) return

    try {
      setLoading(true)
      setError(null)

      // Try to get existing player
      const { data: existingPlayer, error: fetchError } = await supabase
        .from('players')
        .select('*')
        .eq('user_id', user.id)
        .single()

      if (fetchError && fetchError.code !== 'PGRST116') {
        throw fetchError
      }

      if (existingPlayer) {
        setPlayer(existingPlayer)
      } else {
        // Create new player if doesn't exist
        const { data: newPlayer, error: createError } = await supabase
          .from('players')
          .insert({
            user_id: user.id,
            level: 1,
            experience: 0,
            money: 100,
          })
          .select()
          .single()

        if (createError) throw createError
        setPlayer(newPlayer)
      }
    } catch (err) {
      console.error('Error fetching player:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }

  const updatePlayer = async (updates: Partial<Player>) => {
    if (!player) return

    try {
      const { data, error } = await supabase
        .from('players')
        .update(updates)
        .eq('id', player.id)
        .select()
        .single()

      if (error) throw error
      setPlayer(data)
      return { data, error: null }
    } catch (err) {
      console.error('Error updating player:', err)
      return { data: null, error: err instanceof Error ? err.message : 'Unknown error' }
    }
  }

  const addExperience = async (amount: number) => {
    if (!player) return

    const newExperience = player.experience + amount
    const newLevel = calculateLevelFromExperience(newExperience)
    
    return updatePlayer({
      experience: newExperience,
      level: newLevel,
    })
  }

  const addMoney = async (amount: number) => {
    if (!player) return

    return updatePlayer({
      money: player.money + amount,
    })
  }

  const spendMoney = async (amount: number) => {
    if (!player || player.money < amount) {
      return { data: null, error: 'Insufficient funds' }
    }

    return updatePlayer({
      money: player.money - amount,
    })
  }

  return {
    player,
    loading,
    error,
    updatePlayer,
    addExperience,
    addMoney,
    spendMoney,
    refetch: fetchPlayer,
  }
}

function calculateLevelFromExperience(experience: number): number {
  let level = 1
  let totalExp = 0
  
  while (totalExp + (level * 100 + (level - 1) * 50) <= experience) {
    totalExp += level * 100 + (level - 1) * 50
    level++
  }
  
  return level
}
