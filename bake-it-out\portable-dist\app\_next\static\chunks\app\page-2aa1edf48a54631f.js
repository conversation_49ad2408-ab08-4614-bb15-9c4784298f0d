(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{255:(e,s,t)=>{"use strict";function a(e){let{moduleIds:s}=e;return null}Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"PreloadChunks",{enumerable:!0,get:function(){return a}}),t(5155),t(7650),t(5744),t(589)},1006:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>j});var a=t(5155),l=t(2115),r=t(9283),i=t(3741),n=t(637),d=t(2148);function c(e){let{isOpen:s,onClose:t}=e,{t:c}=(0,r.o)(),{setMultiplayerActivity:o}=(0,d.l)(),{isConnected:m,isInRoom:x,connectionError:u,currentRoom:h,currentPlayer:g,players:p,gameState:y,messages:j,createRoom:v,joinRoom:b,leaveRoom:N,startGame:f,sendMessage:w,setPlayerReady:D}=(0,n.K)(),[C,k]=(0,l.useState)("create"),[S,O]=(0,l.useState)(""),[P,E]=(0,l.useState)(""),[_,A]=(0,l.useState)(""),[z,F]=(0,l.useState)("cooperative"),[$,I]=(0,l.useState)(4),[R,L]=(0,l.useState)(""),[M,B]=(0,l.useState)(!1);if(!s)return null;let T=async()=>{if(P.trim()&&S.trim())try{await v({name:S,mode:z,maxPlayers:$,settings:{gameMode:z,difficulty:"medium",allowSpectators:!0}},{name:P,avatar:"\uD83D\uDC68‍\uD83C\uDF73",level:1}),k("room")}catch(e){console.error("Failed to create room:",e)}},q=async()=>{if(P.trim()&&_.trim())try{await b(_.toUpperCase(),{name:P,avatar:"\uD83D\uDC68‍\uD83C\uDF73",level:1}),k("room")}catch(e){console.error("Failed to join room:",e)}},G=()=>{R.trim()&&(w(R),L(""))},K=[{id:"create",name:c("multiplayer.createRoom"),icon:"\uD83C\uDFD7️"},{id:"join",name:c("multiplayer.joinRoom"),icon:"\uD83D\uDEAA"},...x?[{id:"room",name:c("multiplayer.room"),icon:"\uD83C\uDFE0"}]:[]];return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsxs)("div",{className:"p-6 border-b border-gray-200",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:c("multiplayer.lobby")}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"px-3 py-1 rounded-full text-sm ".concat(m?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:m?c("multiplayer.connected"):c("multiplayer.disconnected")}),(0,a.jsx)(i.$,{variant:"secondary",onClick:t,children:c("game.close")})]})]}),u&&(0,a.jsx)("div",{className:"mt-2 p-2 bg-red-100 text-red-800 rounded text-sm",children:c("multiplayer.connection.error",{error:u})})]}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("div",{className:"flex space-x-0",children:K.map(e=>(0,a.jsxs)("button",{onClick:()=>k(e.id),className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat(C===e.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[e.icon," ",e.name]},e.id))})}),(0,a.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["create"===C&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:c("multiplayer.yourName")}),(0,a.jsx)("input",{type:"text",value:P,onChange:e=>E(e.target.value),placeholder:c("multiplayer.enterName"),className:"w-full p-3 border rounded-lg",maxLength:20})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:c("multiplayer.roomName")}),(0,a.jsx)("input",{type:"text",value:S,onChange:e=>O(e.target.value),placeholder:c("multiplayer.enterRoomName"),className:"w-full p-3 border rounded-lg",maxLength:30})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:c("multiplayer.gameMode")}),(0,a.jsxs)("select",{value:z,onChange:e=>F(e.target.value),className:"w-full p-3 border rounded-lg",children:[(0,a.jsx)("option",{value:"cooperative",children:c("multiplayer.cooperative")}),(0,a.jsx)("option",{value:"competitive",children:c("multiplayer.competitive")})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:c("multiplayer.maxPlayers",{count:$.toString()})}),(0,a.jsx)("input",{type:"range",min:"2",max:"8",value:$,onChange:e=>I(parseInt(e.target.value)),className:"w-full"})]})]}),(0,a.jsx)(i.$,{variant:"primary",size:"lg",className:"w-full",onClick:T,disabled:!m||!P.trim()||!S.trim(),children:c("multiplayer.create.title")})]}),"join"===C&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:c("multiplayer.yourName")}),(0,a.jsx)("input",{type:"text",value:P,onChange:e=>E(e.target.value),placeholder:c("multiplayer.enterName"),className:"w-full p-3 border rounded-lg",maxLength:20})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:c("multiplayer.roomId")}),(0,a.jsx)("input",{type:"text",value:_,onChange:e=>A(e.target.value.toUpperCase()),placeholder:c("multiplayer.enterRoomId"),className:"w-full p-3 border rounded-lg font-mono",maxLength:6})]})]}),(0,a.jsx)(i.$,{variant:"primary",size:"lg",className:"w-full",onClick:q,disabled:!m||!P.trim()||!_.trim(),children:c("multiplayer.join.title")})]}),"room"===C&&h&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("h3",{className:"font-semibold text-blue-800",children:h.name}),(0,a.jsxs)("div",{className:"text-sm text-blue-600",children:[c("multiplayer.roomId"),": ",(0,a.jsx)("span",{className:"font-mono font-bold",children:h.id})]})]}),(0,a.jsx)("div",{className:"text-sm text-blue-700",children:c("multiplayer.room.info",{mode:h.mode,current:h.currentPlayers.toString(),max:h.maxPlayers.toString()})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:c("multiplayer.players",{count:p.length.toString()})}),(0,a.jsx)("div",{className:"space-y-2",children:p.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:"text-2xl",children:e.avatar}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("div",{className:"font-medium",children:[e.name,e.isHost&&(0,a.jsx)("span",{className:"ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded",children:c("multiplayer.host")})]}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:c("multiplayer.level",{level:e.level.toString()})})]})]}),(0,a.jsx)("div",{className:"px-2 py-1 rounded text-xs ".concat(e.isReady?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"),children:e.isReady?c("common.ready"):c("common.notReady")})]},e.id))})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)(i.$,{variant:M?"success":"secondary",onClick:()=>{let e=!M;B(e),D(e)},className:"flex-1",children:M?c("multiplayer.room.readyUp"):c("multiplayer.room.notReady")}),(null==g?void 0:g.isHost)&&(0,a.jsx)(i.$,{variant:"primary",onClick:()=>{(null==g?void 0:g.isHost)&&f()},disabled:!p.every(e=>e.isReady)||p.length<2,children:c("multiplayer.room.startGame")}),(0,a.jsx)(i.$,{variant:"secondary",onClick:()=>{N(),k("create"),B(!1)},children:c("multiplayer.room.leaveRoom")})]}),(0,a.jsxs)("div",{className:"border-t pt-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:c("multiplayer.chat")}),(0,a.jsx)("div",{className:"bg-gray-50 p-3 rounded-lg h-32 overflow-y-auto mb-3",children:j.map(e=>(0,a.jsxs)("div",{className:"text-sm mb-1",children:[(0,a.jsxs)("span",{className:"font-medium ".concat("system"===e.playerId?"text-blue-600":"text-gray-800"),children:[e.playerName,":"]}),(0,a.jsx)("span",{className:"ml-2",children:e.content})]},e.id))}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("input",{type:"text",value:R,onChange:e=>L(e.target.value),onKeyDown:e=>"Enter"===e.key&&G(),placeholder:c("multiplayer.typeMessage"),className:"flex-1 p-2 border rounded",maxLength:100}),(0,a.jsx)(i.$,{size:"sm",onClick:G,children:c("common.send")})]})]})]})]})]})})}var o=t(9419),m=t(2163);function x(e){let{isOpen:s,onClose:t}=e,{t:d}=(0,r.o)(),{currentRoom:c,currentPlayer:x,players:u,gameState:h,sharedGameState:g,sendPlayerAction:p,leaveRoom:y}=(0,n.K)(),[j,v]=(0,l.useState)("game");if(!s||!c||"playing"!==h)return null;let b=(e,s)=>{p({type:"use_equipment",data:{equipmentId:e,equipmentName:s,playerId:null==x?void 0:x.id}})},N=(e,s)=>{p({type:"order_action",data:{orderId:e,action:s,playerId:null==x?void 0:x.id}})},f=[{id:"game",name:d("multiplayer.game.tabs.game"),icon:"\uD83C\uDFAE"},{id:"players",name:d("multiplayer.game.tabs.players"),icon:"\uD83D\uDC65"},{id:"chat",name:d("multiplayer.game.tabs.chat"),icon:"\uD83D\uDCAC"}];return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:d("multiplayer.game.title",{roomName:c.name})}),(0,a.jsxs)("p",{className:"text-gray-600",children:[d("multiplayer.game.mode",{mode:c.mode})," • ",d("multiplayer.game.playersCount",{count:u.length.toString()})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,a.jsx)("span",{className:"text-green-800 text-sm",children:d("multiplayer.game.playing")})}),(0,a.jsx)(i.$,{variant:"secondary",onClick:()=>{y(),t()},children:d("multiplayer.game.leaveGame")})]})]})}),(0,a.jsx)("div",{className:"border-b border-gray-200",children:(0,a.jsx)("div",{className:"flex space-x-0",children:f.map(e=>(0,a.jsxs)("button",{onClick:()=>v(e.id),className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat(j===e.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[e.icon," ",e.name]},e.id))})}),(0,a.jsxs)("div",{className:"p-6 max-h-[70vh] overflow-y-auto",children:["game"===j&&(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:d("multiplayer.sharedKitchen")}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[{id:"oven1",name:"Shared Oven",type:"oven",isActive:!1,level:1,efficiency:1,automationLevel:0},{id:"mixer1",name:"Shared Mixer",type:"mixer",isActive:!1,level:1,efficiency:1,automationLevel:0},{id:"counter1",name:"Shared Counter",type:"counter",isActive:!1,level:1,efficiency:1,automationLevel:0}].map(e=>(0,a.jsx)(o.$,{equipment:e,onClick:b},e.id))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 mt-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:d("multiplayer.sharedOrders")}),(0,a.jsx)("div",{className:"space-y-4",children:[{id:"1",customerName:"Shared Customer",items:["Chocolate Chip Cookies"],timeLimit:300,reward:50,status:"pending",difficulty:1}].map(e=>(0,a.jsx)(m.p,{order:e,onAccept:e=>N(e,"accept"),onDecline:e=>N(e,"decline"),onComplete:e=>N(e,"complete")},e.id))})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:d("multiplayer.sharedInventory")}),(0,a.jsx)("div",{className:"space-y-3",children:[{name:"Flour",quantity:20,cost:2},{name:"Sugar",quantity:15,cost:3},{name:"Eggs",quantity:12,cost:4}].map((e,s)=>(0,a.jsx)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-800",children:e.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:["Qty: ",e.quantity]})]})},s))})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-orange-800 mb-4",children:d("multiplayer.teamStats")}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:d("multiplayer.ordersCompleted")}),(0,a.jsx)("span",{className:"font-medium",children:"0"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:d("multiplayer.totalRevenue")}),(0,a.jsx)("span",{className:"font-medium",children:"$0"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:d("multiplayer.teamExperience")}),(0,a.jsx)("span",{className:"font-medium",children:"0 XP"})]})]})]})]})]}),"players"===j&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-orange-800",children:d("multiplayer.players",{count:u.length.toString()})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:u.map(e=>(0,a.jsxs)("div",{className:"p-4 rounded-lg border-2 ".concat(e.id===(null==x?void 0:x.id)?"border-orange-400 bg-orange-50":"border-gray-300 bg-white"),children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-3",children:[(0,a.jsx)("span",{className:"text-3xl",children:e.avatar}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h4",{className:"font-semibold text-gray-800",children:[e.name,e.id===(null==x?void 0:x.id)&&(0,a.jsx)("span",{className:"ml-2 text-sm text-orange-600",children:d("multiplayer.you")})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600",children:[d("multiplayer.level",{level:e.level.toString()}),e.isHost&&(0,a.jsx)("span",{className:"ml-2 bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs",children:d("multiplayer.host")})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:d("multiplayer.status")}),(0,a.jsx)("span",{className:"text-green-600",children:d("multiplayer.online")})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{children:d("multiplayer.contribution")}),(0,a.jsx)("span",{className:"font-medium",children:"0 orders"})]})]})]},e.id))})]}),"chat"===j&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-orange-800",children:d("multiplayer.teamChat")}),(0,a.jsx)("div",{className:"bg-gray-50 p-4 rounded-lg h-64 overflow-y-auto",children:(0,a.jsx)("div",{className:"text-sm text-gray-500 text-center",children:d("multiplayer.chatPlaceholder")})}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("input",{type:"text",placeholder:d("multiplayer.typeMessage"),className:"flex-1 p-3 border rounded-lg"}),(0,a.jsx)(i.$,{children:d("common.send")})]})]})]}),(0,a.jsx)("div",{className:"p-4 bg-blue-50 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("div",{className:"text-sm text-blue-700",children:"cooperative"===c.mode?d("multiplayer.mode.cooperative.description"):d("multiplayer.mode.competitive.description")}),(0,a.jsx)("div",{className:"text-sm text-blue-600",children:d("multiplayer.gameTime",{time:"00:00"})})]})})]})})}function u(e){let{onStartSinglePlayer:s,onStartMultiplayer:t,onShowSettings:n,onShowCredits:d,onExit:c}=e,{language:o,setLanguage:m,t:x}=(0,r.o)(),[u,h]=(0,l.useState)(!1);return(0,a.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-orange-100 via-yellow-50 to-orange-200 flex items-center justify-center relative overflow-hidden",children:[(0,a.jsxs)("div",{className:"absolute inset-0 opacity-10",children:[(0,a.jsx)("div",{className:"absolute top-10 left-10 text-6xl",children:"\uD83E\uDD56"}),(0,a.jsx)("div",{className:"absolute top-20 right-20 text-4xl",children:"\uD83E\uDDC1"}),(0,a.jsx)("div",{className:"absolute bottom-20 left-20 text-5xl",children:"\uD83C\uDF70"}),(0,a.jsx)("div",{className:"absolute bottom-10 right-10 text-3xl",children:"\uD83E\uDD50"}),(0,a.jsx)("div",{className:"absolute top-1/2 left-1/4 text-4xl",children:"\uD83C\uDF6A"}),(0,a.jsx)("div",{className:"absolute top-1/3 right-1/3 text-5xl",children:"\uD83C\uDF82"})]}),(0,a.jsxs)("div",{className:"relative z-10 text-center space-y-8 p-8 max-w-md w-full",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83E\uDD56"}),(0,a.jsx)("h1",{className:"text-5xl font-bold text-orange-800 mb-2",children:"Bake It Out"}),(0,a.jsx)("p",{className:"text-lg text-orange-600 font-medium",children:x("game.subtitle","Multiplayer Bakery Management")})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(i.$,{size:"lg",className:"w-full text-lg py-4 bg-gradient-to-r from-orange-500 to-yellow-500 hover:from-orange-600 hover:to-yellow-600 text-white font-semibold shadow-lg transform hover:scale-105 transition-all duration-200",onClick:s,children:["\uD83C\uDFAE ",x("menu.singlePlayer","Single Player")]}),(0,a.jsxs)(i.$,{size:"lg",className:"w-full text-lg py-4 bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold shadow-lg transform hover:scale-105 transition-all duration-200",onClick:t,children:["\uD83D\uDC65 ",x("menu.multiplayer","Multiplayer")]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)(i.$,{variant:"secondary",size:"md",className:"py-3 bg-white/80 hover:bg-white shadow-md",onClick:n,children:["⚙️ ",x("menu.settings","Settings")]}),(0,a.jsxs)(i.$,{variant:"secondary",size:"md",className:"py-3 bg-white/80 hover:bg-white shadow-md",onClick:()=>h(!u),children:["\uD83C\uDF0D ","en"===o?"English":"Čeština"]})]}),u&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-4 space-y-2",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:x("menu.selectLanguage","Select Language")}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,a.jsx)(i.$,{variant:"en"===o?"primary":"secondary",size:"sm",onClick:()=>{m("en"),h(!1)},children:"\uD83C\uDDFA\uD83C\uDDF8 English"}),(0,a.jsx)(i.$,{variant:"cs"===o?"primary":"secondary",size:"sm",onClick:()=>{m("cs"),h(!1)},children:"\uD83C\uDDE8\uD83C\uDDFF Čeština"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,a.jsxs)(i.$,{variant:"secondary",size:"md",className:"py-3 bg-white/80 hover:bg-white shadow-md",onClick:d,children:["ℹ️ ",x("menu.about","About")]}),c&&(0,a.jsxs)(i.$,{variant:"secondary",size:"md",className:"py-3 bg-red-100 hover:bg-red-200 text-red-700 shadow-md",onClick:c,children:["\uD83D\uDEAA ",x("menu.exit","Exit")]})]})]}),(0,a.jsxs)("div",{className:"text-sm text-orange-500 opacity-75",children:["v1.0.0 - ",x("menu.version","Beta Version")]})]})]})}function h(e){let{isOpen:s,onClose:t}=e,{t:l}=(0,r.o)();return s?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-orange-500 to-yellow-500 p-6 text-white",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold",children:["ℹ️ ",l("credits.title","About Bake It Out")]}),(0,a.jsx)("p",{className:"text-orange-100 text-sm",children:l("credits.subtitle","Game Information & Credits")})]}),(0,a.jsx)(i.$,{variant:"secondary",size:"sm",className:"bg-white/20 hover:bg-white/30 text-white border-white/30",onClick:t,children:"✕"})]})}),(0,a.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[70vh]",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83E\uDD56"}),(0,a.jsx)("h3",{className:"text-3xl font-bold text-orange-800 mb-2",children:"Bake It Out"}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-4",children:l("credits.description","A multiplayer bakery management game with real-time collaboration and localization support")}),(0,a.jsx)("div",{className:"bg-orange-100 rounded-lg p-4 inline-block",children:(0,a.jsxs)("div",{className:"text-sm text-orange-800",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("strong",{children:[l("credits.version","Version"),":"]})," 1.0.0"]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("strong",{children:[l("credits.release","Release"),":"]})," ",l("credits.releaseDate","December 2024")]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("strong",{children:[l("credits.platform","Platform"),":"]})," ",l("credits.platforms","Windows, macOS, Linux")]})]})})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("h4",{className:"text-xl font-semibold text-gray-800 mb-4",children:["\uD83C\uDF1F ",l("credits.features","Key Features")]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83D\uDC65"}),(0,a.jsx)("h5",{className:"font-semibold text-blue-800",children:l("credits.multiplayer","Real-time Multiplayer")}),(0,a.jsx)("p",{className:"text-sm text-blue-600",children:l("credits.multiplayerDesc","Collaborate with friends in real-time bakery management")})]}),(0,a.jsxs)("div",{className:"bg-green-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDF0D"}),(0,a.jsx)("h5",{className:"font-semibold text-green-800",children:l("credits.localization","Localization")}),(0,a.jsx)("p",{className:"text-sm text-green-600",children:l("credits.localizationDesc","Full support for English and Czech languages")})]}),(0,a.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDFC6"}),(0,a.jsx)("h5",{className:"font-semibold text-purple-800",children:l("credits.progression","Progression System")}),(0,a.jsx)("p",{className:"text-sm text-purple-600",children:l("credits.progressionDesc","Achievements, skills, and equipment upgrades")})]}),(0,a.jsxs)("div",{className:"bg-orange-50 rounded-lg p-4",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83E\uDD16"}),(0,a.jsx)("h5",{className:"font-semibold text-orange-800",children:l("credits.automation","Automation")}),(0,a.jsx)("p",{className:"text-sm text-orange-600",children:l("credits.automationDesc","Advanced automation and efficiency systems")})]})]})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("h4",{className:"text-xl font-semibold text-gray-800 mb-4",children:["\uD83D\uDD27 ",l("credits.technology","Technology Stack")]}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-gray-700",children:"Frontend:"}),(0,a.jsx)("div",{className:"text-gray-600",children:"Next.js, React, TypeScript"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-gray-700",children:"Styling:"}),(0,a.jsx)("div",{className:"text-gray-600",children:"Tailwind CSS"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-gray-700",children:"Desktop:"}),(0,a.jsx)("div",{className:"text-gray-600",children:"Electron"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-gray-700",children:"Multiplayer:"}),(0,a.jsx)("div",{className:"text-gray-600",children:"Socket.IO"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-gray-700",children:"Database:"}),(0,a.jsx)("div",{className:"text-gray-600",children:"Supabase"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("strong",{className:"text-gray-700",children:"i18n:"}),(0,a.jsx)("div",{className:"text-gray-600",children:"Custom Context"})]})]})})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("h4",{className:"text-xl font-semibold text-gray-800 mb-4",children:["\uD83D\uDC68‍\uD83D\uDCBB ",l("credits.team","Development Team")]}),(0,a.jsx)("div",{className:"bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-2xl mb-2",children:"\uD83C\uDFAE"}),(0,a.jsx)("div",{className:"font-semibold text-gray-800",children:l("credits.developedBy","Developed by the Bake It Out Team")}),(0,a.jsx)("div",{className:"text-sm text-gray-600 mt-2",children:l("credits.teamDesc","Built with passion for gaming and baking!")})]})})]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsxs)("h4",{className:"text-xl font-semibold text-gray-800 mb-4",children:["\uD83D\uDE4F ",l("credits.thanks","Special Thanks")]}),(0,a.jsxs)("div",{className:"text-sm text-gray-600 space-y-2",children:[(0,a.jsxs)("p",{children:["• ",l("credits.thanksPlayers","All beta testers and players for their feedback")]}),(0,a.jsxs)("p",{children:["• ",l("credits.thanksTranslators","Czech language translators and cultural consultants")]}),(0,a.jsxs)("p",{children:["• ",l("credits.thanksOpenSource","Open source community for amazing tools and libraries")]}),(0,a.jsxs)("p",{children:["• ",l("credits.thanksBakers","Real bakers who inspired the game mechanics")]})]})]}),(0,a.jsxs)("div",{className:"border-t pt-6",children:[(0,a.jsxs)("h4",{className:"text-lg font-semibold text-gray-800 mb-3",children:["\uD83D\uDCDE ",l("credits.contact","Contact & Support")]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("strong",{className:"text-gray-700",children:[l("credits.website","Website"),":"]}),(0,a.jsx)("div",{className:"text-blue-600",children:"www.bakeitout.game"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("strong",{className:"text-gray-700",children:[l("credits.support","Support"),":"]}),(0,a.jsx)("div",{className:"text-blue-600",children:"<EMAIL>"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("strong",{className:"text-gray-700",children:[l("credits.github","GitHub"),":"]}),(0,a.jsx)("div",{className:"text-blue-600",children:"github.com/bakeitout/game"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("strong",{className:"text-gray-700",children:[l("credits.discord","Discord"),":"]}),(0,a.jsx)("div",{className:"text-blue-600",children:"discord.gg/bakeitout"})]})]})]})]}),(0,a.jsx)("div",{className:"bg-gray-50 px-6 py-4 text-center",children:(0,a.jsx)(i.$,{variant:"primary",onClick:t,className:"bg-gradient-to-r from-orange-500 to-yellow-500 hover:from-orange-600 hover:to-yellow-600",children:l("credits.close","Close")})})]})}):null}var g=t(2785),p=t(6645);let y=t.n(p)()(()=>t.e(469).then(t.bind(t,2469)),{loadableGenerated:{webpack:()=>[2469]},ssr:!1});function j(){let{language:e,setLanguage:s,t}=(0,r.o)(),{gameState:i}=(0,n.K)(),{setMenuActivity:o}=(0,d.l)(),[m,p]=(0,l.useState)(!1),[j,v]=(0,l.useState)(!1),[b,N]=(0,l.useState)(!1),[f,w]=(0,l.useState)(!1),[D,C]=(0,l.useState)(!1),[k,S]=(0,l.useState)({soundEnabled:!0,musicEnabled:!0,notifications:!0,autoSave:!0});return((0,l.useEffect)(()=>{b||o()},[b,o]),b)?(0,a.jsx)(y,{}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(u,{onStartSinglePlayer:()=>N(!0),onStartMultiplayer:()=>p(!0),onShowSettings:()=>C(!0),onShowCredits:()=>w(!0),onExit:window.electronAPI?()=>{window.electronAPI&&window.electronAPI.quit()}:void 0}),(0,a.jsx)(h,{isOpen:f,onClose:()=>w(!1)}),(0,a.jsx)(g.b,{isOpen:D,onClose:()=>C(!1),settings:k,onSettingsChange:e=>{S(e)}}),(0,a.jsx)(c,{isOpen:m,onClose:()=>p(!1)}),(0,a.jsx)(x,{isOpen:j||"playing"===i,onClose:()=>v(!1)})]})}},2146:(e,s,t)=>{"use strict";function a(e){let{reason:s,children:t}=e;return t}Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"BailoutToCSR",{enumerable:!0,get:function(){return a}}),t(5262)},2617:(e,s,t)=>{Promise.resolve().then(t.bind(t,1006))},4054:(e,s)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),!function(e,s){for(var t in s)Object.defineProperty(e,t,{enumerable:!0,get:s[t]})}(s,{bindSnapshot:function(){return i},createAsyncLocalStorage:function(){return r},createSnapshot:function(){return n}});let t=Object.defineProperty(Error("Invariant: AsyncLocalStorage accessed in runtime where it is not available"),"__NEXT_ERROR_CODE",{value:"E504",enumerable:!1,configurable:!0});class a{disable(){throw t}getStore(){}run(){throw t}exit(){throw t}enterWith(){throw t}static bind(e){return e}}let l="undefined"!=typeof globalThis&&globalThis.AsyncLocalStorage;function r(){return l?new l:new a}function i(e){return l?l.bind(e):a.bind(e)}function n(){return l?l.snapshot():function(e,...s){return e(...s)}}},5744:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"workAsyncStorage",{enumerable:!0,get:function(){return a.workAsyncStorageInstance}});let a=t(7828)},6645:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"default",{enumerable:!0,get:function(){return l}});let a=t(8229)._(t(7357));function l(e,s){var t;let l={};"function"==typeof e&&(l.loader=e);let r={...l,...s};return(0,a.default)({...r,modules:null==(t=r.loadableGenerated)?void 0:t.modules})}("function"==typeof s.default||"object"==typeof s.default&&null!==s.default)&&void 0===s.default.__esModule&&(Object.defineProperty(s.default,"__esModule",{value:!0}),Object.assign(s.default,s),e.exports=s.default)},7357:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"default",{enumerable:!0,get:function(){return d}});let a=t(5155),l=t(2115),r=t(2146);function i(e){return{default:e&&"default"in e?e.default:e}}t(255);let n={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},d=function(e){let s={...n,...e},t=(0,l.lazy)(()=>s.loader().then(i)),d=s.loading;function c(e){let i=d?(0,a.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,n=!s.ssr||!!s.loading,c=n?l.Suspense:l.Fragment,o=s.ssr?(0,a.jsxs)(a.Fragment,{children:[null,(0,a.jsx)(t,{...e})]}):(0,a.jsx)(r.BailoutToCSR,{reason:"next/dynamic",children:(0,a.jsx)(t,{...e})});return(0,a.jsx)(c,{...n?{fallback:i}:{},children:o})}return c.displayName="LoadableComponent",c}},7828:(e,s,t)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"workAsyncStorageInstance",{enumerable:!0,get:function(){return a}});let a=(0,t(4054).createAsyncLocalStorage)()}},e=>{e.O(0,[298,978,81,441,964,358],()=>e(e.s=2617)),_N_E=e.O()}]);