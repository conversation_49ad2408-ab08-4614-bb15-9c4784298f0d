const jwt = require('jsonwebtoken');
const User = require('../models/User');
const GameRoom = require('../models/GameRoom');

const JWT_SECRET = process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-in-production';

// Store active connections
const activeConnections = new Map();
const roomConnections = new Map();

function socketHandler(io) {
  // Authentication middleware for socket connections
  io.use(async (socket, next) => {
    try {
      const token = socket.handshake.auth.token;
      if (!token) {
        return next(new Error('Authentication error'));
      }

      const decoded = jwt.verify(token, JWT_SECRET);
      const user = await User.findById(decoded.userId).select('-passwordHash');
      
      if (!user || !user.isActive) {
        return next(new Error('User not found'));
      }

      socket.userId = user._id.toString();
      socket.user = user;
      next();
    } catch (error) {
      next(new Error('Authentication error'));
    }
  });

  io.on('connection', (socket) => {
    console.log(`User ${socket.user.username} connected: ${socket.id}`);
    
    // Store connection
    activeConnections.set(socket.userId, {
      socketId: socket.id,
      user: socket.user,
      connectedAt: new Date()
    });

    // Send connection confirmation
    socket.emit('connected', {
      message: 'Connected to Bake It Out server',
      user: socket.user.getPublicProfile()
    });

    // Handle joining a game room
    socket.on('join-room', async (data) => {
      try {
        const { roomId } = data;
        const room = await GameRoom.findById(roomId)
          .populate('host', 'username profile.displayName')
          .populate('players', 'username profile.displayName');

        if (!room || !room.isActive) {
          socket.emit('error', { message: 'Room not found' });
          return;
        }

        // Check if user is in the room
        const isInRoom = room.players.some(player => player._id.toString() === socket.userId);
        if (!isInRoom) {
          socket.emit('error', { message: 'Not authorized to join this room' });
          return;
        }

        // Join socket room
        socket.join(roomId);
        
        // Track room connection
        if (!roomConnections.has(roomId)) {
          roomConnections.set(roomId, new Set());
        }
        roomConnections.get(roomId).add(socket.userId);

        // Notify room of new connection
        socket.to(roomId).emit('player-connected', {
          user: socket.user.getPublicProfile()
        });

        // Send room state to new connection
        socket.emit('room-joined', {
          room: room.getClientInfo(),
          gameState: room.gameState
        });

        console.log(`User ${socket.user.username} joined room ${roomId}`);
      } catch (error) {
        console.error('Join room error:', error);
        socket.emit('error', { message: 'Failed to join room' });
      }
    });

    // Handle leaving a game room
    socket.on('leave-room', async (data) => {
      try {
        const { roomId } = data;
        
        // Leave socket room
        socket.leave(roomId);
        
        // Remove from room connections
        if (roomConnections.has(roomId)) {
          roomConnections.get(roomId).delete(socket.userId);
          if (roomConnections.get(roomId).size === 0) {
            roomConnections.delete(roomId);
          }
        }

        // Notify room of disconnection
        socket.to(roomId).emit('player-disconnected', {
          user: socket.user.getPublicProfile()
        });

        socket.emit('room-left', { roomId });
        console.log(`User ${socket.user.username} left room ${roomId}`);
      } catch (error) {
        console.error('Leave room error:', error);
        socket.emit('error', { message: 'Failed to leave room' });
      }
    });

    // Handle game state updates
    socket.on('game-state-update', async (data) => {
      try {
        const { roomId, gameState, action } = data;
        
        const room = await GameRoom.findById(roomId);
        if (!room || !room.isActive) {
          socket.emit('error', { message: 'Room not found' });
          return;
        }

        // Verify user is in room
        const isInRoom = room.players.some(player => player.toString() === socket.userId);
        if (!isInRoom) {
          socket.emit('error', { message: 'Not authorized' });
          return;
        }

        // Update room game state
        await room.updateGameState(gameState);

        // Broadcast to all players in room
        io.to(roomId).emit('game-state-updated', {
          gameState: room.gameState,
          action,
          updatedBy: socket.user.getPublicProfile()
        });

        console.log(`Game state updated in room ${roomId} by ${socket.user.username}`);
      } catch (error) {
        console.error('Game state update error:', error);
        socket.emit('error', { message: 'Failed to update game state' });
      }
    });

    // Handle player actions
    socket.on('player-action', async (data) => {
      try {
        const { roomId, action, payload } = data;
        
        // Broadcast action to other players in room
        socket.to(roomId).emit('player-action', {
          action,
          payload,
          player: socket.user.getPublicProfile(),
          timestamp: new Date()
        });

        console.log(`Player action in room ${roomId}: ${action} by ${socket.user.username}`);
      } catch (error) {
        console.error('Player action error:', error);
        socket.emit('error', { message: 'Failed to process action' });
      }
    });

    // Handle chat messages
    socket.on('chat-message', async (data) => {
      try {
        const { roomId, message } = data;
        
        if (!message || message.trim().length === 0) {
          return;
        }

        const chatMessage = {
          id: Date.now().toString(),
          message: message.trim(),
          user: socket.user.getPublicProfile(),
          timestamp: new Date()
        };

        // Broadcast to room
        io.to(roomId).emit('chat-message', chatMessage);

        console.log(`Chat message in room ${roomId} from ${socket.user.username}: ${message}`);
      } catch (error) {
        console.error('Chat message error:', error);
        socket.emit('error', { message: 'Failed to send message' });
      }
    });

    // Handle room settings update (host only)
    socket.on('update-room-settings', async (data) => {
      try {
        const { roomId, settings } = data;
        
        const room = await GameRoom.findById(roomId);
        if (!room || !room.isActive) {
          socket.emit('error', { message: 'Room not found' });
          return;
        }

        // Only host can update settings
        if (!room.host.equals(socket.userId)) {
          socket.emit('error', { message: 'Only host can update settings' });
          return;
        }

        // Update room settings
        room.settings = { ...room.settings, ...settings };
        await room.save();

        // Broadcast to room
        io.to(roomId).emit('room-settings-updated', {
          settings: room.settings,
          updatedBy: socket.user.getPublicProfile()
        });

        console.log(`Room settings updated in ${roomId} by ${socket.user.username}`);
      } catch (error) {
        console.error('Update room settings error:', error);
        socket.emit('error', { message: 'Failed to update settings' });
      }
    });

    // Handle game start
    socket.on('start-game', async (data) => {
      try {
        const { roomId } = data;
        
        const room = await GameRoom.findById(roomId);
        if (!room || !room.isActive) {
          socket.emit('error', { message: 'Room not found' });
          return;
        }

        // Only host can start game
        if (!room.host.equals(socket.userId)) {
          socket.emit('error', { message: 'Only host can start game' });
          return;
        }

        // Start the game
        await room.startGame();

        // Broadcast to room
        io.to(roomId).emit('game-started', {
          room: room.getClientInfo(),
          startedBy: socket.user.getPublicProfile()
        });

        console.log(`Game started in room ${roomId} by ${socket.user.username}`);
      } catch (error) {
        console.error('Start game error:', error);
        socket.emit('error', { message: error.message || 'Failed to start game' });
      }
    });

    // Handle disconnect
    socket.on('disconnect', () => {
      console.log(`User ${socket.user.username} disconnected: ${socket.id}`);
      
      // Remove from active connections
      activeConnections.delete(socket.userId);
      
      // Remove from all room connections
      for (const [roomId, connections] of roomConnections.entries()) {
        if (connections.has(socket.userId)) {
          connections.delete(socket.userId);
          
          // Notify room of disconnection
          socket.to(roomId).emit('player-disconnected', {
            user: socket.user.getPublicProfile()
          });
          
          // Clean up empty room connections
          if (connections.size === 0) {
            roomConnections.delete(roomId);
          }
        }
      }
    });

    // Handle ping for connection monitoring
    socket.on('ping', () => {
      socket.emit('pong', { timestamp: Date.now() });
    });
  });

  // Periodic cleanup of inactive rooms
  setInterval(async () => {
    try {
      const cutoff = new Date(Date.now() - 24 * 60 * 60 * 1000); // 24 hours ago
      await GameRoom.updateMany(
        { lastActivity: { $lt: cutoff }, isActive: true },
        { isActive: false }
      );
    } catch (error) {
      console.error('Room cleanup error:', error);
    }
  }, 60 * 60 * 1000); // Run every hour

  return {
    getActiveConnections: () => activeConnections,
    getRoomConnections: () => roomConnections,
    getConnectionCount: () => activeConnections.size,
    getRoomConnectionCount: (roomId) => roomConnections.get(roomId)?.size || 0
  };
}

module.exports = socketHandler;
