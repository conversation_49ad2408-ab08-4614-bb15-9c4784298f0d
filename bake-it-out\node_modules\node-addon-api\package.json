{"bugs": {"url": "https://github.com/nodejs/node-addon-api/issues"}, "contributors": ["<PERSON><PERSON><PERSON><PERSON><PERSON> (https://github.com/abhi11210646)", "<PERSON> (https://github.com/jmendeth)", "<PERSON> (https://github.com/kirbysayshi)", "<PERSON><PERSON><PERSON> (https://github.com/anisha-rohra)", "<PERSON> (https://github.com/addaleax)", "<PERSON><PERSON><PERSON> (https://github.com/BotellaA)", "<PERSON><PERSON><PERSON> (https://github.com/<PERSON><PERSON><PERSON><PERSON><PERSON>)", "<PERSON> (https://github.com/rivertam)", "<PERSON> (https://github.com/kkoopa)", "<PERSON> (https://github.com/gallafent)", "<PERSON> (https://github.com/bmacnaughton)", "<PERSON> (https://github.com/corymickelson)", "<PERSON> (https://github.com/davedoesdev)", "Dongjin Na (https://github.com/nadongguri)", "<PERSON> (https://github.com/ebickle)", "<PERSON> (https://github.com/gabrielschulhof)", "<PERSON> (https://github.com/devsnek)", "<PERSON><PERSON> (https://github.com/digitalinfinity)", "<PERSON> (https://github.com/DuBistKomisch)", "<PERSON> (https://github.com/yjaeseok)", "<PERSON> (https://github.com/jasongin)", "<PERSON> (https://github.com/jschlight)", "<PERSON><PERSON> (https://github.com/romandev)", "joshgarde (https://github.com/joshgarde)", "<PERSON> (https://github.com/<PERSON>)", "<PERSON> (https://github.com/koistya)", "<PERSON> (https://github.com/kfarnung)", "<PERSON> (https://github.com/l<PERSON>orella)", "<PERSON> (https://github.com/mcollina)", "<PERSON> (https://github.com/mhdawson)", "Michele Campus (https://github.com/kYroL01)", "<PERSON> (https://github.com/mcheshkov)", "<PERSON> (https://github.com/NickNaso)", "<PERSON> (https://github.com/iSkore)", "<PERSON> (https://github.com/DaAitch)", "<PERSON> (https://github.com/rolftimmermans)", "<PERSON> (https://github.com/ross-weir)", "<PERSON><PERSON><PERSON> (https://github.com/okuryu)", "<PERSON> (https://github.com/sampsongao)", "<PERSON> (https://github.com/sam-github)", "<PERSON> (https://github.com/boingoing)", "<PERSON> (https://github.com/fraxken)", "<PERSON> (https://github.com/tniessen)", "Tux3 (https://github.com/tux3)", "<PERSON><PERSON><PERSON> (https://github.com/morokosi)"], "dependencies": {}, "description": "Node.js API (N-API)", "devDependencies": {"safe-buffer": "^5.1.1"}, "directories": {}, "homepage": "https://github.com/nodejs/node-addon-api", "keywords": ["n-api", "napi", "addon", "native", "bindings", "c", "c++", "nan", "node-addon-api"], "license": "MIT", "main": "index.js", "name": "node-addon-api", "optionalDependencies": {}, "readme": "README.md", "repository": {"type": "git", "url": "git://github.com/nodejs/node-addon-api.git"}, "scripts": {"pretest": "node-gyp rebuild -C test", "test": "node test", "predev": "node-gyp rebuild -C test --debug", "dev": "node test", "predev:incremental": "node-gyp configure build -C test --debug", "dev:incremental": "node test", "doc": "doxygen doc/Doxyfile"}, "version": "1.7.2"}