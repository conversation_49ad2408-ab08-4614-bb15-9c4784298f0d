{"version": 3, "file": "socket.io.esm.min.js", "sources": ["../../engine.io-parser/build/esm/commons.js", "../../engine.io-parser/build/esm/encodePacket.browser.js", "../../engine.io-parser/build/esm/contrib/base64-arraybuffer.js", "../../engine.io-parser/build/esm/decodePacket.browser.js", "../../engine.io-parser/build/esm/index.js", "../../socket.io-component-emitter/lib/esm/index.js", "../../engine.io-client/build/esm/globals.js", "../../engine.io-client/build/esm/util.js", "../../engine.io-client/build/esm/transport.js", "../../engine.io-client/build/esm/contrib/parseqs.js", "../../engine.io-client/build/esm/transports/polling.js", "../../engine.io-client/build/esm/contrib/has-cors.js", "../../engine.io-client/build/esm/transports/polling-xhr.js", "../../engine.io-client/build/esm/transports/websocket.js", "../../engine.io-client/build/esm/transports/webtransport.js", "../../engine.io-client/build/esm/transports/index.js", "../../engine.io-client/build/esm/contrib/parseuri.js", "../../engine.io-client/build/esm/socket.js", "../../engine.io-client/build/esm/transports/polling-fetch.js", "../../socket.io-parser/build/esm/is-binary.js", "../../socket.io-parser/build/esm/binary.js", "../../socket.io-parser/build/esm/index.js", "../build/esm/on.js", "../build/esm/socket.js", "../build/esm/contrib/backo2.js", "../build/esm/manager.js", "../build/esm/index.js", "../build/esm/url.js"], "sourcesContent": ["const PACKET_TYPES = Object.create(null); // no Map = no polyfill\nPACKET_TYPES[\"open\"] = \"0\";\nPACKET_TYPES[\"close\"] = \"1\";\nPACKET_TYPES[\"ping\"] = \"2\";\nPACKET_TYPES[\"pong\"] = \"3\";\nPACKET_TYPES[\"message\"] = \"4\";\nPACKET_TYPES[\"upgrade\"] = \"5\";\nPACKET_TYPES[\"noop\"] = \"6\";\nconst PACKET_TYPES_REVERSE = Object.create(null);\nObject.keys(PACKET_TYPES).forEach((key) => {\n    PACKET_TYPES_REVERSE[PACKET_TYPES[key]] = key;\n});\nconst ERROR_PACKET = { type: \"error\", data: \"parser error\" };\nexport { PACKET_TYPES, PACKET_TYPES_REVERSE, ERROR_PACKET };\n", "import { PACKET_TYPES } from \"./commons.js\";\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        Object.prototype.toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\n// ArrayBuffer.isView method is not defined in IE10\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj && obj.buffer instanceof ArrayBuffer;\n};\nconst encodePacket = ({ type, data }, supportsBinary, callback) => {\n    if (withNativeBlob && data instanceof Blob) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(data, callback);\n        }\n    }\n    else if (withNativeArrayBuffer &&\n        (data instanceof ArrayBuffer || isView(data))) {\n        if (supportsBinary) {\n            return callback(data);\n        }\n        else {\n            return encodeBlobAsBase64(new Blob([data]), callback);\n        }\n    }\n    // plain string\n    return callback(PACKET_TYPES[type] + (data || \"\"));\n};\nconst encodeBlobAsBase64 = (data, callback) => {\n    const fileReader = new FileReader();\n    fileReader.onload = function () {\n        const content = fileReader.result.split(\",\")[1];\n        callback(\"b\" + (content || \"\"));\n    };\n    return fileReader.readAsDataURL(data);\n};\nfunction toArray(data) {\n    if (data instanceof Uint8Array) {\n        return data;\n    }\n    else if (data instanceof ArrayBuffer) {\n        return new Uint8Array(data);\n    }\n    else {\n        return new Uint8Array(data.buffer, data.byteOffset, data.byteLength);\n    }\n}\nlet TEXT_ENCODER;\nexport function encodePacketToBinary(packet, callback) {\n    if (withNativeBlob && packet.data instanceof Blob) {\n        return packet.data.arrayBuffer().then(toArray).then(callback);\n    }\n    else if (withNativeArrayBuffer &&\n        (packet.data instanceof ArrayBuffer || isView(packet.data))) {\n        return callback(toArray(packet.data));\n    }\n    encodePacket(packet, false, (encoded) => {\n        if (!TEXT_ENCODER) {\n            TEXT_ENCODER = new TextEncoder();\n        }\n        callback(TEXT_ENCODER.encode(encoded));\n    });\n}\nexport { encodePacket };\n", "// imported from https://github.com/socketio/base64-arraybuffer\nconst chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n// Use a lookup table to find the index.\nconst lookup = typeof Uint8Array === 'undefined' ? [] : new Uint8Array(256);\nfor (let i = 0; i < chars.length; i++) {\n    lookup[chars.charCodeAt(i)] = i;\n}\nexport const encode = (arraybuffer) => {\n    let bytes = new Uint8Array(arraybuffer), i, len = bytes.length, base64 = '';\n    for (i = 0; i < len; i += 3) {\n        base64 += chars[bytes[i] >> 2];\n        base64 += chars[((bytes[i] & 3) << 4) | (bytes[i + 1] >> 4)];\n        base64 += chars[((bytes[i + 1] & 15) << 2) | (bytes[i + 2] >> 6)];\n        base64 += chars[bytes[i + 2] & 63];\n    }\n    if (len % 3 === 2) {\n        base64 = base64.substring(0, base64.length - 1) + '=';\n    }\n    else if (len % 3 === 1) {\n        base64 = base64.substring(0, base64.length - 2) + '==';\n    }\n    return base64;\n};\nexport const decode = (base64) => {\n    let bufferLength = base64.length * 0.75, len = base64.length, i, p = 0, encoded1, encoded2, encoded3, encoded4;\n    if (base64[base64.length - 1] === '=') {\n        bufferLength--;\n        if (base64[base64.length - 2] === '=') {\n            bufferLength--;\n        }\n    }\n    const arraybuffer = new ArrayBuffer(bufferLength), bytes = new Uint8Array(arraybuffer);\n    for (i = 0; i < len; i += 4) {\n        encoded1 = lookup[base64.charCodeAt(i)];\n        encoded2 = lookup[base64.charCodeAt(i + 1)];\n        encoded3 = lookup[base64.charCodeAt(i + 2)];\n        encoded4 = lookup[base64.charCodeAt(i + 3)];\n        bytes[p++] = (encoded1 << 2) | (encoded2 >> 4);\n        bytes[p++] = ((encoded2 & 15) << 4) | (encoded3 >> 2);\n        bytes[p++] = ((encoded3 & 3) << 6) | (encoded4 & 63);\n    }\n    return arraybuffer;\n};\n", "import { ERROR_PACKET, PACKET_TYPES_REVERSE, } from \"./commons.js\";\nimport { decode } from \"./contrib/base64-arraybuffer.js\";\nconst withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nexport const decodePacket = (encodedPacket, binaryType) => {\n    if (typeof encodedPacket !== \"string\") {\n        return {\n            type: \"message\",\n            data: mapBinary(encodedPacket, binaryType),\n        };\n    }\n    const type = encodedPacket.charAt(0);\n    if (type === \"b\") {\n        return {\n            type: \"message\",\n            data: decodeBase64Packet(encodedPacket.substring(1), binaryType),\n        };\n    }\n    const packetType = PACKET_TYPES_REVERSE[type];\n    if (!packetType) {\n        return ERROR_PACKET;\n    }\n    return encodedPacket.length > 1\n        ? {\n            type: PACKET_TYPES_REVERSE[type],\n            data: encodedPacket.substring(1),\n        }\n        : {\n            type: PACKET_TYPES_REVERSE[type],\n        };\n};\nconst decodeBase64Packet = (data, binaryType) => {\n    if (withNativeArrayBuffer) {\n        const decoded = decode(data);\n        return mapBinary(decoded, binaryType);\n    }\n    else {\n        return { base64: true, data }; // fallback for old browsers\n    }\n};\nconst mapBinary = (data, binaryType) => {\n    switch (binaryType) {\n        case \"blob\":\n            if (data instanceof Blob) {\n                // from WebSocket + binaryType \"blob\"\n                return data;\n            }\n            else {\n                // from HTTP long-polling or WebTransport\n                return new Blob([data]);\n            }\n        case \"arraybuffer\":\n        default:\n            if (data instanceof ArrayBuffer) {\n                // from HTTP long-polling (base64) or WebSocket + binaryType \"arraybuffer\"\n                return data;\n            }\n            else {\n                // from WebTransport (Uint8Array)\n                return data.buffer;\n            }\n    }\n};\n", "import { encodePacket, encodePacketToBinary } from \"./encodePacket.js\";\nimport { decodePacket } from \"./decodePacket.js\";\nimport { ERROR_PACKET, } from \"./commons.js\";\nconst SEPARATOR = String.fromCharCode(30); // see https://en.wikipedia.org/wiki/Delimiter#ASCII_delimited_text\nconst encodePayload = (packets, callback) => {\n    // some packets may be added to the array while encoding, so the initial length must be saved\n    const length = packets.length;\n    const encodedPackets = new Array(length);\n    let count = 0;\n    packets.forEach((packet, i) => {\n        // force base64 encoding for binary packets\n        encodePacket(packet, false, (encodedPacket) => {\n            encodedPackets[i] = encodedPacket;\n            if (++count === length) {\n                callback(encodedPackets.join(SEPARATOR));\n            }\n        });\n    });\n};\nconst decodePayload = (encodedPayload, binaryType) => {\n    const encodedPackets = encodedPayload.split(SEPARATOR);\n    const packets = [];\n    for (let i = 0; i < encodedPackets.length; i++) {\n        const decodedPacket = decodePacket(encodedPackets[i], binaryType);\n        packets.push(decodedPacket);\n        if (decodedPacket.type === \"error\") {\n            break;\n        }\n    }\n    return packets;\n};\nexport function createPacketEncoderStream() {\n    return new TransformStream({\n        transform(packet, controller) {\n            encodePacketToBinary(packet, (encodedPacket) => {\n                const payloadLength = encodedPacket.length;\n                let header;\n                // inspired by the WebSocket format: https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API/Writing_WebSocket_servers#decoding_payload_length\n                if (payloadLength < 126) {\n                    header = new Uint8Array(1);\n                    new DataView(header.buffer).setUint8(0, payloadLength);\n                }\n                else if (payloadLength < 65536) {\n                    header = new Uint8Array(3);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 126);\n                    view.setUint16(1, payloadLength);\n                }\n                else {\n                    header = new Uint8Array(9);\n                    const view = new DataView(header.buffer);\n                    view.setUint8(0, 127);\n                    view.setBigUint64(1, BigInt(payloadLength));\n                }\n                // first bit indicates whether the payload is plain text (0) or binary (1)\n                if (packet.data && typeof packet.data !== \"string\") {\n                    header[0] |= 0x80;\n                }\n                controller.enqueue(header);\n                controller.enqueue(encodedPacket);\n            });\n        },\n    });\n}\nlet TEXT_DECODER;\nfunction totalLength(chunks) {\n    return chunks.reduce((acc, chunk) => acc + chunk.length, 0);\n}\nfunction concatChunks(chunks, size) {\n    if (chunks[0].length === size) {\n        return chunks.shift();\n    }\n    const buffer = new Uint8Array(size);\n    let j = 0;\n    for (let i = 0; i < size; i++) {\n        buffer[i] = chunks[0][j++];\n        if (j === chunks[0].length) {\n            chunks.shift();\n            j = 0;\n        }\n    }\n    if (chunks.length && j < chunks[0].length) {\n        chunks[0] = chunks[0].slice(j);\n    }\n    return buffer;\n}\nexport function createPacketDecoderStream(maxPayload, binaryType) {\n    if (!TEXT_DECODER) {\n        TEXT_DECODER = new TextDecoder();\n    }\n    const chunks = [];\n    let state = 0 /* State.READ_HEADER */;\n    let expectedLength = -1;\n    let isBinary = false;\n    return new TransformStream({\n        transform(chunk, controller) {\n            chunks.push(chunk);\n            while (true) {\n                if (state === 0 /* State.READ_HEADER */) {\n                    if (totalLength(chunks) < 1) {\n                        break;\n                    }\n                    const header = concatChunks(chunks, 1);\n                    isBinary = (header[0] & 0x80) === 0x80;\n                    expectedLength = header[0] & 0x7f;\n                    if (expectedLength < 126) {\n                        state = 3 /* State.READ_PAYLOAD */;\n                    }\n                    else if (expectedLength === 126) {\n                        state = 1 /* State.READ_EXTENDED_LENGTH_16 */;\n                    }\n                    else {\n                        state = 2 /* State.READ_EXTENDED_LENGTH_64 */;\n                    }\n                }\n                else if (state === 1 /* State.READ_EXTENDED_LENGTH_16 */) {\n                    if (totalLength(chunks) < 2) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 2);\n                    expectedLength = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length).getUint16(0);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else if (state === 2 /* State.READ_EXTENDED_LENGTH_64 */) {\n                    if (totalLength(chunks) < 8) {\n                        break;\n                    }\n                    const headerArray = concatChunks(chunks, 8);\n                    const view = new DataView(headerArray.buffer, headerArray.byteOffset, headerArray.length);\n                    const n = view.getUint32(0);\n                    if (n > Math.pow(2, 53 - 32) - 1) {\n                        // the maximum safe integer in JavaScript is 2^53 - 1\n                        controller.enqueue(ERROR_PACKET);\n                        break;\n                    }\n                    expectedLength = n * Math.pow(2, 32) + view.getUint32(4);\n                    state = 3 /* State.READ_PAYLOAD */;\n                }\n                else {\n                    if (totalLength(chunks) < expectedLength) {\n                        break;\n                    }\n                    const data = concatChunks(chunks, expectedLength);\n                    controller.enqueue(decodePacket(isBinary ? data : TEXT_DECODER.decode(data), binaryType));\n                    state = 0 /* State.READ_HEADER */;\n                }\n                if (expectedLength === 0 || expectedLength > maxPayload) {\n                    controller.enqueue(ERROR_PACKET);\n                    break;\n                }\n            }\n        },\n    });\n}\nexport const protocol = 4;\nexport { encodePacket, encodePayload, decodePacket, decodePayload, };\n", "/**\n * Initialize a new `Emitter`.\n *\n * @api public\n */\n\nexport function Emitter(obj) {\n  if (obj) return mixin(obj);\n}\n\n/**\n * Mixin the emitter properties.\n *\n * @param {Object} obj\n * @return {Object}\n * @api private\n */\n\nfunction mixin(obj) {\n  for (var key in Emitter.prototype) {\n    obj[key] = Emitter.prototype[key];\n  }\n  return obj;\n}\n\n/**\n * Listen on the given `event` with `fn`.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.on =\nEmitter.prototype.addEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n  (this._callbacks['$' + event] = this._callbacks['$' + event] || [])\n    .push(fn);\n  return this;\n};\n\n/**\n * Adds an `event` listener that will be invoked a single\n * time then automatically removed.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.once = function(event, fn){\n  function on() {\n    this.off(event, on);\n    fn.apply(this, arguments);\n  }\n\n  on.fn = fn;\n  this.on(event, on);\n  return this;\n};\n\n/**\n * Remove the given callback for `event` or all\n * registered callbacks.\n *\n * @param {String} event\n * @param {Function} fn\n * @return {Emitter}\n * @api public\n */\n\nEmitter.prototype.off =\nEmitter.prototype.removeListener =\nEmitter.prototype.removeAllListeners =\nEmitter.prototype.removeEventListener = function(event, fn){\n  this._callbacks = this._callbacks || {};\n\n  // all\n  if (0 == arguments.length) {\n    this._callbacks = {};\n    return this;\n  }\n\n  // specific event\n  var callbacks = this._callbacks['$' + event];\n  if (!callbacks) return this;\n\n  // remove all handlers\n  if (1 == arguments.length) {\n    delete this._callbacks['$' + event];\n    return this;\n  }\n\n  // remove specific handler\n  var cb;\n  for (var i = 0; i < callbacks.length; i++) {\n    cb = callbacks[i];\n    if (cb === fn || cb.fn === fn) {\n      callbacks.splice(i, 1);\n      break;\n    }\n  }\n\n  // Remove event specific arrays for event types that no\n  // one is subscribed for to avoid memory leak.\n  if (callbacks.length === 0) {\n    delete this._callbacks['$' + event];\n  }\n\n  return this;\n};\n\n/**\n * Emit `event` with the given args.\n *\n * @param {String} event\n * @param {Mixed} ...\n * @return {Emitter}\n */\n\nEmitter.prototype.emit = function(event){\n  this._callbacks = this._callbacks || {};\n\n  var args = new Array(arguments.length - 1)\n    , callbacks = this._callbacks['$' + event];\n\n  for (var i = 1; i < arguments.length; i++) {\n    args[i - 1] = arguments[i];\n  }\n\n  if (callbacks) {\n    callbacks = callbacks.slice(0);\n    for (var i = 0, len = callbacks.length; i < len; ++i) {\n      callbacks[i].apply(this, args);\n    }\n  }\n\n  return this;\n};\n\n// alias used for reserved events (protected method)\nEmitter.prototype.emitReserved = Emitter.prototype.emit;\n\n/**\n * Return array of callbacks for `event`.\n *\n * @param {String} event\n * @return {Array}\n * @api public\n */\n\nEmitter.prototype.listeners = function(event){\n  this._callbacks = this._callbacks || {};\n  return this._callbacks['$' + event] || [];\n};\n\n/**\n * Check if this emitter has `event` handlers.\n *\n * @param {String} event\n * @return {Boolean}\n * @api public\n */\n\nEmitter.prototype.hasListeners = function(event){\n  return !! this.listeners(event).length;\n};\n", "export const nextTick = (() => {\n    const isPromiseAvailable = typeof Promise === \"function\" && typeof Promise.resolve === \"function\";\n    if (isPromiseAvailable) {\n        return (cb) => Promise.resolve().then(cb);\n    }\n    else {\n        return (cb, setTimeoutFn) => setTimeoutFn(cb, 0);\n    }\n})();\nexport const globalThisShim = (() => {\n    if (typeof self !== \"undefined\") {\n        return self;\n    }\n    else if (typeof window !== \"undefined\") {\n        return window;\n    }\n    else {\n        return Function(\"return this\")();\n    }\n})();\nexport const defaultBinaryType = \"arraybuffer\";\nexport function createCookieJar() { }\n", "import { globalThisShim as globalThis } from \"./globals.node.js\";\nexport function pick(obj, ...attr) {\n    return attr.reduce((acc, k) => {\n        if (obj.hasOwnProperty(k)) {\n            acc[k] = obj[k];\n        }\n        return acc;\n    }, {});\n}\n// Keep a reference to the real timeout functions so they can be used when overridden\nconst NATIVE_SET_TIMEOUT = globalThis.setTimeout;\nconst NATIVE_CLEAR_TIMEOUT = globalThis.clearTimeout;\nexport function installTimerFunctions(obj, opts) {\n    if (opts.useNativeTimers) {\n        obj.setTimeoutFn = NATIVE_SET_TIMEOUT.bind(globalThis);\n        obj.clearTimeoutFn = NATIVE_CLEAR_TIMEOUT.bind(globalThis);\n    }\n    else {\n        obj.setTimeoutFn = globalThis.setTimeout.bind(globalThis);\n        obj.clearTimeoutFn = globalThis.clearTimeout.bind(globalThis);\n    }\n}\n// base64 encoded buffers are about 33% bigger (https://en.wikipedia.org/wiki/Base64)\nconst BASE64_OVERHEAD = 1.33;\n// we could also have used `new Blob([obj]).size`, but it isn't supported in IE9\nexport function byteLength(obj) {\n    if (typeof obj === \"string\") {\n        return utf8Length(obj);\n    }\n    // arraybuffer or blob\n    return Math.ceil((obj.byteLength || obj.size) * BASE64_OVERHEAD);\n}\nfunction utf8Length(str) {\n    let c = 0, length = 0;\n    for (let i = 0, l = str.length; i < l; i++) {\n        c = str.charCodeAt(i);\n        if (c < 0x80) {\n            length += 1;\n        }\n        else if (c < 0x800) {\n            length += 2;\n        }\n        else if (c < 0xd800 || c >= 0xe000) {\n            length += 3;\n        }\n        else {\n            i++;\n            length += 4;\n        }\n    }\n    return length;\n}\n/**\n * Generates a random 8-characters string.\n */\nexport function randomString() {\n    return (Date.now().toString(36).substring(3) +\n        Math.random().toString(36).substring(2, 5));\n}\n", "import { decodePacket } from \"engine.io-parser\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions } from \"./util.js\";\nimport { encode } from \"./contrib/parseqs.js\";\nexport class TransportError extends Error {\n    constructor(reason, description, context) {\n        super(reason);\n        this.description = description;\n        this.context = context;\n        this.type = \"TransportError\";\n    }\n}\nexport class Transport extends Emitter {\n    /**\n     * Transport abstract constructor.\n     *\n     * @param {Object} opts - options\n     * @protected\n     */\n    constructor(opts) {\n        super();\n        this.writable = false;\n        installTimerFunctions(this, opts);\n        this.opts = opts;\n        this.query = opts.query;\n        this.socket = opts.socket;\n        this.supportsBinary = !opts.forceBase64;\n    }\n    /**\n     * Emits an error.\n     *\n     * @param {String} reason\n     * @param description\n     * @param context - the error context\n     * @return {Transport} for chaining\n     * @protected\n     */\n    onError(reason, description, context) {\n        super.emitReserved(\"error\", new TransportError(reason, description, context));\n        return this;\n    }\n    /**\n     * Opens the transport.\n     */\n    open() {\n        this.readyState = \"opening\";\n        this.doOpen();\n        return this;\n    }\n    /**\n     * Closes the transport.\n     */\n    close() {\n        if (this.readyState === \"opening\" || this.readyState === \"open\") {\n            this.doClose();\n            this.onClose();\n        }\n        return this;\n    }\n    /**\n     * Sends multiple packets.\n     *\n     * @param {Array} packets\n     */\n    send(packets) {\n        if (this.readyState === \"open\") {\n            this.write(packets);\n        }\n        else {\n            // this might happen if the transport was silently closed in the beforeunload event handler\n        }\n    }\n    /**\n     * Called upon open\n     *\n     * @protected\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        this.writable = true;\n        super.emitReserved(\"open\");\n    }\n    /**\n     * Called with data.\n     *\n     * @param {String} data\n     * @protected\n     */\n    onData(data) {\n        const packet = decodePacket(data, this.socket.binaryType);\n        this.onPacket(packet);\n    }\n    /**\n     * Called with a decoded packet.\n     *\n     * @protected\n     */\n    onPacket(packet) {\n        super.emitReserved(\"packet\", packet);\n    }\n    /**\n     * Called upon close.\n     *\n     * @protected\n     */\n    onClose(details) {\n        this.readyState = \"closed\";\n        super.emitReserved(\"close\", details);\n    }\n    /**\n     * Pauses the transport, in order not to lose packets during an upgrade.\n     *\n     * @param onPause\n     */\n    pause(onPause) { }\n    createUri(schema, query = {}) {\n        return (schema +\n            \"://\" +\n            this._hostname() +\n            this._port() +\n            this.opts.path +\n            this._query(query));\n    }\n    _hostname() {\n        const hostname = this.opts.hostname;\n        return hostname.indexOf(\":\") === -1 ? hostname : \"[\" + hostname + \"]\";\n    }\n    _port() {\n        if (this.opts.port &&\n            ((this.opts.secure && Number(this.opts.port !== 443)) ||\n                (!this.opts.secure && Number(this.opts.port) !== 80))) {\n            return \":\" + this.opts.port;\n        }\n        else {\n            return \"\";\n        }\n    }\n    _query(query) {\n        const encodedQuery = encode(query);\n        return encodedQuery.length ? \"?\" + encodedQuery : \"\";\n    }\n}\n", "// imported from https://github.com/galkn/querystring\n/**\n * Compiles a querystring\n * Returns string representation of the object\n *\n * @param {Object}\n * @api private\n */\nexport function encode(obj) {\n    let str = '';\n    for (let i in obj) {\n        if (obj.hasOwnProperty(i)) {\n            if (str.length)\n                str += '&';\n            str += encodeURIComponent(i) + '=' + encodeURIComponent(obj[i]);\n        }\n    }\n    return str;\n}\n/**\n * Parses a simple querystring into an object\n *\n * @param {String} qs\n * @api private\n */\nexport function decode(qs) {\n    let qry = {};\n    let pairs = qs.split('&');\n    for (let i = 0, l = pairs.length; i < l; i++) {\n        let pair = pairs[i].split('=');\n        qry[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1]);\n    }\n    return qry;\n}\n", "import { Transport } from \"../transport.js\";\nimport { randomString } from \"../util.js\";\nimport { encodePayload, decodePayload } from \"engine.io-parser\";\nexport class Polling extends Transport {\n    constructor() {\n        super(...arguments);\n        this._polling = false;\n    }\n    get name() {\n        return \"polling\";\n    }\n    /**\n     * Opens the socket (triggers polling). We write a PING message to determine\n     * when the transport is open.\n     *\n     * @protected\n     */\n    doOpen() {\n        this._poll();\n    }\n    /**\n     * Pauses polling.\n     *\n     * @param {Function} onPause - callback upon buffers are flushed and transport is paused\n     * @package\n     */\n    pause(onPause) {\n        this.readyState = \"pausing\";\n        const pause = () => {\n            this.readyState = \"paused\";\n            onPause();\n        };\n        if (this._polling || !this.writable) {\n            let total = 0;\n            if (this._polling) {\n                total++;\n                this.once(\"pollComplete\", function () {\n                    --total || pause();\n                });\n            }\n            if (!this.writable) {\n                total++;\n                this.once(\"drain\", function () {\n                    --total || pause();\n                });\n            }\n        }\n        else {\n            pause();\n        }\n    }\n    /**\n     * Starts polling cycle.\n     *\n     * @private\n     */\n    _poll() {\n        this._polling = true;\n        this.doPoll();\n        this.emitReserved(\"poll\");\n    }\n    /**\n     * Overloads onData to detect payloads.\n     *\n     * @protected\n     */\n    onData(data) {\n        const callback = (packet) => {\n            // if its the first message we consider the transport open\n            if (\"opening\" === this.readyState && packet.type === \"open\") {\n                this.onOpen();\n            }\n            // if its a close packet, we close the ongoing requests\n            if (\"close\" === packet.type) {\n                this.onClose({ description: \"transport closed by the server\" });\n                return false;\n            }\n            // otherwise bypass onData and handle the message\n            this.onPacket(packet);\n        };\n        // decode payload\n        decodePayload(data, this.socket.binaryType).forEach(callback);\n        // if an event did not trigger closing\n        if (\"closed\" !== this.readyState) {\n            // if we got data we're not polling\n            this._polling = false;\n            this.emitReserved(\"pollComplete\");\n            if (\"open\" === this.readyState) {\n                this._poll();\n            }\n            else {\n            }\n        }\n    }\n    /**\n     * For polling, send a close packet.\n     *\n     * @protected\n     */\n    doClose() {\n        const close = () => {\n            this.write([{ type: \"close\" }]);\n        };\n        if (\"open\" === this.readyState) {\n            close();\n        }\n        else {\n            // in case we're trying to close while\n            // handshaking is in progress (GH-164)\n            this.once(\"open\", close);\n        }\n    }\n    /**\n     * Writes a packets payload.\n     *\n     * @param {Array} packets - data packets\n     * @protected\n     */\n    write(packets) {\n        this.writable = false;\n        encodePayload(packets, (data) => {\n            this.doWrite(data, () => {\n                this.writable = true;\n                this.emitReserved(\"drain\");\n            });\n        });\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"https\" : \"http\";\n        const query = this.query || {};\n        // cache busting is forced\n        if (false !== this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        if (!this.supportsBinary && !query.sid) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\n", "// imported from https://github.com/component/has-cors\nlet value = false;\ntry {\n    value = typeof XMLHttpRequest !== 'undefined' &&\n        'withCredentials' in new XMLHttpRequest();\n}\ncatch (err) {\n    // if XMLHttp support is disabled in IE then it will throw\n    // when trying to create\n}\nexport const hasCORS = value;\n", "import { Polling } from \"./polling.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { installTimerFunctions, pick } from \"../util.js\";\nimport { globalThisShim as globalThis } from \"../globals.node.js\";\nimport { hasCORS } from \"../contrib/has-cors.js\";\nfunction empty() { }\nexport class BaseXHR extends Polling {\n    /**\n     * XHR Polling constructor.\n     *\n     * @param {Object} opts\n     * @package\n     */\n    constructor(opts) {\n        super(opts);\n        if (typeof location !== \"undefined\") {\n            const isSSL = \"https:\" === location.protocol;\n            let port = location.port;\n            // some user agents have empty `location.port`\n            if (!port) {\n                port = isSSL ? \"443\" : \"80\";\n            }\n            this.xd =\n                (typeof location !== \"undefined\" &&\n                    opts.hostname !== location.hostname) ||\n                    port !== opts.port;\n        }\n    }\n    /**\n     * Sends data.\n     *\n     * @param {String} data to send.\n     * @param {Function} called upon flush.\n     * @private\n     */\n    doWrite(data, fn) {\n        const req = this.request({\n            method: \"POST\",\n            data: data,\n        });\n        req.on(\"success\", fn);\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr post error\", xhrStatus, context);\n        });\n    }\n    /**\n     * Starts a poll cycle.\n     *\n     * @private\n     */\n    doPoll() {\n        const req = this.request();\n        req.on(\"data\", this.onData.bind(this));\n        req.on(\"error\", (xhrStatus, context) => {\n            this.onError(\"xhr poll error\", xhrStatus, context);\n        });\n        this.pollXhr = req;\n    }\n}\nexport class Request extends Emitter {\n    /**\n     * Request constructor\n     *\n     * @param {Object} options\n     * @package\n     */\n    constructor(createRequest, uri, opts) {\n        super();\n        this.createRequest = createRequest;\n        installTimerFunctions(this, opts);\n        this._opts = opts;\n        this._method = opts.method || \"GET\";\n        this._uri = uri;\n        this._data = undefined !== opts.data ? opts.data : null;\n        this._create();\n    }\n    /**\n     * Creates the XHR object and sends the request.\n     *\n     * @private\n     */\n    _create() {\n        var _a;\n        const opts = pick(this._opts, \"agent\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"autoUnref\");\n        opts.xdomain = !!this._opts.xd;\n        const xhr = (this._xhr = this.createRequest(opts));\n        try {\n            xhr.open(this._method, this._uri, true);\n            try {\n                if (this._opts.extraHeaders) {\n                    // @ts-ignore\n                    xhr.setDisableHeaderCheck && xhr.setDisableHeaderCheck(true);\n                    for (let i in this._opts.extraHeaders) {\n                        if (this._opts.extraHeaders.hasOwnProperty(i)) {\n                            xhr.setRequestHeader(i, this._opts.extraHeaders[i]);\n                        }\n                    }\n                }\n            }\n            catch (e) { }\n            if (\"POST\" === this._method) {\n                try {\n                    xhr.setRequestHeader(\"Content-type\", \"text/plain;charset=UTF-8\");\n                }\n                catch (e) { }\n            }\n            try {\n                xhr.setRequestHeader(\"Accept\", \"*/*\");\n            }\n            catch (e) { }\n            (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.addCookies(xhr);\n            // ie6 check\n            if (\"withCredentials\" in xhr) {\n                xhr.withCredentials = this._opts.withCredentials;\n            }\n            if (this._opts.requestTimeout) {\n                xhr.timeout = this._opts.requestTimeout;\n            }\n            xhr.onreadystatechange = () => {\n                var _a;\n                if (xhr.readyState === 3) {\n                    (_a = this._opts.cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(\n                    // @ts-ignore\n                    xhr.getResponseHeader(\"set-cookie\"));\n                }\n                if (4 !== xhr.readyState)\n                    return;\n                if (200 === xhr.status || 1223 === xhr.status) {\n                    this._onLoad();\n                }\n                else {\n                    // make sure the `error` event handler that's user-set\n                    // does not throw in the same tick and gets caught here\n                    this.setTimeoutFn(() => {\n                        this._onError(typeof xhr.status === \"number\" ? xhr.status : 0);\n                    }, 0);\n                }\n            };\n            xhr.send(this._data);\n        }\n        catch (e) {\n            // Need to defer since .create() is called directly from the constructor\n            // and thus the 'error' event can only be only bound *after* this exception\n            // occurs.  Therefore, also, we cannot throw here at all.\n            this.setTimeoutFn(() => {\n                this._onError(e);\n            }, 0);\n            return;\n        }\n        if (typeof document !== \"undefined\") {\n            this._index = Request.requestsCount++;\n            Request.requests[this._index] = this;\n        }\n    }\n    /**\n     * Called upon error.\n     *\n     * @private\n     */\n    _onError(err) {\n        this.emitReserved(\"error\", err, this._xhr);\n        this._cleanup(true);\n    }\n    /**\n     * Cleans up house.\n     *\n     * @private\n     */\n    _cleanup(fromError) {\n        if (\"undefined\" === typeof this._xhr || null === this._xhr) {\n            return;\n        }\n        this._xhr.onreadystatechange = empty;\n        if (fromError) {\n            try {\n                this._xhr.abort();\n            }\n            catch (e) { }\n        }\n        if (typeof document !== \"undefined\") {\n            delete Request.requests[this._index];\n        }\n        this._xhr = null;\n    }\n    /**\n     * Called upon load.\n     *\n     * @private\n     */\n    _onLoad() {\n        const data = this._xhr.responseText;\n        if (data !== null) {\n            this.emitReserved(\"data\", data);\n            this.emitReserved(\"success\");\n            this._cleanup();\n        }\n    }\n    /**\n     * Aborts the request.\n     *\n     * @package\n     */\n    abort() {\n        this._cleanup();\n    }\n}\nRequest.requestsCount = 0;\nRequest.requests = {};\n/**\n * Aborts pending requests when unloading the window. This is needed to prevent\n * memory leaks (e.g. when using IE) and to ensure that no spurious error is\n * emitted.\n */\nif (typeof document !== \"undefined\") {\n    // @ts-ignore\n    if (typeof attachEvent === \"function\") {\n        // @ts-ignore\n        attachEvent(\"onunload\", unloadHandler);\n    }\n    else if (typeof addEventListener === \"function\") {\n        const terminationEvent = \"onpagehide\" in globalThis ? \"pagehide\" : \"unload\";\n        addEventListener(terminationEvent, unloadHandler, false);\n    }\n}\nfunction unloadHandler() {\n    for (let i in Request.requests) {\n        if (Request.requests.hasOwnProperty(i)) {\n            Request.requests[i].abort();\n        }\n    }\n}\nconst hasXHR2 = (function () {\n    const xhr = newRequest({\n        xdomain: false,\n    });\n    return xhr && xhr.responseType !== null;\n})();\n/**\n * HTTP long-polling based on the built-in `XMLHttpRequest` object.\n *\n * Usage: browser\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/XMLHttpRequest\n */\nexport class XHR extends BaseXHR {\n    constructor(opts) {\n        super(opts);\n        const forceBase64 = opts && opts.forceBase64;\n        this.supportsBinary = hasXHR2 && !forceBase64;\n    }\n    request(opts = {}) {\n        Object.assign(opts, { xd: this.xd }, this.opts);\n        return new Request(newRequest, this.uri(), opts);\n    }\n}\nfunction newRequest(opts) {\n    const xdomain = opts.xdomain;\n    // XMLHttpRequest can be disabled on IE\n    try {\n        if (\"undefined\" !== typeof XMLHttpRequest && (!xdomain || hasCORS)) {\n            return new XMLHttpRequest();\n        }\n    }\n    catch (e) { }\n    if (!xdomain) {\n        try {\n            return new globalThis[[\"Active\"].concat(\"Object\").join(\"X\")](\"Microsoft.XMLHTTP\");\n        }\n        catch (e) { }\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { pick, randomString } from \"../util.js\";\nimport { encodePacket } from \"engine.io-parser\";\nimport { globalThisShim as globalThis, nextTick } from \"../globals.node.js\";\n// detect ReactNative environment\nconst isReactNative = typeof navigator !== \"undefined\" &&\n    typeof navigator.product === \"string\" &&\n    navigator.product.toLowerCase() === \"reactnative\";\nexport class BaseWS extends Transport {\n    get name() {\n        return \"websocket\";\n    }\n    doOpen() {\n        const uri = this.uri();\n        const protocols = this.opts.protocols;\n        // React Native only supports the 'headers' option, and will print a warning if anything else is passed\n        const opts = isReactNative\n            ? {}\n            : pick(this.opts, \"agent\", \"perMessageDeflate\", \"pfx\", \"key\", \"passphrase\", \"cert\", \"ca\", \"ciphers\", \"rejectUnauthorized\", \"localAddress\", \"protocolVersion\", \"origin\", \"maxPayload\", \"family\", \"checkServerIdentity\");\n        if (this.opts.extraHeaders) {\n            opts.headers = this.opts.extraHeaders;\n        }\n        try {\n            this.ws = this.createSocket(uri, protocols, opts);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this.ws.binaryType = this.socket.binaryType;\n        this.addEventListeners();\n    }\n    /**\n     * Adds event listeners to the socket\n     *\n     * @private\n     */\n    addEventListeners() {\n        this.ws.onopen = () => {\n            if (this.opts.autoUnref) {\n                this.ws._socket.unref();\n            }\n            this.onOpen();\n        };\n        this.ws.onclose = (closeEvent) => this.onClose({\n            description: \"websocket connection closed\",\n            context: closeEvent,\n        });\n        this.ws.onmessage = (ev) => this.onData(ev.data);\n        this.ws.onerror = (e) => this.onError(\"websocket error\", e);\n    }\n    write(packets) {\n        this.writable = false;\n        // encodePacket efficient as it uses WS framing\n        // no need for encodePayload\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            encodePacket(packet, this.supportsBinary, (data) => {\n                // Sometimes the websocket has already been closed but the browser didn't\n                // have a chance of informing us about it yet, in that case send will\n                // throw an error\n                try {\n                    this.doWrite(packet, data);\n                }\n                catch (e) {\n                }\n                if (lastPacket) {\n                    // fake drain\n                    // defer to next tick to allow Socket to clear writeBuffer\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        if (typeof this.ws !== \"undefined\") {\n            this.ws.onerror = () => { };\n            this.ws.close();\n            this.ws = null;\n        }\n    }\n    /**\n     * Generates uri for connection.\n     *\n     * @private\n     */\n    uri() {\n        const schema = this.opts.secure ? \"wss\" : \"ws\";\n        const query = this.query || {};\n        // append timestamp to URI\n        if (this.opts.timestampRequests) {\n            query[this.opts.timestampParam] = randomString();\n        }\n        // communicate binary support capabilities\n        if (!this.supportsBinary) {\n            query.b64 = 1;\n        }\n        return this.createUri(schema, query);\n    }\n}\nconst WebSocketCtor = globalThis.WebSocket || globalThis.MozWebSocket;\n/**\n * WebSocket transport based on the built-in `WebSocket` object.\n *\n * Usage: browser, Node.js (since v21), Deno, Bun\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket\n * @see https://caniuse.com/mdn-api_websocket\n * @see https://nodejs.org/api/globals.html#websocket\n */\nexport class WS extends BaseWS {\n    createSocket(uri, protocols, opts) {\n        return !isReactNative\n            ? protocols\n                ? new WebSocketCtor(uri, protocols)\n                : new WebSocketCtor(uri)\n            : new WebSocketCtor(uri, protocols, opts);\n    }\n    doWrite(_packet, data) {\n        this.ws.send(data);\n    }\n}\n", "import { Transport } from \"../transport.js\";\nimport { nextTick } from \"../globals.node.js\";\nimport { createPacketDecoderStream, createPacketEncoderStream, } from \"engine.io-parser\";\n/**\n * WebTransport transport based on the built-in `WebTransport` object.\n *\n * Usage: browser, Node.js (with the `@fails-components/webtransport` package)\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/WebTransport\n * @see https://caniuse.com/webtransport\n */\nexport class WT extends Transport {\n    get name() {\n        return \"webtransport\";\n    }\n    doOpen() {\n        try {\n            // @ts-ignore\n            this._transport = new WebTransport(this.createUri(\"https\"), this.opts.transportOptions[this.name]);\n        }\n        catch (err) {\n            return this.emitReserved(\"error\", err);\n        }\n        this._transport.closed\n            .then(() => {\n            this.onClose();\n        })\n            .catch((err) => {\n            this.onError(\"webtransport error\", err);\n        });\n        // note: we could have used async/await, but that would require some additional polyfills\n        this._transport.ready.then(() => {\n            this._transport.createBidirectionalStream().then((stream) => {\n                const decoderStream = createPacketDecoderStream(Number.MAX_SAFE_INTEGER, this.socket.binaryType);\n                const reader = stream.readable.pipeThrough(decoderStream).getReader();\n                const encoderStream = createPacketEncoderStream();\n                encoderStream.readable.pipeTo(stream.writable);\n                this._writer = encoderStream.writable.getWriter();\n                const read = () => {\n                    reader\n                        .read()\n                        .then(({ done, value }) => {\n                        if (done) {\n                            return;\n                        }\n                        this.onPacket(value);\n                        read();\n                    })\n                        .catch((err) => {\n                    });\n                };\n                read();\n                const packet = { type: \"open\" };\n                if (this.query.sid) {\n                    packet.data = `{\"sid\":\"${this.query.sid}\"}`;\n                }\n                this._writer.write(packet).then(() => this.onOpen());\n            });\n        });\n    }\n    write(packets) {\n        this.writable = false;\n        for (let i = 0; i < packets.length; i++) {\n            const packet = packets[i];\n            const lastPacket = i === packets.length - 1;\n            this._writer.write(packet).then(() => {\n                if (lastPacket) {\n                    nextTick(() => {\n                        this.writable = true;\n                        this.emitReserved(\"drain\");\n                    }, this.setTimeoutFn);\n                }\n            });\n        }\n    }\n    doClose() {\n        var _a;\n        (_a = this._transport) === null || _a === void 0 ? void 0 : _a.close();\n    }\n}\n", "import { XHR } from \"./polling-xhr.node.js\";\nimport { WS } from \"./websocket.node.js\";\nimport { WT } from \"./webtransport.js\";\nexport const transports = {\n    websocket: WS,\n    webtransport: WT,\n    polling: XHR,\n};\n", "// imported from https://github.com/galkn/parseuri\n/**\n * Parses a URI\n *\n * Note: we could also have used the built-in URL object, but it isn't supported on all platforms.\n *\n * See:\n * - https://developer.mozilla.org/en-US/docs/Web/API/URL\n * - https://caniuse.com/url\n * - https://www.rfc-editor.org/rfc/rfc3986#appendix-B\n *\n * History of the parse() method:\n * - first commit: https://github.com/socketio/socket.io-client/commit/4ee1d5d94b3906a9c052b459f1a818b15f38f91c\n * - export into its own module: https://github.com/socketio/engine.io-client/commit/de2c561e4564efeb78f1bdb1ba39ef81b2822cb3\n * - reimport: https://github.com/socketio/engine.io-client/commit/df32277c3f6d622eec5ed09f493cae3f3391d242\n *\n * <AUTHOR> <stevenlevithan.com> (MIT license)\n * @api private\n */\nconst re = /^(?:(?![^:@\\/?#]+:[^:@\\/]*@)(http|https|ws|wss):\\/\\/)?((?:(([^:@\\/?#]*)(?::([^:@\\/?#]*))?)?@)?((?:[a-f0-9]{0,4}:){2,7}[a-f0-9]{0,4}|[^:\\/?#]*)(?::(\\d*))?)(((\\/(?:[^?#](?![^?#\\/]*\\.[^?#\\/.]+(?:[?#]|$)))*\\/?)?([^?#\\/]*))(?:\\?([^#]*))?(?:#(.*))?)/;\nconst parts = [\n    'source', 'protocol', 'authority', 'userInfo', 'user', 'password', 'host', 'port', 'relative', 'path', 'directory', 'file', 'query', 'anchor'\n];\nexport function parse(str) {\n    if (str.length > 8000) {\n        throw \"URI too long\";\n    }\n    const src = str, b = str.indexOf('['), e = str.indexOf(']');\n    if (b != -1 && e != -1) {\n        str = str.substring(0, b) + str.substring(b, e).replace(/:/g, ';') + str.substring(e, str.length);\n    }\n    let m = re.exec(str || ''), uri = {}, i = 14;\n    while (i--) {\n        uri[parts[i]] = m[i] || '';\n    }\n    if (b != -1 && e != -1) {\n        uri.source = src;\n        uri.host = uri.host.substring(1, uri.host.length - 1).replace(/;/g, ':');\n        uri.authority = uri.authority.replace('[', '').replace(']', '').replace(/;/g, ':');\n        uri.ipv6uri = true;\n    }\n    uri.pathNames = pathNames(uri, uri['path']);\n    uri.queryKey = queryKey(uri, uri['query']);\n    return uri;\n}\nfunction pathNames(obj, path) {\n    const regx = /\\/{2,9}/g, names = path.replace(regx, \"/\").split(\"/\");\n    if (path.slice(0, 1) == '/' || path.length === 0) {\n        names.splice(0, 1);\n    }\n    if (path.slice(-1) == '/') {\n        names.splice(names.length - 1, 1);\n    }\n    return names;\n}\nfunction queryKey(uri, query) {\n    const data = {};\n    query.replace(/(?:^|&)([^&=]*)=?([^&]*)/g, function ($0, $1, $2) {\n        if ($1) {\n            data[$1] = $2;\n        }\n    });\n    return data;\n}\n", "import { transports as DEFAULT_TRANSPORTS } from \"./transports/index.js\";\nimport { installTimerFunctions, byteLength } from \"./util.js\";\nimport { decode } from \"./contrib/parseqs.js\";\nimport { parse } from \"./contrib/parseuri.js\";\nimport { Emitter } from \"@socket.io/component-emitter\";\nimport { protocol } from \"engine.io-parser\";\nimport { createCookieJar, defaultBinaryType, nextTick, } from \"./globals.node.js\";\nconst withEventListeners = typeof addEventListener === \"function\" &&\n    typeof removeEventListener === \"function\";\nconst OFFLINE_EVENT_LISTENERS = [];\nif (withEventListeners) {\n    // within a ServiceWorker, any event handler for the 'offline' event must be added on the initial evaluation of the\n    // script, so we create one single event listener here which will forward the event to the socket instances\n    addEventListener(\"offline\", () => {\n        OFFLINE_EVENT_LISTENERS.forEach((listener) => listener());\n    }, false);\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes without upgrade mechanism, which means that it will keep the first low-level transport that\n * successfully establishes the connection.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithoutUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithoutUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithUpgrade\n * @see Socket\n */\nexport class SocketWithoutUpgrade extends Emitter {\n    /**\n     * Socket constructor.\n     *\n     * @param {String|Object} uri - uri or options\n     * @param {Object} opts - options\n     */\n    constructor(uri, opts) {\n        super();\n        this.binaryType = defaultBinaryType;\n        this.writeBuffer = [];\n        this._prevBufferLen = 0;\n        this._pingInterval = -1;\n        this._pingTimeout = -1;\n        this._maxPayload = -1;\n        /**\n         * The expiration timestamp of the {@link _pingTimeoutTimer} object is tracked, in case the timer is throttled and the\n         * callback is not fired on time. This can happen for example when a laptop is suspended or when a phone is locked.\n         */\n        this._pingTimeoutTime = Infinity;\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = null;\n        }\n        if (uri) {\n            const parsedUri = parse(uri);\n            opts.hostname = parsedUri.host;\n            opts.secure =\n                parsedUri.protocol === \"https\" || parsedUri.protocol === \"wss\";\n            opts.port = parsedUri.port;\n            if (parsedUri.query)\n                opts.query = parsedUri.query;\n        }\n        else if (opts.host) {\n            opts.hostname = parse(opts.host).host;\n        }\n        installTimerFunctions(this, opts);\n        this.secure =\n            null != opts.secure\n                ? opts.secure\n                : typeof location !== \"undefined\" && \"https:\" === location.protocol;\n        if (opts.hostname && !opts.port) {\n            // if no port is specified manually, use the protocol default\n            opts.port = this.secure ? \"443\" : \"80\";\n        }\n        this.hostname =\n            opts.hostname ||\n                (typeof location !== \"undefined\" ? location.hostname : \"localhost\");\n        this.port =\n            opts.port ||\n                (typeof location !== \"undefined\" && location.port\n                    ? location.port\n                    : this.secure\n                        ? \"443\"\n                        : \"80\");\n        this.transports = [];\n        this._transportsByName = {};\n        opts.transports.forEach((t) => {\n            const transportName = t.prototype.name;\n            this.transports.push(transportName);\n            this._transportsByName[transportName] = t;\n        });\n        this.opts = Object.assign({\n            path: \"/engine.io\",\n            agent: false,\n            withCredentials: false,\n            upgrade: true,\n            timestampParam: \"t\",\n            rememberUpgrade: false,\n            addTrailingSlash: true,\n            rejectUnauthorized: true,\n            perMessageDeflate: {\n                threshold: 1024,\n            },\n            transportOptions: {},\n            closeOnBeforeunload: false,\n        }, opts);\n        this.opts.path =\n            this.opts.path.replace(/\\/$/, \"\") +\n                (this.opts.addTrailingSlash ? \"/\" : \"\");\n        if (typeof this.opts.query === \"string\") {\n            this.opts.query = decode(this.opts.query);\n        }\n        if (withEventListeners) {\n            if (this.opts.closeOnBeforeunload) {\n                // Firefox closes the connection when the \"beforeunload\" event is emitted but not Chrome. This event listener\n                // ensures every browser behaves the same (no \"disconnect\" event at the Socket.IO level when the page is\n                // closed/reloaded)\n                this._beforeunloadEventListener = () => {\n                    if (this.transport) {\n                        // silently close the transport\n                        this.transport.removeAllListeners();\n                        this.transport.close();\n                    }\n                };\n                addEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n            }\n            if (this.hostname !== \"localhost\") {\n                this._offlineEventListener = () => {\n                    this._onClose(\"transport close\", {\n                        description: \"network connection lost\",\n                    });\n                };\n                OFFLINE_EVENT_LISTENERS.push(this._offlineEventListener);\n            }\n        }\n        if (this.opts.withCredentials) {\n            this._cookieJar = createCookieJar();\n        }\n        this._open();\n    }\n    /**\n     * Creates transport of the given type.\n     *\n     * @param {String} name - transport name\n     * @return {Transport}\n     * @private\n     */\n    createTransport(name) {\n        const query = Object.assign({}, this.opts.query);\n        // append engine.io protocol identifier\n        query.EIO = protocol;\n        // transport name\n        query.transport = name;\n        // session id if we already have one\n        if (this.id)\n            query.sid = this.id;\n        const opts = Object.assign({}, this.opts, {\n            query,\n            socket: this,\n            hostname: this.hostname,\n            secure: this.secure,\n            port: this.port,\n        }, this.opts.transportOptions[name]);\n        return new this._transportsByName[name](opts);\n    }\n    /**\n     * Initializes transport to use and starts probe.\n     *\n     * @private\n     */\n    _open() {\n        if (this.transports.length === 0) {\n            // Emit error on next tick so it can be listened to\n            this.setTimeoutFn(() => {\n                this.emitReserved(\"error\", \"No transports available\");\n            }, 0);\n            return;\n        }\n        const transportName = this.opts.rememberUpgrade &&\n            SocketWithoutUpgrade.priorWebsocketSuccess &&\n            this.transports.indexOf(\"websocket\") !== -1\n            ? \"websocket\"\n            : this.transports[0];\n        this.readyState = \"opening\";\n        const transport = this.createTransport(transportName);\n        transport.open();\n        this.setTransport(transport);\n    }\n    /**\n     * Sets the current transport. Disables the existing one (if any).\n     *\n     * @private\n     */\n    setTransport(transport) {\n        if (this.transport) {\n            this.transport.removeAllListeners();\n        }\n        // set up transport\n        this.transport = transport;\n        // set up transport listeners\n        transport\n            .on(\"drain\", this._onDrain.bind(this))\n            .on(\"packet\", this._onPacket.bind(this))\n            .on(\"error\", this._onError.bind(this))\n            .on(\"close\", (reason) => this._onClose(\"transport close\", reason));\n    }\n    /**\n     * Called when connection is deemed open.\n     *\n     * @private\n     */\n    onOpen() {\n        this.readyState = \"open\";\n        SocketWithoutUpgrade.priorWebsocketSuccess =\n            \"websocket\" === this.transport.name;\n        this.emitReserved(\"open\");\n        this.flush();\n    }\n    /**\n     * Handles a packet.\n     *\n     * @private\n     */\n    _onPacket(packet) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            this.emitReserved(\"packet\", packet);\n            // Socket is live - any packet counts\n            this.emitReserved(\"heartbeat\");\n            switch (packet.type) {\n                case \"open\":\n                    this.onHandshake(JSON.parse(packet.data));\n                    break;\n                case \"ping\":\n                    this._sendPacket(\"pong\");\n                    this.emitReserved(\"ping\");\n                    this.emitReserved(\"pong\");\n                    this._resetPingTimeout();\n                    break;\n                case \"error\":\n                    const err = new Error(\"server error\");\n                    // @ts-ignore\n                    err.code = packet.data;\n                    this._onError(err);\n                    break;\n                case \"message\":\n                    this.emitReserved(\"data\", packet.data);\n                    this.emitReserved(\"message\", packet.data);\n                    break;\n            }\n        }\n        else {\n        }\n    }\n    /**\n     * Called upon handshake completion.\n     *\n     * @param {Object} data - handshake obj\n     * @private\n     */\n    onHandshake(data) {\n        this.emitReserved(\"handshake\", data);\n        this.id = data.sid;\n        this.transport.query.sid = data.sid;\n        this._pingInterval = data.pingInterval;\n        this._pingTimeout = data.pingTimeout;\n        this._maxPayload = data.maxPayload;\n        this.onOpen();\n        // In case open handler closes socket\n        if (\"closed\" === this.readyState)\n            return;\n        this._resetPingTimeout();\n    }\n    /**\n     * Sets and resets ping timeout timer based on server pings.\n     *\n     * @private\n     */\n    _resetPingTimeout() {\n        this.clearTimeoutFn(this._pingTimeoutTimer);\n        const delay = this._pingInterval + this._pingTimeout;\n        this._pingTimeoutTime = Date.now() + delay;\n        this._pingTimeoutTimer = this.setTimeoutFn(() => {\n            this._onClose(\"ping timeout\");\n        }, delay);\n        if (this.opts.autoUnref) {\n            this._pingTimeoutTimer.unref();\n        }\n    }\n    /**\n     * Called on `drain` event\n     *\n     * @private\n     */\n    _onDrain() {\n        this.writeBuffer.splice(0, this._prevBufferLen);\n        // setting prevBufferLen = 0 is very important\n        // for example, when upgrading, upgrade packet is sent over,\n        // and a nonzero prevBufferLen could cause problems on `drain`\n        this._prevBufferLen = 0;\n        if (0 === this.writeBuffer.length) {\n            this.emitReserved(\"drain\");\n        }\n        else {\n            this.flush();\n        }\n    }\n    /**\n     * Flush write buffers.\n     *\n     * @private\n     */\n    flush() {\n        if (\"closed\" !== this.readyState &&\n            this.transport.writable &&\n            !this.upgrading &&\n            this.writeBuffer.length) {\n            const packets = this._getWritablePackets();\n            this.transport.send(packets);\n            // keep track of current length of writeBuffer\n            // splice writeBuffer and callbackBuffer on `drain`\n            this._prevBufferLen = packets.length;\n            this.emitReserved(\"flush\");\n        }\n    }\n    /**\n     * Ensure the encoded size of the writeBuffer is below the maxPayload value sent by the server (only for HTTP\n     * long-polling)\n     *\n     * @private\n     */\n    _getWritablePackets() {\n        const shouldCheckPayloadSize = this._maxPayload &&\n            this.transport.name === \"polling\" &&\n            this.writeBuffer.length > 1;\n        if (!shouldCheckPayloadSize) {\n            return this.writeBuffer;\n        }\n        let payloadSize = 1; // first packet type\n        for (let i = 0; i < this.writeBuffer.length; i++) {\n            const data = this.writeBuffer[i].data;\n            if (data) {\n                payloadSize += byteLength(data);\n            }\n            if (i > 0 && payloadSize > this._maxPayload) {\n                return this.writeBuffer.slice(0, i);\n            }\n            payloadSize += 2; // separator + packet type\n        }\n        return this.writeBuffer;\n    }\n    /**\n     * Checks whether the heartbeat timer has expired but the socket has not yet been notified.\n     *\n     * Note: this method is private for now because it does not really fit the WebSocket API, but if we put it in the\n     * `write()` method then the message would not be buffered by the Socket.IO client.\n     *\n     * @return {boolean}\n     * @private\n     */\n    /* private */ _hasPingExpired() {\n        if (!this._pingTimeoutTime)\n            return true;\n        const hasExpired = Date.now() > this._pingTimeoutTime;\n        if (hasExpired) {\n            this._pingTimeoutTime = 0;\n            nextTick(() => {\n                this._onClose(\"ping timeout\");\n            }, this.setTimeoutFn);\n        }\n        return hasExpired;\n    }\n    /**\n     * Sends a message.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    write(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a message. Alias of {@link Socket#write}.\n     *\n     * @param {String} msg - message.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @return {Socket} for chaining.\n     */\n    send(msg, options, fn) {\n        this._sendPacket(\"message\", msg, options, fn);\n        return this;\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param {String} type: packet type.\n     * @param {String} data.\n     * @param {Object} options.\n     * @param {Function} fn - callback function.\n     * @private\n     */\n    _sendPacket(type, data, options, fn) {\n        if (\"function\" === typeof data) {\n            fn = data;\n            data = undefined;\n        }\n        if (\"function\" === typeof options) {\n            fn = options;\n            options = null;\n        }\n        if (\"closing\" === this.readyState || \"closed\" === this.readyState) {\n            return;\n        }\n        options = options || {};\n        options.compress = false !== options.compress;\n        const packet = {\n            type: type,\n            data: data,\n            options: options,\n        };\n        this.emitReserved(\"packetCreate\", packet);\n        this.writeBuffer.push(packet);\n        if (fn)\n            this.once(\"flush\", fn);\n        this.flush();\n    }\n    /**\n     * Closes the connection.\n     */\n    close() {\n        const close = () => {\n            this._onClose(\"forced close\");\n            this.transport.close();\n        };\n        const cleanupAndClose = () => {\n            this.off(\"upgrade\", cleanupAndClose);\n            this.off(\"upgradeError\", cleanupAndClose);\n            close();\n        };\n        const waitForUpgrade = () => {\n            // wait for upgrade to finish since we can't send packets while pausing a transport\n            this.once(\"upgrade\", cleanupAndClose);\n            this.once(\"upgradeError\", cleanupAndClose);\n        };\n        if (\"opening\" === this.readyState || \"open\" === this.readyState) {\n            this.readyState = \"closing\";\n            if (this.writeBuffer.length) {\n                this.once(\"drain\", () => {\n                    if (this.upgrading) {\n                        waitForUpgrade();\n                    }\n                    else {\n                        close();\n                    }\n                });\n            }\n            else if (this.upgrading) {\n                waitForUpgrade();\n            }\n            else {\n                close();\n            }\n        }\n        return this;\n    }\n    /**\n     * Called upon transport error\n     *\n     * @private\n     */\n    _onError(err) {\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        if (this.opts.tryAllTransports &&\n            this.transports.length > 1 &&\n            this.readyState === \"opening\") {\n            this.transports.shift();\n            return this._open();\n        }\n        this.emitReserved(\"error\", err);\n        this._onClose(\"transport error\", err);\n    }\n    /**\n     * Called upon transport close.\n     *\n     * @private\n     */\n    _onClose(reason, description) {\n        if (\"opening\" === this.readyState ||\n            \"open\" === this.readyState ||\n            \"closing\" === this.readyState) {\n            // clear timers\n            this.clearTimeoutFn(this._pingTimeoutTimer);\n            // stop event from firing again for transport\n            this.transport.removeAllListeners(\"close\");\n            // ensure transport won't stay open\n            this.transport.close();\n            // ignore further transport communication\n            this.transport.removeAllListeners();\n            if (withEventListeners) {\n                if (this._beforeunloadEventListener) {\n                    removeEventListener(\"beforeunload\", this._beforeunloadEventListener, false);\n                }\n                if (this._offlineEventListener) {\n                    const i = OFFLINE_EVENT_LISTENERS.indexOf(this._offlineEventListener);\n                    if (i !== -1) {\n                        OFFLINE_EVENT_LISTENERS.splice(i, 1);\n                    }\n                }\n            }\n            // set ready state\n            this.readyState = \"closed\";\n            // clear session id\n            this.id = null;\n            // emit close event\n            this.emitReserved(\"close\", reason, description);\n            // clean buffers after, so users can still\n            // grab the buffers on `close` event\n            this.writeBuffer = [];\n            this._prevBufferLen = 0;\n        }\n    }\n}\nSocketWithoutUpgrade.protocol = protocol;\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * In order to allow tree-shaking, there are no transports included, that's why the `transports` option is mandatory.\n *\n * @example\n * import { SocketWithUpgrade, WebSocket } from \"engine.io-client\";\n *\n * const socket = new SocketWithUpgrade({\n *   transports: [WebSocket]\n * });\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see Socket\n */\nexport class SocketWithUpgrade extends SocketWithoutUpgrade {\n    constructor() {\n        super(...arguments);\n        this._upgrades = [];\n    }\n    onOpen() {\n        super.onOpen();\n        if (\"open\" === this.readyState && this.opts.upgrade) {\n            for (let i = 0; i < this._upgrades.length; i++) {\n                this._probe(this._upgrades[i]);\n            }\n        }\n    }\n    /**\n     * Probes a transport.\n     *\n     * @param {String} name - transport name\n     * @private\n     */\n    _probe(name) {\n        let transport = this.createTransport(name);\n        let failed = false;\n        SocketWithoutUpgrade.priorWebsocketSuccess = false;\n        const onTransportOpen = () => {\n            if (failed)\n                return;\n            transport.send([{ type: \"ping\", data: \"probe\" }]);\n            transport.once(\"packet\", (msg) => {\n                if (failed)\n                    return;\n                if (\"pong\" === msg.type && \"probe\" === msg.data) {\n                    this.upgrading = true;\n                    this.emitReserved(\"upgrading\", transport);\n                    if (!transport)\n                        return;\n                    SocketWithoutUpgrade.priorWebsocketSuccess =\n                        \"websocket\" === transport.name;\n                    this.transport.pause(() => {\n                        if (failed)\n                            return;\n                        if (\"closed\" === this.readyState)\n                            return;\n                        cleanup();\n                        this.setTransport(transport);\n                        transport.send([{ type: \"upgrade\" }]);\n                        this.emitReserved(\"upgrade\", transport);\n                        transport = null;\n                        this.upgrading = false;\n                        this.flush();\n                    });\n                }\n                else {\n                    const err = new Error(\"probe error\");\n                    // @ts-ignore\n                    err.transport = transport.name;\n                    this.emitReserved(\"upgradeError\", err);\n                }\n            });\n        };\n        function freezeTransport() {\n            if (failed)\n                return;\n            // Any callback called by transport should be ignored since now\n            failed = true;\n            cleanup();\n            transport.close();\n            transport = null;\n        }\n        // Handle any error that happens while probing\n        const onerror = (err) => {\n            const error = new Error(\"probe error: \" + err);\n            // @ts-ignore\n            error.transport = transport.name;\n            freezeTransport();\n            this.emitReserved(\"upgradeError\", error);\n        };\n        function onTransportClose() {\n            onerror(\"transport closed\");\n        }\n        // When the socket is closed while we're probing\n        function onclose() {\n            onerror(\"socket closed\");\n        }\n        // When the socket is upgraded while we're probing\n        function onupgrade(to) {\n            if (transport && to.name !== transport.name) {\n                freezeTransport();\n            }\n        }\n        // Remove all listeners on the transport and on self\n        const cleanup = () => {\n            transport.removeListener(\"open\", onTransportOpen);\n            transport.removeListener(\"error\", onerror);\n            transport.removeListener(\"close\", onTransportClose);\n            this.off(\"close\", onclose);\n            this.off(\"upgrading\", onupgrade);\n        };\n        transport.once(\"open\", onTransportOpen);\n        transport.once(\"error\", onerror);\n        transport.once(\"close\", onTransportClose);\n        this.once(\"close\", onclose);\n        this.once(\"upgrading\", onupgrade);\n        if (this._upgrades.indexOf(\"webtransport\") !== -1 &&\n            name !== \"webtransport\") {\n            // favor WebTransport\n            this.setTimeoutFn(() => {\n                if (!failed) {\n                    transport.open();\n                }\n            }, 200);\n        }\n        else {\n            transport.open();\n        }\n    }\n    onHandshake(data) {\n        this._upgrades = this._filterUpgrades(data.upgrades);\n        super.onHandshake(data);\n    }\n    /**\n     * Filters upgrades, returning only those matching client transports.\n     *\n     * @param {Array} upgrades - server upgrades\n     * @private\n     */\n    _filterUpgrades(upgrades) {\n        const filteredUpgrades = [];\n        for (let i = 0; i < upgrades.length; i++) {\n            if (~this.transports.indexOf(upgrades[i]))\n                filteredUpgrades.push(upgrades[i]);\n        }\n        return filteredUpgrades;\n    }\n}\n/**\n * This class provides a WebSocket-like interface to connect to an Engine.IO server. The connection will be established\n * with one of the available low-level transports, like HTTP long-polling, WebSocket or WebTransport.\n *\n * This class comes with an upgrade mechanism, which means that once the connection is established with the first\n * low-level transport, it will try to upgrade to a better transport.\n *\n * @example\n * import { Socket } from \"engine.io-client\";\n *\n * const socket = new Socket();\n *\n * socket.on(\"open\", () => {\n *   socket.send(\"hello\");\n * });\n *\n * @see SocketWithoutUpgrade\n * @see SocketWithUpgrade\n */\nexport class Socket extends SocketWithUpgrade {\n    constructor(uri, opts = {}) {\n        const o = typeof uri === \"object\" ? uri : opts;\n        if (!o.transports ||\n            (o.transports && typeof o.transports[0] === \"string\")) {\n            o.transports = (o.transports || [\"polling\", \"websocket\", \"webtransport\"])\n                .map((transportName) => DEFAULT_TRANSPORTS[transportName])\n                .filter((t) => !!t);\n        }\n        super(uri, o);\n    }\n}\n", "import { Polling } from \"./polling.js\";\n/**\n * HTTP long-polling based on the built-in `fetch()` method.\n *\n * Usage: browser, Node.js (since v18), <PERSON><PERSON>, <PERSON>un\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/API/fetch\n * @see https://caniuse.com/fetch\n * @see https://nodejs.org/api/globals.html#fetch\n */\nexport class Fetch extends Polling {\n    doPoll() {\n        this._fetch()\n            .then((res) => {\n            if (!res.ok) {\n                return this.onError(\"fetch read error\", res.status, res);\n            }\n            res.text().then((data) => this.onData(data));\n        })\n            .catch((err) => {\n            this.onError(\"fetch read error\", err);\n        });\n    }\n    doWrite(data, callback) {\n        this._fetch(data)\n            .then((res) => {\n            if (!res.ok) {\n                return this.onError(\"fetch write error\", res.status, res);\n            }\n            callback();\n        })\n            .catch((err) => {\n            this.onError(\"fetch write error\", err);\n        });\n    }\n    _fetch(data) {\n        var _a;\n        const isPost = data !== undefined;\n        const headers = new Headers(this.opts.extraHeaders);\n        if (isPost) {\n            headers.set(\"content-type\", \"text/plain;charset=UTF-8\");\n        }\n        (_a = this.socket._cookieJar) === null || _a === void 0 ? void 0 : _a.appendCookies(headers);\n        return fetch(this.uri(), {\n            method: isPost ? \"POST\" : \"GET\",\n            body: isPost ? data : null,\n            headers,\n            credentials: this.opts.withCredentials ? \"include\" : \"omit\",\n        }).then((res) => {\n            var _a;\n            // @ts-ignore getSetCookie() was added in Node.js v19.7.0\n            (_a = this.socket._cookieJar) === null || _a === void 0 ? void 0 : _a.parseCookies(res.headers.getSetCookie());\n            return res;\n        });\n    }\n}\n", "const withNativeArrayBuffer = typeof ArrayBuffer === \"function\";\nconst isView = (obj) => {\n    return typeof ArrayBuffer.isView === \"function\"\n        ? ArrayBuffer.isView(obj)\n        : obj.buffer instanceof ArrayBuffer;\n};\nconst toString = Object.prototype.toString;\nconst withNativeBlob = typeof Blob === \"function\" ||\n    (typeof Blob !== \"undefined\" &&\n        toString.call(Blob) === \"[object BlobConstructor]\");\nconst withNativeFile = typeof File === \"function\" ||\n    (typeof File !== \"undefined\" &&\n        toString.call(File) === \"[object FileConstructor]\");\n/**\n * Returns true if obj is a Buffer, an ArrayBuffer, a Blob or a File.\n *\n * @private\n */\nexport function isBinary(obj) {\n    return ((withNativeArrayBuffer && (obj instanceof ArrayBuffer || isView(obj))) ||\n        (withNativeBlob && obj instanceof Blob) ||\n        (withNativeFile && obj instanceof File));\n}\nexport function hasBinary(obj, toJSON) {\n    if (!obj || typeof obj !== \"object\") {\n        return false;\n    }\n    if (Array.isArray(obj)) {\n        for (let i = 0, l = obj.length; i < l; i++) {\n            if (hasBinary(obj[i])) {\n                return true;\n            }\n        }\n        return false;\n    }\n    if (isBinary(obj)) {\n        return true;\n    }\n    if (obj.toJSON &&\n        typeof obj.toJSON === \"function\" &&\n        arguments.length === 1) {\n        return hasBinary(obj.toJSON(), true);\n    }\n    for (const key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key) && hasBinary(obj[key])) {\n            return true;\n        }\n    }\n    return false;\n}\n", "import { isBinary } from \"./is-binary.js\";\n/**\n * Replaces every Buffer | ArrayBuffer | Blob | File in packet with a numbered placeholder.\n *\n * @param {Object} packet - socket.io event packet\n * @return {Object} with deconstructed packet and list of buffers\n * @public\n */\nexport function deconstructPacket(packet) {\n    const buffers = [];\n    const packetData = packet.data;\n    const pack = packet;\n    pack.data = _deconstructPacket(packetData, buffers);\n    pack.attachments = buffers.length; // number of binary 'attachments'\n    return { packet: pack, buffers: buffers };\n}\nfunction _deconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (isBinary(data)) {\n        const placeholder = { _placeholder: true, num: buffers.length };\n        buffers.push(data);\n        return placeholder;\n    }\n    else if (Array.isArray(data)) {\n        const newData = new Array(data.length);\n        for (let i = 0; i < data.length; i++) {\n            newData[i] = _deconstructPacket(data[i], buffers);\n        }\n        return newData;\n    }\n    else if (typeof data === \"object\" && !(data instanceof Date)) {\n        const newData = {};\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                newData[key] = _deconstructPacket(data[key], buffers);\n            }\n        }\n        return newData;\n    }\n    return data;\n}\n/**\n * Reconstructs a binary packet from its placeholder packet and buffers\n *\n * @param {Object} packet - event packet with placeholders\n * @param {Array} buffers - binary buffers to put in placeholder positions\n * @return {Object} reconstructed packet\n * @public\n */\nexport function reconstructPacket(packet, buffers) {\n    packet.data = _reconstructPacket(packet.data, buffers);\n    delete packet.attachments; // no longer useful\n    return packet;\n}\nfunction _reconstructPacket(data, buffers) {\n    if (!data)\n        return data;\n    if (data && data._placeholder === true) {\n        const isIndexValid = typeof data.num === \"number\" &&\n            data.num >= 0 &&\n            data.num < buffers.length;\n        if (isIndexValid) {\n            return buffers[data.num]; // appropriate buffer (should be natural order anyway)\n        }\n        else {\n            throw new Error(\"illegal attachments\");\n        }\n    }\n    else if (Array.isArray(data)) {\n        for (let i = 0; i < data.length; i++) {\n            data[i] = _reconstructPacket(data[i], buffers);\n        }\n    }\n    else if (typeof data === \"object\") {\n        for (const key in data) {\n            if (Object.prototype.hasOwnProperty.call(data, key)) {\n                data[key] = _reconstructPacket(data[key], buffers);\n            }\n        }\n    }\n    return data;\n}\n", "import { Emitter } from \"@socket.io/component-emitter\";\nimport { deconstructPacket, reconstructPacket } from \"./binary.js\";\nimport { isBinary, hasBinary } from \"./is-binary.js\";\n/**\n * These strings must not be used as event names, as they have a special meaning.\n */\nconst RESERVED_EVENTS = [\n    \"connect\", // used on the client side\n    \"connect_error\", // used on the client side\n    \"disconnect\", // used on both sides\n    \"disconnecting\", // used on the server side\n    \"newListener\", // used by the Node.js EventEmitter\n    \"removeListener\", // used by the Node.js EventEmitter\n];\n/**\n * Protocol version.\n *\n * @public\n */\nexport const protocol = 5;\nexport var PacketType;\n(function (PacketType) {\n    PacketType[PacketType[\"CONNECT\"] = 0] = \"CONNECT\";\n    PacketType[PacketType[\"DISCONNECT\"] = 1] = \"DISCONNECT\";\n    PacketType[PacketType[\"EVENT\"] = 2] = \"EVENT\";\n    PacketType[PacketType[\"ACK\"] = 3] = \"ACK\";\n    PacketType[PacketType[\"CONNECT_ERROR\"] = 4] = \"CONNECT_ERROR\";\n    PacketType[PacketType[\"BINARY_EVENT\"] = 5] = \"BINARY_EVENT\";\n    PacketType[PacketType[\"BINARY_ACK\"] = 6] = \"BINARY_ACK\";\n})(PacketType || (PacketType = {}));\n/**\n * A socket.io Encoder instance\n */\nexport class Encoder {\n    /**\n     * Encoder constructor\n     *\n     * @param {function} replacer - custom replacer to pass down to JSON.parse\n     */\n    constructor(replacer) {\n        this.replacer = replacer;\n    }\n    /**\n     * Encode a packet as a single string if non-binary, or as a\n     * buffer sequence, depending on packet type.\n     *\n     * @param {Object} obj - packet object\n     */\n    encode(obj) {\n        if (obj.type === PacketType.EVENT || obj.type === PacketType.ACK) {\n            if (hasBinary(obj)) {\n                return this.encodeAsBinary({\n                    type: obj.type === PacketType.EVENT\n                        ? PacketType.BINARY_EVENT\n                        : PacketType.BINARY_ACK,\n                    nsp: obj.nsp,\n                    data: obj.data,\n                    id: obj.id,\n                });\n            }\n        }\n        return [this.encodeAsString(obj)];\n    }\n    /**\n     * Encode packet as string.\n     */\n    encodeAsString(obj) {\n        // first is type\n        let str = \"\" + obj.type;\n        // attachments if we have them\n        if (obj.type === PacketType.BINARY_EVENT ||\n            obj.type === PacketType.BINARY_ACK) {\n            str += obj.attachments + \"-\";\n        }\n        // if we have a namespace other than `/`\n        // we append it followed by a comma `,`\n        if (obj.nsp && \"/\" !== obj.nsp) {\n            str += obj.nsp + \",\";\n        }\n        // immediately followed by the id\n        if (null != obj.id) {\n            str += obj.id;\n        }\n        // json data\n        if (null != obj.data) {\n            str += JSON.stringify(obj.data, this.replacer);\n        }\n        return str;\n    }\n    /**\n     * Encode packet as 'buffer sequence' by removing blobs, and\n     * deconstructing packet into object with placeholders and\n     * a list of buffers.\n     */\n    encodeAsBinary(obj) {\n        const deconstruction = deconstructPacket(obj);\n        const pack = this.encodeAsString(deconstruction.packet);\n        const buffers = deconstruction.buffers;\n        buffers.unshift(pack); // add packet info to beginning of data list\n        return buffers; // write all the buffers\n    }\n}\n/**\n * A socket.io Decoder instance\n *\n * @return {Object} decoder\n */\nexport class Decoder extends Emitter {\n    /**\n     * Decoder constructor\n     *\n     * @param {function} reviver - custom reviver to pass down to JSON.stringify\n     */\n    constructor(reviver) {\n        super();\n        this.reviver = reviver;\n    }\n    /**\n     * Decodes an encoded packet string into packet JSON.\n     *\n     * @param {String} obj - encoded packet\n     */\n    add(obj) {\n        let packet;\n        if (typeof obj === \"string\") {\n            if (this.reconstructor) {\n                throw new Error(\"got plaintext data when reconstructing a packet\");\n            }\n            packet = this.decodeString(obj);\n            const isBinaryEvent = packet.type === PacketType.BINARY_EVENT;\n            if (isBinaryEvent || packet.type === PacketType.BINARY_ACK) {\n                packet.type = isBinaryEvent ? PacketType.EVENT : PacketType.ACK;\n                // binary packet's json\n                this.reconstructor = new BinaryReconstructor(packet);\n                // no attachments, labeled binary but no binary data to follow\n                if (packet.attachments === 0) {\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n            else {\n                // non-binary full packet\n                super.emitReserved(\"decoded\", packet);\n            }\n        }\n        else if (isBinary(obj) || obj.base64) {\n            // raw binary data\n            if (!this.reconstructor) {\n                throw new Error(\"got binary data when not reconstructing a packet\");\n            }\n            else {\n                packet = this.reconstructor.takeBinaryData(obj);\n                if (packet) {\n                    // received final buffer\n                    this.reconstructor = null;\n                    super.emitReserved(\"decoded\", packet);\n                }\n            }\n        }\n        else {\n            throw new Error(\"Unknown type: \" + obj);\n        }\n    }\n    /**\n     * Decode a packet String (JSON data)\n     *\n     * @param {String} str\n     * @return {Object} packet\n     */\n    decodeString(str) {\n        let i = 0;\n        // look up type\n        const p = {\n            type: Number(str.charAt(0)),\n        };\n        if (PacketType[p.type] === undefined) {\n            throw new Error(\"unknown packet type \" + p.type);\n        }\n        // look up attachments if type binary\n        if (p.type === PacketType.BINARY_EVENT ||\n            p.type === PacketType.BINARY_ACK) {\n            const start = i + 1;\n            while (str.charAt(++i) !== \"-\" && i != str.length) { }\n            const buf = str.substring(start, i);\n            if (buf != Number(buf) || str.charAt(i) !== \"-\") {\n                throw new Error(\"Illegal attachments\");\n            }\n            p.attachments = Number(buf);\n        }\n        // look up namespace (if any)\n        if (\"/\" === str.charAt(i + 1)) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (\",\" === c)\n                    break;\n                if (i === str.length)\n                    break;\n            }\n            p.nsp = str.substring(start, i);\n        }\n        else {\n            p.nsp = \"/\";\n        }\n        // look up id\n        const next = str.charAt(i + 1);\n        if (\"\" !== next && Number(next) == next) {\n            const start = i + 1;\n            while (++i) {\n                const c = str.charAt(i);\n                if (null == c || Number(c) != c) {\n                    --i;\n                    break;\n                }\n                if (i === str.length)\n                    break;\n            }\n            p.id = Number(str.substring(start, i + 1));\n        }\n        // look up json data\n        if (str.charAt(++i)) {\n            const payload = this.tryParse(str.substr(i));\n            if (Decoder.isPayloadValid(p.type, payload)) {\n                p.data = payload;\n            }\n            else {\n                throw new Error(\"invalid payload\");\n            }\n        }\n        return p;\n    }\n    tryParse(str) {\n        try {\n            return JSON.parse(str, this.reviver);\n        }\n        catch (e) {\n            return false;\n        }\n    }\n    static isPayloadValid(type, payload) {\n        switch (type) {\n            case PacketType.CONNECT:\n                return isObject(payload);\n            case PacketType.DISCONNECT:\n                return payload === undefined;\n            case PacketType.CONNECT_ERROR:\n                return typeof payload === \"string\" || isObject(payload);\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                return (Array.isArray(payload) &&\n                    (typeof payload[0] === \"number\" ||\n                        (typeof payload[0] === \"string\" &&\n                            RESERVED_EVENTS.indexOf(payload[0]) === -1)));\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                return Array.isArray(payload);\n        }\n    }\n    /**\n     * Deallocates a parser's resources\n     */\n    destroy() {\n        if (this.reconstructor) {\n            this.reconstructor.finishedReconstruction();\n            this.reconstructor = null;\n        }\n    }\n}\n/**\n * A manager of a binary event's 'buffer sequence'. Should\n * be constructed whenever a packet of type BINARY_EVENT is\n * decoded.\n *\n * @param {Object} packet\n * @return {BinaryReconstructor} initialized reconstructor\n */\nclass BinaryReconstructor {\n    constructor(packet) {\n        this.packet = packet;\n        this.buffers = [];\n        this.reconPack = packet;\n    }\n    /**\n     * Method to be called when binary data received from connection\n     * after a BINARY_EVENT packet.\n     *\n     * @param {Buffer | ArrayBuffer} binData - the raw binary data received\n     * @return {null | Object} returns null if more binary data is expected or\n     *   a reconstructed packet object if all buffers have been received.\n     */\n    takeBinaryData(binData) {\n        this.buffers.push(binData);\n        if (this.buffers.length === this.reconPack.attachments) {\n            // done with buffer list\n            const packet = reconstructPacket(this.reconPack, this.buffers);\n            this.finishedReconstruction();\n            return packet;\n        }\n        return null;\n    }\n    /**\n     * Cleans up binary packet reconstruction variables.\n     */\n    finishedReconstruction() {\n        this.reconPack = null;\n        this.buffers = [];\n    }\n}\nfunction isNamespaceValid(nsp) {\n    return typeof nsp === \"string\";\n}\n// see https://caniuse.com/mdn-javascript_builtins_number_isinteger\nconst isInteger = Number.isInteger ||\n    function (value) {\n        return (typeof value === \"number\" &&\n            isFinite(value) &&\n            Math.floor(value) === value);\n    };\nfunction isAckIdValid(id) {\n    return id === undefined || isInteger(id);\n}\n// see https://stackoverflow.com/questions/8511281/check-if-a-value-is-an-object-in-javascript\nfunction isObject(value) {\n    return Object.prototype.toString.call(value) === \"[object Object]\";\n}\nfunction isDataValid(type, payload) {\n    switch (type) {\n        case PacketType.CONNECT:\n            return payload === undefined || isObject(payload);\n        case PacketType.DISCONNECT:\n            return payload === undefined;\n        case PacketType.EVENT:\n            return (Array.isArray(payload) &&\n                (typeof payload[0] === \"number\" ||\n                    (typeof payload[0] === \"string\" &&\n                        RESERVED_EVENTS.indexOf(payload[0]) === -1)));\n        case PacketType.ACK:\n            return Array.isArray(payload);\n        case PacketType.CONNECT_ERROR:\n            return typeof payload === \"string\" || isObject(payload);\n        default:\n            return false;\n    }\n}\nexport function isPacketValid(packet) {\n    return (isNamespaceValid(packet.nsp) &&\n        isAckIdValid(packet.id) &&\n        isDataValid(packet.type, packet.data));\n}\n", "export function on(obj, ev, fn) {\n    obj.on(ev, fn);\n    return function subDestroy() {\n        obj.off(ev, fn);\n    };\n}\n", "import { PacketType } from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\n/**\n * Internal events.\n * These events can't be emitted by the user.\n */\nconst RESERVED_EVENTS = Object.freeze({\n    connect: 1,\n    connect_error: 1,\n    disconnect: 1,\n    disconnecting: 1,\n    // EventEmitter reserved events: https://nodejs.org/api/events.html#events_event_newlistener\n    newListener: 1,\n    removeListener: 1,\n});\n/**\n * A Socket is the fundamental class for interacting with the server.\n *\n * A Socket belongs to a certain Namespace (by default /) and uses an underlying {@link Manager} to communicate.\n *\n * @example\n * const socket = io();\n *\n * socket.on(\"connect\", () => {\n *   console.log(\"connected\");\n * });\n *\n * // send an event to the server\n * socket.emit(\"foo\", \"bar\");\n *\n * socket.on(\"foobar\", () => {\n *   // an event was received from the server\n * });\n *\n * // upon disconnection\n * socket.on(\"disconnect\", (reason) => {\n *   console.log(`disconnected due to ${reason}`);\n * });\n */\nexport class Socket extends Emitter {\n    /**\n     * `Socket` constructor.\n     */\n    constructor(io, nsp, opts) {\n        super();\n        /**\n         * Whether the socket is currently connected to the server.\n         *\n         * @example\n         * const socket = io();\n         *\n         * socket.on(\"connect\", () => {\n         *   console.log(socket.connected); // true\n         * });\n         *\n         * socket.on(\"disconnect\", () => {\n         *   console.log(socket.connected); // false\n         * });\n         */\n        this.connected = false;\n        /**\n         * Whether the connection state was recovered after a temporary disconnection. In that case, any missed packets will\n         * be transmitted by the server.\n         */\n        this.recovered = false;\n        /**\n         * Buffer for packets received before the CONNECT packet\n         */\n        this.receiveBuffer = [];\n        /**\n         * Buffer for packets that will be sent once the socket is connected\n         */\n        this.sendBuffer = [];\n        /**\n         * The queue of packets to be sent with retry in case of failure.\n         *\n         * Packets are sent one by one, each waiting for the server acknowledgement, in order to guarantee the delivery order.\n         * @private\n         */\n        this._queue = [];\n        /**\n         * A sequence to generate the ID of the {@link QueuedPacket}.\n         * @private\n         */\n        this._queueSeq = 0;\n        this.ids = 0;\n        /**\n         * A map containing acknowledgement handlers.\n         *\n         * The `withError` attribute is used to differentiate handlers that accept an error as first argument:\n         *\n         * - `socket.emit(\"test\", (err, value) => { ... })` with `ackTimeout` option\n         * - `socket.timeout(5000).emit(\"test\", (err, value) => { ... })`\n         * - `const value = await socket.emitWithAck(\"test\")`\n         *\n         * From those that don't:\n         *\n         * - `socket.emit(\"test\", (value) => { ... });`\n         *\n         * In the first case, the handlers will be called with an error when:\n         *\n         * - the timeout is reached\n         * - the socket gets disconnected\n         *\n         * In the second case, the handlers will be simply discarded upon disconnection, since the client will never receive\n         * an acknowledgement from the server.\n         *\n         * @private\n         */\n        this.acks = {};\n        this.flags = {};\n        this.io = io;\n        this.nsp = nsp;\n        if (opts && opts.auth) {\n            this.auth = opts.auth;\n        }\n        this._opts = Object.assign({}, opts);\n        if (this.io._autoConnect)\n            this.open();\n    }\n    /**\n     * Whether the socket is currently disconnected\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"connect\", () => {\n     *   console.log(socket.disconnected); // false\n     * });\n     *\n     * socket.on(\"disconnect\", () => {\n     *   console.log(socket.disconnected); // true\n     * });\n     */\n    get disconnected() {\n        return !this.connected;\n    }\n    /**\n     * Subscribe to open, close and packet events\n     *\n     * @private\n     */\n    subEvents() {\n        if (this.subs)\n            return;\n        const io = this.io;\n        this.subs = [\n            on(io, \"open\", this.onopen.bind(this)),\n            on(io, \"packet\", this.onpacket.bind(this)),\n            on(io, \"error\", this.onerror.bind(this)),\n            on(io, \"close\", this.onclose.bind(this)),\n        ];\n    }\n    /**\n     * Whether the Socket will try to reconnect when its Manager connects or reconnects.\n     *\n     * @example\n     * const socket = io();\n     *\n     * console.log(socket.active); // true\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   if (reason === \"io server disconnect\") {\n     *     // the disconnection was initiated by the server, you need to manually reconnect\n     *     console.log(socket.active); // false\n     *   }\n     *   // else the socket will automatically try to reconnect\n     *   console.log(socket.active); // true\n     * });\n     */\n    get active() {\n        return !!this.subs;\n    }\n    /**\n     * \"Opens\" the socket.\n     *\n     * @example\n     * const socket = io({\n     *   autoConnect: false\n     * });\n     *\n     * socket.connect();\n     */\n    connect() {\n        if (this.connected)\n            return this;\n        this.subEvents();\n        if (!this.io[\"_reconnecting\"])\n            this.io.open(); // ensure open\n        if (\"open\" === this.io._readyState)\n            this.onopen();\n        return this;\n    }\n    /**\n     * Alias for {@link connect()}.\n     */\n    open() {\n        return this.connect();\n    }\n    /**\n     * Sends a `message` event.\n     *\n     * This method mimics the WebSocket.send() method.\n     *\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/WebSocket/send\n     *\n     * @example\n     * socket.send(\"hello\");\n     *\n     * // this is equivalent to\n     * socket.emit(\"message\", \"hello\");\n     *\n     * @return self\n     */\n    send(...args) {\n        args.unshift(\"message\");\n        this.emit.apply(this, args);\n        return this;\n    }\n    /**\n     * Override `emit`.\n     * If the event is in `events`, it's emitted normally.\n     *\n     * @example\n     * socket.emit(\"hello\", \"world\");\n     *\n     * // all serializable datastructures are supported (no need to call JSON.stringify)\n     * socket.emit(\"hello\", 1, \"2\", { 3: [\"4\"], 5: Uint8Array.from([6]) });\n     *\n     * // with an acknowledgement from the server\n     * socket.emit(\"hello\", \"world\", (val) => {\n     *   // ...\n     * });\n     *\n     * @return self\n     */\n    emit(ev, ...args) {\n        var _a, _b, _c;\n        if (RESERVED_EVENTS.hasOwnProperty(ev)) {\n            throw new Error('\"' + ev.toString() + '\" is a reserved event name');\n        }\n        args.unshift(ev);\n        if (this._opts.retries && !this.flags.fromQueue && !this.flags.volatile) {\n            this._addToQueue(args);\n            return this;\n        }\n        const packet = {\n            type: PacketType.EVENT,\n            data: args,\n        };\n        packet.options = {};\n        packet.options.compress = this.flags.compress !== false;\n        // event ack callback\n        if (\"function\" === typeof args[args.length - 1]) {\n            const id = this.ids++;\n            const ack = args.pop();\n            this._registerAckCallback(id, ack);\n            packet.id = id;\n        }\n        const isTransportWritable = (_b = (_a = this.io.engine) === null || _a === void 0 ? void 0 : _a.transport) === null || _b === void 0 ? void 0 : _b.writable;\n        const isConnected = this.connected && !((_c = this.io.engine) === null || _c === void 0 ? void 0 : _c._hasPingExpired());\n        const discardPacket = this.flags.volatile && !isTransportWritable;\n        if (discardPacket) {\n        }\n        else if (isConnected) {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        }\n        else {\n            this.sendBuffer.push(packet);\n        }\n        this.flags = {};\n        return this;\n    }\n    /**\n     * @private\n     */\n    _registerAckCallback(id, ack) {\n        var _a;\n        const timeout = (_a = this.flags.timeout) !== null && _a !== void 0 ? _a : this._opts.ackTimeout;\n        if (timeout === undefined) {\n            this.acks[id] = ack;\n            return;\n        }\n        // @ts-ignore\n        const timer = this.io.setTimeoutFn(() => {\n            delete this.acks[id];\n            for (let i = 0; i < this.sendBuffer.length; i++) {\n                if (this.sendBuffer[i].id === id) {\n                    this.sendBuffer.splice(i, 1);\n                }\n            }\n            ack.call(this, new Error(\"operation has timed out\"));\n        }, timeout);\n        const fn = (...args) => {\n            // @ts-ignore\n            this.io.clearTimeoutFn(timer);\n            ack.apply(this, args);\n        };\n        fn.withError = true;\n        this.acks[id] = fn;\n    }\n    /**\n     * Emits an event and waits for an acknowledgement\n     *\n     * @example\n     * // without timeout\n     * const response = await socket.emitWithAck(\"hello\", \"world\");\n     *\n     * // with a specific timeout\n     * try {\n     *   const response = await socket.timeout(1000).emitWithAck(\"hello\", \"world\");\n     * } catch (err) {\n     *   // the server did not acknowledge the event in the given delay\n     * }\n     *\n     * @return a Promise that will be fulfilled when the server acknowledges the event\n     */\n    emitWithAck(ev, ...args) {\n        return new Promise((resolve, reject) => {\n            const fn = (arg1, arg2) => {\n                return arg1 ? reject(arg1) : resolve(arg2);\n            };\n            fn.withError = true;\n            args.push(fn);\n            this.emit(ev, ...args);\n        });\n    }\n    /**\n     * Add the packet to the queue.\n     * @param args\n     * @private\n     */\n    _addToQueue(args) {\n        let ack;\n        if (typeof args[args.length - 1] === \"function\") {\n            ack = args.pop();\n        }\n        const packet = {\n            id: this._queueSeq++,\n            tryCount: 0,\n            pending: false,\n            args,\n            flags: Object.assign({ fromQueue: true }, this.flags),\n        };\n        args.push((err, ...responseArgs) => {\n            if (packet !== this._queue[0]) {\n                // the packet has already been acknowledged\n                return;\n            }\n            const hasError = err !== null;\n            if (hasError) {\n                if (packet.tryCount > this._opts.retries) {\n                    this._queue.shift();\n                    if (ack) {\n                        ack(err);\n                    }\n                }\n            }\n            else {\n                this._queue.shift();\n                if (ack) {\n                    ack(null, ...responseArgs);\n                }\n            }\n            packet.pending = false;\n            return this._drainQueue();\n        });\n        this._queue.push(packet);\n        this._drainQueue();\n    }\n    /**\n     * Send the first packet of the queue, and wait for an acknowledgement from the server.\n     * @param force - whether to resend a packet that has not been acknowledged yet\n     *\n     * @private\n     */\n    _drainQueue(force = false) {\n        if (!this.connected || this._queue.length === 0) {\n            return;\n        }\n        const packet = this._queue[0];\n        if (packet.pending && !force) {\n            return;\n        }\n        packet.pending = true;\n        packet.tryCount++;\n        this.flags = packet.flags;\n        this.emit.apply(this, packet.args);\n    }\n    /**\n     * Sends a packet.\n     *\n     * @param packet\n     * @private\n     */\n    packet(packet) {\n        packet.nsp = this.nsp;\n        this.io._packet(packet);\n    }\n    /**\n     * Called upon engine `open`.\n     *\n     * @private\n     */\n    onopen() {\n        if (typeof this.auth == \"function\") {\n            this.auth((data) => {\n                this._sendConnectPacket(data);\n            });\n        }\n        else {\n            this._sendConnectPacket(this.auth);\n        }\n    }\n    /**\n     * Sends a CONNECT packet to initiate the Socket.IO session.\n     *\n     * @param data\n     * @private\n     */\n    _sendConnectPacket(data) {\n        this.packet({\n            type: PacketType.CONNECT,\n            data: this._pid\n                ? Object.assign({ pid: this._pid, offset: this._lastOffset }, data)\n                : data,\n        });\n    }\n    /**\n     * Called upon engine or manager `error`.\n     *\n     * @param err\n     * @private\n     */\n    onerror(err) {\n        if (!this.connected) {\n            this.emitReserved(\"connect_error\", err);\n        }\n    }\n    /**\n     * Called upon engine `close`.\n     *\n     * @param reason\n     * @param description\n     * @private\n     */\n    onclose(reason, description) {\n        this.connected = false;\n        delete this.id;\n        this.emitReserved(\"disconnect\", reason, description);\n        this._clearAcks();\n    }\n    /**\n     * Clears the acknowledgement handlers upon disconnection, since the client will never receive an acknowledgement from\n     * the server.\n     *\n     * @private\n     */\n    _clearAcks() {\n        Object.keys(this.acks).forEach((id) => {\n            const isBuffered = this.sendBuffer.some((packet) => String(packet.id) === id);\n            if (!isBuffered) {\n                // note: handlers that do not accept an error as first argument are ignored here\n                const ack = this.acks[id];\n                delete this.acks[id];\n                if (ack.withError) {\n                    ack.call(this, new Error(\"socket has been disconnected\"));\n                }\n            }\n        });\n    }\n    /**\n     * Called with socket packet.\n     *\n     * @param packet\n     * @private\n     */\n    onpacket(packet) {\n        const sameNamespace = packet.nsp === this.nsp;\n        if (!sameNamespace)\n            return;\n        switch (packet.type) {\n            case PacketType.CONNECT:\n                if (packet.data && packet.data.sid) {\n                    this.onconnect(packet.data.sid, packet.data.pid);\n                }\n                else {\n                    this.emitReserved(\"connect_error\", new Error(\"It seems you are trying to reach a Socket.IO server in v2.x with a v3.x client, but they are not compatible (more information here: https://socket.io/docs/v3/migrating-from-2-x-to-3-0/)\"));\n                }\n                break;\n            case PacketType.EVENT:\n            case PacketType.BINARY_EVENT:\n                this.onevent(packet);\n                break;\n            case PacketType.ACK:\n            case PacketType.BINARY_ACK:\n                this.onack(packet);\n                break;\n            case PacketType.DISCONNECT:\n                this.ondisconnect();\n                break;\n            case PacketType.CONNECT_ERROR:\n                this.destroy();\n                const err = new Error(packet.data.message);\n                // @ts-ignore\n                err.data = packet.data.data;\n                this.emitReserved(\"connect_error\", err);\n                break;\n        }\n    }\n    /**\n     * Called upon a server event.\n     *\n     * @param packet\n     * @private\n     */\n    onevent(packet) {\n        const args = packet.data || [];\n        if (null != packet.id) {\n            args.push(this.ack(packet.id));\n        }\n        if (this.connected) {\n            this.emitEvent(args);\n        }\n        else {\n            this.receiveBuffer.push(Object.freeze(args));\n        }\n    }\n    emitEvent(args) {\n        if (this._anyListeners && this._anyListeners.length) {\n            const listeners = this._anyListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, args);\n            }\n        }\n        super.emit.apply(this, args);\n        if (this._pid && args.length && typeof args[args.length - 1] === \"string\") {\n            this._lastOffset = args[args.length - 1];\n        }\n    }\n    /**\n     * Produces an ack callback to emit with an event.\n     *\n     * @private\n     */\n    ack(id) {\n        const self = this;\n        let sent = false;\n        return function (...args) {\n            // prevent double callbacks\n            if (sent)\n                return;\n            sent = true;\n            self.packet({\n                type: PacketType.ACK,\n                id: id,\n                data: args,\n            });\n        };\n    }\n    /**\n     * Called upon a server acknowledgement.\n     *\n     * @param packet\n     * @private\n     */\n    onack(packet) {\n        const ack = this.acks[packet.id];\n        if (typeof ack !== \"function\") {\n            return;\n        }\n        delete this.acks[packet.id];\n        // @ts-ignore FIXME ack is incorrectly inferred as 'never'\n        if (ack.withError) {\n            packet.data.unshift(null);\n        }\n        // @ts-ignore\n        ack.apply(this, packet.data);\n    }\n    /**\n     * Called upon server connect.\n     *\n     * @private\n     */\n    onconnect(id, pid) {\n        this.id = id;\n        this.recovered = pid && this._pid === pid;\n        this._pid = pid; // defined only if connection state recovery is enabled\n        this.connected = true;\n        this.emitBuffered();\n        this.emitReserved(\"connect\");\n        this._drainQueue(true);\n    }\n    /**\n     * Emit buffered events (received and emitted).\n     *\n     * @private\n     */\n    emitBuffered() {\n        this.receiveBuffer.forEach((args) => this.emitEvent(args));\n        this.receiveBuffer = [];\n        this.sendBuffer.forEach((packet) => {\n            this.notifyOutgoingListeners(packet);\n            this.packet(packet);\n        });\n        this.sendBuffer = [];\n    }\n    /**\n     * Called upon server disconnect.\n     *\n     * @private\n     */\n    ondisconnect() {\n        this.destroy();\n        this.onclose(\"io server disconnect\");\n    }\n    /**\n     * Called upon forced client/server side disconnections,\n     * this method ensures the manager stops tracking us and\n     * that reconnections don't get triggered for this.\n     *\n     * @private\n     */\n    destroy() {\n        if (this.subs) {\n            // clean subscriptions to avoid reconnections\n            this.subs.forEach((subDestroy) => subDestroy());\n            this.subs = undefined;\n        }\n        this.io[\"_destroy\"](this);\n    }\n    /**\n     * Disconnects the socket manually. In that case, the socket will not try to reconnect.\n     *\n     * If this is the last active Socket instance of the {@link Manager}, the low-level connection will be closed.\n     *\n     * @example\n     * const socket = io();\n     *\n     * socket.on(\"disconnect\", (reason) => {\n     *   // console.log(reason); prints \"io client disconnect\"\n     * });\n     *\n     * socket.disconnect();\n     *\n     * @return self\n     */\n    disconnect() {\n        if (this.connected) {\n            this.packet({ type: PacketType.DISCONNECT });\n        }\n        // remove socket from pool\n        this.destroy();\n        if (this.connected) {\n            // fire events\n            this.onclose(\"io client disconnect\");\n        }\n        return this;\n    }\n    /**\n     * Alias for {@link disconnect()}.\n     *\n     * @return self\n     */\n    close() {\n        return this.disconnect();\n    }\n    /**\n     * Sets the compress flag.\n     *\n     * @example\n     * socket.compress(false).emit(\"hello\");\n     *\n     * @param compress - if `true`, compresses the sending data\n     * @return self\n     */\n    compress(compress) {\n        this.flags.compress = compress;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the event message will be dropped when this socket is not\n     * ready to send messages.\n     *\n     * @example\n     * socket.volatile.emit(\"hello\"); // the server may or may not receive it\n     *\n     * @returns self\n     */\n    get volatile() {\n        this.flags.volatile = true;\n        return this;\n    }\n    /**\n     * Sets a modifier for a subsequent event emission that the callback will be called with an error when the\n     * given number of milliseconds have elapsed without an acknowledgement from the server:\n     *\n     * @example\n     * socket.timeout(5000).emit(\"my-event\", (err) => {\n     *   if (err) {\n     *     // the server did not acknowledge the event in the given delay\n     *   }\n     * });\n     *\n     * @returns self\n     */\n    timeout(timeout) {\n        this.flags.timeout = timeout;\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * @example\n     * socket.onAny((event, ...args) => {\n     *   console.log(`got ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * @example\n     * socket.prependAny((event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAny(listener) {\n        this._anyListeners = this._anyListeners || [];\n        this._anyListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`got event ${event}`);\n     * }\n     *\n     * socket.onAny(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAny(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAny();\n     *\n     * @param listener\n     */\n    offAny(listener) {\n        if (!this._anyListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAny() {\n        return this._anyListeners || [];\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.onAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    onAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.push(listener);\n        return this;\n    }\n    /**\n     * Adds a listener that will be fired when any event is emitted. The event name is passed as the first argument to the\n     * callback. The listener is added to the beginning of the listeners array.\n     *\n     * Note: acknowledgements sent to the server are not included.\n     *\n     * @example\n     * socket.prependAnyOutgoing((event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * });\n     *\n     * @param listener\n     */\n    prependAnyOutgoing(listener) {\n        this._anyOutgoingListeners = this._anyOutgoingListeners || [];\n        this._anyOutgoingListeners.unshift(listener);\n        return this;\n    }\n    /**\n     * Removes the listener that will be fired when any event is emitted.\n     *\n     * @example\n     * const catchAllListener = (event, ...args) => {\n     *   console.log(`sent event ${event}`);\n     * }\n     *\n     * socket.onAnyOutgoing(catchAllListener);\n     *\n     * // remove a specific listener\n     * socket.offAnyOutgoing(catchAllListener);\n     *\n     * // or remove all listeners\n     * socket.offAnyOutgoing();\n     *\n     * @param [listener] - the catch-all listener (optional)\n     */\n    offAnyOutgoing(listener) {\n        if (!this._anyOutgoingListeners) {\n            return this;\n        }\n        if (listener) {\n            const listeners = this._anyOutgoingListeners;\n            for (let i = 0; i < listeners.length; i++) {\n                if (listener === listeners[i]) {\n                    listeners.splice(i, 1);\n                    return this;\n                }\n            }\n        }\n        else {\n            this._anyOutgoingListeners = [];\n        }\n        return this;\n    }\n    /**\n     * Returns an array of listeners that are listening for any event that is specified. This array can be manipulated,\n     * e.g. to remove listeners.\n     */\n    listenersAnyOutgoing() {\n        return this._anyOutgoingListeners || [];\n    }\n    /**\n     * Notify the listeners for each packet sent\n     *\n     * @param packet\n     *\n     * @private\n     */\n    notifyOutgoingListeners(packet) {\n        if (this._anyOutgoingListeners && this._anyOutgoingListeners.length) {\n            const listeners = this._anyOutgoingListeners.slice();\n            for (const listener of listeners) {\n                listener.apply(this, packet.data);\n            }\n        }\n    }\n}\n", "/**\n * Initialize backoff timer with `opts`.\n *\n * - `min` initial timeout in milliseconds [100]\n * - `max` max timeout [10000]\n * - `jitter` [0]\n * - `factor` [2]\n *\n * @param {Object} opts\n * @api public\n */\nexport function Backoff(opts) {\n    opts = opts || {};\n    this.ms = opts.min || 100;\n    this.max = opts.max || 10000;\n    this.factor = opts.factor || 2;\n    this.jitter = opts.jitter > 0 && opts.jitter <= 1 ? opts.jitter : 0;\n    this.attempts = 0;\n}\n/**\n * Return the backoff duration.\n *\n * @return {Number}\n * @api public\n */\nBackoff.prototype.duration = function () {\n    var ms = this.ms * Math.pow(this.factor, this.attempts++);\n    if (this.jitter) {\n        var rand = Math.random();\n        var deviation = Math.floor(rand * this.jitter * ms);\n        ms = (Math.floor(rand * 10) & 1) == 0 ? ms - deviation : ms + deviation;\n    }\n    return Math.min(ms, this.max) | 0;\n};\n/**\n * Reset the number of attempts.\n *\n * @api public\n */\nBackoff.prototype.reset = function () {\n    this.attempts = 0;\n};\n/**\n * Set the minimum duration\n *\n * @api public\n */\nBackoff.prototype.setMin = function (min) {\n    this.ms = min;\n};\n/**\n * Set the maximum duration\n *\n * @api public\n */\nBackoff.prototype.setMax = function (max) {\n    this.max = max;\n};\n/**\n * Set the jitter\n *\n * @api public\n */\nBackoff.prototype.setJitter = function (jitter) {\n    this.jitter = jitter;\n};\n", "import { Socket as Engine, installTimerFunctions, nextTick, } from \"engine.io-client\";\nimport { Socket } from \"./socket.js\";\nimport * as parser from \"socket.io-parser\";\nimport { on } from \"./on.js\";\nimport { Backoff } from \"./contrib/backo2.js\";\nimport { Emitter, } from \"@socket.io/component-emitter\";\nexport class Manager extends Emitter {\n    constructor(uri, opts) {\n        var _a;\n        super();\n        this.nsps = {};\n        this.subs = [];\n        if (uri && \"object\" === typeof uri) {\n            opts = uri;\n            uri = undefined;\n        }\n        opts = opts || {};\n        opts.path = opts.path || \"/socket.io\";\n        this.opts = opts;\n        installTimerFunctions(this, opts);\n        this.reconnection(opts.reconnection !== false);\n        this.reconnectionAttempts(opts.reconnectionAttempts || Infinity);\n        this.reconnectionDelay(opts.reconnectionDelay || 1000);\n        this.reconnectionDelayMax(opts.reconnectionDelayMax || 5000);\n        this.randomizationFactor((_a = opts.randomizationFactor) !== null && _a !== void 0 ? _a : 0.5);\n        this.backoff = new Backoff({\n            min: this.reconnectionDelay(),\n            max: this.reconnectionDelayMax(),\n            jitter: this.randomizationFactor(),\n        });\n        this.timeout(null == opts.timeout ? 20000 : opts.timeout);\n        this._readyState = \"closed\";\n        this.uri = uri;\n        const _parser = opts.parser || parser;\n        this.encoder = new _parser.Encoder();\n        this.decoder = new _parser.Decoder();\n        this._autoConnect = opts.autoConnect !== false;\n        if (this._autoConnect)\n            this.open();\n    }\n    reconnection(v) {\n        if (!arguments.length)\n            return this._reconnection;\n        this._reconnection = !!v;\n        if (!v) {\n            this.skipReconnect = true;\n        }\n        return this;\n    }\n    reconnectionAttempts(v) {\n        if (v === undefined)\n            return this._reconnectionAttempts;\n        this._reconnectionAttempts = v;\n        return this;\n    }\n    reconnectionDelay(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelay;\n        this._reconnectionDelay = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMin(v);\n        return this;\n    }\n    randomizationFactor(v) {\n        var _a;\n        if (v === undefined)\n            return this._randomizationFactor;\n        this._randomizationFactor = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setJitter(v);\n        return this;\n    }\n    reconnectionDelayMax(v) {\n        var _a;\n        if (v === undefined)\n            return this._reconnectionDelayMax;\n        this._reconnectionDelayMax = v;\n        (_a = this.backoff) === null || _a === void 0 ? void 0 : _a.setMax(v);\n        return this;\n    }\n    timeout(v) {\n        if (!arguments.length)\n            return this._timeout;\n        this._timeout = v;\n        return this;\n    }\n    /**\n     * Starts trying to reconnect if reconnection is enabled and we have not\n     * started reconnecting yet\n     *\n     * @private\n     */\n    maybeReconnectOnOpen() {\n        // Only try to reconnect if it's the first time we're connecting\n        if (!this._reconnecting &&\n            this._reconnection &&\n            this.backoff.attempts === 0) {\n            // keeps reconnection from firing twice for the same reconnection loop\n            this.reconnect();\n        }\n    }\n    /**\n     * Sets the current transport `socket`.\n     *\n     * @param {Function} fn - optional, callback\n     * @return self\n     * @public\n     */\n    open(fn) {\n        if (~this._readyState.indexOf(\"open\"))\n            return this;\n        this.engine = new Engine(this.uri, this.opts);\n        const socket = this.engine;\n        const self = this;\n        this._readyState = \"opening\";\n        this.skipReconnect = false;\n        // emit `open`\n        const openSubDestroy = on(socket, \"open\", function () {\n            self.onopen();\n            fn && fn();\n        });\n        const onError = (err) => {\n            this.cleanup();\n            this._readyState = \"closed\";\n            this.emitReserved(\"error\", err);\n            if (fn) {\n                fn(err);\n            }\n            else {\n                // Only do this if there is no fn to handle the error\n                this.maybeReconnectOnOpen();\n            }\n        };\n        // emit `error`\n        const errorSub = on(socket, \"error\", onError);\n        if (false !== this._timeout) {\n            const timeout = this._timeout;\n            // set timer\n            const timer = this.setTimeoutFn(() => {\n                openSubDestroy();\n                onError(new Error(\"timeout\"));\n                socket.close();\n            }, timeout);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n        this.subs.push(openSubDestroy);\n        this.subs.push(errorSub);\n        return this;\n    }\n    /**\n     * Alias for open()\n     *\n     * @return self\n     * @public\n     */\n    connect(fn) {\n        return this.open(fn);\n    }\n    /**\n     * Called upon transport open.\n     *\n     * @private\n     */\n    onopen() {\n        // clear old subs\n        this.cleanup();\n        // mark as open\n        this._readyState = \"open\";\n        this.emitReserved(\"open\");\n        // add new subs\n        const socket = this.engine;\n        this.subs.push(on(socket, \"ping\", this.onping.bind(this)), on(socket, \"data\", this.ondata.bind(this)), on(socket, \"error\", this.onerror.bind(this)), on(socket, \"close\", this.onclose.bind(this)), \n        // @ts-ignore\n        on(this.decoder, \"decoded\", this.ondecoded.bind(this)));\n    }\n    /**\n     * Called upon a ping.\n     *\n     * @private\n     */\n    onping() {\n        this.emitReserved(\"ping\");\n    }\n    /**\n     * Called with data.\n     *\n     * @private\n     */\n    ondata(data) {\n        try {\n            this.decoder.add(data);\n        }\n        catch (e) {\n            this.onclose(\"parse error\", e);\n        }\n    }\n    /**\n     * Called when parser fully decodes a packet.\n     *\n     * @private\n     */\n    ondecoded(packet) {\n        // the nextTick call prevents an exception in a user-provided event listener from triggering a disconnection due to a \"parse error\"\n        nextTick(() => {\n            this.emitReserved(\"packet\", packet);\n        }, this.setTimeoutFn);\n    }\n    /**\n     * Called upon socket error.\n     *\n     * @private\n     */\n    onerror(err) {\n        this.emitReserved(\"error\", err);\n    }\n    /**\n     * Creates a new socket for the given `nsp`.\n     *\n     * @return {Socket}\n     * @public\n     */\n    socket(nsp, opts) {\n        let socket = this.nsps[nsp];\n        if (!socket) {\n            socket = new Socket(this, nsp, opts);\n            this.nsps[nsp] = socket;\n        }\n        else if (this._autoConnect && !socket.active) {\n            socket.connect();\n        }\n        return socket;\n    }\n    /**\n     * Called upon a socket close.\n     *\n     * @param socket\n     * @private\n     */\n    _destroy(socket) {\n        const nsps = Object.keys(this.nsps);\n        for (const nsp of nsps) {\n            const socket = this.nsps[nsp];\n            if (socket.active) {\n                return;\n            }\n        }\n        this._close();\n    }\n    /**\n     * Writes a packet.\n     *\n     * @param packet\n     * @private\n     */\n    _packet(packet) {\n        const encodedPackets = this.encoder.encode(packet);\n        for (let i = 0; i < encodedPackets.length; i++) {\n            this.engine.write(encodedPackets[i], packet.options);\n        }\n    }\n    /**\n     * Clean up transport subscriptions and packet buffer.\n     *\n     * @private\n     */\n    cleanup() {\n        this.subs.forEach((subDestroy) => subDestroy());\n        this.subs.length = 0;\n        this.decoder.destroy();\n    }\n    /**\n     * Close the current socket.\n     *\n     * @private\n     */\n    _close() {\n        this.skipReconnect = true;\n        this._reconnecting = false;\n        this.onclose(\"forced close\");\n    }\n    /**\n     * Alias for close()\n     *\n     * @private\n     */\n    disconnect() {\n        return this._close();\n    }\n    /**\n     * Called when:\n     *\n     * - the low-level engine is closed\n     * - the parser encountered a badly formatted packet\n     * - all sockets are disconnected\n     *\n     * @private\n     */\n    onclose(reason, description) {\n        var _a;\n        this.cleanup();\n        (_a = this.engine) === null || _a === void 0 ? void 0 : _a.close();\n        this.backoff.reset();\n        this._readyState = \"closed\";\n        this.emitReserved(\"close\", reason, description);\n        if (this._reconnection && !this.skipReconnect) {\n            this.reconnect();\n        }\n    }\n    /**\n     * Attempt a reconnection.\n     *\n     * @private\n     */\n    reconnect() {\n        if (this._reconnecting || this.skipReconnect)\n            return this;\n        const self = this;\n        if (this.backoff.attempts >= this._reconnectionAttempts) {\n            this.backoff.reset();\n            this.emitReserved(\"reconnect_failed\");\n            this._reconnecting = false;\n        }\n        else {\n            const delay = this.backoff.duration();\n            this._reconnecting = true;\n            const timer = this.setTimeoutFn(() => {\n                if (self.skipReconnect)\n                    return;\n                this.emitReserved(\"reconnect_attempt\", self.backoff.attempts);\n                // check again for the case socket closed in above events\n                if (self.skipReconnect)\n                    return;\n                self.open((err) => {\n                    if (err) {\n                        self._reconnecting = false;\n                        self.reconnect();\n                        this.emitReserved(\"reconnect_error\", err);\n                    }\n                    else {\n                        self.onreconnect();\n                    }\n                });\n            }, delay);\n            if (this.opts.autoUnref) {\n                timer.unref();\n            }\n            this.subs.push(() => {\n                this.clearTimeoutFn(timer);\n            });\n        }\n    }\n    /**\n     * Called upon successful reconnect.\n     *\n     * @private\n     */\n    onreconnect() {\n        const attempt = this.backoff.attempts;\n        this._reconnecting = false;\n        this.backoff.reset();\n        this.emitReserved(\"reconnect\", attempt);\n    }\n}\n", "import { url } from \"./url.js\";\nimport { Manager } from \"./manager.js\";\nimport { Socket } from \"./socket.js\";\n/**\n * Managers cache.\n */\nconst cache = {};\nfunction lookup(uri, opts) {\n    if (typeof uri === \"object\") {\n        opts = uri;\n        uri = undefined;\n    }\n    opts = opts || {};\n    const parsed = url(uri, opts.path || \"/socket.io\");\n    const source = parsed.source;\n    const id = parsed.id;\n    const path = parsed.path;\n    const sameNamespace = cache[id] && path in cache[id][\"nsps\"];\n    const newConnection = opts.forceNew ||\n        opts[\"force new connection\"] ||\n        false === opts.multiplex ||\n        sameNamespace;\n    let io;\n    if (newConnection) {\n        io = new Manager(source, opts);\n    }\n    else {\n        if (!cache[id]) {\n            cache[id] = new Manager(source, opts);\n        }\n        io = cache[id];\n    }\n    if (parsed.query && !opts.query) {\n        opts.query = parsed.queryKey;\n    }\n    return io.socket(parsed.path, opts);\n}\n// so that \"lookup\" can be used both as a function (e.g. `io(...)`) and as a\n// namespace (e.g. `io.connect(...)`), for backward compatibility\nObject.assign(lookup, {\n    Manager,\n    Socket,\n    io: lookup,\n    connect: lookup,\n});\n/**\n * Protocol version.\n *\n * @public\n */\nexport { protocol } from \"socket.io-parser\";\n/**\n * Expose constructors for standalone build.\n *\n * @public\n */\nexport { Manager, Socket, lookup as io, lookup as connect, lookup as default, };\nexport { Fetch, NodeXHR, XHR, NodeWebSocket, WebSocket, WebTransport, } from \"engine.io-client\";\n", "import { parse } from \"engine.io-client\";\n/**\n * URL parser.\n *\n * @param uri - url\n * @param path - the request path of the connection\n * @param loc - An object meant to mimic window.location.\n *        Defaults to window.location.\n * @public\n */\nexport function url(uri, path = \"\", loc) {\n    let obj = uri;\n    // default to window.location\n    loc = loc || (typeof location !== \"undefined\" && location);\n    if (null == uri)\n        uri = loc.protocol + \"//\" + loc.host;\n    // relative path support\n    if (typeof uri === \"string\") {\n        if (\"/\" === uri.charAt(0)) {\n            if (\"/\" === uri.charAt(1)) {\n                uri = loc.protocol + uri;\n            }\n            else {\n                uri = loc.host + uri;\n            }\n        }\n        if (!/^(https?|wss?):\\/\\//.test(uri)) {\n            if (\"undefined\" !== typeof loc) {\n                uri = loc.protocol + \"//\" + uri;\n            }\n            else {\n                uri = \"https://\" + uri;\n            }\n        }\n        // parse\n        obj = parse(uri);\n    }\n    // make sure we treat `localhost:80` and `localhost` equally\n    if (!obj.port) {\n        if (/^(http|ws)$/.test(obj.protocol)) {\n            obj.port = \"80\";\n        }\n        else if (/^(http|ws)s$/.test(obj.protocol)) {\n            obj.port = \"443\";\n        }\n    }\n    obj.path = obj.path || \"/\";\n    const ipv6 = obj.host.indexOf(\":\") !== -1;\n    const host = ipv6 ? \"[\" + obj.host + \"]\" : obj.host;\n    // define unique id\n    obj.id = obj.protocol + \"://\" + host + \":\" + obj.port + path;\n    // define href\n    obj.href =\n        obj.protocol +\n            \"://\" +\n            host +\n            (loc && loc.port === obj.port ? \"\" : \":\" + obj.port);\n    return obj;\n}\n"], "names": ["PACKET_TYPES", "Object", "create", "PACKET_TYPES_REVERSE", "keys", "for<PERSON>ach", "key", "ERROR_PACKET", "type", "data", "withNativeBlob", "Blob", "prototype", "toString", "call", "with<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "obj", "buffer", "encodePacket", "supportsBinary", "callback", "encodeBlobAsBase64", "fileReader", "FileReader", "onload", "content", "result", "split", "readAsDataURL", "toArray", "Uint8Array", "byteOffset", "byteLength", "TEXT_ENCODER", "chars", "lookup", "i", "charCodeAt", "decodePacket", "encodedPacket", "binaryType", "mapBinary", "char<PERSON>t", "decodeBase64Packet", "substring", "length", "decoded", "base64", "encoded1", "encoded2", "encoded3", "encoded4", "bufferLength", "len", "p", "arraybuffer", "bytes", "decode", "SEPARATOR", "String", "fromCharCode", "createPacketEncoderStream", "TransformStream", "transform", "packet", "controller", "arrayBuffer", "then", "encoded", "TextEncoder", "encode", "encodePacketToBinary", "payloadLength", "header", "DataView", "setUint8", "view", "setUint16", "setBigUint64", "BigInt", "enqueue", "TEXT_DECODER", "totalLength", "chunks", "reduce", "acc", "chunk", "concatChunks", "size", "shift", "j", "slice", "Emitter", "mixin", "on", "addEventListener", "event", "fn", "this", "_callbacks", "push", "once", "off", "apply", "arguments", "removeListener", "removeAllListeners", "removeEventListener", "cb", "callbacks", "splice", "emit", "args", "Array", "emit<PERSON><PERSON><PERSON><PERSON>", "listeners", "hasListeners", "nextTick", "Promise", "resolve", "setTimeoutFn", "globalThisShim", "self", "window", "Function", "pick", "attr", "k", "hasOwnProperty", "NATIVE_SET_TIMEOUT", "globalThis", "setTimeout", "NATIVE_CLEAR_TIMEOUT", "clearTimeout", "installTimerFunctions", "opts", "useNativeTimers", "bind", "clearTimeoutFn", "randomString", "Date", "now", "Math", "random", "TransportError", "Error", "constructor", "reason", "description", "context", "super", "Transport", "writable", "query", "socket", "forceBase64", "onError", "open", "readyState", "doOpen", "close", "doClose", "onClose", "send", "packets", "write", "onOpen", "onData", "onPacket", "details", "pause", "onPause", "createUri", "schema", "_hostname", "_port", "path", "_query", "hostname", "indexOf", "port", "secure", "Number", "<PERSON><PERSON><PERSON><PERSON>", "str", "encodeURIComponent", "Polling", "_polling", "name", "_poll", "total", "doPoll", "encodedPayload", "encodedPackets", "decodedPacket", "decodePayload", "count", "join", "encodePayload", "doWrite", "uri", "timestampRequests", "timestampParam", "sid", "b64", "value", "XMLHttpRequest", "err", "hasCORS", "empty", "BaseXHR", "location", "isSSL", "protocol", "xd", "req", "request", "method", "xhrStatus", "pollXhr", "Request", "createRequest", "_opts", "_method", "_uri", "_data", "undefined", "_create", "_a", "xdomain", "xhr", "_xhr", "extraHeaders", "setDisableHeaderCheck", "setRequestHeader", "e", "cookieJar", "addCookies", "withCredentials", "requestTimeout", "timeout", "onreadystatechange", "parseCookies", "getResponseHeader", "status", "_onLoad", "_onError", "document", "_index", "requestsCount", "requests", "_cleanup", "fromError", "abort", "responseText", "attachEvent", "unload<PERSON><PERSON><PERSON>", "hasXHR2", "newRequest", "responseType", "XHR", "assign", "concat", "isReactNative", "navigator", "product", "toLowerCase", "BaseWS", "protocols", "headers", "ws", "createSocket", "addEventListeners", "onopen", "autoUnref", "_socket", "unref", "onclose", "closeEvent", "onmessage", "ev", "onerror", "lastPacket", "WebSocketCtor", "WebSocket", "MozWebSocket", "WS", "_packet", "WT", "_transport", "WebTransport", "transportOptions", "closed", "catch", "ready", "createBidirectionalStream", "stream", "decoderStream", "maxPayload", "TextDecoder", "state", "<PERSON><PERSON><PERSON><PERSON>", "isBinary", "headerArray", "getUint16", "n", "getUint32", "pow", "createPacketDecoderStream", "MAX_SAFE_INTEGER", "reader", "readable", "pipeThrough", "<PERSON><PERSON><PERSON><PERSON>", "encoderStream", "pipeTo", "_writer", "getWriter", "read", "done", "transports", "websocket", "webtransport", "polling", "re", "parts", "parse", "src", "b", "replace", "m", "exec", "source", "host", "authority", "ipv6uri", "pathNames", "regx", "names", "query<PERSON><PERSON>", "$0", "$1", "$2", "withEventListeners", "OFFLINE_EVENT_LISTENERS", "listener", "SocketWithoutUpgrade", "writeBuffer", "_prevBufferLen", "_pingInterval", "_pingTimeout", "_maxPayload", "_pingTimeoutTime", "Infinity", "parsed<PERSON><PERSON>", "_transportsByName", "t", "transportName", "agent", "upgrade", "rememberUpgrade", "addTrailingSlash", "rejectUnauthorized", "perMessageDeflate", "threshold", "closeOnBeforeunload", "qs", "qry", "pairs", "l", "pair", "decodeURIComponent", "_beforeunloadEventListener", "transport", "_offlineEventListener", "_onClose", "_cookieJar", "createCookieJar", "_open", "createTransport", "EIO", "id", "priorWebsocketSuccess", "setTransport", "_onDrain", "_onPacket", "flush", "onHandshake", "JSON", "_sendPacket", "_resetPingTimeout", "code", "pingInterval", "pingTimeout", "_pingTimeoutTimer", "delay", "upgrading", "_getWritablePackets", "payloadSize", "c", "utf8Length", "ceil", "_hasPingExpired", "hasExpired", "msg", "options", "compress", "cleanupAndClose", "waitForUpgrade", "tryAllTransports", "SocketWithUpgrade", "_upgrades", "_probe", "failed", "onTransportOpen", "cleanup", "freezeTransport", "error", "onTransportClose", "onupgrade", "to", "_filterUpgrades", "upgrades", "filteredUpgrades", "Socket", "o", "map", "DEFAULT_TRANSPORTS", "filter", "<PERSON>tch", "_fetch", "res", "ok", "text", "isPost", "Headers", "set", "appendCookies", "fetch", "body", "credentials", "getSetCookie", "withNativeFile", "File", "hasBinary", "toJSON", "isArray", "deconstructPacket", "buffers", "packetData", "pack", "_deconstructPacket", "attachments", "placeholder", "_placeholder", "num", "newData", "reconstructPacket", "_reconstructPacket", "RESERVED_EVENTS", "PacketType", "Decoder", "reviver", "add", "reconstructor", "decodeString", "isBinaryEvent", "BINARY_EVENT", "BINARY_ACK", "EVENT", "ACK", "BinaryReconstructor", "takeBinaryData", "start", "buf", "nsp", "next", "payload", "try<PERSON><PERSON><PERSON>", "substr", "isPayloadValid", "CONNECT", "isObject", "DISCONNECT", "CONNECT_ERROR", "destroy", "finishedReconstruction", "reconPack", "binData", "isInteger", "isFinite", "floor", "replacer", "encodeAsString", "encodeAsBinary", "stringify", "deconstruction", "unshift", "isDataValid", "freeze", "connect", "connect_error", "disconnect", "disconnecting", "newListener", "io", "connected", "recovered", "<PERSON><PERSON><PERSON><PERSON>", "send<PERSON><PERSON><PERSON>", "_queue", "_queueSeq", "ids", "acks", "flags", "auth", "_autoConnect", "disconnected", "subEvents", "subs", "onpacket", "active", "_readyState", "_b", "_c", "retries", "fromQueue", "volatile", "_addToQueue", "ack", "pop", "_registerAckCallback", "isTransportWritable", "engine", "isConnected", "notifyOutgoingListeners", "ackTimeout", "timer", "with<PERSON><PERSON><PERSON>", "emitWithAck", "reject", "arg1", "arg2", "tryCount", "pending", "responseArgs", "_drainQueue", "force", "_sendConnectPacket", "_pid", "pid", "offset", "_lastOffset", "_clearAcks", "some", "onconnect", "onevent", "onack", "ondisconnect", "message", "emitEvent", "_anyListeners", "sent", "emitBuffered", "subDestroy", "onAny", "prependAny", "offAny", "listenersAny", "onAnyOutgoing", "_anyOutgoingListeners", "prependAnyOutgoing", "offAnyOutgoing", "listenersAnyOutgoing", "Backoff", "ms", "min", "max", "factor", "jitter", "attempts", "duration", "rand", "deviation", "reset", "setMin", "setMax", "setJitter", "Manager", "nsps", "reconnection", "reconnectionAttempts", "reconnectionDelay", "reconnectionDelayMax", "randomizationFactor", "backoff", "_parser", "parser", "encoder", "Encoder", "decoder", "autoConnect", "v", "_reconnection", "skipReconnect", "_reconnectionAttempts", "_reconnectionDelay", "_randomizationFactor", "_reconnectionDelayMax", "_timeout", "maybeReconnectOnOpen", "_reconnecting", "reconnect", "Engine", "openSubDestroy", "errorSub", "onping", "ondata", "ondecoded", "_destroy", "_close", "onreconnect", "attempt", "cache", "parsed", "loc", "test", "href", "url", "sameNamespace", "forceNew", "multiplex"], "mappings": ";;;;;AAAA,MAAMA,EAAeC,OAAOC,OAAO,MACnCF,EAAmB,KAAI,IACvBA,EAAoB,MAAI,IACxBA,EAAmB,KAAI,IACvBA,EAAmB,KAAI,IACvBA,EAAsB,QAAI,IAC1BA,EAAsB,QAAI,IAC1BA,EAAmB,KAAI,IACvB,MAAMG,EAAuBF,OAAOC,OAAO,MAC3CD,OAAOG,KAAKJ,GAAcK,SAASC,IAC/BH,EAAqBH,EAAaM,IAAQA,CAAG,IAEjD,MAAMC,EAAe,CAAEC,KAAM,QAASC,KAAM,gBCXtCC,EAAiC,mBAATC,MACT,oBAATA,MACqC,6BAAzCV,OAAOW,UAAUC,SAASC,KAAKH,MACjCI,EAA+C,mBAAhBC,YAE/BC,EAAUC,GACyB,mBAAvBF,YAAYC,OACpBD,YAAYC,OAAOC,GACnBA,GAAOA,EAAIC,kBAAkBH,YAEjCI,EAAe,EAAGZ,OAAMC,QAAQY,EAAgBC,IAC9CZ,GAAkBD,aAAgBE,KAC9BU,EACOC,EAASb,GAGTc,EAAmBd,EAAMa,GAG/BP,IACJN,aAAgBO,aAAeC,EAAOR,IACnCY,EACOC,EAASb,GAGTc,EAAmB,IAAIZ,KAAK,CAACF,IAAQa,GAI7CA,EAAStB,EAAaQ,IAASC,GAAQ,KAE5Cc,EAAqB,CAACd,EAAMa,KAC9B,MAAME,EAAa,IAAIC,WAKvB,OAJAD,EAAWE,OAAS,WAChB,MAAMC,EAAUH,EAAWI,OAAOC,MAAM,KAAK,GAC7CP,EAAS,KAAOK,GAAW,IACnC,EACWH,EAAWM,cAAcrB,EAAK,EAEzC,SAASsB,EAAQtB,GACb,OAAIA,aAAgBuB,WACTvB,EAEFA,aAAgBO,YACd,IAAIgB,WAAWvB,GAGf,IAAIuB,WAAWvB,EAAKU,OAAQV,EAAKwB,WAAYxB,EAAKyB,WAEjE,CACA,IAAIC,EClDJ,MAAMC,EAAQ,mEAERC,EAA+B,oBAAfL,WAA6B,GAAK,IAAIA,WAAW,KACvE,IAAK,IAAIM,EAAI,EAAGA,EAAIF,GAAcE,IAC9BD,EAAOD,EAAMG,WAAWD,IAAMA,EAkB3B,MCrBDvB,EAA+C,mBAAhBC,YACxBwB,EAAe,CAACC,EAAeC,KACxC,GAA6B,iBAAlBD,EACP,MAAO,CACHjC,KAAM,UACNC,KAAMkC,EAAUF,EAAeC,IAGvC,MAAMlC,EAAOiC,EAAcG,OAAO,GAClC,GAAa,MAATpC,EACA,MAAO,CACHA,KAAM,UACNC,KAAMoC,EAAmBJ,EAAcK,UAAU,GAAIJ,IAI7D,OADmBvC,EAAqBK,GAIjCiC,EAAcM,OAAS,EACxB,CACEvC,KAAML,EAAqBK,GAC3BC,KAAMgC,EAAcK,UAAU,IAEhC,CACEtC,KAAML,EAAqBK,IARxBD,CASN,EAEHsC,EAAqB,CAACpC,EAAMiC,KAC9B,GAAI3B,EAAuB,CACvB,MAAMiC,EDTQ,CAACC,IACnB,IAA8DX,EAAUY,EAAUC,EAAUC,EAAUC,EAAlGC,EAA+B,IAAhBL,EAAOF,OAAeQ,EAAMN,EAAOF,OAAWS,EAAI,EACnC,MAA9BP,EAAOA,EAAOF,OAAS,KACvBO,IACkC,MAA9BL,EAAOA,EAAOF,OAAS,IACvBO,KAGR,MAAMG,EAAc,IAAIzC,YAAYsC,GAAeI,EAAQ,IAAI1B,WAAWyB,GAC1E,IAAKnB,EAAI,EAAGA,EAAIiB,EAAKjB,GAAK,EACtBY,EAAWb,EAAOY,EAAOV,WAAWD,IACpCa,EAAWd,EAAOY,EAAOV,WAAWD,EAAI,IACxCc,EAAWf,EAAOY,EAAOV,WAAWD,EAAI,IACxCe,EAAWhB,EAAOY,EAAOV,WAAWD,EAAI,IACxCoB,EAAMF,KAAQN,GAAY,EAAMC,GAAY,EAC5CO,EAAMF,MAAoB,GAAXL,IAAkB,EAAMC,GAAY,EACnDM,EAAMF,MAAoB,EAAXJ,IAAiB,EAAiB,GAAXC,EAE1C,OAAOI,CAAW,ECTEE,CAAOlD,GACvB,OAAOkC,EAAUK,EAASN,EAC7B,CAEG,MAAO,CAAEO,QAAQ,EAAMxC,OAC1B,EAECkC,EAAY,CAAClC,EAAMiC,IAEZ,SADDA,EAEIjC,aAAgBE,KAETF,EAIA,IAAIE,KAAK,CAACF,IAIjBA,aAAgBO,YAETP,EAIAA,EAAKU,OCvDtByC,EAAYC,OAAOC,aAAa,IA4B/B,SAASC,IACZ,OAAO,IAAIC,gBAAgB,CACvB,SAAAC,CAAUC,EAAQC,IHmBnB,SAA8BD,EAAQ5C,GACrCZ,GAAkBwD,EAAOzD,gBAAgBE,KAClCuD,EAAOzD,KAAK2D,cAAcC,KAAKtC,GAASsC,KAAK/C,GAE/CP,IACJmD,EAAOzD,gBAAgBO,aAAeC,EAAOiD,EAAOzD,OAC9Ca,EAASS,EAAQmC,EAAOzD,OAEnCW,EAAa8C,GAAQ,GAAQI,IACpBnC,IACDA,EAAe,IAAIoC,aAEvBjD,EAASa,EAAaqC,OAAOF,GAAS,GAE9C,CGhCYG,CAAqBP,GAASzB,IAC1B,MAAMiC,EAAgBjC,EAAcM,OACpC,IAAI4B,EAEJ,GAAID,EAAgB,IAChBC,EAAS,IAAI3C,WAAW,GACxB,IAAI4C,SAASD,EAAOxD,QAAQ0D,SAAS,EAAGH,QAEvC,GAAIA,EAAgB,MAAO,CAC5BC,EAAS,IAAI3C,WAAW,GACxB,MAAM8C,EAAO,IAAIF,SAASD,EAAOxD,QACjC2D,EAAKD,SAAS,EAAG,KACjBC,EAAKC,UAAU,EAAGL,EACrB,KACI,CACDC,EAAS,IAAI3C,WAAW,GACxB,MAAM8C,EAAO,IAAIF,SAASD,EAAOxD,QACjC2D,EAAKD,SAAS,EAAG,KACjBC,EAAKE,aAAa,EAAGC,OAAOP,GAC/B,CAEGR,EAAOzD,MAA+B,iBAAhByD,EAAOzD,OAC7BkE,EAAO,IAAM,KAEjBR,EAAWe,QAAQP,GACnBR,EAAWe,QAAQzC,EAAc,GAExC,GAET,CACA,IAAI0C,EACJ,SAASC,EAAYC,GACjB,OAAOA,EAAOC,QAAO,CAACC,EAAKC,IAAUD,EAAMC,EAAMzC,QAAQ,EAC7D,CACA,SAAS0C,EAAaJ,EAAQK,GAC1B,GAAIL,EAAO,GAAGtC,SAAW2C,EACrB,OAAOL,EAAOM,QAElB,MAAMxE,EAAS,IAAIa,WAAW0D,GAC9B,IAAIE,EAAI,EACR,IAAK,IAAItD,EAAI,EAAGA,EAAIoD,EAAMpD,IACtBnB,EAAOmB,GAAK+C,EAAO,GAAGO,KAClBA,IAAMP,EAAO,GAAGtC,SAChBsC,EAAOM,QACPC,EAAI,GAMZ,OAHIP,EAAOtC,QAAU6C,EAAIP,EAAO,GAAGtC,SAC/BsC,EAAO,GAAKA,EAAO,GAAGQ,MAAMD,IAEzBzE,CACX,CC/EO,SAAS2E,EAAQ5E,GACtB,GAAIA,EAAK,OAWX,SAAeA,GACb,IAAK,IAAIZ,KAAOwF,EAAQlF,UACtBM,EAAIZ,GAAOwF,EAAQlF,UAAUN,GAE/B,OAAOY,CACT,CAhBkB6E,CAAM7E,EACxB,CA0BA4E,EAAQlF,UAAUoF,GAClBF,EAAQlF,UAAUqF,iBAAmB,SAASC,EAAOC,GAInD,OAHAC,KAAKC,EAAaD,KAAKC,GAAc,CAAA,GACpCD,KAAKC,EAAW,IAAMH,GAASE,KAAKC,EAAW,IAAMH,IAAU,IAC7DI,KAAKH,GACDC,IACT,EAYAN,EAAQlF,UAAU2F,KAAO,SAASL,EAAOC,GACvC,SAASH,IACPI,KAAKI,IAAIN,EAAOF,GAChBG,EAAGM,MAAML,KAAMM,UAChB,CAID,OAFAV,EAAGG,GAAKA,EACRC,KAAKJ,GAAGE,EAAOF,GACRI,IACT,EAYAN,EAAQlF,UAAU4F,IAClBV,EAAQlF,UAAU+F,eAClBb,EAAQlF,UAAUgG,mBAClBd,EAAQlF,UAAUiG,oBAAsB,SAASX,EAAOC,GAItD,GAHAC,KAAKC,EAAaD,KAAKC,GAAc,CAAA,EAGjC,GAAKK,UAAU3D,OAEjB,OADAqD,KAAKC,EAAa,GACXD,KAIT,IAUIU,EAVAC,EAAYX,KAAKC,EAAW,IAAMH,GACtC,IAAKa,EAAW,OAAOX,KAGvB,GAAI,GAAKM,UAAU3D,OAEjB,cADOqD,KAAKC,EAAW,IAAMH,GACtBE,KAKT,IAAK,IAAI9D,EAAI,EAAGA,EAAIyE,EAAUhE,OAAQT,IAEpC,IADAwE,EAAKC,EAAUzE,MACJ6D,GAAMW,EAAGX,KAAOA,EAAI,CAC7BY,EAAUC,OAAO1E,EAAG,GACpB,KACD,CASH,OAJyB,IAArByE,EAAUhE,eACLqD,KAAKC,EAAW,IAAMH,GAGxBE,IACT,EAUAN,EAAQlF,UAAUqG,KAAO,SAASf,GAChCE,KAAKC,EAAaD,KAAKC,GAAc,CAAA,EAKrC,IAHA,IAAIa,EAAO,IAAIC,MAAMT,UAAU3D,OAAS,GACpCgE,EAAYX,KAAKC,EAAW,IAAMH,GAE7B5D,EAAI,EAAGA,EAAIoE,UAAU3D,OAAQT,IACpC4E,EAAK5E,EAAI,GAAKoE,UAAUpE,GAG1B,GAAIyE,EAEG,CAAIzE,EAAI,EAAb,IAAK,IAAWiB,GADhBwD,EAAYA,EAAUlB,MAAM,IACI9C,OAAQT,EAAIiB,IAAOjB,EACjDyE,EAAUzE,GAAGmE,MAAML,KAAMc,EADKnE,CAKlC,OAAOqD,IACT,EAGAN,EAAQlF,UAAUwG,aAAetB,EAAQlF,UAAUqG,KAUnDnB,EAAQlF,UAAUyG,UAAY,SAASnB,GAErC,OADAE,KAAKC,EAAaD,KAAKC,GAAc,CAAA,EAC9BD,KAAKC,EAAW,IAAMH,IAAU,EACzC,EAUAJ,EAAQlF,UAAU0G,aAAe,SAASpB,GACxC,QAAUE,KAAKiB,UAAUnB,GAAOnD,MAClC,ECxKO,MAAMwE,EACqC,mBAAZC,SAAqD,mBAApBA,QAAQC,QAE/DX,GAAOU,QAAQC,UAAUpD,KAAKyC,GAG/B,CAACA,EAAIY,IAAiBA,EAAaZ,EAAI,GAGzCa,EACW,oBAATC,KACAA,KAEgB,oBAAXC,OACLA,OAGAC,SAAS,cAATA,GChBR,SAASC,EAAK7G,KAAQ8G,GACzB,OAAOA,EAAK1C,QAAO,CAACC,EAAK0C,KACjB/G,EAAIgH,eAAeD,KACnB1C,EAAI0C,GAAK/G,EAAI+G,IAEV1C,IACR,CAAE,EACT,CAEA,MAAM4C,EAAqBC,EAAWC,WAChCC,EAAuBF,EAAWG,aACjC,SAASC,EAAsBtH,EAAKuH,GACnCA,EAAKC,iBACLxH,EAAIwG,aAAeS,EAAmBQ,KAAKP,GAC3ClH,EAAI0H,eAAiBN,EAAqBK,KAAKP,KAG/ClH,EAAIwG,aAAeU,EAAWC,WAAWM,KAAKP,GAC9ClH,EAAI0H,eAAiBR,EAAWG,aAAaI,KAAKP,GAE1D,CAkCO,SAASS,IACZ,OAAQC,KAAKC,MAAMlI,SAAS,IAAIiC,UAAU,GACtCkG,KAAKC,SAASpI,SAAS,IAAIiC,UAAU,EAAG,EAChD,CCtDO,MAAMoG,UAAuBC,MAChC,WAAAC,CAAYC,EAAQC,EAAaC,GAC7BC,MAAMH,GACNjD,KAAKkD,YAAcA,EACnBlD,KAAKmD,QAAUA,EACfnD,KAAK5F,KAAO,gBACf,EAEE,MAAMiJ,UAAkB3D,EAO3B,WAAAsD,CAAYX,GACRe,QACApD,KAAKsD,UAAW,EAChBlB,EAAsBpC,KAAMqC,GAC5BrC,KAAKqC,KAAOA,EACZrC,KAAKuD,MAAQlB,EAAKkB,MAClBvD,KAAKwD,OAASnB,EAAKmB,OACnBxD,KAAK/E,gBAAkBoH,EAAKoB,WAC/B,CAUD,OAAAC,CAAQT,EAAQC,EAAaC,GAEzB,OADAC,MAAMpC,aAAa,QAAS,IAAI8B,EAAeG,EAAQC,EAAaC,IAC7DnD,IACV,CAID,IAAA2D,GAGI,OAFA3D,KAAK4D,WAAa,UAClB5D,KAAK6D,SACE7D,IACV,CAID,KAAA8D,GAKI,MAJwB,YAApB9D,KAAK4D,YAAgD,SAApB5D,KAAK4D,aACtC5D,KAAK+D,UACL/D,KAAKgE,WAEFhE,IACV,CAMD,IAAAiE,CAAKC,GACuB,SAApBlE,KAAK4D,YACL5D,KAAKmE,MAAMD,EAKlB,CAMD,MAAAE,GACIpE,KAAK4D,WAAa,OAClB5D,KAAKsD,UAAW,EAChBF,MAAMpC,aAAa,OACtB,CAOD,MAAAqD,CAAOhK,GACH,MAAMyD,EAAS1B,EAAa/B,EAAM2F,KAAKwD,OAAOlH,YAC9C0D,KAAKsE,SAASxG,EACjB,CAMD,QAAAwG,CAASxG,GACLsF,MAAMpC,aAAa,SAAUlD,EAChC,CAMD,OAAAkG,CAAQO,GACJvE,KAAK4D,WAAa,SAClBR,MAAMpC,aAAa,QAASuD,EAC/B,CAMD,KAAAC,CAAMC,GAAY,CAClB,SAAAC,CAAUC,EAAQpB,EAAQ,IACtB,OAAQoB,EACJ,MACA3E,KAAK4E,IACL5E,KAAK6E,IACL7E,KAAKqC,KAAKyC,KACV9E,KAAK+E,EAAOxB,EACnB,CACD,CAAAqB,GACI,MAAMI,EAAWhF,KAAKqC,KAAK2C,SAC3B,OAAkC,IAA3BA,EAASC,QAAQ,KAAcD,EAAW,IAAMA,EAAW,GACrE,CACD,CAAAH,GACI,OAAI7E,KAAKqC,KAAK6C,OACRlF,KAAKqC,KAAK8C,QAAUC,OAA0B,MAAnBpF,KAAKqC,KAAK6C,QACjClF,KAAKqC,KAAK8C,QAAqC,KAA3BC,OAAOpF,KAAKqC,KAAK6C,OACpC,IAAMlF,KAAKqC,KAAK6C,KAGhB,EAEd,CACD,CAAAH,CAAOxB,GACH,MAAM8B,EClIP,SAAgBvK,GACnB,IAAIwK,EAAM,GACV,IAAK,IAAIpJ,KAAKpB,EACNA,EAAIgH,eAAe5F,KACfoJ,EAAI3I,SACJ2I,GAAO,KACXA,GAAOC,mBAAmBrJ,GAAK,IAAMqJ,mBAAmBzK,EAAIoB,KAGpE,OAAOoJ,CACX,CDwH6BlH,CAAOmF,GAC5B,OAAO8B,EAAa1I,OAAS,IAAM0I,EAAe,EACrD,EEzIE,MAAMG,UAAgBnC,EACzB,WAAAL,GACII,SAAS9C,WACTN,KAAKyF,GAAW,CACnB,CACD,QAAIC,GACA,MAAO,SACV,CAOD,MAAA7B,GACI7D,KAAK2F,GACR,CAOD,KAAAnB,CAAMC,GACFzE,KAAK4D,WAAa,UAClB,MAAMY,EAAQ,KACVxE,KAAK4D,WAAa,SAClBa,GAAS,EAEb,GAAIzE,KAAKyF,IAAazF,KAAKsD,SAAU,CACjC,IAAIsC,EAAQ,EACR5F,KAAKyF,IACLG,IACA5F,KAAKG,KAAK,gBAAgB,aACpByF,GAASpB,GAC/B,KAEiBxE,KAAKsD,WACNsC,IACA5F,KAAKG,KAAK,SAAS,aACbyF,GAASpB,GAC/B,IAES,MAEGA,GAEP,CAMD,CAAAmB,GACI3F,KAAKyF,GAAW,EAChBzF,KAAK6F,SACL7F,KAAKgB,aAAa,OACrB,CAMD,MAAAqD,CAAOhK,GN/CW,EAACyL,EAAgBxJ,KACnC,MAAMyJ,EAAiBD,EAAerK,MAAM+B,GACtC0G,EAAU,GAChB,IAAK,IAAIhI,EAAI,EAAGA,EAAI6J,EAAepJ,OAAQT,IAAK,CAC5C,MAAM8J,EAAgB5J,EAAa2J,EAAe7J,GAAII,GAEtD,GADA4H,EAAQhE,KAAK8F,GACc,UAAvBA,EAAc5L,KACd,KAEP,CACD,OAAO8J,CAAO,EMoDV+B,CAAc5L,EAAM2F,KAAKwD,OAAOlH,YAAYrC,SAd1B6D,IAMd,GAJI,YAAckC,KAAK4D,YAA8B,SAAhB9F,EAAO1D,MACxC4F,KAAKoE,SAGL,UAAYtG,EAAO1D,KAEnB,OADA4F,KAAKgE,QAAQ,CAAEd,YAAa,oCACrB,EAGXlD,KAAKsE,SAASxG,EAAO,IAKrB,WAAakC,KAAK4D,aAElB5D,KAAKyF,GAAW,EAChBzF,KAAKgB,aAAa,gBACd,SAAWhB,KAAK4D,YAChB5D,KAAK2F,IAKhB,CAMD,OAAA5B,GACI,MAAMD,EAAQ,KACV9D,KAAKmE,MAAM,CAAC,CAAE/J,KAAM,UAAW,EAE/B,SAAW4F,KAAK4D,WAChBE,IAKA9D,KAAKG,KAAK,OAAQ2D,EAEzB,CAOD,KAAAK,CAAMD,GACFlE,KAAKsD,UAAW,ENnHF,EAACY,EAAShJ,KAE5B,MAAMyB,EAASuH,EAAQvH,OACjBoJ,EAAiB,IAAIhF,MAAMpE,GACjC,IAAIuJ,EAAQ,EACZhC,EAAQjK,SAAQ,CAAC6D,EAAQ5B,KAErBlB,EAAa8C,GAAQ,GAAQzB,IACzB0J,EAAe7J,GAAKG,IACd6J,IAAUvJ,GACZzB,EAAS6K,EAAeI,KAAK3I,GAChC,GACH,GACJ,EMuGE4I,CAAclC,GAAU7J,IACpB2F,KAAKqG,QAAQhM,GAAM,KACf2F,KAAKsD,UAAW,EAChBtD,KAAKgB,aAAa,QAAQ,GAC5B,GAET,CAMD,GAAAsF,GACI,MAAM3B,EAAS3E,KAAKqC,KAAK8C,OAAS,QAAU,OACtC5B,EAAQvD,KAAKuD,OAAS,GAQ5B,OANI,IAAUvD,KAAKqC,KAAKkE,oBACpBhD,EAAMvD,KAAKqC,KAAKmE,gBAAkB/D,KAEjCzC,KAAK/E,gBAAmBsI,EAAMkD,MAC/BlD,EAAMmD,IAAM,GAET1G,KAAK0E,UAAUC,EAAQpB,EACjC,EC9IL,IAAIoD,GAAQ,EACZ,IACIA,EAAkC,oBAAnBC,gBACX,oBAAqB,IAAIA,cACjC,CACA,MAAOC,GAGP,CACO,MAAMC,EAAUH,ECLvB,SAASI,IAAW,CACb,MAAMC,UAAgBxB,EAOzB,WAAAxC,CAAYX,GAER,GADAe,MAAMf,GACkB,oBAAb4E,SAA0B,CACjC,MAAMC,EAAQ,WAAaD,SAASE,SACpC,IAAIjC,EAAO+B,SAAS/B,KAEfA,IACDA,EAAOgC,EAAQ,MAAQ,MAE3BlH,KAAKoH,GACoB,oBAAbH,UACJ5E,EAAK2C,WAAaiC,SAASjC,UAC3BE,IAAS7C,EAAK6C,IACzB,CACJ,CAQD,OAAAmB,CAAQhM,EAAM0F,GACV,MAAMsH,EAAMrH,KAAKsH,QAAQ,CACrBC,OAAQ,OACRlN,KAAMA,IAEVgN,EAAIzH,GAAG,UAAWG,GAClBsH,EAAIzH,GAAG,SAAS,CAAC4H,EAAWrE,KACxBnD,KAAK0D,QAAQ,iBAAkB8D,EAAWrE,EAAQ,GAEzD,CAMD,MAAA0C,GACI,MAAMwB,EAAMrH,KAAKsH,UACjBD,EAAIzH,GAAG,OAAQI,KAAKqE,OAAO9B,KAAKvC,OAChCqH,EAAIzH,GAAG,SAAS,CAAC4H,EAAWrE,KACxBnD,KAAK0D,QAAQ,iBAAkB8D,EAAWrE,EAAQ,IAEtDnD,KAAKyH,QAAUJ,CAClB,EAEE,MAAMK,UAAgBhI,EAOzB,WAAAsD,CAAY2E,EAAerB,EAAKjE,GAC5Be,QACApD,KAAK2H,cAAgBA,EACrBvF,EAAsBpC,KAAMqC,GAC5BrC,KAAK4H,EAAQvF,EACbrC,KAAK6H,EAAUxF,EAAKkF,QAAU,MAC9BvH,KAAK8H,EAAOxB,EACZtG,KAAK+H,OAAQC,IAAc3F,EAAKhI,KAAOgI,EAAKhI,KAAO,KACnD2F,KAAKiI,GACR,CAMD,CAAAA,GACI,IAAIC,EACJ,MAAM7F,EAAOV,EAAK3B,KAAK4H,EAAO,QAAS,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,aAClHvF,EAAK8F,UAAYnI,KAAK4H,EAAMR,GAC5B,MAAMgB,EAAOpI,KAAKqI,EAAOrI,KAAK2H,cAActF,GAC5C,IACI+F,EAAIzE,KAAK3D,KAAK6H,EAAS7H,KAAK8H,GAAM,GAClC,IACI,GAAI9H,KAAK4H,EAAMU,aAAc,CAEzBF,EAAIG,uBAAyBH,EAAIG,uBAAsB,GACvD,IAAK,IAAIrM,KAAK8D,KAAK4H,EAAMU,aACjBtI,KAAK4H,EAAMU,aAAaxG,eAAe5F,IACvCkM,EAAII,iBAAiBtM,EAAG8D,KAAK4H,EAAMU,aAAapM,GAG3D,CACJ,CACD,MAAOuM,GAAM,CACb,GAAI,SAAWzI,KAAK6H,EAChB,IACIO,EAAII,iBAAiB,eAAgB,2BACxC,CACD,MAAOC,GAAM,CAEjB,IACIL,EAAII,iBAAiB,SAAU,MAClC,CACD,MAAOC,GAAM,CACmB,QAA/BP,EAAKlI,KAAK4H,EAAMc,iBAA8B,IAAPR,GAAyBA,EAAGS,WAAWP,GAE3E,oBAAqBA,IACrBA,EAAIQ,gBAAkB5I,KAAK4H,EAAMgB,iBAEjC5I,KAAK4H,EAAMiB,iBACXT,EAAIU,QAAU9I,KAAK4H,EAAMiB,gBAE7BT,EAAIW,mBAAqB,KACrB,IAAIb,EACmB,IAAnBE,EAAIxE,aAC4B,QAA/BsE,EAAKlI,KAAK4H,EAAMc,iBAA8B,IAAPR,GAAyBA,EAAGc,aAEpEZ,EAAIa,kBAAkB,gBAEtB,IAAMb,EAAIxE,aAEV,MAAQwE,EAAIc,QAAU,OAASd,EAAIc,OACnClJ,KAAKmJ,IAKLnJ,KAAKsB,cAAa,KACdtB,KAAKoJ,EAA+B,iBAAfhB,EAAIc,OAAsBd,EAAIc,OAAS,EAAE,GAC/D,GACN,EAELd,EAAInE,KAAKjE,KAAK+H,EACjB,CACD,MAAOU,GAOH,YAHAzI,KAAKsB,cAAa,KACdtB,KAAKoJ,EAASX,EAAE,GACjB,EAEN,CACuB,oBAAbY,WACPrJ,KAAKsJ,EAAS5B,EAAQ6B,gBACtB7B,EAAQ8B,SAASxJ,KAAKsJ,GAAUtJ,KAEvC,CAMD,CAAAoJ,CAASvC,GACL7G,KAAKgB,aAAa,QAAS6F,EAAK7G,KAAKqI,GACrCrI,KAAKyJ,GAAS,EACjB,CAMD,CAAAA,CAASC,GACL,QAAI,IAAuB1J,KAAKqI,GAAQ,OAASrI,KAAKqI,EAAtD,CAIA,GADArI,KAAKqI,EAAKU,mBAAqBhC,EAC3B2C,EACA,IACI1J,KAAKqI,EAAKsB,OACb,CACD,MAAOlB,GAAM,CAEO,oBAAbY,iBACA3B,EAAQ8B,SAASxJ,KAAKsJ,GAEjCtJ,KAAKqI,EAAO,IAXX,CAYJ,CAMD,CAAAc,GACI,MAAM9O,EAAO2F,KAAKqI,EAAKuB,aACV,OAATvP,IACA2F,KAAKgB,aAAa,OAAQ3G,GAC1B2F,KAAKgB,aAAa,WAClBhB,KAAKyJ,IAEZ,CAMD,KAAAE,GACI3J,KAAKyJ,GACR,EASL,GAPA/B,EAAQ6B,cAAgB,EACxB7B,EAAQ8B,SAAW,CAAA,EAMK,oBAAbH,SAEP,GAA2B,mBAAhBQ,YAEPA,YAAY,WAAYC,QAEvB,GAAgC,mBAArBjK,iBAAiC,CAE7CA,iBADyB,eAAgBmC,EAAa,WAAa,SAChC8H,GAAe,EACrD,CAEL,SAASA,IACL,IAAK,IAAI5N,KAAKwL,EAAQ8B,SACd9B,EAAQ8B,SAAS1H,eAAe5F,IAChCwL,EAAQ8B,SAAStN,GAAGyN,OAGhC,CACA,MAAMI,EAAU,WACZ,MAAM3B,EAAM4B,EAAW,CACnB7B,SAAS,IAEb,OAAOC,GAA4B,OAArBA,EAAI6B,YACrB,CALe,GAaT,MAAMC,UAAYlD,EACrB,WAAAhE,CAAYX,GACRe,MAAMf,GACN,MAAMoB,EAAcpB,GAAQA,EAAKoB,YACjCzD,KAAK/E,eAAiB8O,IAAYtG,CACrC,CACD,OAAA6D,CAAQjF,EAAO,IAEX,OADAxI,OAAOsQ,OAAO9H,EAAM,CAAE+E,GAAIpH,KAAKoH,IAAMpH,KAAKqC,MACnC,IAAIqF,EAAQsC,EAAYhK,KAAKsG,MAAOjE,EAC9C,EAEL,SAAS2H,EAAW3H,GAChB,MAAM8F,EAAU9F,EAAK8F,QAErB,IACI,GAAI,oBAAuBvB,kBAAoBuB,GAAWrB,GACtD,OAAO,IAAIF,cAElB,CACD,MAAO6B,GAAM,CACb,IAAKN,EACD,IACI,OAAO,IAAInG,EAAW,CAAC,UAAUoI,OAAO,UAAUjE,KAAK,OAAM,oBAChE,CACD,MAAOsC,GAAM,CAErB,CCzQA,MAAM4B,EAAqC,oBAAdC,WACI,iBAAtBA,UAAUC,SACmB,gBAApCD,UAAUC,QAAQC,cACf,MAAMC,UAAepH,EACxB,QAAIqC,GACA,MAAO,WACV,CACD,MAAA7B,GACI,MAAMyC,EAAMtG,KAAKsG,MACXoE,EAAY1K,KAAKqC,KAAKqI,UAEtBrI,EAAOgI,EACP,CAAE,EACF1I,EAAK3B,KAAKqC,KAAM,QAAS,oBAAqB,MAAO,MAAO,aAAc,OAAQ,KAAM,UAAW,qBAAsB,eAAgB,kBAAmB,SAAU,aAAc,SAAU,uBAChMrC,KAAKqC,KAAKiG,eACVjG,EAAKsI,QAAU3K,KAAKqC,KAAKiG,cAE7B,IACItI,KAAK4K,GAAK5K,KAAK6K,aAAavE,EAAKoE,EAAWrI,EAC/C,CACD,MAAOwE,GACH,OAAO7G,KAAKgB,aAAa,QAAS6F,EACrC,CACD7G,KAAK4K,GAAGtO,WAAa0D,KAAKwD,OAAOlH,WACjC0D,KAAK8K,mBACR,CAMD,iBAAAA,GACI9K,KAAK4K,GAAGG,OAAS,KACT/K,KAAKqC,KAAK2I,WACVhL,KAAK4K,GAAGK,EAAQC,QAEpBlL,KAAKoE,QAAQ,EAEjBpE,KAAK4K,GAAGO,QAAWC,GAAepL,KAAKgE,QAAQ,CAC3Cd,YAAa,8BACbC,QAASiI,IAEbpL,KAAK4K,GAAGS,UAAaC,GAAOtL,KAAKqE,OAAOiH,EAAGjR,MAC3C2F,KAAK4K,GAAGW,QAAW9C,GAAMzI,KAAK0D,QAAQ,kBAAmB+E,EAC5D,CACD,KAAAtE,CAAMD,GACFlE,KAAKsD,UAAW,EAGhB,IAAK,IAAIpH,EAAI,EAAGA,EAAIgI,EAAQvH,OAAQT,IAAK,CACrC,MAAM4B,EAASoG,EAAQhI,GACjBsP,EAAatP,IAAMgI,EAAQvH,OAAS,EAC1C3B,EAAa8C,EAAQkC,KAAK/E,gBAAiBZ,IAIvC,IACI2F,KAAKqG,QAAQvI,EAAQzD,EACxB,CACD,MAAOoO,GACN,CACG+C,GAGArK,GAAS,KACLnB,KAAKsD,UAAW,EAChBtD,KAAKgB,aAAa,QAAQ,GAC3BhB,KAAKsB,aACX,GAER,CACJ,CACD,OAAAyC,QAC2B,IAAZ/D,KAAK4K,KACZ5K,KAAK4K,GAAGW,QAAU,OAClBvL,KAAK4K,GAAG9G,QACR9D,KAAK4K,GAAK,KAEjB,CAMD,GAAAtE,GACI,MAAM3B,EAAS3E,KAAKqC,KAAK8C,OAAS,MAAQ,KACpC5B,EAAQvD,KAAKuD,OAAS,GAS5B,OAPIvD,KAAKqC,KAAKkE,oBACVhD,EAAMvD,KAAKqC,KAAKmE,gBAAkB/D,KAGjCzC,KAAK/E,iBACNsI,EAAMmD,IAAM,GAET1G,KAAK0E,UAAUC,EAAQpB,EACjC,EAEL,MAAMkI,EAAgBzJ,EAAW0J,WAAa1J,EAAW2J,aAUlD,MAAMC,UAAWnB,EACpB,YAAAI,CAAavE,EAAKoE,EAAWrI,GACzB,OAAQgI,EAIF,IAAIoB,EAAcnF,EAAKoE,EAAWrI,GAHlCqI,EACI,IAAIe,EAAcnF,EAAKoE,GACvB,IAAIe,EAAcnF,EAE/B,CACD,OAAAD,CAAQwF,EAASxR,GACb2F,KAAK4K,GAAG3G,KAAK5J,EAChB,EChHE,MAAMyR,UAAWzI,EACpB,QAAIqC,GACA,MAAO,cACV,CACD,MAAA7B,GACI,IAEI7D,KAAK+L,EAAa,IAAIC,aAAahM,KAAK0E,UAAU,SAAU1E,KAAKqC,KAAK4J,iBAAiBjM,KAAK0F,MAC/F,CACD,MAAOmB,GACH,OAAO7G,KAAKgB,aAAa,QAAS6F,EACrC,CACD7G,KAAK+L,EAAWG,OACXjO,MAAK,KACN+B,KAAKgE,SAAS,IAEbmI,OAAOtF,IACR7G,KAAK0D,QAAQ,qBAAsBmD,EAAI,IAG3C7G,KAAK+L,EAAWK,MAAMnO,MAAK,KACvB+B,KAAK+L,EAAWM,4BAA4BpO,MAAMqO,IAC9C,MAAMC,EVqDf,SAAmCC,EAAYlQ,GAC7CyC,IACDA,EAAe,IAAI0N,aAEvB,MAAMxN,EAAS,GACf,IAAIyN,EAAQ,EACRC,GAAkB,EAClBC,GAAW,EACf,OAAO,IAAIhP,gBAAgB,CACvB,SAAAC,CAAUuB,EAAOrB,GAEb,IADAkB,EAAOiB,KAAKd,KACC,CACT,GAAc,IAAVsN,EAAqC,CACrC,GAAI1N,EAAYC,GAAU,EACtB,MAEJ,MAAMV,EAASc,EAAaJ,EAAQ,GACpC2N,IAAkC,KAAtBrO,EAAO,IACnBoO,EAA6B,IAAZpO,EAAO,GAEpBmO,EADAC,EAAiB,IACT,EAEgB,MAAnBA,EACG,EAGA,CAEf,MACI,GAAc,IAAVD,EAAiD,CACtD,GAAI1N,EAAYC,GAAU,EACtB,MAEJ,MAAM4N,EAAcxN,EAAaJ,EAAQ,GACzC0N,EAAiB,IAAInO,SAASqO,EAAY9R,OAAQ8R,EAAYhR,WAAYgR,EAAYlQ,QAAQmQ,UAAU,GACxGJ,EAAQ,CACX,MACI,GAAc,IAAVA,EAAiD,CACtD,GAAI1N,EAAYC,GAAU,EACtB,MAEJ,MAAM4N,EAAcxN,EAAaJ,EAAQ,GACnCP,EAAO,IAAIF,SAASqO,EAAY9R,OAAQ8R,EAAYhR,WAAYgR,EAAYlQ,QAC5EoQ,EAAIrO,EAAKsO,UAAU,GACzB,GAAID,EAAInK,KAAKqK,IAAI,EAAG,IAAW,EAAG,CAE9BlP,EAAWe,QAAQ3E,GACnB,KACH,CACDwS,EAAiBI,EAAInK,KAAKqK,IAAI,EAAG,IAAMvO,EAAKsO,UAAU,GACtDN,EAAQ,CACX,KACI,CACD,GAAI1N,EAAYC,GAAU0N,EACtB,MAEJ,MAAMtS,EAAOgF,EAAaJ,EAAQ0N,GAClC5O,EAAWe,QAAQ1C,EAAawQ,EAAWvS,EAAO0E,EAAaxB,OAAOlD,GAAOiC,IAC7EoQ,EAAQ,CACX,CACD,GAAuB,IAAnBC,GAAwBA,EAAiBH,EAAY,CACrDzO,EAAWe,QAAQ3E,GACnB,KACH,CACJ,CACJ,GAET,CUxHsC+S,CAA0B9H,OAAO+H,iBAAkBnN,KAAKwD,OAAOlH,YAC/E8Q,EAASd,EAAOe,SAASC,YAAYf,GAAegB,YACpDC,EAAgB7P,IACtB6P,EAAcH,SAASI,OAAOnB,EAAOhJ,UACrCtD,KAAK0N,EAAUF,EAAclK,SAASqK,YACtC,MAAMC,EAAO,KACTR,EACKQ,OACA3P,MAAK,EAAG4P,OAAMlH,YACXkH,IAGJ7N,KAAKsE,SAASqC,GACdiH,IAAM,IAELzB,OAAOtF,IAAD,GACT,EAEN+G,IACA,MAAM9P,EAAS,CAAE1D,KAAM,QACnB4F,KAAKuD,MAAMkD,MACX3I,EAAOzD,KAAO,WAAW2F,KAAKuD,MAAMkD,SAExCzG,KAAK0N,EAAQvJ,MAAMrG,GAAQG,MAAK,IAAM+B,KAAKoE,UAAS,GACtD,GAET,CACD,KAAAD,CAAMD,GACFlE,KAAKsD,UAAW,EAChB,IAAK,IAAIpH,EAAI,EAAGA,EAAIgI,EAAQvH,OAAQT,IAAK,CACrC,MAAM4B,EAASoG,EAAQhI,GACjBsP,EAAatP,IAAMgI,EAAQvH,OAAS,EAC1CqD,KAAK0N,EAAQvJ,MAAMrG,GAAQG,MAAK,KACxBuN,GACArK,GAAS,KACLnB,KAAKsD,UAAW,EAChBtD,KAAKgB,aAAa,QAAQ,GAC3BhB,KAAKsB,aACX,GAER,CACJ,CACD,OAAAyC,GACI,IAAImE,EACuB,QAA1BA,EAAKlI,KAAK+L,SAA+B,IAAP7D,GAAyBA,EAAGpE,OAClE,EC3EE,MAAMgK,EAAa,CACtBC,UAAWnC,EACXoC,aAAclC,EACdmC,QAAS/D,GCaPgE,EAAK,sPACLC,EAAQ,CACV,SAAU,WAAY,YAAa,WAAY,OAAQ,WAAY,OAAQ,OAAQ,WAAY,OAAQ,YAAa,OAAQ,QAAS,UAElI,SAASC,EAAM9I,GAClB,GAAIA,EAAI3I,OAAS,IACb,KAAM,eAEV,MAAM0R,EAAM/I,EAAKgJ,EAAIhJ,EAAIL,QAAQ,KAAMwD,EAAInD,EAAIL,QAAQ,MAC7C,GAANqJ,IAAiB,GAAN7F,IACXnD,EAAMA,EAAI5I,UAAU,EAAG4R,GAAKhJ,EAAI5I,UAAU4R,EAAG7F,GAAG8F,QAAQ,KAAM,KAAOjJ,EAAI5I,UAAU+L,EAAGnD,EAAI3I,SAE9F,IAAI6R,EAAIN,EAAGO,KAAKnJ,GAAO,IAAKgB,EAAM,CAAA,EAAIpK,EAAI,GAC1C,KAAOA,KACHoK,EAAI6H,EAAMjS,IAAMsS,EAAEtS,IAAM,GAU5B,OARU,GAANoS,IAAiB,GAAN7F,IACXnC,EAAIoI,OAASL,EACb/H,EAAIqI,KAAOrI,EAAIqI,KAAKjS,UAAU,EAAG4J,EAAIqI,KAAKhS,OAAS,GAAG4R,QAAQ,KAAM,KACpEjI,EAAIsI,UAAYtI,EAAIsI,UAAUL,QAAQ,IAAK,IAAIA,QAAQ,IAAK,IAAIA,QAAQ,KAAM,KAC9EjI,EAAIuI,SAAU,GAElBvI,EAAIwI,UAIR,SAAmBhU,EAAKgK,GACpB,MAAMiK,EAAO,WAAYC,EAAQlK,EAAKyJ,QAAQQ,EAAM,KAAKtT,MAAM,KACvC,KAApBqJ,EAAKrF,MAAM,EAAG,IAA6B,IAAhBqF,EAAKnI,QAChCqS,EAAMpO,OAAO,EAAG,GAEE,KAAlBkE,EAAKrF,OAAO,IACZuP,EAAMpO,OAAOoO,EAAMrS,OAAS,EAAG,GAEnC,OAAOqS,CACX,CAboBF,CAAUxI,EAAKA,EAAU,MACzCA,EAAI2I,SAaR,SAAkB3I,EAAK/C,GACnB,MAAMlJ,EAAO,CAAA,EAMb,OALAkJ,EAAMgL,QAAQ,6BAA6B,SAAUW,EAAIC,EAAIC,GACrDD,IACA9U,EAAK8U,GAAMC,EAEvB,IACW/U,CACX,CArBmB4U,CAAS3I,EAAKA,EAAW,OACjCA,CACX,CCrCA,MAAM+I,EAAiD,mBAArBxP,kBACC,mBAAxBY,oBACL6O,EAA0B,GAC5BD,GAGAxP,iBAAiB,WAAW,KACxByP,EAAwBrV,SAASsV,GAAaA,KAAW,IAC1D,GAyBA,MAAMC,UAA6B9P,EAOtC,WAAAsD,CAAYsD,EAAKjE,GAiBb,GAhBAe,QACApD,KAAK1D,WX7BoB,cW8BzB0D,KAAKyP,YAAc,GACnBzP,KAAK0P,EAAiB,EACtB1P,KAAK2P,GAAiB,EACtB3P,KAAK4P,GAAgB,EACrB5P,KAAK6P,GAAe,EAKpB7P,KAAK8P,EAAmBC,IACpBzJ,GAAO,iBAAoBA,IAC3BjE,EAAOiE,EACPA,EAAM,MAENA,EAAK,CACL,MAAM0J,EAAY5B,EAAM9H,GACxBjE,EAAK2C,SAAWgL,EAAUrB,KAC1BtM,EAAK8C,OACsB,UAAvB6K,EAAU7I,UAA+C,QAAvB6I,EAAU7I,SAChD9E,EAAK6C,KAAO8K,EAAU9K,KAClB8K,EAAUzM,QACVlB,EAAKkB,MAAQyM,EAAUzM,MAC9B,MACQlB,EAAKsM,OACVtM,EAAK2C,SAAWoJ,EAAM/L,EAAKsM,MAAMA,MAErCvM,EAAsBpC,KAAMqC,GAC5BrC,KAAKmF,OACD,MAAQ9C,EAAK8C,OACP9C,EAAK8C,OACe,oBAAb8B,UAA4B,WAAaA,SAASE,SAC/D9E,EAAK2C,WAAa3C,EAAK6C,OAEvB7C,EAAK6C,KAAOlF,KAAKmF,OAAS,MAAQ,MAEtCnF,KAAKgF,SACD3C,EAAK2C,WACoB,oBAAbiC,SAA2BA,SAASjC,SAAW,aAC/DhF,KAAKkF,KACD7C,EAAK6C,OACoB,oBAAb+B,UAA4BA,SAAS/B,KACvC+B,SAAS/B,KACTlF,KAAKmF,OACD,MACA,MAClBnF,KAAK8N,WAAa,GAClB9N,KAAKiQ,EAAoB,GACzB5N,EAAKyL,WAAW7T,SAASiW,IACrB,MAAMC,EAAgBD,EAAE1V,UAAUkL,KAClC1F,KAAK8N,WAAW5N,KAAKiQ,GACrBnQ,KAAKiQ,EAAkBE,GAAiBD,CAAC,IAE7ClQ,KAAKqC,KAAOxI,OAAOsQ,OAAO,CACtBrF,KAAM,aACNsL,OAAO,EACPxH,iBAAiB,EACjByH,SAAS,EACT7J,eAAgB,IAChB8J,iBAAiB,EACjBC,kBAAkB,EAClBC,oBAAoB,EACpBC,kBAAmB,CACfC,UAAW,MAEfzE,iBAAkB,CAAE,EACpB0E,qBAAqB,GACtBtO,GACHrC,KAAKqC,KAAKyC,KACN9E,KAAKqC,KAAKyC,KAAKyJ,QAAQ,MAAO,KACzBvO,KAAKqC,KAAKkO,iBAAmB,IAAM,IACb,iBAApBvQ,KAAKqC,KAAKkB,QACjBvD,KAAKqC,KAAKkB,MRhGf,SAAgBqN,GACnB,IAAIC,EAAM,CAAA,EACNC,EAAQF,EAAGnV,MAAM,KACrB,IAAK,IAAIS,EAAI,EAAG6U,EAAID,EAAMnU,OAAQT,EAAI6U,EAAG7U,IAAK,CAC1C,IAAI8U,EAAOF,EAAM5U,GAAGT,MAAM,KAC1BoV,EAAII,mBAAmBD,EAAK,KAAOC,mBAAmBD,EAAK,GAC9D,CACD,OAAOH,CACX,CQwF8BtT,CAAOyC,KAAKqC,KAAKkB,QAEnC8L,IACIrP,KAAKqC,KAAKsO,sBAIV3Q,KAAKkR,EAA6B,KAC1BlR,KAAKmR,YAELnR,KAAKmR,UAAU3Q,qBACfR,KAAKmR,UAAUrN,QAClB,EAELjE,iBAAiB,eAAgBG,KAAKkR,GAA4B,IAEhD,cAAlBlR,KAAKgF,WACLhF,KAAKoR,EAAwB,KACzBpR,KAAKqR,EAAS,kBAAmB,CAC7BnO,YAAa,2BACf,EAENoM,EAAwBpP,KAAKF,KAAKoR,KAGtCpR,KAAKqC,KAAKuG,kBACV5I,KAAKsR,OAAaC,GAEtBvR,KAAKwR,GACR,CAQD,eAAAC,CAAgB/L,GACZ,MAAMnC,EAAQ1J,OAAOsQ,OAAO,CAAE,EAAEnK,KAAKqC,KAAKkB,OAE1CA,EAAMmO,IbPU,EaShBnO,EAAM4N,UAAYzL,EAEd1F,KAAK2R,KACLpO,EAAMkD,IAAMzG,KAAK2R,IACrB,MAAMtP,EAAOxI,OAAOsQ,OAAO,CAAA,EAAInK,KAAKqC,KAAM,CACtCkB,QACAC,OAAQxD,KACRgF,SAAUhF,KAAKgF,SACfG,OAAQnF,KAAKmF,OACbD,KAAMlF,KAAKkF,MACZlF,KAAKqC,KAAK4J,iBAAiBvG,IAC9B,OAAO,IAAI1F,KAAKiQ,EAAkBvK,GAAMrD,EAC3C,CAMD,CAAAmP,GACI,GAA+B,IAA3BxR,KAAK8N,WAAWnR,OAKhB,YAHAqD,KAAKsB,cAAa,KACdtB,KAAKgB,aAAa,QAAS,0BAA0B,GACtD,GAGP,MAAMmP,EAAgBnQ,KAAKqC,KAAKiO,iBAC5Bd,EAAqBoC,wBACqB,IAA1C5R,KAAK8N,WAAW7I,QAAQ,aACtB,YACAjF,KAAK8N,WAAW,GACtB9N,KAAK4D,WAAa,UAClB,MAAMuN,EAAYnR,KAAKyR,gBAAgBtB,GACvCgB,EAAUxN,OACV3D,KAAK6R,aAAaV,EACrB,CAMD,YAAAU,CAAaV,GACLnR,KAAKmR,WACLnR,KAAKmR,UAAU3Q,qBAGnBR,KAAKmR,UAAYA,EAEjBA,EACKvR,GAAG,QAASI,KAAK8R,EAASvP,KAAKvC,OAC/BJ,GAAG,SAAUI,KAAK+R,EAAUxP,KAAKvC,OACjCJ,GAAG,QAASI,KAAKoJ,EAAS7G,KAAKvC,OAC/BJ,GAAG,SAAUqD,GAAWjD,KAAKqR,EAAS,kBAAmBpO,IACjE,CAMD,MAAAmB,GACIpE,KAAK4D,WAAa,OAClB4L,EAAqBoC,sBACjB,cAAgB5R,KAAKmR,UAAUzL,KACnC1F,KAAKgB,aAAa,QAClBhB,KAAKgS,OACR,CAMD,CAAAD,CAAUjU,GACN,GAAI,YAAckC,KAAK4D,YACnB,SAAW5D,KAAK4D,YAChB,YAAc5D,KAAK4D,WAInB,OAHA5D,KAAKgB,aAAa,SAAUlD,GAE5BkC,KAAKgB,aAAa,aACVlD,EAAO1D,MACX,IAAK,OACD4F,KAAKiS,YAAYC,KAAK9D,MAAMtQ,EAAOzD,OACnC,MACJ,IAAK,OACD2F,KAAKmS,EAAY,QACjBnS,KAAKgB,aAAa,QAClBhB,KAAKgB,aAAa,QAClBhB,KAAKoS,IACL,MACJ,IAAK,QACD,MAAMvL,EAAM,IAAI9D,MAAM,gBAEtB8D,EAAIwL,KAAOvU,EAAOzD,KAClB2F,KAAKoJ,EAASvC,GACd,MACJ,IAAK,UACD7G,KAAKgB,aAAa,OAAQlD,EAAOzD,MACjC2F,KAAKgB,aAAa,UAAWlD,EAAOzD,MAMnD,CAOD,WAAA4X,CAAY5X,GACR2F,KAAKgB,aAAa,YAAa3G,GAC/B2F,KAAK2R,GAAKtX,EAAKoM,IACfzG,KAAKmR,UAAU5N,MAAMkD,IAAMpM,EAAKoM,IAChCzG,KAAK2P,EAAgBtV,EAAKiY,aAC1BtS,KAAK4P,EAAevV,EAAKkY,YACzBvS,KAAK6P,EAAcxV,EAAKmS,WACxBxM,KAAKoE,SAED,WAAapE,KAAK4D,YAEtB5D,KAAKoS,GACR,CAMD,CAAAA,GACIpS,KAAKwC,eAAexC,KAAKwS,GACzB,MAAMC,EAAQzS,KAAK2P,EAAgB3P,KAAK4P,EACxC5P,KAAK8P,EAAmBpN,KAAKC,MAAQ8P,EACrCzS,KAAKwS,EAAoBxS,KAAKsB,cAAa,KACvCtB,KAAKqR,EAAS,eAAe,GAC9BoB,GACCzS,KAAKqC,KAAK2I,WACVhL,KAAKwS,EAAkBtH,OAE9B,CAMD,CAAA4G,GACI9R,KAAKyP,YAAY7O,OAAO,EAAGZ,KAAK0P,GAIhC1P,KAAK0P,EAAiB,EAClB,IAAM1P,KAAKyP,YAAY9S,OACvBqD,KAAKgB,aAAa,SAGlBhB,KAAKgS,OAEZ,CAMD,KAAAA,GACI,GAAI,WAAahS,KAAK4D,YAClB5D,KAAKmR,UAAU7N,WACdtD,KAAK0S,WACN1S,KAAKyP,YAAY9S,OAAQ,CACzB,MAAMuH,EAAUlE,KAAK2S,IACrB3S,KAAKmR,UAAUlN,KAAKC,GAGpBlE,KAAK0P,EAAiBxL,EAAQvH,OAC9BqD,KAAKgB,aAAa,QACrB,CACJ,CAOD,CAAA2R,GAII,KAH+B3S,KAAK6P,GACR,YAAxB7P,KAAKmR,UAAUzL,MACf1F,KAAKyP,YAAY9S,OAAS,GAE1B,OAAOqD,KAAKyP,YAEhB,IAAImD,EAAc,EAClB,IAAK,IAAI1W,EAAI,EAAGA,EAAI8D,KAAKyP,YAAY9S,OAAQT,IAAK,CAC9C,MAAM7B,EAAO2F,KAAKyP,YAAYvT,GAAG7B,KAIjC,GAHIA,IACAuY,GVxUO,iBADI9X,EUyUeT,GVlU1C,SAAoBiL,GAChB,IAAIuN,EAAI,EAAGlW,EAAS,EACpB,IAAK,IAAIT,EAAI,EAAG6U,EAAIzL,EAAI3I,OAAQT,EAAI6U,EAAG7U,IACnC2W,EAAIvN,EAAInJ,WAAWD,GACf2W,EAAI,IACJlW,GAAU,EAELkW,EAAI,KACTlW,GAAU,EAELkW,EAAI,OAAUA,GAAK,MACxBlW,GAAU,GAGVT,IACAS,GAAU,GAGlB,OAAOA,CACX,CAxBemW,CAAWhY,GAGf8H,KAAKmQ,KAPQ,MAOFjY,EAAIgB,YAAchB,EAAIwE,QUsU5BpD,EAAI,GAAK0W,EAAc5S,KAAK6P,EAC5B,OAAO7P,KAAKyP,YAAYhQ,MAAM,EAAGvD,GAErC0W,GAAe,CAClB,CV/UF,IAAoB9X,EUgVnB,OAAOkF,KAAKyP,WACf,CAUa,CAAAuD,GACV,IAAKhT,KAAK8P,EACN,OAAO,EACX,MAAMmD,EAAavQ,KAAKC,MAAQ3C,KAAK8P,EAOrC,OANImD,IACAjT,KAAK8P,EAAmB,EACxB3O,GAAS,KACLnB,KAAKqR,EAAS,eAAe,GAC9BrR,KAAKsB,eAEL2R,CACV,CASD,KAAA9O,CAAM+O,EAAKC,EAASpT,GAEhB,OADAC,KAAKmS,EAAY,UAAWe,EAAKC,EAASpT,GACnCC,IACV,CASD,IAAAiE,CAAKiP,EAAKC,EAASpT,GAEf,OADAC,KAAKmS,EAAY,UAAWe,EAAKC,EAASpT,GACnCC,IACV,CAUD,CAAAmS,CAAY/X,EAAMC,EAAM8Y,EAASpT,GAS7B,GARI,mBAAsB1F,IACtB0F,EAAK1F,EACLA,OAAO2N,GAEP,mBAAsBmL,IACtBpT,EAAKoT,EACLA,EAAU,MAEV,YAAcnT,KAAK4D,YAAc,WAAa5D,KAAK4D,WACnD,QAEJuP,EAAUA,GAAW,IACbC,UAAW,IAAUD,EAAQC,SACrC,MAAMtV,EAAS,CACX1D,KAAMA,EACNC,KAAMA,EACN8Y,QAASA,GAEbnT,KAAKgB,aAAa,eAAgBlD,GAClCkC,KAAKyP,YAAYvP,KAAKpC,GAClBiC,GACAC,KAAKG,KAAK,QAASJ,GACvBC,KAAKgS,OACR,CAID,KAAAlO,GACI,MAAMA,EAAQ,KACV9D,KAAKqR,EAAS,gBACdrR,KAAKmR,UAAUrN,OAAO,EAEpBuP,EAAkB,KACpBrT,KAAKI,IAAI,UAAWiT,GACpBrT,KAAKI,IAAI,eAAgBiT,GACzBvP,GAAO,EAELwP,EAAiB,KAEnBtT,KAAKG,KAAK,UAAWkT,GACrBrT,KAAKG,KAAK,eAAgBkT,EAAgB,EAqB9C,MAnBI,YAAcrT,KAAK4D,YAAc,SAAW5D,KAAK4D,aACjD5D,KAAK4D,WAAa,UACd5D,KAAKyP,YAAY9S,OACjBqD,KAAKG,KAAK,SAAS,KACXH,KAAK0S,UACLY,IAGAxP,GACH,IAGA9D,KAAK0S,UACVY,IAGAxP,KAGD9D,IACV,CAMD,CAAAoJ,CAASvC,GAEL,GADA2I,EAAqBoC,uBAAwB,EACzC5R,KAAKqC,KAAKkR,kBACVvT,KAAK8N,WAAWnR,OAAS,GACL,YAApBqD,KAAK4D,WAEL,OADA5D,KAAK8N,WAAWvO,QACTS,KAAKwR,IAEhBxR,KAAKgB,aAAa,QAAS6F,GAC3B7G,KAAKqR,EAAS,kBAAmBxK,EACpC,CAMD,CAAAwK,CAASpO,EAAQC,GACb,GAAI,YAAclD,KAAK4D,YACnB,SAAW5D,KAAK4D,YAChB,YAAc5D,KAAK4D,WAAY,CAS/B,GAPA5D,KAAKwC,eAAexC,KAAKwS,GAEzBxS,KAAKmR,UAAU3Q,mBAAmB,SAElCR,KAAKmR,UAAUrN,QAEf9D,KAAKmR,UAAU3Q,qBACX6O,IACIrP,KAAKkR,GACLzQ,oBAAoB,eAAgBT,KAAKkR,GAA4B,GAErElR,KAAKoR,GAAuB,CAC5B,MAAMlV,EAAIoT,EAAwBrK,QAAQjF,KAAKoR,IACpC,IAAPlV,GACAoT,EAAwB1O,OAAO1E,EAAG,EAEzC,CAGL8D,KAAK4D,WAAa,SAElB5D,KAAK2R,GAAK,KAEV3R,KAAKgB,aAAa,QAASiC,EAAQC,GAGnClD,KAAKyP,YAAc,GACnBzP,KAAK0P,EAAiB,CACzB,CACJ,EAELF,EAAqBrI,SbhYG,EawZjB,MAAMqM,UAA0BhE,EACnC,WAAAxM,GACII,SAAS9C,WACTN,KAAKyT,EAAY,EACpB,CACD,MAAArP,GAEI,GADAhB,MAAMgB,SACF,SAAWpE,KAAK4D,YAAc5D,KAAKqC,KAAKgO,QACxC,IAAK,IAAInU,EAAI,EAAGA,EAAI8D,KAAKyT,EAAU9W,OAAQT,IACvC8D,KAAK0T,GAAO1T,KAAKyT,EAAUvX,GAGtC,CAOD,EAAAwX,CAAOhO,GACH,IAAIyL,EAAYnR,KAAKyR,gBAAgB/L,GACjCiO,GAAS,EACbnE,EAAqBoC,uBAAwB,EAC7C,MAAMgC,EAAkB,KAChBD,IAEJxC,EAAUlN,KAAK,CAAC,CAAE7J,KAAM,OAAQC,KAAM,WACtC8W,EAAUhR,KAAK,UAAW+S,IACtB,IAAIS,EAEJ,GAAI,SAAWT,EAAI9Y,MAAQ,UAAY8Y,EAAI7Y,KAAM,CAG7C,GAFA2F,KAAK0S,WAAY,EACjB1S,KAAKgB,aAAa,YAAamQ,IAC1BA,EACD,OACJ3B,EAAqBoC,sBACjB,cAAgBT,EAAUzL,KAC9B1F,KAAKmR,UAAU3M,OAAM,KACbmP,GAEA,WAAa3T,KAAK4D,aAEtBiQ,IACA7T,KAAK6R,aAAaV,GAClBA,EAAUlN,KAAK,CAAC,CAAE7J,KAAM,aACxB4F,KAAKgB,aAAa,UAAWmQ,GAC7BA,EAAY,KACZnR,KAAK0S,WAAY,EACjB1S,KAAKgS,QAAO,GAEnB,KACI,CACD,MAAMnL,EAAM,IAAI9D,MAAM,eAEtB8D,EAAIsK,UAAYA,EAAUzL,KAC1B1F,KAAKgB,aAAa,eAAgB6F,EACrC,KACH,EAEN,SAASiN,IACDH,IAGJA,GAAS,EACTE,IACA1C,EAAUrN,QACVqN,EAAY,KACf,CAED,MAAM5F,EAAW1E,IACb,MAAMkN,EAAQ,IAAIhR,MAAM,gBAAkB8D,GAE1CkN,EAAM5C,UAAYA,EAAUzL,KAC5BoO,IACA9T,KAAKgB,aAAa,eAAgB+S,EAAM,EAE5C,SAASC,IACLzI,EAAQ,mBACX,CAED,SAASJ,IACLI,EAAQ,gBACX,CAED,SAAS0I,EAAUC,GACX/C,GAAa+C,EAAGxO,OAASyL,EAAUzL,MACnCoO,GAEP,CAED,MAAMD,EAAU,KACZ1C,EAAU5Q,eAAe,OAAQqT,GACjCzC,EAAU5Q,eAAe,QAASgL,GAClC4F,EAAU5Q,eAAe,QAASyT,GAClChU,KAAKI,IAAI,QAAS+K,GAClBnL,KAAKI,IAAI,YAAa6T,EAAU,EAEpC9C,EAAUhR,KAAK,OAAQyT,GACvBzC,EAAUhR,KAAK,QAASoL,GACxB4F,EAAUhR,KAAK,QAAS6T,GACxBhU,KAAKG,KAAK,QAASgL,GACnBnL,KAAKG,KAAK,YAAa8T,IACyB,IAA5CjU,KAAKyT,EAAUxO,QAAQ,iBACd,iBAATS,EAEA1F,KAAKsB,cAAa,KACTqS,GACDxC,EAAUxN,MACb,GACF,KAGHwN,EAAUxN,MAEjB,CACD,WAAAsO,CAAY5X,GACR2F,KAAKyT,EAAYzT,KAAKmU,GAAgB9Z,EAAK+Z,UAC3ChR,MAAM6O,YAAY5X,EACrB,CAOD,EAAA8Z,CAAgBC,GACZ,MAAMC,EAAmB,GACzB,IAAK,IAAInY,EAAI,EAAGA,EAAIkY,EAASzX,OAAQT,KAC5B8D,KAAK8N,WAAW7I,QAAQmP,EAASlY,KAClCmY,EAAiBnU,KAAKkU,EAASlY,IAEvC,OAAOmY,CACV,EAqBE,MAAMC,WAAed,EACxB,WAAAxQ,CAAYsD,EAAKjE,EAAO,IACpB,MAAMkS,EAAmB,iBAARjO,EAAmBA,EAAMjE,IACrCkS,EAAEzG,YACFyG,EAAEzG,YAAyC,iBAApByG,EAAEzG,WAAW,MACrCyG,EAAEzG,YAAcyG,EAAEzG,YAAc,CAAC,UAAW,YAAa,iBACpD0G,KAAKrE,GAAkBsE,EAAmBtE,KAC1CuE,QAAQxE,KAAQA,KAEzB9M,MAAMkD,EAAKiO,EACd,EC3sBE,MAAMI,WAAcnP,EACvB,MAAAK,GACI7F,KAAK4U,KACA3W,MAAM4W,IACP,IAAKA,EAAIC,GACL,OAAO9U,KAAK0D,QAAQ,mBAAoBmR,EAAI3L,OAAQ2L,GAExDA,EAAIE,OAAO9W,MAAM5D,GAAS2F,KAAKqE,OAAOhK,IAAM,IAE3C8R,OAAOtF,IACR7G,KAAK0D,QAAQ,mBAAoBmD,EAAI,GAE5C,CACD,OAAAR,CAAQhM,EAAMa,GACV8E,KAAK4U,GAAOva,GACP4D,MAAM4W,IACP,IAAKA,EAAIC,GACL,OAAO9U,KAAK0D,QAAQ,oBAAqBmR,EAAI3L,OAAQ2L,GAEzD3Z,GAAU,IAETiR,OAAOtF,IACR7G,KAAK0D,QAAQ,oBAAqBmD,EAAI,GAE7C,CACD,EAAA+N,CAAOva,GACH,IAAI6N,EACJ,MAAM8M,OAAkBhN,IAAT3N,EACTsQ,EAAU,IAAIsK,QAAQjV,KAAKqC,KAAKiG,cAKtC,OAJI0M,GACArK,EAAQuK,IAAI,eAAgB,4BAEE,QAAjChN,EAAKlI,KAAKwD,OAAO8N,SAA+B,IAAPpJ,GAAyBA,EAAGiN,cAAcxK,GAC7EyK,MAAMpV,KAAKsG,MAAO,CACrBiB,OAAQyN,EAAS,OAAS,MAC1BK,KAAML,EAAS3a,EAAO,KACtBsQ,UACA2K,YAAatV,KAAKqC,KAAKuG,gBAAkB,UAAY,SACtD3K,MAAM4W,IACL,IAAI3M,EAGJ,OADkC,QAAjCA,EAAKlI,KAAKwD,OAAO8N,SAA+B,IAAPpJ,GAAyBA,EAAGc,aAAa6L,EAAIlK,QAAQ4K,gBACxFV,CAAG,GAEjB,ECtDL,MAAMla,GAA+C,mBAAhBC,YAC/BC,GAAUC,GACyB,mBAAvBF,YAAYC,OACpBD,YAAYC,OAAOC,GACnBA,EAAIC,kBAAkBH,YAE1BH,GAAWZ,OAAOW,UAAUC,SAC5BH,GAAiC,mBAATC,MACT,oBAATA,MACoB,6BAAxBE,GAASC,KAAKH,MAChBib,GAAiC,mBAATC,MACT,oBAATA,MACoB,6BAAxBhb,GAASC,KAAK+a,MAMf,SAAS7I,GAAS9R,GACrB,OAASH,KAA0BG,aAAeF,aAAeC,GAAOC,KACnER,IAAkBQ,aAAeP,MACjCib,IAAkB1a,aAAe2a,IAC1C,CACO,SAASC,GAAU5a,EAAK6a,GAC3B,IAAK7a,GAAsB,iBAARA,EACf,OAAO,EAEX,GAAIiG,MAAM6U,QAAQ9a,GAAM,CACpB,IAAK,IAAIoB,EAAI,EAAG6U,EAAIjW,EAAI6B,OAAQT,EAAI6U,EAAG7U,IACnC,GAAIwZ,GAAU5a,EAAIoB,IACd,OAAO,EAGf,OAAO,CACV,CACD,GAAI0Q,GAAS9R,GACT,OAAO,EAEX,GAAIA,EAAI6a,QACkB,mBAAf7a,EAAI6a,QACU,IAArBrV,UAAU3D,OACV,OAAO+Y,GAAU5a,EAAI6a,UAAU,GAEnC,IAAK,MAAMzb,KAAOY,EACd,GAAIjB,OAAOW,UAAUsH,eAAepH,KAAKI,EAAKZ,IAAQwb,GAAU5a,EAAIZ,IAChE,OAAO,EAGf,OAAO,CACX,CCzCO,SAAS2b,GAAkB/X,GAC9B,MAAMgY,EAAU,GACVC,EAAajY,EAAOzD,KACpB2b,EAAOlY,EAGb,OAFAkY,EAAK3b,KAAO4b,GAAmBF,EAAYD,GAC3CE,EAAKE,YAAcJ,EAAQnZ,OACpB,CAAEmB,OAAQkY,EAAMF,QAASA,EACpC,CACA,SAASG,GAAmB5b,EAAMyb,GAC9B,IAAKzb,EACD,OAAOA,EACX,GAAIuS,GAASvS,GAAO,CAChB,MAAM8b,EAAc,CAAEC,IAAc,EAAMC,IAAKP,EAAQnZ,QAEvD,OADAmZ,EAAQ5V,KAAK7F,GACN8b,CACV,CACI,GAAIpV,MAAM6U,QAAQvb,GAAO,CAC1B,MAAMic,EAAU,IAAIvV,MAAM1G,EAAKsC,QAC/B,IAAK,IAAIT,EAAI,EAAGA,EAAI7B,EAAKsC,OAAQT,IAC7Boa,EAAQpa,GAAK+Z,GAAmB5b,EAAK6B,GAAI4Z,GAE7C,OAAOQ,CACV,CACI,GAAoB,iBAATjc,KAAuBA,aAAgBqI,MAAO,CAC1D,MAAM4T,EAAU,CAAA,EAChB,IAAK,MAAMpc,KAAOG,EACVR,OAAOW,UAAUsH,eAAepH,KAAKL,EAAMH,KAC3Coc,EAAQpc,GAAO+b,GAAmB5b,EAAKH,GAAM4b,IAGrD,OAAOQ,CACV,CACD,OAAOjc,CACX,CASO,SAASkc,GAAkBzY,EAAQgY,GAGtC,OAFAhY,EAAOzD,KAAOmc,GAAmB1Y,EAAOzD,KAAMyb,UACvChY,EAAOoY,YACPpY,CACX,CACA,SAAS0Y,GAAmBnc,EAAMyb,GAC9B,IAAKzb,EACD,OAAOA,EACX,GAAIA,IAA8B,IAAtBA,EAAK+b,GAAuB,CAIpC,GAHyC,iBAAb/b,EAAKgc,KAC7Bhc,EAAKgc,KAAO,GACZhc,EAAKgc,IAAMP,EAAQnZ,OAEnB,OAAOmZ,EAAQzb,EAAKgc,KAGpB,MAAM,IAAItT,MAAM,sBAEvB,CACI,GAAIhC,MAAM6U,QAAQvb,GACnB,IAAK,IAAI6B,EAAI,EAAGA,EAAI7B,EAAKsC,OAAQT,IAC7B7B,EAAK6B,GAAKsa,GAAmBnc,EAAK6B,GAAI4Z,QAGzC,GAAoB,iBAATzb,EACZ,IAAK,MAAMH,KAAOG,EACVR,OAAOW,UAAUsH,eAAepH,KAAKL,EAAMH,KAC3CG,EAAKH,GAAOsc,GAAmBnc,EAAKH,GAAM4b,IAItD,OAAOzb,CACX,CC5EA,MAAMoc,GAAkB,CACpB,UACA,gBACA,aACA,gBACA,cACA,kBAOStP,GAAW,EACjB,IAAIuP,IACX,SAAWA,GACPA,EAAWA,EAAoB,QAAI,GAAK,UACxCA,EAAWA,EAAuB,WAAI,GAAK,aAC3CA,EAAWA,EAAkB,MAAI,GAAK,QACtCA,EAAWA,EAAgB,IAAI,GAAK,MACpCA,EAAWA,EAA0B,cAAI,GAAK,gBAC9CA,EAAWA,EAAyB,aAAI,GAAK,eAC7CA,EAAWA,EAAuB,WAAI,GAAK,YAC9C,CARD,CAQGA,KAAeA,GAAa,CAAE,IA8E1B,MAAMC,WAAgBjX,EAMzB,WAAAsD,CAAY4T,GACRxT,QACApD,KAAK4W,QAAUA,CAClB,CAMD,GAAAC,CAAI/b,GACA,IAAIgD,EACJ,GAAmB,iBAARhD,EAAkB,CACzB,GAAIkF,KAAK8W,cACL,MAAM,IAAI/T,MAAM,mDAEpBjF,EAASkC,KAAK+W,aAAajc,GAC3B,MAAMkc,EAAgBlZ,EAAO1D,OAASsc,GAAWO,aAC7CD,GAAiBlZ,EAAO1D,OAASsc,GAAWQ,YAC5CpZ,EAAO1D,KAAO4c,EAAgBN,GAAWS,MAAQT,GAAWU,IAE5DpX,KAAK8W,cAAgB,IAAIO,GAAoBvZ,GAElB,IAAvBA,EAAOoY,aACP9S,MAAMpC,aAAa,UAAWlD,IAKlCsF,MAAMpC,aAAa,UAAWlD,EAErC,KACI,KAAI8O,GAAS9R,KAAQA,EAAI+B,OAe1B,MAAM,IAAIkG,MAAM,iBAAmBjI,GAbnC,IAAKkF,KAAK8W,cACN,MAAM,IAAI/T,MAAM,oDAGhBjF,EAASkC,KAAK8W,cAAcQ,eAAexc,GACvCgD,IAEAkC,KAAK8W,cAAgB,KACrB1T,MAAMpC,aAAa,UAAWlD,GAMzC,CACJ,CAOD,YAAAiZ,CAAazR,GACT,IAAIpJ,EAAI,EAER,MAAMkB,EAAI,CACNhD,KAAMgL,OAAOE,EAAI9I,OAAO,KAE5B,QAA2BwL,IAAvB0O,GAAWtZ,EAAEhD,MACb,MAAM,IAAI2I,MAAM,uBAAyB3F,EAAEhD,MAG/C,GAAIgD,EAAEhD,OAASsc,GAAWO,cACtB7Z,EAAEhD,OAASsc,GAAWQ,WAAY,CAClC,MAAMK,EAAQrb,EAAI,EAClB,KAA2B,MAApBoJ,EAAI9I,SAASN,IAAcA,GAAKoJ,EAAI3I,SAC3C,MAAM6a,EAAMlS,EAAI5I,UAAU6a,EAAOrb,GACjC,GAAIsb,GAAOpS,OAAOoS,IAA0B,MAAlBlS,EAAI9I,OAAON,GACjC,MAAM,IAAI6G,MAAM,uBAEpB3F,EAAE8Y,YAAc9Q,OAAOoS,EAC1B,CAED,GAAI,MAAQlS,EAAI9I,OAAON,EAAI,GAAI,CAC3B,MAAMqb,EAAQrb,EAAI,EAClB,OAASA,GAAG,CAER,GAAI,MADMoJ,EAAI9I,OAAON,GAEjB,MACJ,GAAIA,IAAMoJ,EAAI3I,OACV,KACP,CACDS,EAAEqa,IAAMnS,EAAI5I,UAAU6a,EAAOrb,EAChC,MAEGkB,EAAEqa,IAAM,IAGZ,MAAMC,EAAOpS,EAAI9I,OAAON,EAAI,GAC5B,GAAI,KAAOwb,GAAQtS,OAAOsS,IAASA,EAAM,CACrC,MAAMH,EAAQrb,EAAI,EAClB,OAASA,GAAG,CACR,MAAM2W,EAAIvN,EAAI9I,OAAON,GACrB,GAAI,MAAQ2W,GAAKzN,OAAOyN,IAAMA,EAAG,GAC3B3W,EACF,KACH,CACD,GAAIA,IAAMoJ,EAAI3I,OACV,KACP,CACDS,EAAEuU,GAAKvM,OAAOE,EAAI5I,UAAU6a,EAAOrb,EAAI,GAC1C,CAED,GAAIoJ,EAAI9I,SAASN,GAAI,CACjB,MAAMyb,EAAU3X,KAAK4X,SAAStS,EAAIuS,OAAO3b,IACzC,IAAIya,GAAQmB,eAAe1a,EAAEhD,KAAMud,GAI/B,MAAM,IAAI5U,MAAM,mBAHhB3F,EAAE/C,KAAOsd,CAKhB,CACD,OAAOva,CACV,CACD,QAAAwa,CAAStS,GACL,IACI,OAAO4M,KAAK9D,MAAM9I,EAAKtF,KAAK4W,QAC/B,CACD,MAAOnO,GACH,OAAO,CACV,CACJ,CACD,qBAAOqP,CAAe1d,EAAMud,GACxB,OAAQvd,GACJ,KAAKsc,GAAWqB,QACZ,OAAOC,GAASL,GACpB,KAAKjB,GAAWuB,WACZ,YAAmBjQ,IAAZ2P,EACX,KAAKjB,GAAWwB,cACZ,MAA0B,iBAAZP,GAAwBK,GAASL,GACnD,KAAKjB,GAAWS,MAChB,KAAKT,GAAWO,aACZ,OAAQlW,MAAM6U,QAAQ+B,KACK,iBAAfA,EAAQ,IACW,iBAAfA,EAAQ,KAC6B,IAAzClB,GAAgBxR,QAAQ0S,EAAQ,KAChD,KAAKjB,GAAWU,IAChB,KAAKV,GAAWQ,WACZ,OAAOnW,MAAM6U,QAAQ+B,GAEhC,CAID,OAAAQ,GACQnY,KAAK8W,gBACL9W,KAAK8W,cAAcsB,yBACnBpY,KAAK8W,cAAgB,KAE5B,EAUL,MAAMO,GACF,WAAArU,CAAYlF,GACRkC,KAAKlC,OAASA,EACdkC,KAAK8V,QAAU,GACf9V,KAAKqY,UAAYva,CACpB,CASD,cAAAwZ,CAAegB,GAEX,GADAtY,KAAK8V,QAAQ5V,KAAKoY,GACdtY,KAAK8V,QAAQnZ,SAAWqD,KAAKqY,UAAUnC,YAAa,CAEpD,MAAMpY,EAASyY,GAAkBvW,KAAKqY,UAAWrY,KAAK8V,SAEtD,OADA9V,KAAKoY,yBACEta,CACV,CACD,OAAO,IACV,CAID,sBAAAsa,GACIpY,KAAKqY,UAAY,KACjBrY,KAAK8V,QAAU,EAClB,EAML,MAAMyC,GAAYnT,OAAOmT,WACrB,SAAU5R,GACN,MAAyB,iBAAVA,GACX6R,SAAS7R,IACT/D,KAAK6V,MAAM9R,KAAWA,CAClC,EAKA,SAASqR,GAASrR,GACd,MAAiD,oBAA1C9M,OAAOW,UAAUC,SAASC,KAAKiM,EAC1C,+CAhTwB,sCAcjB,MAMH,WAAA3D,CAAY0V,GACR1Y,KAAK0Y,SAAWA,CACnB,CAOD,MAAAta,CAAOtD,GACH,OAAIA,EAAIV,OAASsc,GAAWS,OAASrc,EAAIV,OAASsc,GAAWU,MACrD1B,GAAU5a,GAWX,CAACkF,KAAK2Y,eAAe7d,IAVbkF,KAAK4Y,eAAe,CACvBxe,KAAMU,EAAIV,OAASsc,GAAWS,MACxBT,GAAWO,aACXP,GAAWQ,WACjBO,IAAK3c,EAAI2c,IACTpd,KAAMS,EAAIT,KACVsX,GAAI7W,EAAI6W,IAKvB,CAID,cAAAgH,CAAe7d,GAEX,IAAIwK,EAAM,GAAKxK,EAAIV,KAmBnB,OAjBIU,EAAIV,OAASsc,GAAWO,cACxBnc,EAAIV,OAASsc,GAAWQ,aACxB5R,GAAOxK,EAAIob,YAAc,KAIzBpb,EAAI2c,KAAO,MAAQ3c,EAAI2c,MACvBnS,GAAOxK,EAAI2c,IAAM,KAGjB,MAAQ3c,EAAI6W,KACZrM,GAAOxK,EAAI6W,IAGX,MAAQ7W,EAAIT,OACZiL,GAAO4M,KAAK2G,UAAU/d,EAAIT,KAAM2F,KAAK0Y,WAElCpT,CACV,CAMD,cAAAsT,CAAe9d,GACX,MAAMge,EAAiBjD,GAAkB/a,GACnCkb,EAAOhW,KAAK2Y,eAAeG,EAAehb,QAC1CgY,EAAUgD,EAAehD,QAE/B,OADAA,EAAQiD,QAAQ/C,GACTF,CACV,4BAmPE,SAAuBhY,GAC1B,MApCsB,iBAoCGA,EAAO2Z,WA1BlBzP,KADI2J,EA4BD7T,EAAO6T,KA3BG4G,GAAU5G,KAMzC,SAAqBvX,EAAMud,GACvB,OAAQvd,GACJ,KAAKsc,GAAWqB,QACZ,YAAmB/P,IAAZ2P,GAAyBK,GAASL,GAC7C,KAAKjB,GAAWuB,WACZ,YAAmBjQ,IAAZ2P,EACX,KAAKjB,GAAWS,MACZ,OAAQpW,MAAM6U,QAAQ+B,KACK,iBAAfA,EAAQ,IACW,iBAAfA,EAAQ,KAC6B,IAAzClB,GAAgBxR,QAAQ0S,EAAQ,KAChD,KAAKjB,GAAWU,IACZ,OAAOrW,MAAM6U,QAAQ+B,GACzB,KAAKjB,GAAWwB,cACZ,MAA0B,iBAAZP,GAAwBK,GAASL,GACnD,QACI,OAAO,EAEnB,CAIQqB,CAAYlb,EAAO1D,KAAM0D,EAAOzD,MA7BxC,IAAsBsX,CA8BtB,IC3VO,SAAS/R,GAAG9E,EAAKwQ,EAAIvL,GAExB,OADAjF,EAAI8E,GAAG0L,EAAIvL,GACJ,WACHjF,EAAIsF,IAAIkL,EAAIvL,EACpB,CACA,CCEA,MAAM0W,GAAkB5c,OAAOof,OAAO,CAClCC,QAAS,EACTC,cAAe,EACfC,WAAY,EACZC,cAAe,EAEfC,YAAa,EACb/Y,eAAgB,IA0Bb,MAAM+T,WAAe5U,EAIxB,WAAAsD,CAAYuW,EAAI9B,EAAKpV,GACjBe,QAeApD,KAAKwZ,WAAY,EAKjBxZ,KAAKyZ,WAAY,EAIjBzZ,KAAK0Z,cAAgB,GAIrB1Z,KAAK2Z,WAAa,GAOlB3Z,KAAK4Z,GAAS,GAKd5Z,KAAK6Z,GAAY,EACjB7Z,KAAK8Z,IAAM,EAwBX9Z,KAAK+Z,KAAO,GACZ/Z,KAAKga,MAAQ,GACbha,KAAKuZ,GAAKA,EACVvZ,KAAKyX,IAAMA,EACPpV,GAAQA,EAAK4X,OACbja,KAAKia,KAAO5X,EAAK4X,MAErBja,KAAK4H,EAAQ/N,OAAOsQ,OAAO,CAAE,EAAE9H,GAC3BrC,KAAKuZ,GAAGW,IACRla,KAAK2D,MACZ,CAeD,gBAAIwW,GACA,OAAQna,KAAKwZ,SAChB,CAMD,SAAAY,GACI,GAAIpa,KAAKqa,KACL,OACJ,MAAMd,EAAKvZ,KAAKuZ,GAChBvZ,KAAKqa,KAAO,CACRza,GAAG2Z,EAAI,OAAQvZ,KAAK+K,OAAOxI,KAAKvC,OAChCJ,GAAG2Z,EAAI,SAAUvZ,KAAKsa,SAAS/X,KAAKvC,OACpCJ,GAAG2Z,EAAI,QAASvZ,KAAKuL,QAAQhJ,KAAKvC,OAClCJ,GAAG2Z,EAAI,QAASvZ,KAAKmL,QAAQ5I,KAAKvC,OAEzC,CAkBD,UAAIua,GACA,QAASva,KAAKqa,IACjB,CAWD,OAAAnB,GACI,OAAIlZ,KAAKwZ,YAETxZ,KAAKoa,YACApa,KAAKuZ,GAAkB,IACxBvZ,KAAKuZ,GAAG5V,OACR,SAAW3D,KAAKuZ,GAAGiB,IACnBxa,KAAK+K,UALE/K,IAOd,CAID,IAAA2D,GACI,OAAO3D,KAAKkZ,SACf,CAgBD,IAAAjV,IAAQnD,GAGJ,OAFAA,EAAKiY,QAAQ,WACb/Y,KAAKa,KAAKR,MAAML,KAAMc,GACfd,IACV,CAkBD,IAAAa,CAAKyK,KAAOxK,GACR,IAAIoH,EAAIuS,EAAIC,EACZ,GAAIjE,GAAgB3U,eAAewJ,GAC/B,MAAM,IAAIvI,MAAM,IAAMuI,EAAG7Q,WAAa,8BAG1C,GADAqG,EAAKiY,QAAQzN,GACTtL,KAAK4H,EAAM+S,UAAY3a,KAAKga,MAAMY,YAAc5a,KAAKga,MAAMa,SAE3D,OADA7a,KAAK8a,GAAYha,GACVd,KAEX,MAAMlC,EAAS,CACX1D,KAAMsc,GAAWS,MACjB9c,KAAMyG,EAEVhD,QAAiB,IAGjB,GAFAA,EAAOqV,QAAQC,UAAmC,IAAxBpT,KAAKga,MAAM5G,SAEjC,mBAAsBtS,EAAKA,EAAKnE,OAAS,GAAI,CAC7C,MAAMgV,EAAK3R,KAAK8Z,MACViB,EAAMja,EAAKka,MACjBhb,KAAKib,GAAqBtJ,EAAIoJ,GAC9Bjd,EAAO6T,GAAKA,CACf,CACD,MAAMuJ,EAAyG,QAAlFT,EAA+B,QAAzBvS,EAAKlI,KAAKuZ,GAAG4B,cAA2B,IAAPjT,OAAgB,EAASA,EAAGiJ,iBAA8B,IAAPsJ,OAAgB,EAASA,EAAGnX,SAC7I8X,EAAcpb,KAAKwZ,aAAyC,QAAzBkB,EAAK1a,KAAKuZ,GAAG4B,cAA2B,IAAPT,OAAgB,EAASA,EAAG1H,KAYtG,OAXsBhT,KAAKga,MAAMa,WAAaK,IAGrCE,GACLpb,KAAKqb,wBAAwBvd,GAC7BkC,KAAKlC,OAAOA,IAGZkC,KAAK2Z,WAAWzZ,KAAKpC,IAEzBkC,KAAKga,MAAQ,GACNha,IACV,CAID,EAAAib,CAAqBtJ,EAAIoJ,GACrB,IAAI7S,EACJ,MAAMY,EAAwC,QAA7BZ,EAAKlI,KAAKga,MAAMlR,eAA4B,IAAPZ,EAAgBA,EAAKlI,KAAK4H,EAAM0T,WACtF,QAAgBtT,IAAZc,EAEA,YADA9I,KAAK+Z,KAAKpI,GAAMoJ,GAIpB,MAAMQ,EAAQvb,KAAKuZ,GAAGjY,cAAa,YACxBtB,KAAK+Z,KAAKpI,GACjB,IAAK,IAAIzV,EAAI,EAAGA,EAAI8D,KAAK2Z,WAAWhd,OAAQT,IACpC8D,KAAK2Z,WAAWzd,GAAGyV,KAAOA,GAC1B3R,KAAK2Z,WAAW/Y,OAAO1E,EAAG,GAGlC6e,EAAIrgB,KAAKsF,KAAM,IAAI+C,MAAM,2BAA2B,GACrD+F,GACG/I,EAAK,IAAIe,KAEXd,KAAKuZ,GAAG/W,eAAe+Y,GACvBR,EAAI1a,MAAML,KAAMc,EAAK,EAEzBf,EAAGyb,WAAY,EACfxb,KAAK+Z,KAAKpI,GAAM5R,CACnB,CAiBD,WAAA0b,CAAYnQ,KAAOxK,GACf,OAAO,IAAIM,SAAQ,CAACC,EAASqa,KACzB,MAAM3b,EAAK,CAAC4b,EAAMC,IACPD,EAAOD,EAAOC,GAAQta,EAAQua,GAEzC7b,EAAGyb,WAAY,EACf1a,EAAKZ,KAAKH,GACVC,KAAKa,KAAKyK,KAAOxK,EAAK,GAE7B,CAMD,EAAAga,CAAYha,GACR,IAAIia,EACiC,mBAA1Bja,EAAKA,EAAKnE,OAAS,KAC1Boe,EAAMja,EAAKka,OAEf,MAAMld,EAAS,CACX6T,GAAI3R,KAAK6Z,KACTgC,SAAU,EACVC,SAAS,EACThb,OACAkZ,MAAOngB,OAAOsQ,OAAO,CAAEyQ,WAAW,GAAQ5a,KAAKga,QAEnDlZ,EAAKZ,MAAK,CAAC2G,KAAQkV,KACf,GAAIje,IAAWkC,KAAK4Z,GAAO,GAEvB,OAkBJ,OAhByB,OAAR/S,EAET/I,EAAO+d,SAAW7b,KAAK4H,EAAM+S,UAC7B3a,KAAK4Z,GAAOra,QACRwb,GACAA,EAAIlU,KAKZ7G,KAAK4Z,GAAOra,QACRwb,GACAA,EAAI,QAASgB,IAGrBje,EAAOge,SAAU,EACV9b,KAAKgc,IAAa,IAE7Bhc,KAAK4Z,GAAO1Z,KAAKpC,GACjBkC,KAAKgc,IACR,CAOD,EAAAA,CAAYC,GAAQ,GAChB,IAAKjc,KAAKwZ,WAAoC,IAAvBxZ,KAAK4Z,GAAOjd,OAC/B,OAEJ,MAAMmB,EAASkC,KAAK4Z,GAAO,GACvB9b,EAAOge,UAAYG,IAGvBne,EAAOge,SAAU,EACjBhe,EAAO+d,WACP7b,KAAKga,MAAQlc,EAAOkc,MACpBha,KAAKa,KAAKR,MAAML,KAAMlC,EAAOgD,MAChC,CAOD,MAAAhD,CAAOA,GACHA,EAAO2Z,IAAMzX,KAAKyX,IAClBzX,KAAKuZ,GAAG1N,GAAQ/N,EACnB,CAMD,MAAAiN,GAC4B,mBAAb/K,KAAKia,KACZja,KAAKia,MAAM5f,IACP2F,KAAKkc,GAAmB7hB,EAAK,IAIjC2F,KAAKkc,GAAmBlc,KAAKia,KAEpC,CAOD,EAAAiC,CAAmB7hB,GACf2F,KAAKlC,OAAO,CACR1D,KAAMsc,GAAWqB,QACjB1d,KAAM2F,KAAKmc,GACLtiB,OAAOsQ,OAAO,CAAEiS,IAAKpc,KAAKmc,GAAME,OAAQrc,KAAKsc,IAAejiB,GAC5DA,GAEb,CAOD,OAAAkR,CAAQ1E,GACC7G,KAAKwZ,WACNxZ,KAAKgB,aAAa,gBAAiB6F,EAE1C,CAQD,OAAAsE,CAAQlI,EAAQC,GACZlD,KAAKwZ,WAAY,SACVxZ,KAAK2R,GACZ3R,KAAKgB,aAAa,aAAciC,EAAQC,GACxClD,KAAKuc,IACR,CAOD,EAAAA,GACI1iB,OAAOG,KAAKgG,KAAK+Z,MAAM9f,SAAS0X,IAE5B,IADmB3R,KAAK2Z,WAAW6C,MAAM1e,GAAWL,OAAOK,EAAO6T,MAAQA,IACzD,CAEb,MAAMoJ,EAAM/a,KAAK+Z,KAAKpI,UACf3R,KAAK+Z,KAAKpI,GACboJ,EAAIS,WACJT,EAAIrgB,KAAKsF,KAAM,IAAI+C,MAAM,gCAEhC,IAER,CAOD,QAAAuX,CAASxc,GAEL,GADsBA,EAAO2Z,MAAQzX,KAAKyX,IAG1C,OAAQ3Z,EAAO1D,MACX,KAAKsc,GAAWqB,QACRja,EAAOzD,MAAQyD,EAAOzD,KAAKoM,IAC3BzG,KAAKyc,UAAU3e,EAAOzD,KAAKoM,IAAK3I,EAAOzD,KAAK+hB,KAG5Cpc,KAAKgB,aAAa,gBAAiB,IAAI+B,MAAM,8LAEjD,MACJ,KAAK2T,GAAWS,MAChB,KAAKT,GAAWO,aACZjX,KAAK0c,QAAQ5e,GACb,MACJ,KAAK4Y,GAAWU,IAChB,KAAKV,GAAWQ,WACZlX,KAAK2c,MAAM7e,GACX,MACJ,KAAK4Y,GAAWuB,WACZjY,KAAK4c,eACL,MACJ,KAAKlG,GAAWwB,cACZlY,KAAKmY,UACL,MAAMtR,EAAM,IAAI9D,MAAMjF,EAAOzD,KAAKwiB,SAElChW,EAAIxM,KAAOyD,EAAOzD,KAAKA,KACvB2F,KAAKgB,aAAa,gBAAiB6F,GAG9C,CAOD,OAAA6V,CAAQ5e,GACJ,MAAMgD,EAAOhD,EAAOzD,MAAQ,GACxB,MAAQyD,EAAO6T,IACf7Q,EAAKZ,KAAKF,KAAK+a,IAAIjd,EAAO6T,KAE1B3R,KAAKwZ,UACLxZ,KAAK8c,UAAUhc,GAGfd,KAAK0Z,cAAcxZ,KAAKrG,OAAOof,OAAOnY,GAE7C,CACD,SAAAgc,CAAUhc,GACN,GAAId,KAAK+c,IAAiB/c,KAAK+c,GAAcpgB,OAAQ,CACjD,MAAMsE,EAAYjB,KAAK+c,GAActd,QACrC,IAAK,MAAM8P,KAAYtO,EACnBsO,EAASlP,MAAML,KAAMc,EAE5B,CACDsC,MAAMvC,KAAKR,MAAML,KAAMc,GACnBd,KAAKmc,IAAQrb,EAAKnE,QAA2C,iBAA1BmE,EAAKA,EAAKnE,OAAS,KACtDqD,KAAKsc,GAAcxb,EAAKA,EAAKnE,OAAS,GAE7C,CAMD,GAAAoe,CAAIpJ,GACA,MAAMnQ,EAAOxB,KACb,IAAIgd,GAAO,EACX,OAAO,YAAalc,GAEZkc,IAEJA,GAAO,EACPxb,EAAK1D,OAAO,CACR1D,KAAMsc,GAAWU,IACjBzF,GAAIA,EACJtX,KAAMyG,IAEtB,CACK,CAOD,KAAA6b,CAAM7e,GACF,MAAMid,EAAM/a,KAAK+Z,KAAKjc,EAAO6T,IACV,mBAARoJ,WAGJ/a,KAAK+Z,KAAKjc,EAAO6T,IAEpBoJ,EAAIS,WACJ1d,EAAOzD,KAAK0e,QAAQ,MAGxBgC,EAAI1a,MAAML,KAAMlC,EAAOzD,MAC1B,CAMD,SAAAoiB,CAAU9K,EAAIyK,GACVpc,KAAK2R,GAAKA,EACV3R,KAAKyZ,UAAY2C,GAAOpc,KAAKmc,KAASC,EACtCpc,KAAKmc,GAAOC,EACZpc,KAAKwZ,WAAY,EACjBxZ,KAAKid,eACLjd,KAAKgB,aAAa,WAClBhB,KAAKgc,IAAY,EACpB,CAMD,YAAAiB,GACIjd,KAAK0Z,cAAczf,SAAS6G,GAASd,KAAK8c,UAAUhc,KACpDd,KAAK0Z,cAAgB,GACrB1Z,KAAK2Z,WAAW1f,SAAS6D,IACrBkC,KAAKqb,wBAAwBvd,GAC7BkC,KAAKlC,OAAOA,EAAO,IAEvBkC,KAAK2Z,WAAa,EACrB,CAMD,YAAAiD,GACI5c,KAAKmY,UACLnY,KAAKmL,QAAQ,uBAChB,CAQD,OAAAgN,GACQnY,KAAKqa,OAELra,KAAKqa,KAAKpgB,SAASijB,GAAeA,MAClCld,KAAKqa,UAAOrS,GAEhBhI,KAAKuZ,GAAa,GAAEvZ,KACvB,CAiBD,UAAAoZ,GAUI,OATIpZ,KAAKwZ,WACLxZ,KAAKlC,OAAO,CAAE1D,KAAMsc,GAAWuB,aAGnCjY,KAAKmY,UACDnY,KAAKwZ,WAELxZ,KAAKmL,QAAQ,wBAEVnL,IACV,CAMD,KAAA8D,GACI,OAAO9D,KAAKoZ,YACf,CAUD,QAAAhG,CAASA,GAEL,OADApT,KAAKga,MAAM5G,SAAWA,EACfpT,IACV,CAUD,YAAI6a,GAEA,OADA7a,KAAKga,MAAMa,UAAW,EACf7a,IACV,CAcD,OAAA8I,CAAQA,GAEJ,OADA9I,KAAKga,MAAMlR,QAAUA,EACd9I,IACV,CAYD,KAAAmd,CAAM5N,GAGF,OAFAvP,KAAK+c,GAAgB/c,KAAK+c,IAAiB,GAC3C/c,KAAK+c,GAAc7c,KAAKqP,GACjBvP,IACV,CAYD,UAAAod,CAAW7N,GAGP,OAFAvP,KAAK+c,GAAgB/c,KAAK+c,IAAiB,GAC3C/c,KAAK+c,GAAchE,QAAQxJ,GACpBvP,IACV,CAmBD,MAAAqd,CAAO9N,GACH,IAAKvP,KAAK+c,GACN,OAAO/c,KAEX,GAAIuP,EAAU,CACV,MAAMtO,EAAYjB,KAAK+c,GACvB,IAAK,IAAI7gB,EAAI,EAAGA,EAAI+E,EAAUtE,OAAQT,IAClC,GAAIqT,IAAatO,EAAU/E,GAEvB,OADA+E,EAAUL,OAAO1E,EAAG,GACb8D,IAGlB,MAEGA,KAAK+c,GAAgB,GAEzB,OAAO/c,IACV,CAKD,YAAAsd,GACI,OAAOtd,KAAK+c,IAAiB,EAChC,CAcD,aAAAQ,CAAchO,GAGV,OAFAvP,KAAKwd,GAAwBxd,KAAKwd,IAAyB,GAC3Dxd,KAAKwd,GAAsBtd,KAAKqP,GACzBvP,IACV,CAcD,kBAAAyd,CAAmBlO,GAGf,OAFAvP,KAAKwd,GAAwBxd,KAAKwd,IAAyB,GAC3Dxd,KAAKwd,GAAsBzE,QAAQxJ,GAC5BvP,IACV,CAmBD,cAAA0d,CAAenO,GACX,IAAKvP,KAAKwd,GACN,OAAOxd,KAEX,GAAIuP,EAAU,CACV,MAAMtO,EAAYjB,KAAKwd,GACvB,IAAK,IAAIthB,EAAI,EAAGA,EAAI+E,EAAUtE,OAAQT,IAClC,GAAIqT,IAAatO,EAAU/E,GAEvB,OADA+E,EAAUL,OAAO1E,EAAG,GACb8D,IAGlB,MAEGA,KAAKwd,GAAwB,GAEjC,OAAOxd,IACV,CAKD,oBAAA2d,GACI,OAAO3d,KAAKwd,IAAyB,EACxC,CAQD,uBAAAnC,CAAwBvd,GACpB,GAAIkC,KAAKwd,IAAyBxd,KAAKwd,GAAsB7gB,OAAQ,CACjE,MAAMsE,EAAYjB,KAAKwd,GAAsB/d,QAC7C,IAAK,MAAM8P,KAAYtO,EACnBsO,EAASlP,MAAML,KAAMlC,EAAOzD,KAEnC,CACJ,ECr2BE,SAASujB,GAAQvb,GACpBA,EAAOA,GAAQ,GACfrC,KAAK6d,GAAKxb,EAAKyb,KAAO,IACtB9d,KAAK+d,IAAM1b,EAAK0b,KAAO,IACvB/d,KAAKge,OAAS3b,EAAK2b,QAAU,EAC7Bhe,KAAKie,OAAS5b,EAAK4b,OAAS,GAAK5b,EAAK4b,QAAU,EAAI5b,EAAK4b,OAAS,EAClEje,KAAKke,SAAW,CACpB,CAOAN,GAAQpjB,UAAU2jB,SAAW,WACzB,IAAIN,EAAK7d,KAAK6d,GAAKjb,KAAKqK,IAAIjN,KAAKge,OAAQhe,KAAKke,YAC9C,GAAIle,KAAKie,OAAQ,CACb,IAAIG,EAAOxb,KAAKC,SACZwb,EAAYzb,KAAK6V,MAAM2F,EAAOpe,KAAKie,OAASJ,GAChDA,EAA8B,EAAxBjb,KAAK6V,MAAa,GAAP2F,GAAwCP,EAAKQ,EAAtBR,EAAKQ,CAChD,CACD,OAAgC,EAAzBzb,KAAKkb,IAAID,EAAI7d,KAAK+d,IAC7B,EAMAH,GAAQpjB,UAAU8jB,MAAQ,WACtBte,KAAKke,SAAW,CACpB,EAMAN,GAAQpjB,UAAU+jB,OAAS,SAAUT,GACjC9d,KAAK6d,GAAKC,CACd,EAMAF,GAAQpjB,UAAUgkB,OAAS,SAAUT,GACjC/d,KAAK+d,IAAMA,CACf,EAMAH,GAAQpjB,UAAUikB,UAAY,SAAUR,GACpCje,KAAKie,OAASA,CAClB,EC3DO,MAAMS,WAAgBhf,EACzB,WAAAsD,CAAYsD,EAAKjE,GACb,IAAI6F,EACJ9E,QACApD,KAAK2e,KAAO,GACZ3e,KAAKqa,KAAO,GACR/T,GAAO,iBAAoBA,IAC3BjE,EAAOiE,EACPA,OAAM0B,IAEV3F,EAAOA,GAAQ,IACVyC,KAAOzC,EAAKyC,MAAQ,aACzB9E,KAAKqC,KAAOA,EACZD,EAAsBpC,KAAMqC,GAC5BrC,KAAK4e,cAAmC,IAAtBvc,EAAKuc,cACvB5e,KAAK6e,qBAAqBxc,EAAKwc,sBAAwB9O,KACvD/P,KAAK8e,kBAAkBzc,EAAKyc,mBAAqB,KACjD9e,KAAK+e,qBAAqB1c,EAAK0c,sBAAwB,KACvD/e,KAAKgf,oBAAwD,QAAnC9W,EAAK7F,EAAK2c,2BAAwC,IAAP9W,EAAgBA,EAAK,IAC1FlI,KAAKif,QAAU,IAAIrB,GAAQ,CACvBE,IAAK9d,KAAK8e,oBACVf,IAAK/d,KAAK+e,uBACVd,OAAQje,KAAKgf,wBAEjBhf,KAAK8I,QAAQ,MAAQzG,EAAKyG,QAAU,IAAQzG,EAAKyG,SACjD9I,KAAKwa,GAAc,SACnBxa,KAAKsG,IAAMA,EACX,MAAM4Y,EAAU7c,EAAK8c,QAAUA,GAC/Bnf,KAAKof,QAAU,IAAIF,EAAQG,QAC3Brf,KAAKsf,QAAU,IAAIJ,EAAQvI,QAC3B3W,KAAKka,IAAoC,IAArB7X,EAAKkd,YACrBvf,KAAKka,IACLla,KAAK2D,MACZ,CACD,YAAAib,CAAaY,GACT,OAAKlf,UAAU3D,QAEfqD,KAAKyf,KAAkBD,EAClBA,IACDxf,KAAK0f,eAAgB,GAElB1f,MALIA,KAAKyf,EAMnB,CACD,oBAAAZ,CAAqBW,GACjB,YAAUxX,IAANwX,EACOxf,KAAK2f,IAChB3f,KAAK2f,GAAwBH,EACtBxf,KACV,CACD,iBAAA8e,CAAkBU,GACd,IAAItX,EACJ,YAAUF,IAANwX,EACOxf,KAAK4f,IAChB5f,KAAK4f,GAAqBJ,EACF,QAAvBtX,EAAKlI,KAAKif,eAA4B,IAAP/W,GAAyBA,EAAGqW,OAAOiB,GAC5Dxf,KACV,CACD,mBAAAgf,CAAoBQ,GAChB,IAAItX,EACJ,YAAUF,IAANwX,EACOxf,KAAK6f,IAChB7f,KAAK6f,GAAuBL,EACJ,QAAvBtX,EAAKlI,KAAKif,eAA4B,IAAP/W,GAAyBA,EAAGuW,UAAUe,GAC/Dxf,KACV,CACD,oBAAA+e,CAAqBS,GACjB,IAAItX,EACJ,YAAUF,IAANwX,EACOxf,KAAK8f,IAChB9f,KAAK8f,GAAwBN,EACL,QAAvBtX,EAAKlI,KAAKif,eAA4B,IAAP/W,GAAyBA,EAAGsW,OAAOgB,GAC5Dxf,KACV,CACD,OAAA8I,CAAQ0W,GACJ,OAAKlf,UAAU3D,QAEfqD,KAAK+f,GAAWP,EACTxf,MAFIA,KAAK+f,EAGnB,CAOD,oBAAAC,IAEShgB,KAAKigB,IACNjgB,KAAKyf,IACqB,IAA1Bzf,KAAKif,QAAQf,UAEble,KAAKkgB,WAEZ,CAQD,IAAAvc,CAAK5D,GACD,IAAKC,KAAKwa,GAAYvV,QAAQ,QAC1B,OAAOjF,KACXA,KAAKmb,OAAS,IAAIgF,GAAOngB,KAAKsG,IAAKtG,KAAKqC,MACxC,MAAMmB,EAASxD,KAAKmb,OACd3Z,EAAOxB,KACbA,KAAKwa,GAAc,UACnBxa,KAAK0f,eAAgB,EAErB,MAAMU,EAAiBxgB,GAAG4D,EAAQ,QAAQ,WACtChC,EAAKuJ,SACLhL,GAAMA,GAClB,IACc2D,EAAWmD,IACb7G,KAAK6T,UACL7T,KAAKwa,GAAc,SACnBxa,KAAKgB,aAAa,QAAS6F,GACvB9G,EACAA,EAAG8G,GAIH7G,KAAKggB,sBACR,EAGCK,EAAWzgB,GAAG4D,EAAQ,QAASE,GACrC,IAAI,IAAU1D,KAAK+f,GAAU,CACzB,MAAMjX,EAAU9I,KAAK+f,GAEfxE,EAAQvb,KAAKsB,cAAa,KAC5B8e,IACA1c,EAAQ,IAAIX,MAAM,YAClBS,EAAOM,OAAO,GACfgF,GACC9I,KAAKqC,KAAK2I,WACVuQ,EAAMrQ,QAEVlL,KAAKqa,KAAKna,MAAK,KACXF,KAAKwC,eAAe+Y,EAAM,GAEjC,CAGD,OAFAvb,KAAKqa,KAAKna,KAAKkgB,GACfpgB,KAAKqa,KAAKna,KAAKmgB,GACRrgB,IACV,CAOD,OAAAkZ,CAAQnZ,GACJ,OAAOC,KAAK2D,KAAK5D,EACpB,CAMD,MAAAgL,GAEI/K,KAAK6T,UAEL7T,KAAKwa,GAAc,OACnBxa,KAAKgB,aAAa,QAElB,MAAMwC,EAASxD,KAAKmb,OACpBnb,KAAKqa,KAAKna,KAAKN,GAAG4D,EAAQ,OAAQxD,KAAKsgB,OAAO/d,KAAKvC,OAAQJ,GAAG4D,EAAQ,OAAQxD,KAAKugB,OAAOhe,KAAKvC,OAAQJ,GAAG4D,EAAQ,QAASxD,KAAKuL,QAAQhJ,KAAKvC,OAAQJ,GAAG4D,EAAQ,QAASxD,KAAKmL,QAAQ5I,KAAKvC,OAE3LJ,GAAGI,KAAKsf,QAAS,UAAWtf,KAAKwgB,UAAUje,KAAKvC,OACnD,CAMD,MAAAsgB,GACItgB,KAAKgB,aAAa,OACrB,CAMD,MAAAuf,CAAOlmB,GACH,IACI2F,KAAKsf,QAAQzI,IAAIxc,EACpB,CACD,MAAOoO,GACHzI,KAAKmL,QAAQ,cAAe1C,EAC/B,CACJ,CAMD,SAAA+X,CAAU1iB,GAENqD,GAAS,KACLnB,KAAKgB,aAAa,SAAUlD,EAAO,GACpCkC,KAAKsB,aACX,CAMD,OAAAiK,CAAQ1E,GACJ7G,KAAKgB,aAAa,QAAS6F,EAC9B,CAOD,MAAArD,CAAOiU,EAAKpV,GACR,IAAImB,EAASxD,KAAK2e,KAAKlH,GAQvB,OAPKjU,EAIIxD,KAAKka,KAAiB1W,EAAO+W,QAClC/W,EAAO0V,WAJP1V,EAAS,IAAI8Q,GAAOtU,KAAMyX,EAAKpV,GAC/BrC,KAAK2e,KAAKlH,GAAOjU,GAKdA,CACV,CAOD,EAAAid,CAASjd,GACL,MAAMmb,EAAO9kB,OAAOG,KAAKgG,KAAK2e,MAC9B,IAAK,MAAMlH,KAAOkH,EAAM,CAEpB,GADe3e,KAAK2e,KAAKlH,GACd8C,OACP,MAEP,CACDva,KAAK0gB,IACR,CAOD,EAAA7U,CAAQ/N,GACJ,MAAMiI,EAAiB/F,KAAKof,QAAQhhB,OAAON,GAC3C,IAAK,IAAI5B,EAAI,EAAGA,EAAI6J,EAAepJ,OAAQT,IACvC8D,KAAKmb,OAAOhX,MAAM4B,EAAe7J,GAAI4B,EAAOqV,QAEnD,CAMD,OAAAU,GACI7T,KAAKqa,KAAKpgB,SAASijB,GAAeA,MAClCld,KAAKqa,KAAK1d,OAAS,EACnBqD,KAAKsf,QAAQnH,SAChB,CAMD,EAAAuI,GACI1gB,KAAK0f,eAAgB,EACrB1f,KAAKigB,IAAgB,EACrBjgB,KAAKmL,QAAQ,eAChB,CAMD,UAAAiO,GACI,OAAOpZ,KAAK0gB,IACf,CAUD,OAAAvV,CAAQlI,EAAQC,GACZ,IAAIgF,EACJlI,KAAK6T,UACkB,QAAtB3L,EAAKlI,KAAKmb,cAA2B,IAAPjT,GAAyBA,EAAGpE,QAC3D9D,KAAKif,QAAQX,QACbte,KAAKwa,GAAc,SACnBxa,KAAKgB,aAAa,QAASiC,EAAQC,GAC/BlD,KAAKyf,KAAkBzf,KAAK0f,eAC5B1f,KAAKkgB,WAEZ,CAMD,SAAAA,GACI,GAAIlgB,KAAKigB,IAAiBjgB,KAAK0f,cAC3B,OAAO1f,KACX,MAAMwB,EAAOxB,KACb,GAAIA,KAAKif,QAAQf,UAAYle,KAAK2f,GAC9B3f,KAAKif,QAAQX,QACbte,KAAKgB,aAAa,oBAClBhB,KAAKigB,IAAgB,MAEpB,CACD,MAAMxN,EAAQzS,KAAKif,QAAQd,WAC3Bne,KAAKigB,IAAgB,EACrB,MAAM1E,EAAQvb,KAAKsB,cAAa,KACxBE,EAAKke,gBAET1f,KAAKgB,aAAa,oBAAqBQ,EAAKyd,QAAQf,UAEhD1c,EAAKke,eAETle,EAAKmC,MAAMkD,IACHA,GACArF,EAAKye,IAAgB,EACrBze,EAAK0e,YACLlgB,KAAKgB,aAAa,kBAAmB6F,IAGrCrF,EAAKmf,aACR,IACH,GACHlO,GACCzS,KAAKqC,KAAK2I,WACVuQ,EAAMrQ,QAEVlL,KAAKqa,KAAKna,MAAK,KACXF,KAAKwC,eAAe+Y,EAAM,GAEjC,CACJ,CAMD,WAAAoF,GACI,MAAMC,EAAU5gB,KAAKif,QAAQf,SAC7Ble,KAAKigB,IAAgB,EACrBjgB,KAAKif,QAAQX,QACbte,KAAKgB,aAAa,YAAa4f,EAClC,ECvWL,MAAMC,GAAQ,CAAA,EACd,SAAS5kB,GAAOqK,EAAKjE,GACE,iBAARiE,IACPjE,EAAOiE,EACPA,OAAM0B,GAGV,MAAM8Y,ECHH,SAAaxa,EAAKxB,EAAO,GAAIic,GAChC,IAAIjmB,EAAMwL,EAEVya,EAAMA,GAA4B,oBAAb9Z,UAA4BA,SAC7C,MAAQX,IACRA,EAAMya,EAAI5Z,SAAW,KAAO4Z,EAAIpS,MAEjB,iBAARrI,IACH,MAAQA,EAAI9J,OAAO,KAEf8J,EADA,MAAQA,EAAI9J,OAAO,GACbukB,EAAI5Z,SAAWb,EAGfya,EAAIpS,KAAOrI,GAGpB,sBAAsB0a,KAAK1a,KAExBA,OADA,IAAuBya,EACjBA,EAAI5Z,SAAW,KAAOb,EAGtB,WAAaA,GAI3BxL,EAAMsT,EAAM9H,IAGXxL,EAAIoK,OACD,cAAc8b,KAAKlmB,EAAIqM,UACvBrM,EAAIoK,KAAO,KAEN,eAAe8b,KAAKlmB,EAAIqM,YAC7BrM,EAAIoK,KAAO,QAGnBpK,EAAIgK,KAAOhK,EAAIgK,MAAQ,IACvB,MACM6J,GADkC,IAA3B7T,EAAI6T,KAAK1J,QAAQ,KACV,IAAMnK,EAAI6T,KAAO,IAAM7T,EAAI6T,KAS/C,OAPA7T,EAAI6W,GAAK7W,EAAIqM,SAAW,MAAQwH,EAAO,IAAM7T,EAAIoK,KAAOJ,EAExDhK,EAAImmB,KACAnmB,EAAIqM,SACA,MACAwH,GACCoS,GAAOA,EAAI7b,OAASpK,EAAIoK,KAAO,GAAK,IAAMpK,EAAIoK,MAChDpK,CACX,CD7CmBomB,CAAI5a,GADnBjE,EAAOA,GAAQ,IACcyC,MAAQ,cAC/B4J,EAASoS,EAAOpS,OAChBiD,EAAKmP,EAAOnP,GACZ7M,EAAOgc,EAAOhc,KACdqc,EAAgBN,GAAMlP,IAAO7M,KAAQ+b,GAAMlP,GAAU,KAK3D,IAAI4H,EAaJ,OAjBsBlX,EAAK+e,UACvB/e,EAAK,0BACL,IAAUA,EAAKgf,WACfF,EAGA5H,EAAK,IAAImF,GAAQhQ,EAAQrM,IAGpBwe,GAAMlP,KACPkP,GAAMlP,GAAM,IAAI+M,GAAQhQ,EAAQrM,IAEpCkX,EAAKsH,GAAMlP,IAEXmP,EAAOvd,QAAUlB,EAAKkB,QACtBlB,EAAKkB,MAAQud,EAAO7R,UAEjBsK,EAAG/V,OAAOsd,EAAOhc,KAAMzC,EAClC,CAGAxI,OAAOsQ,OAAOlO,GAAQ,CAClByiB,WACApK,UACAiF,GAAItd,GACJid,QAASjd"}