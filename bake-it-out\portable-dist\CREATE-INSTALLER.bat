@echo off
echo 🚀 Bake It Out - Complete Installer Builder
echo ==========================================

echo.
echo 📦 Step 1: Building Next.js application...
call npm run build
if %errorlevel% neq 0 (
    echo ❌ Failed to build Next.js application
    pause
    exit /b 1
)

echo.
echo ✅ Next.js build completed!

echo.
echo 📱 Step 2: Creating portable package...
if not exist "dist-manual" mkdir "dist-manual"
if not exist "dist-manual\Bake-It-Out-Portable" mkdir "dist-manual\Bake-It-Out-Portable"

echo Copying application files...
xcopy /E /Y "out\*" "dist-manual\Bake-It-Out-Portable\app\" > nul
xcopy /E /Y "electron\*" "dist-manual\Bake-It-Out-Portable\electron\" > nul
xcopy /E /Y "server\*" "dist-manual\Bake-It-Out-Portable\server\" > nul
copy "package.json" "dist-manual\Bake-It-Out-Portable\" > nul

echo.
echo 📝 Creating launcher script...
echo @echo off > "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo title Bake It Out - Starting... >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo. >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo ­čžü Welcome to Bake It Out! >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo ========================== >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo. >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo ­čÜÇ Starting server... >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo cd /d "%%~dp0server" >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo start /min "Bake It Out Server" cmd /c "npm install && npm start" >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo. >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo ÔÖ│ Waiting for server to start... >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo timeout /t 8 /nobreak ^> nul >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo. >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo ­čÄ« Opening game in browser... >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo start "" "http://localhost:3000" >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo. >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo ­čžü Bake It Out is now running! >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo ­čöÇ Keep this window open while playing >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo ­čöÇ Close this window to stop the game >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo echo. >> "dist-manual\Bake-It-Out-Portable\start-game.bat"
echo pause >> "dist-manual\Bake-It-Out-Portable\start-game.bat"

echo.
echo 📝 Creating README...
echo # Bake It Out - Portable Version > "dist-manual\Bake-It-Out-Portable\README.txt"
echo. >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo ## How to Run: >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo 1. Double-click "start-game.bat" >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo 2. Wait for the server to start (about 10 seconds) >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo 3. The game will open automatically in your browser >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo 4. Enjoy baking! >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo. >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo ## Features: >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo - Single player bakery management >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo - Multiplayer support with friends >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo - Cloud save functionality >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo - Achievement system >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo - Multiple languages (English/Czech) >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo. >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo ## Requirements: >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo - Windows 10 or later >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo - Node.js (will be installed automatically) >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo - Internet connection for multiplayer and cloud saves >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo. >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo ## Support: >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo Visit: https://github.com/TAZZMC/bake-it-out >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo. >> "dist-manual\Bake-It-Out-Portable\README.txt"
echo Happy baking! ­čžü >> "dist-manual\Bake-It-Out-Portable\README.txt"

echo.
echo ✅ Portable package created successfully!

echo.
echo 📦 Step 3: Creating Windows installer...

REM Check if NSIS is installed
where makensis >nul 2>nul
if %errorlevel% neq 0 (
    echo ⚠️ NSIS not found. Trying common installation paths...
    
    if exist "C:\Program Files (x86)\NSIS\makensis.exe" (
        set "NSIS_PATH=C:\Program Files (x86)\NSIS\makensis.exe"
    ) else if exist "C:\Program Files\NSIS\makensis.exe" (
        set "NSIS_PATH=C:\Program Files\NSIS\makensis.exe"
    ) else (
        echo ❌ NSIS not found. Please install NSIS from https://nsis.sourceforge.io/
        echo.
        echo 💡 Alternative: Use the portable version in dist-manual\Bake-It-Out-Portable\
        echo.
        goto :skip_installer
    )
) else (
    set "NSIS_PATH=makensis"
)

echo Using NSIS at: %NSIS_PATH%
echo.
echo Building installer...
"%NSIS_PATH%" installer.nsi

if %errorlevel% equ 0 (
    echo ✅ Installer created successfully!
    echo 📁 Installer location: Bake-It-Out-Setup.exe
) else (
    echo ❌ Failed to create installer
    echo 💡 You can still use the portable version
)

:skip_installer

echo.
echo 🎉 Build process completed!
echo.
echo 📁 Available distributions:
if exist "Bake-It-Out-Setup.exe" echo    ✅ Windows Installer: Bake-It-Out-Setup.exe
echo    ✅ Portable Version: dist-manual\Bake-It-Out-Portable\
echo.
echo 🧁 Your Bake It Out game is ready to distribute!
echo.
echo 💡 Tips:
echo    - Use the installer for easy installation
echo    - Use the portable version for USB drives or no-install scenarios
echo    - Both versions include the complete game with multiplayer support
echo.

pause
