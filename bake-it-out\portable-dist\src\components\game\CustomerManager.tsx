'use client'

import { useState, useEffect } from 'react'
import { useLanguage } from '@/contexts/LanguageContext'
import { useGame } from '@/contexts/GameContext'

interface Customer {
  id: string
  name: string
  avatar: string
  orderItems: string[]
  patience: number
  maxPatience: number
  satisfaction: number
  status: 'entering' | 'waiting' | 'ordering' | 'served' | 'eating' | 'leaving'
  tableNumber?: number
  orderValue: number
  preferences: string[]
  mood: 'happy' | 'neutral' | 'impatient' | 'angry'
}

interface CustomerManagerProps {
  isOpen: boolean
  onClose: () => void
}

export function CustomerManager({ isOpen, onClose }: CustomerManagerProps) {
  const { t } = useLanguage()
  const { orders, player, completeOrder } = useGame()
  const [customers, setCustomers] = useState<Customer[]>([])
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null)

  // Helper functions
  const getCustomerAvatar = (name: string) => {
    const avatars = [
      '👨‍💼', '👩‍💼', '👨‍🎓', '👩‍🎓', '👨‍🍳', '👩‍🍳',
      '👨‍⚕️', '👩‍⚕️', '👨‍🎨', '👩‍🎨', '👨‍💻', '👩‍💻',
      '👨‍🔬', '👩‍🔬', '👨‍🏫', '👩‍🏫', '👨‍🎤', '👩‍🎤'
    ]
    return avatars[name.length % avatars.length]
  }

  const getCustomerPreferences = (name: string) => {
    const allPreferences = ['sweet', 'savory', 'healthy', 'indulgent', 'traditional', 'exotic']
    const hash = name.split('').reduce((a, b) => a + b.charCodeAt(0), 0)
    return allPreferences.slice(0, 2 + (hash % 3))
  }

  const getMoodFromPatience = (satisfaction: number): Customer['mood'] => {
    if (satisfaction > 80) return 'happy'
    if (satisfaction > 50) return 'neutral'
    if (satisfaction > 20) return 'impatient'
    return 'angry'
  }

  // Generate customers from orders
  useEffect(() => {
    const newCustomers: Customer[] = orders.map((order, index) => {
      const patience = order.timeLimit
      const maxPatience = 300
      const satisfaction = Math.max(0, Math.min(100, (patience / maxPatience) * 100))

      return {
        id: order.id,
        name: order.customerName,
        avatar: getCustomerAvatar(order.customerName),
        orderItems: order.items,
        patience,
        maxPatience,
        satisfaction,
        status: order.status === 'pending' ? 'waiting' :
                order.status === 'in_progress' ? 'ordering' : 'served',
        tableNumber: index + 1,
        orderValue: order.reward,
        preferences: getCustomerPreferences(order.customerName),
        mood: getMoodFromPatience(satisfaction)
      }
    })
    setCustomers(newCustomers)
  }, [orders])

  const getMoodIcon = (mood: Customer['mood']) => {
    switch (mood) {
      case 'happy': return '😊'
      case 'neutral': return '😐'
      case 'impatient': return '😤'
      case 'angry': return '😠'
    }
  }

  const getMoodColor = (mood: Customer['mood']) => {
    switch (mood) {
      case 'happy': return 'text-green-600 bg-green-100'
      case 'neutral': return 'text-yellow-600 bg-yellow-100'
      case 'impatient': return 'text-orange-600 bg-orange-100'
      case 'angry': return 'text-red-600 bg-red-100'
    }
  }

  const getStatusIcon = (status: Customer['status']) => {
    switch (status) {
      case 'entering': return '🚶'
      case 'waiting': return '⏰'
      case 'ordering': return '📝'
      case 'served': return '🍽️'
      case 'eating': return '😋'
      case 'leaving': return '👋'
    }
  }

  const handleServeCustomer = (customer: Customer) => {
    completeOrder(customer.id)
    setCustomers(prev => prev.map(c => 
      c.id === customer.id ? { ...c, status: 'served' } : c
    ))
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-500 to-purple-500 p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">👥 {t('customers.manager.title', 'Customer Manager')}</h2>
              <p className="text-blue-100 text-sm">
                {t('customers.manager.subtitle', 'Monitor and serve your customers')}
              </p>
            </div>
            <button
              onClick={onClose}
              className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors"
            >
              ✕ {t('common.close', 'Close')}
            </button>
          </div>
        </div>

        <div className="flex h-[70vh]">
          {/* Customer List */}
          <div className="w-1/2 p-6 border-r border-gray-200 overflow-y-auto">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">
              📋 {t('customers.current.list', 'Current Customers')} ({customers.length})
            </h3>
            
            <div className="space-y-3">
              {customers.map(customer => (
                <div
                  key={customer.id}
                  onClick={() => setSelectedCustomer(customer)}
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md ${
                    selectedCustomer?.id === customer.id
                      ? 'border-blue-400 bg-blue-50'
                      : 'border-gray-300 bg-white hover:border-gray-400'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{customer.avatar}</span>
                      <div>
                        <h4 className="font-medium text-gray-800">{customer.name}</h4>
                        <p className="text-sm text-gray-600">
                          {t('customers.table', 'Table')} {customer.tableNumber}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getMoodColor(customer.mood)}`}>
                        {getMoodIcon(customer.mood)} {customer.mood}
                      </div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-2">
                      <span className="text-lg">{getStatusIcon(customer.status)}</span>
                      <span className="text-sm text-gray-600 capitalize">{customer.status}</span>
                    </div>
                    <div className="text-sm font-bold text-green-600">
                      ${customer.orderValue}
                    </div>
                  </div>

                  {/* Patience Bar */}
                  <div className="mt-2">
                    <div className="flex justify-between text-xs text-gray-500 mb-1">
                      <span>{t('customers.patience', 'Patience')}</span>
                      <span>{Math.round(customer.satisfaction)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full transition-all duration-300 ${
                          customer.satisfaction > 60 ? 'bg-green-500' :
                          customer.satisfaction > 30 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${customer.satisfaction}%` }}
                      ></div>
                    </div>
                  </div>
                </div>
              ))}

              {customers.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  <div className="text-4xl mb-2">🏪</div>
                  <p>{t('customers.no.customers', 'No customers currently')}</p>
                  <p className="text-sm">{t('customers.waiting.for.orders', 'Waiting for new orders...')}</p>
                </div>
              )}
            </div>
          </div>

          {/* Customer Details */}
          <div className="w-1/2 p-6 overflow-y-auto">
            {selectedCustomer ? (
              <div>
                <div className="text-center mb-6">
                  <div className="text-6xl mb-2">{selectedCustomer.avatar}</div>
                  <h3 className="text-xl font-bold text-gray-800">{selectedCustomer.name}</h3>
                  <p className="text-gray-600">{t('customers.table', 'Table')} {selectedCustomer.tableNumber}</p>
                </div>

                {/* Order Details */}
                <div className="bg-gray-50 rounded-lg p-4 mb-4">
                  <h4 className="font-semibold text-gray-800 mb-3">📝 {t('customers.order.details', 'Order Details')}</h4>
                  <div className="space-y-2">
                    {selectedCustomer.orderItems.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-white rounded border">
                        <div className="flex items-center space-x-2">
                          <span>🧁</span>
                          <span className="font-medium">{item}</span>
                        </div>
                        <span className="text-green-600 font-bold">${Math.round(selectedCustomer.orderValue / selectedCustomer.orderItems.length)}</span>
                      </div>
                    ))}
                  </div>
                  <div className="mt-3 pt-3 border-t border-gray-200">
                    <div className="flex justify-between font-bold">
                      <span>{t('customers.total', 'Total')}:</span>
                      <span className="text-green-600">${selectedCustomer.orderValue}</span>
                    </div>
                  </div>
                </div>

                {/* Customer Info */}
                <div className="bg-blue-50 rounded-lg p-4 mb-4">
                  <h4 className="font-semibold text-blue-800 mb-3">ℹ️ {t('customers.info', 'Customer Info')}</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>{t('customers.mood', 'Mood')}:</span>
                      <span className={`font-medium ${getMoodColor(selectedCustomer.mood)}`}>
                        {getMoodIcon(selectedCustomer.mood)} {selectedCustomer.mood}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>{t('customers.status', 'Status')}:</span>
                      <span className="font-medium capitalize">
                        {getStatusIcon(selectedCustomer.status)} {selectedCustomer.status}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>{t('customers.patience', 'Patience')}:</span>
                      <span className="font-medium">{Math.round(selectedCustomer.satisfaction)}%</span>
                    </div>
                  </div>
                </div>

                {/* Preferences */}
                <div className="bg-purple-50 rounded-lg p-4 mb-4">
                  <h4 className="font-semibold text-purple-800 mb-3">❤️ {t('customers.preferences', 'Preferences')}</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedCustomer.preferences.map((pref, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-purple-200 text-purple-800 rounded-full text-sm font-medium"
                      >
                        {pref}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Actions */}
                {selectedCustomer.status === 'waiting' && (
                  <div className="space-y-2">
                    <button
                      onClick={() => handleServeCustomer(selectedCustomer)}
                      className="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-4 rounded-lg transition-colors"
                    >
                      🍽️ {t('customers.serve.order', 'Serve Order')}
                    </button>
                  </div>
                )}
              </div>
            ) : (
              <div className="text-center py-12 text-gray-500">
                <div className="text-4xl mb-4">👆</div>
                <p className="text-lg">{t('customers.select.customer', 'Select a customer')}</p>
                <p className="text-sm">{t('customers.select.to.view.details', 'Select a customer to view details')}</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
