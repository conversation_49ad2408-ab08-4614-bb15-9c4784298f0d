<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %></title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.socket.io/4.7.4/socket.io.min.js"></script>
    <style>
        .sidebar-transition {
            transition: transform 0.3s ease-in-out;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .stat-card-blue {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stat-card-green {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .stat-card-orange {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div id="sidebar" class="bg-gray-800 text-white w-64 sidebar-transition">
            <div class="p-4">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-bread-slice text-orange-400 text-2xl"></i>
                    <h1 class="text-xl font-bold">Bake It Out</h1>
                </div>
                <p class="text-gray-400 text-sm">Server Dashboard</p>
            </div>
            
            <nav class="mt-8">
                <a href="/dashboard" class="flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white <%= currentPage === 'dashboard' ? 'bg-gray-700 text-white' : '' %>">
                    <i class="fas fa-tachometer-alt mr-3"></i>
                    Dashboard
                </a>
                <a href="/dashboard/users" class="flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white <%= currentPage === 'users' ? 'bg-gray-700 text-white' : '' %>">
                    <i class="fas fa-users mr-3"></i>
                    Users
                </a>
                <a href="/dashboard/rooms" class="flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white <%= currentPage === 'rooms' ? 'bg-gray-700 text-white' : '' %>">
                    <i class="fas fa-gamepad mr-3"></i>
                    Game Rooms
                </a>
                <a href="/dashboard/saves" class="flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white <%= currentPage === 'saves' ? 'bg-gray-700 text-white' : '' %>">
                    <i class="fas fa-cloud mr-3"></i>
                    Cloud Saves
                </a>
                <a href="/dashboard/analytics" class="flex items-center px-4 py-3 text-gray-300 hover:bg-gray-700 hover:text-white <%= currentPage === 'analytics' ? 'bg-gray-700 text-white' : '' %>">
                    <i class="fas fa-chart-bar mr-3"></i>
                    Analytics
                </a>
            </nav>
            
            <div class="absolute bottom-0 w-64 p-4 border-t border-gray-700">
                <div class="flex items-center space-x-3">
                    <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                    <div>
                        <p class="text-sm font-medium"><%= user.username %></p>
                        <p class="text-xs text-gray-400">Administrator</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="flex items-center justify-between px-6 py-4">
                    <div class="flex items-center space-x-4">
                        <button id="sidebar-toggle" class="text-gray-500 hover:text-gray-700 lg:hidden">
                            <i class="fas fa-bars"></i>
                        </button>
                        <h2 class="text-xl font-semibold text-gray-800"><%= title %></h2>
                    </div>
                    
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-2 text-sm text-gray-600">
                            <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                            <span>Server Online</span>
                        </div>
                        <div class="text-sm text-gray-500" id="current-time"></div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <%- body %>
            </main>
        </div>
    </div>

    <script>
        // Sidebar toggle
        document.getElementById('sidebar-toggle').addEventListener('click', function() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('-translate-x-full');
        });

        // Update current time
        function updateTime() {
            const now = new Date();
            document.getElementById('current-time').textContent = now.toLocaleString();
        }
        updateTime();
        setInterval(updateTime, 1000);

        // Auto-refresh stats every 30 seconds
        setInterval(async function() {
            try {
                const response = await fetch('/dashboard/api/stats');
                const stats = await response.json();
                updateStatsDisplay(stats);
            } catch (error) {
                console.error('Failed to refresh stats:', error);
            }
        }, 30000);

        function updateStatsDisplay(stats) {
            // Update stats if elements exist
            const elements = {
                'total-users': stats.users?.total,
                'active-users': stats.users?.active,
                'total-saves': stats.saves?.total,
                'active-rooms': stats.rooms?.active
            };

            for (const [id, value] of Object.entries(elements)) {
                const element = document.getElementById(id);
                if (element && value !== undefined) {
                    element.textContent = value.toLocaleString();
                }
            }
        }

        // Socket.IO connection for real-time updates
        const token = new URLSearchParams(window.location.search).get('token');
        if (token) {
            const socket = io({
                auth: { token }
            });

            socket.on('connect', () => {
                console.log('Connected to server');
            });

            socket.on('stats-update', (stats) => {
                updateStatsDisplay(stats);
            });

            socket.on('disconnect', () => {
                console.log('Disconnected from server');
            });
        }
    </script>
</body>
</html>
