'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/Button'
import { useLanguage } from '@/contexts/LanguageContext'
import { useCloudSave } from '@/contexts/CloudSaveContext'
import { useGame } from '@/contexts/GameContext'
import { saveSystem, GameSave } from '@/lib/saveSystem'
import { fileSaveSystem, FileSaveMetadata } from '@/lib/fileSaveSystem'
import { CloudSaveModal } from '@/components/cloud/CloudSaveModal'

interface SaveSlot {
  id: number
  name: string
  timestamp: number
  playerLevel: number
  money: number
  bakeryName: string
  playTime: number
  isEmpty: boolean
  data?: GameSave
}

interface SaveLoadModalProps {
  isOpen: boolean
  onClose: () => void
  mode: 'save' | 'load'
  onSaveSuccess?: () => void
  onLoadSuccess?: () => void
}

export function SaveLoadModal({ 
  isOpen, 
  onClose, 
  mode, 
  onSaveSuccess, 
  onLoadSuccess 
}: SaveLoadModalProps) {
  const { t } = useLanguage()
  const { player, saveGameState, loadGameState } = useGame()
  const { isAuthenticated } = useCloudSave()
  const [saveSlots, setSaveSlots] = useState<SaveSlot[]>([])
  const [fileSaves, setFileSaves] = useState<FileSaveMetadata[]>([])
  const [selectedSlot, setSelectedSlot] = useState<number | null>(null)
  const [selectedFile, setSelectedFile] = useState<string | null>(null)
  const [saveName, setSaveName] = useState('')
  const [saveType, setSaveType] = useState<'local' | 'file' | 'cloud'>('local')
  const [showCloudModal, setShowCloudModal] = useState(false)
  const [loading, setLoading] = useState(false)
  const [showConfirmOverwrite, setShowConfirmOverwrite] = useState(false)
  const [useFileSystem, setUseFileSystem] = useState(false)

  useEffect(() => {
    if (isOpen) {
      loadSaveSlots()
      loadFileSaves()
      // Check if we're in Electron environment
      setUseFileSystem(typeof window !== 'undefined' && window.electronAPI !== undefined)
    }
  }, [isOpen])

  const loadFileSaves = async () => {
    try {
      const files = await fileSaveSystem.getSaveFiles()
      setFileSaves(files)
    } catch (error) {
      console.error('Failed to load file saves:', error)
    }
  }

  const loadSaveSlots = () => {
    const slots: SaveSlot[] = []
    
    // Load up to 8 save slots
    for (let i = 1; i <= 8; i++) {
      const slotKey = `bakeItOut_save_slot_${i}`
      const slotData = localStorage.getItem(slotKey)
      
      if (slotData) {
        try {
          const save: GameSave = JSON.parse(slotData)
          slots.push({
            id: i,
            name: save.player.name || `Save ${i}`,
            timestamp: save.timestamp,
            playerLevel: save.player.level,
            money: save.player.money,
            bakeryName: save.bakeries?.[0]?.name || 'Main Bakery',
            playTime: save.player.playTime || 0,
            isEmpty: false,
            data: save
          })
        } catch (error) {
          console.error(`Failed to load save slot ${i}:`, error)
          slots.push(createEmptySlot(i))
        }
      } else {
        slots.push(createEmptySlot(i))
      }
    }
    
    setSaveSlots(slots)
  }

  const createEmptySlot = (id: number): SaveSlot => ({
    id,
    name: `Empty Slot ${id}`,
    timestamp: 0,
    playerLevel: 0,
    money: 0,
    bakeryName: '',
    playTime: 0,
    isEmpty: true
  })

  const handleSave = async (slotId: number) => {
    if (!selectedSlot) return

    const slot = saveSlots.find(s => s.id === slotId)
    if (!slot?.isEmpty && !showConfirmOverwrite) {
      setShowConfirmOverwrite(true)
      return
    }

    setLoading(true)
    try {
      const success = await saveGameState(slotId, saveName || `Save ${slotId}`)
      if (success) {
        onSaveSuccess?.()
        loadSaveSlots() // Refresh the slots
        setShowConfirmOverwrite(false)
        setSaveName('')
      }
    } catch (error) {
      console.error('Save failed:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLoad = async (slotId: number) => {
    const slot = saveSlots.find(s => s.id === slotId)
    if (!slot || slot.isEmpty) return

    setLoading(true)
    try {
      const success = await loadGameState(slotId)
      if (success) {
        onLoadSuccess?.()
        onClose()
      }
    } catch (error) {
      console.error('Load failed:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteSave = (slotId: number) => {
    const slotKey = `bakeItOut_save_slot_${slotId}`
    localStorage.removeItem(slotKey)
    loadSaveSlots()
  }

  const handleSaveToFile = async () => {
    if (!saveName.trim()) return

    setLoading(true)
    try {
      const gameData = await saveGameState(0, saveName) // Get game data
      if (gameData) {
        const fileName = `${saveName.replace(/[^a-zA-Z0-9]/g, '_')}_${Date.now()}.json`
        const success = await fileSaveSystem.saveToFile(gameData, fileName)
        if (success) {
          onSaveSuccess?.()
          loadFileSaves()
          setSaveName('')
        }
      }
    } catch (error) {
      console.error('File save failed:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleLoadFromFile = async (fileName: string) => {
    setLoading(true)
    try {
      const gameData = await fileSaveSystem.loadFromFile(fileName)
      if (gameData) {
        // Load the game data into the game state
        const success = await loadGameState(-1) // Special slot for file loads
        if (success) {
          onLoadSuccess?.()
          onClose()
        }
      }
    } catch (error) {
      console.error('File load failed:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDeleteFile = async (fileName: string) => {
    try {
      await fileSaveSystem.deleteSaveFile(fileName)
      loadFileSaves()
    } catch (error) {
      console.error('Failed to delete file:', error)
    }
  }

  const handleExportSave = async () => {
    try {
      const gameData = await saveGameState(0, 'Export') // Get current game data
      if (gameData) {
        await fileSaveSystem.exportSave(gameData, saveName || 'bake-it-out-save')
      }
    } catch (error) {
      console.error('Export failed:', error)
    }
  }

  const handleImportSave = async () => {
    try {
      const gameData = await fileSaveSystem.importSave()
      if (gameData) {
        // Load the imported data
        const success = await loadGameState(-1) // Special slot for imports
        if (success) {
          onLoadSuccess?.()
          onClose()
        }
      }
    } catch (error) {
      console.error('Import failed:', error)
    }
  }

  const formatTimestamp = (timestamp: number) => {
    if (!timestamp) return ''
    return new Date(timestamp).toLocaleString()
  }

  const formatPlayTime = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    return `${hours}h ${minutes}m`
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-500 to-purple-500 p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">
                {mode === 'save' ? '💾 Save Game' : '📁 Load Game'}
              </h2>
              <p className="text-blue-100 text-sm">
                {mode === 'save' 
                  ? t('saveLoad.saveDesc', 'Choose a slot to save your progress')
                  : t('saveLoad.loadDesc', 'Select a save file to load')
                }
              </p>
            </div>
            <Button
              variant="secondary"
              size="sm"
              className="bg-white/20 hover:bg-white/30 text-white border-white/30"
              onClick={onClose}
            >
              ✕
            </Button>
          </div>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <div className="flex">
            <button
              className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
                saveType === 'local'
                  ? 'border-blue-500 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => {
                setSaveType('local')
                setSelectedSlot(1)
                setSelectedFile(null)
              }}
            >
              📁 {t('saveLoad.local_saves', 'Local Saves')}
            </button>
            {useFileSystem && (
              <button
                className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
                  saveType === 'file'
                    ? 'border-blue-500 text-blue-600 bg-blue-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
                onClick={() => {
                  setSaveType('file')
                  setSelectedSlot(null)
                  setSelectedFile(null)
                }}
              >
                💾 {t('saveLoad.file_saves', 'File Saves')}
              </button>
            )}
            <button
              className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
                saveType === 'cloud'
                  ? 'border-blue-500 text-blue-600 bg-blue-50'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
              onClick={() => {
                setSaveType('cloud')
                setSelectedSlot(null)
                setSelectedFile(null)
                setShowCloudModal(true)
              }}
            >
              ☁️ {t('saveLoad.cloud_saves', 'Cloud Saves')}
              {isAuthenticated && <span className="ml-1 text-green-500">●</span>}
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[70vh]">
          {/* Save Name Input (Save Mode Only) */}
          {mode === 'save' && selectedSlot && (
            <div className="mb-6 p-4 bg-blue-50 rounded-lg">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {t('saveLoad.saveName', 'Save Name')}
              </label>
              <input
                type="text"
                value={saveName}
                onChange={(e) => setSaveName(e.target.value)}
                placeholder={`Save ${selectedSlot}`}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          )}

          {/* Save Slots Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {saveSlots.map((slot) => (
              <div
                key={slot.id}
                className={`border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                  selectedSlot === slot.id
                    ? 'border-blue-500 bg-blue-50'
                    : slot.isEmpty
                    ? 'border-gray-200 bg-gray-50 hover:border-gray-300'
                    : 'border-gray-300 bg-white hover:border-blue-300 hover:shadow-md'
                }`}
                onClick={() => setSelectedSlot(slot.id)}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gradient-to-br from-orange-400 to-yellow-400 rounded-lg flex items-center justify-center text-white font-bold text-lg">
                      {slot.id}
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-800">
                        {slot.isEmpty ? `Slot ${slot.id}` : slot.name}
                      </h3>
                      {!slot.isEmpty && (
                        <p className="text-sm text-gray-500">
                          {formatTimestamp(slot.timestamp)}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  {!slot.isEmpty && (
                    <Button
                      variant="secondary"
                      size="sm"
                      className="text-red-600 hover:bg-red-50"
                      onClick={(e) => {
                        e.stopPropagation()
                        handleDeleteSave(slot.id)
                      }}
                    >
                      🗑️
                    </Button>
                  )}
                </div>

                {slot.isEmpty ? (
                  <div className="text-center py-8 text-gray-400">
                    <div className="text-3xl mb-2">📄</div>
                    <p className="text-sm">{t('saveLoad.emptySlot', 'Empty Slot')}</p>
                  </div>
                ) : (
                  <div className="space-y-2">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Level:</span>
                        <span className="ml-2 font-medium">{slot.playerLevel}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Money:</span>
                        <span className="ml-2 font-medium">${slot.money}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Bakery:</span>
                        <span className="ml-2 font-medium">{slot.bakeryName}</span>
                      </div>
                      <div>
                        <span className="text-gray-500">Play Time:</span>
                        <span className="ml-2 font-medium">{formatPlayTime(slot.playTime)}</span>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 flex justify-between items-center">
          <div className="text-sm text-gray-500">
            {selectedSlot && (
              <span>
                {mode === 'save' 
                  ? t('saveLoad.selectedSaveSlot', `Selected: Slot ${selectedSlot}`)
                  : t('saveLoad.selectedLoadSlot', `Selected: Slot ${selectedSlot}`)
                }
              </span>
            )}
          </div>
          
          <div className="flex space-x-3">
            <Button variant="secondary" onClick={onClose}>
              {t('common.cancel', 'Cancel')}
            </Button>
            
            {mode === 'save' ? (
              <Button
                variant="primary"
                onClick={() => selectedSlot && handleSave(selectedSlot)}
                disabled={!selectedSlot || loading}
                className="bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600"
              >
                {loading ? '💾 Saving...' : '💾 Save Game'}
              </Button>
            ) : (
              <Button
                variant="primary"
                onClick={() => selectedSlot && handleLoad(selectedSlot)}
                disabled={!selectedSlot || loading || saveSlots.find(s => s.id === selectedSlot)?.isEmpty}
                className="bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600"
              >
                {loading ? '📁 Loading...' : '📁 Load Game'}
              </Button>
            )}
          </div>
        </div>

        {/* Confirm Overwrite Dialog */}
        {showConfirmOverwrite && (
          <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
            <div className="bg-white rounded-lg p-6 max-w-md mx-4">
              <h3 className="text-lg font-semibold text-gray-800 mb-4">
                {t('saveLoad.confirmOverwrite', 'Overwrite Save?')}
              </h3>
              <p className="text-gray-600 mb-6">
                {t('saveLoad.overwriteWarning', 'This will overwrite the existing save. This action cannot be undone.')}
              </p>
              <div className="flex space-x-3">
                <Button
                  variant="secondary"
                  onClick={() => setShowConfirmOverwrite(false)}
                >
                  {t('common.cancel', 'Cancel')}
                </Button>
                <Button
                  variant="primary"
                  className="bg-red-500 hover:bg-red-600"
                  onClick={() => selectedSlot && handleSave(selectedSlot)}
                >
                  {t('saveLoad.overwrite', 'Overwrite')}
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Cloud Save Modal */}
      <CloudSaveModal
        isOpen={showCloudModal}
        onClose={() => setShowCloudModal(false)}
        mode={mode}
        currentGameData={mode === 'save' ? { player, gameState: {} } : undefined}
        onLoadGame={(gameData) => {
          // Handle cloud save load
          if (gameData && onLoadSuccess) {
            onLoadSuccess()
            onClose()
          }
        }}
      />
    </div>
  )
}
