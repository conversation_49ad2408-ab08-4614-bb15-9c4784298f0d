'use client'

import { useState, useEffect } from 'react'
import { useLanguage } from '@/contexts/LanguageContext'
import { useGame } from '@/contexts/GameContext'
import { EquipmentData } from './Equipment'

interface Customer {
  id: string
  name: string
  avatar: string
  position: { x: number; y: number }
  order?: string
  satisfaction: number
  waitTime: number
  status: 'entering' | 'waiting' | 'ordering' | 'eating' | 'leaving'
}

interface BakeryLayoutProps {
  equipment: EquipmentData[]
  onEquipmentClick: (equipmentId: string, equipmentName: string) => void
}

export function BakeryLayout({ equipment, onEquipmentClick }: BakeryLayoutProps) {
  const { t } = useLanguage()
  const { orders, player } = useGame()
  const [customers, setCustomers] = useState<Customer[]>([])
  const [selectedArea, setSelectedArea] = useState<'kitchen' | 'dining' | 'counter'>('kitchen')

  // Generate customers based on orders
  useEffect(() => {
    const newCustomers: Customer[] = orders.map((order, index) => ({
      id: order.id,
      name: order.customerName,
      avatar: getCustomerAvatar(order.customerName),
      position: getDiningPosition(index),
      order: order.items[0],
      satisfaction: 100 - (order.timeLimit < 180 ? 20 : 0),
      waitTime: 300 - order.timeLimit,
      status: order.status === 'pending' ? 'waiting' : order.status === 'in_progress' ? 'ordering' : 'eating'
    }))
    setCustomers(newCustomers)
  }, [orders])

  const getCustomerAvatar = (name: string) => {
    const avatars = ['👨‍💼', '👩‍💼', '👨‍🎓', '👩‍🎓', '👨‍🍳', '👩‍🍳', '👨‍⚕️', '👩‍⚕️', '👨‍🎨', '👩‍🎨']
    return avatars[name.length % avatars.length]
  }

  const getDiningPosition = (index: number) => {
    const positions = [
      { x: 20, y: 20 }, { x: 60, y: 20 }, { x: 100, y: 20 },
      { x: 20, y: 60 }, { x: 60, y: 60 }, { x: 100, y: 60 },
      { x: 20, y: 100 }, { x: 60, y: 100 }, { x: 100, y: 100 }
    ]
    return positions[index % positions.length]
  }

  const getEquipmentIcon = (type: string) => {
    switch (type) {
      case 'oven': return '🔥'
      case 'mixer': return '🥄'
      case 'counter': return '🏪'
      case 'auto_oven': return '🤖'
      case 'auto_mixer': return '⚙️'
      case 'conveyor': return '🔄'
      default: return '📦'
    }
  }

  const getEquipmentStatus = (eq: EquipmentData) => {
    if (eq.isActive) return 'bg-green-100 border-green-400'
    if (eq.automationLevel > 0) return 'bg-blue-100 border-blue-400'
    return 'bg-gray-100 border-gray-300'
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-orange-800">
          🏪 {t('bakery.layout.title', 'Bakery Layout')}
        </h2>
        <div className="flex space-x-2">
          <button
            onClick={() => setSelectedArea('kitchen')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              selectedArea === 'kitchen'
                ? 'bg-orange-500 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            👨‍🍳 {t('bakery.kitchen', 'Kitchen')}
          </button>
          <button
            onClick={() => setSelectedArea('dining')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              selectedArea === 'dining'
                ? 'bg-orange-500 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            🍽️ {t('bakery.dining', 'Dining Area')}
          </button>
          <button
            onClick={() => setSelectedArea('counter')}
            className={`px-4 py-2 rounded-lg font-medium transition-colors ${
              selectedArea === 'counter'
                ? 'bg-orange-500 text-white'
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            🛒 {t('bakery.counter', 'Service Counter')}
          </button>
        </div>
      </div>

      {/* Main Layout Area */}
      <div className="relative bg-gradient-to-br from-yellow-50 to-orange-50 rounded-lg border-2 border-orange-200 min-h-[500px] overflow-hidden">
        
        {/* Kitchen Area */}
        {selectedArea === 'kitchen' && (
          <div className="p-6">
            <div className="grid grid-cols-3 gap-4 mb-6">
              <div className="bg-red-100 rounded-lg p-4 border-2 border-red-300">
                <h3 className="font-semibold text-red-800 mb-3">🔥 {t('bakery.baking.area', 'Baking Area')}</h3>
                <div className="space-y-2">
                  {equipment.filter(eq => eq.type.includes('oven')).map(eq => (
                    <div
                      key={eq.id}
                      onClick={() => onEquipmentClick(eq.id, eq.name)}
                      className={`p-3 rounded-lg border-2 cursor-pointer transition-all hover:scale-105 ${getEquipmentStatus(eq)}`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="text-xl">{getEquipmentIcon(eq.type)}</span>
                          <span className="font-medium text-sm">{eq.name}</span>
                        </div>
                        {eq.isActive && (
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        )}
                      </div>
                      {eq.currentRecipe && (
                        <div className="text-xs text-gray-600 mt-1">
                          Making: {eq.currentRecipe}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-blue-100 rounded-lg p-4 border-2 border-blue-300">
                <h3 className="font-semibold text-blue-800 mb-3">🥄 {t('bakery.prep.area', 'Prep Area')}</h3>
                <div className="space-y-2">
                  {equipment.filter(eq => eq.type.includes('mixer')).map(eq => (
                    <div
                      key={eq.id}
                      onClick={() => onEquipmentClick(eq.id, eq.name)}
                      className={`p-3 rounded-lg border-2 cursor-pointer transition-all hover:scale-105 ${getEquipmentStatus(eq)}`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="text-xl">{getEquipmentIcon(eq.type)}</span>
                          <span className="font-medium text-sm">{eq.name}</span>
                        </div>
                        {eq.isActive && (
                          <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div className="bg-purple-100 rounded-lg p-4 border-2 border-purple-300">
                <h3 className="font-semibold text-purple-800 mb-3">⚙️ {t('bakery.automation.area', 'Automation')}</h3>
                <div className="space-y-2">
                  {equipment.filter(eq => eq.type.includes('conveyor') || eq.automationLevel > 0).map(eq => (
                    <div
                      key={eq.id}
                      onClick={() => onEquipmentClick(eq.id, eq.name)}
                      className={`p-3 rounded-lg border-2 cursor-pointer transition-all hover:scale-105 ${getEquipmentStatus(eq)}`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <span className="text-xl">{getEquipmentIcon(eq.type)}</span>
                          <span className="font-medium text-sm">{eq.name}</span>
                        </div>
                        <div className="text-xs bg-purple-200 px-2 py-1 rounded">
                          Auto Lv.{eq.automationLevel}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Kitchen Stats */}
            <div className="bg-white rounded-lg p-4 border border-gray-300">
              <h4 className="font-semibold text-gray-800 mb-2">📊 {t('bakery.kitchen.stats', 'Kitchen Stats')}</h4>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600">{equipment.filter(eq => eq.isActive).length}</div>
                  <div className="text-gray-600">{t('bakery.active.equipment', 'Active Equipment')}</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">{equipment.filter(eq => eq.automationLevel > 0).length}</div>
                  <div className="text-gray-600">{t('bakery.automated.equipment', 'Automated Equipment')}</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-purple-600">{Math.round(equipment.reduce((sum, eq) => sum + eq.efficiency, 0) / equipment.length * 100)}%</div>
                  <div className="text-gray-600">{t('bakery.efficiency', 'Efficiency')}</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Dining Area */}
        {selectedArea === 'dining' && (
          <div className="p-6">
            <div className="grid grid-cols-4 gap-4 mb-6">
              {/* Tables */}
              {[1, 2, 3, 4, 5, 6, 7, 8].map(tableNum => {
                const customer = customers[tableNum - 1]
                return (
                  <div
                    key={tableNum}
                    className="bg-white rounded-lg p-4 border-2 border-brown-300 relative"
                    style={{ backgroundColor: '#8B4513', color: 'white' }}
                  >
                    <div className="text-center mb-2">
                      <div className="text-2xl">🪑</div>
                      <div className="text-xs">Table {tableNum}</div>
                    </div>
                    
                    {customer ? (
                      <div className="text-center">
                        <div className="text-2xl mb-1">{customer.avatar}</div>
                        <div className="text-xs font-medium">{customer.name}</div>
                        <div className="text-xs opacity-75">{customer.order}</div>
                        <div className="mt-2">
                          <div className={`w-full h-1 rounded ${
                            customer.satisfaction > 70 ? 'bg-green-400' :
                            customer.satisfaction > 40 ? 'bg-yellow-400' : 'bg-red-400'
                          }`} style={{ width: `${customer.satisfaction}%` }}></div>
                        </div>
                      </div>
                    ) : (
                      <div className="text-center text-gray-400">
                        <div className="text-xs">Empty</div>
                      </div>
                    )}
                  </div>
                )
              })}
            </div>

            {/* Dining Stats */}
            <div className="bg-white rounded-lg p-4 border border-gray-300">
              <h4 className="font-semibold text-gray-800 mb-2">🍽️ {t('bakery.dining.stats', 'Dining Stats')}</h4>
              <div className="grid grid-cols-4 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-lg font-bold text-green-600">{customers.length}</div>
                  <div className="text-gray-600">{t('bakery.current.customers', 'Current Customers')}</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-blue-600">{customers.filter(c => c.status === 'waiting').length}</div>
                  <div className="text-gray-600">{t('bakery.waiting.customers', 'Waiting')}</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-purple-600">{customers.filter(c => c.status === 'eating').length}</div>
                  <div className="text-gray-600">{t('bakery.eating.customers', 'Eating')}</div>
                </div>
                <div className="text-center">
                  <div className="text-lg font-bold text-orange-600">
                    {customers.length > 0 ? Math.round(customers.reduce((sum, c) => sum + c.satisfaction, 0) / customers.length) : 100}%
                  </div>
                  <div className="text-gray-600">{t('bakery.avg.satisfaction', 'Avg Satisfaction')}</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Service Counter */}
        {selectedArea === 'counter' && (
          <div className="p-6">
            <div className="bg-gradient-to-r from-yellow-100 to-orange-100 rounded-lg p-6 border-2 border-orange-300">
              <h3 className="text-xl font-bold text-orange-800 mb-4 text-center">
                🛒 {t('bakery.service.counter', 'Service Counter')}
              </h3>
              
              {/* Display Case */}
              <div className="bg-white rounded-lg p-4 border-2 border-gray-300 mb-4">
                <h4 className="font-semibold text-gray-800 mb-3">🧁 {t('bakery.display.case', 'Display Case')}</h4>
                <div className="grid grid-cols-4 gap-3">
                  {['🍪', '🧁', '🥐', '🍞', '🥧', '🍰', '🥨', '🧇'].map((item, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-3 text-center border">
                      <div className="text-2xl mb-1">{item}</div>
                      <div className="text-xs text-gray-600">Fresh</div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Order Queue */}
              <div className="bg-white rounded-lg p-4 border-2 border-gray-300">
                <h4 className="font-semibold text-gray-800 mb-3">📋 {t('bakery.order.queue', 'Order Queue')}</h4>
                <div className="space-y-2">
                  {orders.slice(0, 3).map((order, index) => (
                    <div key={order.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg">{getCustomerAvatar(order.customerName)}</span>
                        <div>
                          <div className="font-medium text-sm">{order.customerName}</div>
                          <div className="text-xs text-gray-600">{order.items[0]}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-bold text-green-600">${order.reward}</div>
                        <div className="text-xs text-gray-500">{Math.floor(order.timeLimit / 60)}m left</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Bottom Status Bar */}
      <div className="mt-4 bg-gray-100 rounded-lg p-3">
        <div className="flex justify-between items-center text-sm">
          <div className="flex space-x-4">
            <span>💰 ${player.money}</span>
            <span>⭐ Level {player.level}</span>
            <span>📦 {orders.length} Orders</span>
          </div>
          <div className="text-gray-600">
            {t('bakery.last.updated', 'Last updated')}: {new Date().toLocaleTimeString()}
          </div>
        </div>
      </div>
    </div>
  )
}
