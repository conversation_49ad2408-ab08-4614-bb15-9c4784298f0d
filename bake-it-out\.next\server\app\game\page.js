(()=>{var a={};a.id=925,a.ids=[925],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},1105:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\coding stuff\\\\bake it out\\\\bake-it-out\\\\src\\\\app\\\\game\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\app\\game\\page.tsx","default")},1630:a=>{"use strict";a.exports=require("http")},1645:a=>{"use strict";a.exports=require("net")},1820:a=>{"use strict";a.exports=require("os")},3033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:a=>{"use strict";a.exports=require("path")},3997:a=>{"use strict";a.exports=require("tty")},4075:a=>{"use strict";a.exports=require("zlib")},4631:a=>{"use strict";a.exports=require("tls")},4663:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>I});var d=c(687),e=c(3210),f=c(4393),g=c(2728),h=c(2643),i=c(3211),j=c(4579),k=c(9721);function l({isOpen:a,onClose:b}){let{player:c,inventory:i}=(0,g.I)(),{t:j}=(0,f.o)(),[l,m]=(0,e.useState)("all");if(!a)return null;let n=(0,k.x0)(c.level),o="all"===l?n:n.filter(a=>a.category===l),p=a=>a.ingredients.every(a=>{let b=i.find(b=>b.name===a.name);return b&&b.quantity>=a.quantity}),q=[{id:"all",name:j("recipes.all"),icon:"\uD83C\uDF7D️"},{id:"cookies",name:j("recipes.cookies"),icon:"\uD83C\uDF6A"},{id:"cakes",name:j("recipes.cakes"),icon:"\uD83E\uDDC1"},{id:"bread",name:j("recipes.bread"),icon:"\uD83C\uDF5E"},{id:"pastries",name:j("recipes.pastries"),icon:"\uD83E\uDD50"}];return(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:j("modal.recipes.title")}),(0,d.jsx)(h.$,{variant:"secondary",onClick:b,children:j("game.close")})]})}),(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:q.map(a=>(0,d.jsxs)(h.$,{variant:l===a.id?"primary":"secondary",size:"sm",onClick:()=>m(a.id),children:[a.icon," ",a.name]},a.id))}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto",children:o.map(a=>{let b;return(0,d.jsxs)("div",{className:`p-4 rounded-lg border-2 ${p(a)?"border-green-300 bg-green-50":"border-gray-300 bg-gray-50"}`,children:[(0,d.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800",children:a.name}),(0,d.jsxs)("span",{className:"text-sm text-green-600",children:["$",a.basePrice]})]}),(0,d.jsxs)("div",{className:"text-xs text-gray-500 mb-2",children:[(b=a.difficulty,"⭐".repeat(b)+"☆".repeat(5-b))," • ⏱️ ",(a=>{let b=Math.floor(a/60);return`${b}:${(a%60).toString().padStart(2,"0")}`})(a.bakingTime)]}),(0,d.jsxs)("div",{className:"space-y-1 mb-3",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-700",children:j("recipes.ingredients")}),a.ingredients.map((a,b)=>{let c=i.find(b=>b.name===a.name),e=c&&c.quantity>=a.quantity;return(0,d.jsxs)("div",{className:`text-xs flex justify-between ${e?"text-green-600":"text-red-600"}`,children:[(0,d.jsx)("span",{children:a.name}),(0,d.jsxs)("span",{children:[a.quantity,c&&(0,d.jsxs)("span",{className:"ml-1",children:["(",c.quantity," available)"]})]})]},b)})]}),(0,d.jsx)("div",{className:"text-xs text-gray-500",children:j("recipes.unlockLevel",{level:a.unlockLevel.toString()})}),p(a)&&(0,d.jsx)("div",{className:"mt-2",children:(0,d.jsx)(h.$,{size:"sm",variant:"success",className:"w-full",children:j("recipes.canCraft")})})]},a.id)})}),0===o.length&&(0,d.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,d.jsx)("div",{className:"text-4xl mb-2",children:"\uD83D\uDCDD"}),(0,d.jsx)("p",{children:j("recipes.noRecipes")}),(0,d.jsx)("p",{className:"text-sm",children:j("recipes.levelUpToUnlock")})]})]})]})})}function m({isOpen:a,onClose:b}){let{player:c,inventory:i,spendMoney:j,addIngredient:k}=(0,g.I)(),{t:l}=(0,f.o)(),[m,n]=(0,e.useState)({});if(!a)return null;let o=(a,b)=>{n(c=>({...c,[a]:Math.max(0,b)}))},p=(a,b)=>b*(m[a]||1),q=(a,b)=>c.money>=p(a,b);return(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:l("modal.shop.title")}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,d.jsx)("span",{className:"text-green-800 font-medium",children:l("ui.money",{amount:c.money.toString()})})}),(0,d.jsx)(h.$,{variant:"secondary",onClick:b,children:l("game.close")})]})]})}),(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("div",{className:"space-y-4 max-h-[60vh] overflow-y-auto",children:i.map(a=>{let b=m[a.name]||1,c=p(a.name,a.cost),e=q(a.name,a.cost);return(0,d.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"text-2xl",children:a.icon}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-medium text-gray-800",children:a.name}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:l("shop.currentStock",{quantity:a.quantity.toString()})}),(0,d.jsx)("p",{className:"text-sm text-green-600",children:l("inventory.cost",{cost:a.cost.toString()})})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(h.$,{size:"sm",variant:"secondary",onClick:()=>o(a.name,b-1),disabled:b<=1,children:"-"}),(0,d.jsx)("span",{className:"w-12 text-center font-mono",children:b}),(0,d.jsx)(h.$,{size:"sm",variant:"secondary",onClick:()=>o(a.name,b+1),disabled:!q(a.name,a.cost)&&b>=1,children:"+"})]}),(0,d.jsxs)("div",{className:"text-right min-w-[80px]",children:[(0,d.jsxs)("div",{className:`font-medium ${e?"text-green-600":"text-red-600"}`,children:["$",c]}),(0,d.jsx)(h.$,{size:"sm",variant:e?"success":"secondary",onClick:()=>((a,b)=>{let c=m[a]||1;j(b*c)&&(k(a,c),n(b=>({...b,[a]:0})))})(a.name,a.cost),disabled:!e,className:"mt-1",children:e?l("shop.buy"):l("shop.tooExpensive")})]})]})]},a.name)})}),(0,d.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,d.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:l("shop.tips.title")}),(0,d.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,d.jsx)("li",{children:l("shop.tips.bulk")}),(0,d.jsx)("li",{children:l("shop.tips.stock")}),(0,d.jsx)("li",{children:l("shop.tips.rare")}),(0,d.jsx)("li",{children:l("shop.tips.prices")})]})]})]})]})})}function n({isOpen:a,onClose:b,equipmentId:c,equipmentName:f}){let{player:i,inventory:j,updateEquipment:l,useIngredient:m}=(0,g.I)(),[n,o]=(0,e.useState)(null);if(!a)return null;let p=(0,k.x0)(i.level).filter(a=>(0,k.hF)(a,j)),q=a=>{let b=Math.floor(a/60);return`${b}:${(a%60).toString().padStart(2,"0")}`},r=a=>"⭐".repeat(a)+"☆".repeat(5-a);return(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-hidden",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("h2",{className:"text-2xl font-bold text-orange-800",children:["\uD83D\uDD25 ",f," - Select Recipe"]}),(0,d.jsx)(h.$,{variant:"secondary",onClick:b,children:"✕ Close"})]})}),(0,d.jsxs)("div",{className:"p-6",children:[0===p.length?(0,d.jsxs)("div",{className:"text-center py-8",children:[(0,d.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDE14"}),(0,d.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-2",children:"No recipes available"}),(0,d.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have enough ingredients to craft any recipes."}),(0,d.jsx)(h.$,{variant:"primary",onClick:b,children:"Buy Ingredients"})]}):(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[60vh] overflow-y-auto",children:p.map(a=>(0,d.jsxs)("div",{className:`p-4 rounded-lg border-2 cursor-pointer transition-all ${n?.id===a.id?"border-orange-400 bg-orange-50":"border-gray-300 bg-gray-50 hover:border-orange-300"}`,onClick:()=>o(a),children:[(0,d.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-2xl",children:(a=>{switch(a){case"cookies":return"\uD83C\uDF6A";case"cakes":return"\uD83E\uDDC1";case"bread":return"\uD83C\uDF5E";case"pastries":return"\uD83E\uDD50";default:return"\uD83C\uDF7D️"}})(a.category)}),(0,d.jsx)("h3",{className:"font-semibold text-gray-800",children:a.name})]}),(0,d.jsxs)("span",{className:"text-sm text-green-600",children:["$",a.basePrice]})]}),(0,d.jsxs)("div",{className:"text-xs text-gray-500 mb-2",children:[r(a.difficulty)," • ⏱️ ",q(a.bakingTime)]}),(0,d.jsxs)("div",{className:"space-y-1 mb-3",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-700",children:"Ingredients:"}),a.ingredients.map((a,b)=>{let c=j.find(b=>b.name===a.name);return(0,d.jsxs)("div",{className:"text-xs flex justify-between text-green-600",children:[(0,d.jsx)("span",{children:a.name}),(0,d.jsxs)("span",{children:[a.quantity,(0,d.jsxs)("span",{className:"ml-1",children:["(",c?.quantity||0," available)"]})]})]},b)})]}),n?.id===a.id&&(0,d.jsx)(h.$,{variant:"success",size:"sm",className:"w-full",onClick:()=>{var d;return d=a,void((0,k.hF)(d,j)&&d.ingredients.every(a=>{let b=j.find(b=>b.name===a.name);return b&&b.quantity>=a.quantity})&&(d.ingredients.forEach(a=>{m(a.name,a.quantity)}),l(c,{isActive:!0,timeRemaining:d.bakingTime,currentRecipe:d.name}),b()))},children:"\uD83D\uDD25 Start Baking"})]},a.id))}),n&&p.length>0&&(0,d.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,d.jsxs)("h3",{className:"font-medium text-blue-800 mb-2",children:["\uD83D\uDCCB Baking Instructions for ",n.name]}),(0,d.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,d.jsxs)("p",{children:["• Baking time: ",q(n.bakingTime)]}),(0,d.jsxs)("p",{children:["• Difficulty: ",r(n.difficulty)]}),(0,d.jsxs)("p",{children:["• Expected reward: $",n.basePrice]}),(0,d.jsx)("p",{children:"• Make sure you have all ingredients before starting!"})]})]})]})]})})}function o({notifications:a,onRemove:b}){return 0===a.length?null:(0,d.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2 max-w-sm",children:a.map(a=>(0,d.jsx)("div",{className:`p-4 rounded-lg border-l-4 shadow-lg transition-all duration-300 ${(a=>{switch(a){case"success":return"bg-green-100 border-green-400 text-green-800";case"error":return"bg-red-100 border-red-400 text-red-800";case"warning":return"bg-yellow-100 border-yellow-400 text-yellow-800";case"info":return"bg-blue-100 border-blue-400 text-blue-800";default:return"bg-gray-100 border-gray-400 text-gray-800"}})(a.type)}`,children:(0,d.jsxs)("div",{className:"flex items-start justify-between",children:[(0,d.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,d.jsx)("span",{className:"text-lg",children:(a=>{switch(a){case"success":return"✅";case"error":return"❌";case"warning":return"⚠️";case"info":return"ℹ️";default:return"\uD83D\uDCE2"}})(a.type)}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium",children:a.title}),(0,d.jsx)("p",{className:"text-sm opacity-90",children:a.message})]})]}),(0,d.jsx)("button",{onClick:()=>b(a.id),className:"text-lg opacity-60 hover:opacity-100 transition-opacity",children:"\xd7"})]})},a.id))})}function p({isOpen:a,onClose:b,newLevel:c,rewards:e}){return a?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full overflow-hidden",children:[(0,d.jsxs)("div",{className:"bg-gradient-to-r from-yellow-400 to-orange-500 p-6 text-center",children:[(0,d.jsx)("div",{className:"text-6xl mb-2",children:"\uD83C\uDF89"}),(0,d.jsx)("h2",{className:"text-3xl font-bold text-white mb-2",children:"Level Up!"}),(0,d.jsxs)("p",{className:"text-xl text-yellow-100",children:["You reached Level ",c,"!"]})]}),(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"\uD83C\uDF81 Level Rewards"}),(0,d.jsx)("div",{className:"space-y-3 mb-6",children:e.map((a,b)=>(0,d.jsx)("div",{className:`p-3 rounded-lg border ${(a=>{switch(a){case"recipe":return"bg-blue-50 border-blue-300 text-blue-800";case"equipment":return"bg-purple-50 border-purple-300 text-purple-800";case"money":return"bg-green-50 border-green-300 text-green-800";case"skill_point":return"bg-yellow-50 border-yellow-300 text-yellow-800";case"achievement":return"bg-orange-50 border-orange-300 text-orange-800";default:return"bg-gray-50 border-gray-300 text-gray-800"}})(a.type)}`,children:(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("span",{className:"text-2xl",children:(a=>{switch(a){case"recipe":return"\uD83D\uDCD6";case"equipment":return"⚙️";case"money":return"\uD83D\uDCB0";case"skill_point":return"⭐";case"achievement":return"\uD83C\uDFC6";default:return"\uD83C\uDF81"}})(a.type)}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsx)("h4",{className:"font-medium",children:a.name}),(0,d.jsx)("p",{className:"text-sm opacity-80",children:a.description}),a.value&&(0,d.jsx)("p",{className:"text-sm font-semibold",children:"money"===a.type?`$${a.value}`:`+${a.value}`})]})]})},b))}),(0,d.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg mb-6",children:[(0,d.jsx)("h4",{className:"font-medium text-blue-800 mb-2",children:"\uD83D\uDCA1 What's Next?"}),(0,d.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,d.jsx)("li",{children:"• Check out new recipes in your recipe book"}),(0,d.jsx)("li",{children:"• Visit the shop for new equipment"}),(0,d.jsx)("li",{children:"• Take on more challenging orders"}),(0,d.jsx)("li",{children:"• Invest in skill upgrades"})]})]}),(0,d.jsx)(h.$,{variant:"primary",size:"lg",className:"w-full",onClick:b,children:"\uD83D\uDE80 Continue Playing"})]})]})}):null}function q({isOpen:a,onClose:b,achievements:c}){let[f,g]=(0,e.useState)("all");if(!a)return null;let i="all"===f?c:c.filter(a=>a.category===f),j=c.filter(a=>a.completed).length,k=c.length;return(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:"\uD83C\uDFC6 Achievements"}),(0,d.jsxs)("p",{className:"text-gray-600",children:[j," of ",k," achievements completed"]})]}),(0,d.jsx)(h.$,{variant:"secondary",onClick:b,children:"✕ Close"})]})}),(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("div",{className:"mb-6",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,d.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Overall Progress"}),(0,d.jsxs)("span",{className:"text-sm text-gray-500",children:[Math.round(j/k*100),"%"]})]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,d.jsx)("div",{className:"bg-gradient-to-r from-yellow-400 to-orange-500 h-3 rounded-full transition-all duration-500",style:{width:`${j/k*100}%`}})})]}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:[{id:"all",name:"All",icon:"\uD83C\uDFC6"},{id:"baking",name:"Baking",icon:"\uD83D\uDC68‍\uD83C\uDF73"},{id:"business",name:"Business",icon:"\uD83D\uDCBC"},{id:"efficiency",name:"Efficiency",icon:"⚡"},{id:"collection",name:"Collection",icon:"\uD83D\uDCDA"},{id:"special",name:"Special",icon:"⭐"}].map(a=>(0,d.jsxs)(h.$,{variant:f===a.id?"primary":"secondary",size:"sm",onClick:()=>g(a.id),children:[a.icon," ",a.name]},a.id))}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[50vh] overflow-y-auto",children:i.map(a=>{let b=a.completed?100:Math.min(...a.requirements.map(a=>a.current?Math.min(100,a.current/a.target*100):0)),c=a.completed,e=a.unlocked;return(0,d.jsx)("div",{className:`p-4 rounded-lg border-2 ${c?"border-green-400 bg-green-50":e?"border-gray-300 bg-white":"border-gray-200 bg-gray-50 opacity-60"}`,children:(0,d.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,d.jsx)("div",{className:`text-3xl ${c?"grayscale-0":"grayscale"}`,children:a.icon}),(0,d.jsxs)("div",{className:"flex-1",children:[(0,d.jsxs)("h3",{className:`font-semibold ${c?"text-green-800":"text-gray-800"}`,children:[a.name,c&&(0,d.jsx)("span",{className:"ml-2",children:"✅"})]}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:a.description}),e&&!c&&(0,d.jsxs)("div",{className:"mb-2",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,d.jsx)("span",{className:"text-xs text-gray-500",children:"Progress"}),(0,d.jsx)("span",{className:"text-xs text-gray-500",children:(a=>{if(a.completed)return"Completed!";let b=a.requirements[0];return`${b.current||0} / ${b.target}`})(a)})]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:`${b}%`}})})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,d.jsx)("span",{className:"text-gray-500",children:"Reward:"}),(0,d.jsx)("span",{className:"text-lg",children:(a=>{switch(a){case"recipe":return"\uD83D\uDCD6";case"equipment":return"⚙️";case"money":return"\uD83D\uDCB0";case"skill_point":return"⭐";default:return"\uD83C\uDF81"}})(a.reward.type)}),(0,d.jsx)("span",{className:"text-gray-700",children:a.reward.name}),a.reward.value&&(0,d.jsx)("span",{className:"text-green-600 font-medium",children:"money"===a.reward.type?`$${a.reward.value}`:`+${a.reward.value}`})]})]})]})},a.id)})}),0===i.length&&(0,d.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,d.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFC6"}),(0,d.jsx)("p",{children:"No achievements in this category."})]})]})]})})}function r({isOpen:a,onClose:b,skills:c,skillPoints:f,playerLevel:g,onUpgradeSkill:i}){let[j,k]=(0,e.useState)("all");if(!a)return null;let l="all"===j?c:c.filter(a=>a.category===j),m=a=>!(a.level>=a.maxLevel)&&!(f<a.cost)&&(!a.requirements.playerLevel||!(g<a.requirements.playerLevel))&&(!a.requirements.skills||a.requirements.skills.every(a=>{let b=c.find(b=>b.id===a);return b&&b.level>0}));return(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] overflow-hidden",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:"\uD83C\uDF1F Skill Tree"}),(0,d.jsxs)("p",{className:"text-gray-600",children:["Available Skill Points: ",(0,d.jsx)("span",{className:"font-semibold text-blue-600",children:f})]})]}),(0,d.jsx)(h.$,{variant:"secondary",onClick:b,children:"✕ Close"})]})}),(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:[{id:"all",name:"All",icon:"\uD83C\uDF1F"},{id:"efficiency",name:"Efficiency",icon:"⚡"},{id:"automation",name:"Automation",icon:"\uD83E\uDD16"},{id:"quality",name:"Quality",icon:"\uD83D\uDC8E"},{id:"business",name:"Business",icon:"\uD83D\uDCBC"}].map(a=>(0,d.jsxs)(h.$,{variant:j===a.id?"primary":"secondary",size:"sm",onClick:()=>k(a.id),children:[a.icon," ",a.name]},a.id))}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto",children:l.map(a=>{let b=a.level>=a.maxLevel?"maxed":m(a)?"available":"locked",e=m(a);return(0,d.jsxs)("div",{className:`p-4 rounded-lg border-2 ${(a=>{switch(a){case"maxed":return"border-green-400 bg-green-50";case"available":return"border-blue-400 bg-blue-50";default:return"border-gray-300 bg-gray-50"}})(b)}`,children:[(0,d.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-2xl",children:a.icon}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800",children:a.name}),(0,d.jsx)("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:a.category})]})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsxs)("div",{className:"text-sm font-medium text-gray-700",children:["Level ",a.level,"/",a.maxLevel]}),"maxed"!==b&&(0,d.jsxs)("div",{className:"text-xs text-blue-600",children:["Cost: ",a.cost," SP"]})]})]}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:a.description}),(0,d.jsxs)("div",{className:"mb-3",children:[(0,d.jsx)("h4",{className:"text-xs font-medium text-gray-700 mb-1",children:"Effects:"}),a.effects.map((a,b)=>(0,d.jsxs)("div",{className:"text-xs text-green-600",children:["• ",(a=>{let b=Math.round(100*a.value);switch(a.type){case"baking_speed":return`+${b}% baking speed`;case"money_multiplier":return`+${b}% money earned`;case"xp_multiplier":return`+${b}% experience gained`;case"ingredient_efficiency":return`${b}% less ingredients used`;case"automation_unlock":return"Unlock automation features";default:return`+${b}% bonus`}})(a)]},b))]}),a.requirements.playerLevel&&g<a.requirements.playerLevel&&(0,d.jsx)("div",{className:"mb-3",children:(0,d.jsxs)("div",{className:"text-xs text-red-600",children:["Requires Level ",a.requirements.playerLevel]})}),a.requirements.skills&&(0,d.jsx)("div",{className:"mb-3",children:(0,d.jsxs)("div",{className:"text-xs text-gray-600",children:["Requires: ",a.requirements.skills.map(a=>{let b=c.find(b=>b.id===a);return b?.name}).join(", ")]})}),(0,d.jsx)("div",{className:"mb-3",children:(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:`${a.level/a.maxLevel*100}%`}})})}),"maxed"===b?(0,d.jsx)(h.$,{variant:"success",size:"sm",className:"w-full",disabled:!0,children:"✅ Maxed"}):e?(0,d.jsxs)(h.$,{variant:"primary",size:"sm",className:"w-full",onClick:()=>i(a.id),children:["⬆️ Upgrade (",a.cost," SP)"]}):(0,d.jsx)(h.$,{variant:"secondary",size:"sm",className:"w-full",disabled:!0,children:"\uD83D\uDD12 Locked"})]},a.id)})}),0===l.length&&(0,d.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,d.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDF1F"}),(0,d.jsx)("p",{children:"No skills in this category."})]}),(0,d.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,d.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"\uD83D\uDCA1 Skill Tips"}),(0,d.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,d.jsx)("li",{children:"• Earn skill points by leveling up (1 point every 2 levels)"}),(0,d.jsx)("li",{children:"• Some skills require other skills to be unlocked first"}),(0,d.jsx)("li",{children:"• Focus on skills that match your playstyle"}),(0,d.jsx)("li",{children:"• Efficiency skills help with resource management"})]})]})]})]})})}var s=c(3955);function t({isOpen:a,onClose:b}){let{player:c,equipment:f,automationSettings:i,updateAutomationSettings:j,purchaseAutomationUpgrade:k}=(0,g.I)(),[l,m]=(0,e.useState)("settings");if(!a)return null;let n=f.filter(a=>a.automationLevel>0),o=s.sA.filter(a=>c.level>=a.unlockLevel&&!c.automationUpgrades?.includes(a.id)),p=(a,b)=>{j({[a]:b})};return(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:"\uD83E\uDD16 Automation Control"}),(0,d.jsx)(h.$,{variant:"secondary",onClick:b,children:"✕ Close"})]})}),(0,d.jsx)("div",{className:"border-b border-gray-200",children:(0,d.jsx)("div",{className:"flex space-x-0",children:[{id:"settings",name:"Settings",icon:"⚙️"},{id:"upgrades",name:"Upgrades",icon:"\uD83D\uDD27"},{id:"status",name:"Status",icon:"\uD83D\uDCCA"}].map(a=>(0,d.jsxs)("button",{onClick:()=>m(a.id),className:`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${l===a.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"}`,children:[a.icon," ",a.name]},a.id))})}),(0,d.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["settings"===l&&(0,d.jsx)("div",{className:"space-y-6",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,d.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,d.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"\uD83C\uDF9B️ Master Control"}),(0,d.jsxs)("div",{className:"space-y-3",children:[(0,d.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,d.jsx)("input",{type:"checkbox",checked:i?.enabled||!1,onChange:a=>p("enabled",a.target.checked),className:"rounded"}),(0,d.jsx)("span",{className:"text-sm",children:"Enable Automation"})]}),(0,d.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,d.jsx)("input",{type:"checkbox",checked:i?.autoStart||!1,onChange:a=>p("autoStart",a.target.checked),className:"rounded"}),(0,d.jsx)("span",{className:"text-sm",children:"Auto-start Equipment"})]})]})]}),(0,d.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,d.jsx)("h3",{className:"font-semibold text-green-800 mb-3",children:"\uD83C\uDFAF Priority Mode"}),(0,d.jsxs)("select",{value:i?.priorityMode||"efficiency",onChange:a=>p("priorityMode",a.target.value),className:"w-full p-2 border rounded-lg",children:[(0,d.jsx)("option",{value:"efficiency",children:"Efficiency (Orders First)"}),(0,d.jsx)("option",{value:"profit",children:"Profit (Highest Value)"}),(0,d.jsx)("option",{value:"speed",children:"Speed (Fastest Recipes)"})]}),(0,d.jsx)("p",{className:"text-xs text-green-600 mt-1",children:"How automation chooses what to bake"})]}),(0,d.jsxs)("div",{className:"bg-purple-50 p-4 rounded-lg",children:[(0,d.jsx)("h3",{className:"font-semibold text-purple-800 mb-3",children:"⚡ Performance"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("label",{className:"block text-sm",children:["Max Concurrent Jobs: ",i?.maxConcurrentJobs||2]}),(0,d.jsx)("input",{type:"range",min:"1",max:"5",value:i?.maxConcurrentJobs||2,onChange:a=>p("maxConcurrentJobs",parseInt(a.target.value)),className:"w-full"})]})]}),(0,d.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,d.jsx)("h3",{className:"font-semibold text-yellow-800 mb-3",children:"\uD83D\uDEE1️ Safety"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("label",{className:"block text-sm",children:["Stop when ingredients below: ",i?.ingredientThreshold||5]}),(0,d.jsx)("input",{type:"range",min:"0",max:"20",value:i?.ingredientThreshold||5,onChange:a=>p("ingredientThreshold",parseInt(a.target.value)),className:"w-full"})]})]})]})}),"upgrades"===l&&(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg mb-4",children:[(0,d.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"\uD83D\uDCA1 Automation Upgrades"}),(0,d.jsx)("p",{className:"text-sm text-blue-700",children:"Improve your automation efficiency, speed, and intelligence with these upgrades."})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:o.map(a=>(0,d.jsxs)("div",{className:"p-4 border rounded-lg bg-white hover:bg-gray-50 transition-colors",children:[(0,d.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,d.jsx)("h4",{className:"font-semibold text-gray-800",children:a.name}),(0,d.jsxs)("span",{className:"text-sm text-green-600 font-medium",children:["$",a.cost]})]}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:a.description}),(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-xs text-gray-500 uppercase tracking-wide",children:a.type}),(0,d.jsx)(h.$,{size:"sm",variant:c.money>=a.cost?"primary":"secondary",disabled:c.money<a.cost,onClick:()=>k(a.id),children:c.money>=a.cost?"Purchase":"Too Expensive"})]})]},a.id))}),0===o.length&&(0,d.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,d.jsx)("div",{className:"text-4xl mb-2",children:"\uD83D\uDD27"}),(0,d.jsx)("p",{children:"No upgrades available at your current level."}),(0,d.jsx)("p",{className:"text-sm",children:"Level up to unlock more automation upgrades!"})]})]}),"status"===l&&(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,d.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg text-center",children:[(0,d.jsx)("div",{className:"text-2xl text-green-600 mb-1",children:n.length}),(0,d.jsx)("div",{className:"text-sm text-green-800",children:"Automated Equipment"})]}),(0,d.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg text-center",children:[(0,d.jsx)("div",{className:"text-2xl text-blue-600 mb-1",children:i?.enabled?"✅":"❌"}),(0,d.jsx)("div",{className:"text-sm text-blue-800",children:"Automation Status"})]}),(0,d.jsxs)("div",{className:"bg-purple-50 p-4 rounded-lg text-center",children:[(0,d.jsx)("div",{className:"text-2xl text-purple-600 mb-1",children:c.automationUpgrades?.length||0}),(0,d.jsx)("div",{className:"text-sm text-purple-800",children:"Active Upgrades"})]})]}),(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800",children:"\uD83C\uDFED Equipment Status"}),n.length>0?(0,d.jsx)("div",{className:"space-y-2",children:n.map(a=>(0,d.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"font-medium",children:a.name}),(0,d.jsxs)("span",{className:"text-sm text-gray-500 ml-2",children:["Level ",a.automationLevel," • ",a.efficiency,"x efficiency"]})]}),(0,d.jsx)("div",{className:`px-2 py-1 rounded text-xs ${a.isActive?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"}`,children:a.isActive?"Running":"Idle"})]},a.id))}):(0,d.jsxs)("div",{className:"text-center py-4 text-gray-500",children:[(0,d.jsx)("p",{children:"No automated equipment available."}),(0,d.jsx)("p",{className:"text-sm",children:"Purchase auto-equipment from the shop to get started!"})]})]})]})]})]})})}let u=[{id:"professional_oven",name:"Professional Oven",type:"oven",description:"Faster and more efficient than basic oven",cost:500,unlockLevel:3,automationLevel:0,efficiency:1.3,icon:"\uD83D\uDD25",category:"basic"},{id:"auto_oven",name:"Automated Oven",type:"auto_oven",description:"Fully automated oven that can run without supervision",cost:1500,unlockLevel:5,automationLevel:2,efficiency:1.5,icon:"\uD83E\uDD16",category:"automated"},{id:"industrial_mixer",name:"Industrial Mixer",type:"mixer",description:"High-capacity mixer for large batches",cost:750,unlockLevel:4,automationLevel:0,efficiency:1.4,icon:"\uD83E\uDD44",category:"basic"},{id:"auto_mixer",name:"Automated Mixer",type:"auto_mixer",description:"Self-operating mixer with ingredient dispensing",cost:2e3,unlockLevel:6,automationLevel:2,efficiency:1.6,icon:"\uD83E\uDD16",category:"automated"},{id:"conveyor_belt_basic",name:"Basic Conveyor Belt",type:"conveyor",description:"Moves items between equipment automatically",cost:1e3,unlockLevel:7,automationLevel:1,efficiency:1.2,icon:"\uD83D\uDD04",category:"automated"},{id:"smart_conveyor",name:"Smart Conveyor System",type:"conveyor",description:"Intelligent conveyor with sorting and routing",cost:3e3,unlockLevel:10,automationLevel:3,efficiency:1.8,icon:"\uD83E\uDDE0",category:"advanced"},{id:"master_oven",name:"Master Oven",type:"oven",description:"The ultimate baking machine with AI assistance",cost:5e3,unlockLevel:12,automationLevel:3,efficiency:2,icon:"\uD83D\uDC51",category:"advanced"}];function v({isOpen:a,onClose:b,onShowSuccess:c}){let{player:f,equipment:i,spendMoney:j,addEquipment:k}=(0,g.I)(),[l,m]=(0,e.useState)("all");if(!a)return null;let n=u.filter(a=>f.level>=a.unlockLevel&&("all"===l||a.category===l));return(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:"\uD83C\uDFEA Equipment Shop"}),(0,d.jsx)("p",{className:"text-gray-600",children:"Upgrade your bakery with professional equipment"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,d.jsxs)("span",{className:"text-green-800 font-medium",children:["$",f.money]})}),(0,d.jsx)(h.$,{variant:"secondary",onClick:b,children:"✕ Close"})]})]})}),(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:[{id:"all",name:"All",icon:"\uD83C\uDFEA"},{id:"basic",name:"Basic",icon:"\uD83D\uDD27"},{id:"automated",name:"Automated",icon:"\uD83E\uDD16"},{id:"advanced",name:"Advanced",icon:"⚡"}].map(a=>(0,d.jsxs)(h.$,{variant:l===a.id?"primary":"secondary",size:"sm",onClick:()=>m(a.id),children:[a.icon," ",a.name]},a.id))}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto",children:n.map(a=>{var b;return(0,d.jsxs)("div",{className:`p-4 rounded-lg border-2 ${(a=>{switch(a){case"basic":return"border-gray-300 bg-gray-50";case"automated":return"border-blue-300 bg-blue-50";case"advanced":return"border-purple-300 bg-purple-50";default:return"border-gray-300 bg-white"}})(a.category)}`,children:[(0,d.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-2xl",children:a.icon}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800",children:a.name}),(0,d.jsx)("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:a.category})]})]}),(0,d.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["$",a.cost]})]}),(0,d.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:a.description}),(0,d.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,d.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Efficiency:"}),(0,d.jsxs)("span",{className:"font-medium",children:[a.efficiency,"x"]})]}),a.automationLevel>0&&(0,d.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Automation:"}),0===(b=a.automationLevel)?null:(0,d.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:["\uD83E\uDD16 Auto Level ",b]})]}),(0,d.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,d.jsx)("span",{className:"text-gray-600",children:"Unlock Level:"}),(0,d.jsx)("span",{className:"font-medium",children:a.unlockLevel})]})]}),(0,d.jsx)(h.$,{variant:f.money>=a.cost?"success":"secondary",size:"sm",className:"w-full",disabled:f.money<a.cost,onClick:()=>{!(f.money<a.cost)&&j(a.cost)&&(k({name:a.name,type:a.type,isActive:!1,level:1,efficiency:a.efficiency,automationLevel:a.automationLevel}),c&&c("Equipment Purchased!",`You bought ${a.name}!`))},children:f.money>=a.cost?"\uD83D\uDCB0 Purchase":"\uD83D\uDCB8 Too Expensive"})]},a.id)})}),0===n.length&&(0,d.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,d.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFEA"}),(0,d.jsx)("p",{children:"No equipment available in this category."}),(0,d.jsx)("p",{className:"text-sm",children:"Level up to unlock more equipment!"})]}),(0,d.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,d.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"\uD83D\uDCA1 Equipment Tips"}),(0,d.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,d.jsx)("li",{children:"• Automated equipment can run without your supervision"}),(0,d.jsx)("li",{children:"• Higher efficiency means faster production and better quality"}),(0,d.jsx)("li",{children:"• Conveyor belts connect equipment for seamless workflow"}),(0,d.jsx)("li",{children:"• Advanced equipment unlocks at higher levels"})]})]})]})]})})}var w=c(5745);let x=[{name:"Downtown Delights",location:"City Center",specialization:"general",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:0},{name:"Cookie Corner",location:"Shopping Mall",specialization:"cookies",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:2500},{name:"Cake Castle",location:"Wedding District",specialization:"cakes",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:3500},{name:"Bread Basket",location:"Farmers Market",specialization:"bread",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:3e3},{name:"Pastry Palace",location:"French Quarter",specialization:"pastries",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:4e3}];function y({isOpen:a,onClose:b,bakeries:c,currentBakeryId:g,onSwitchBakery:i,onPurchaseBakery:j,playerMoney:k}){let{t:l}=(0,f.o)(),[m,n]=(0,e.useState)("owned");if(!a)return null;let o=c.filter(a=>a.unlocked),p=x.filter(a=>!c.some(b=>b.name===a.name&&b.unlocked)),q=a=>{switch(a){case"cookies":return"\uD83C\uDF6A";case"cakes":return"\uD83E\uDDC1";case"bread":return"\uD83C\uDF5E";case"pastries":return"\uD83E\uDD50";default:return"\uD83C\uDFEA"}},r=a=>{switch(a){case"cookies":return"+20% Cookie Production Speed";case"cakes":return"+25% Cake Profit Margin";case"bread":return"+15% Bread Ingredient Efficiency";case"pastries":return"+30% Pastry Experience Gain";default:return"Balanced Production"}},s=[{id:"owned",name:l("bakeries.owned")||"My Bakeries",icon:"\uD83C\uDFEA"},{id:"available",name:l("bakeries.available")||"Available",icon:"\uD83D\uDED2"}];return(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,d.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:l("bakeries.title")||"\uD83C\uDFEA Bakery Manager"}),(0,d.jsx)("p",{className:"text-gray-600",children:l("bakeries.subtitle")||"Manage your bakery empire"})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,d.jsxs)("span",{className:"text-green-800 font-medium",children:["$",k]})}),(0,d.jsx)(h.$,{variant:"secondary",onClick:b,children:l("game.close")||"✕ Close"})]})]})}),(0,d.jsx)("div",{className:"border-b border-gray-200",children:(0,d.jsx)("div",{className:"flex space-x-0",children:s.map(a=>(0,d.jsxs)("button",{onClick:()=>n(a.id),className:`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${m===a.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"}`,children:[a.icon," ",a.name]},a.id))})}),(0,d.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["owned"===m&&(0,d.jsx)("div",{className:"space-y-4",children:o.length>0?(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:o.map(a=>(0,d.jsxs)("div",{className:`p-4 rounded-lg border-2 cursor-pointer transition-all ${a.id===g?"border-orange-400 bg-orange-50":"border-gray-300 bg-white hover:border-orange-300"}`,onClick:()=>i(a.id),children:[(0,d.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-2xl",children:q(a.specialization)}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800",children:a.name}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:a.location})]})]}),a.id===g&&(0,d.jsx)("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full",children:l("bakeries.current")||"Current"})]}),(0,d.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsxs)("span",{className:"text-gray-600",children:[l("bakeries.level")||"Level",":"]}),(0,d.jsx)("span",{className:"font-medium",children:a.level})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsxs)("span",{className:"text-gray-600",children:[l("bakeries.specialization")||"Specialization",":"]}),(0,d.jsx)("span",{className:"font-medium capitalize",children:a.specialization})]}),(0,d.jsx)("div",{className:"text-xs text-blue-600",children:r(a.specialization)})]}),(0,d.jsxs)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:[(0,d.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,d.jsxs)("span",{className:"text-gray-600",children:[l("bakeries.equipment")||"Equipment",":"]}),(0,d.jsx)("span",{children:a.equipment.length})]}),(0,d.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,d.jsxs)("span",{className:"text-gray-600",children:[l("bakeries.orders")||"Active Orders",":"]}),(0,d.jsx)("span",{children:a.orders.length})]})]}),a.id!==g&&(0,d.jsx)(h.$,{variant:"primary",size:"sm",className:"w-full mt-3",onClick:b=>{b.stopPropagation(),i(a.id)},children:l("bakeries.switchTo")||"Switch To"})]},a.id))}):(0,d.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,d.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFEA"}),(0,d.jsx)("p",{children:l("bakeries.noOwned")||"You don't own any bakeries yet."})]})}),"available"===m&&(0,d.jsx)("div",{className:"space-y-4",children:p.length>0?(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:p.map((a,b)=>(0,d.jsxs)("div",{className:"p-4 rounded-lg border-2 border-gray-300 bg-white",children:[(0,d.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-2xl",children:q(a.specialization)}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800",children:a.name}),(0,d.jsx)("p",{className:"text-sm text-gray-600",children:a.location})]})]}),(0,d.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["$",a.purchaseCost]})]}),(0,d.jsxs)("div",{className:"space-y-2 text-sm mb-4",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsxs)("span",{className:"text-gray-600",children:[l("bakeries.specialization")||"Specialization",":"]}),(0,d.jsx)("span",{className:"font-medium capitalize",children:a.specialization})]}),(0,d.jsx)("div",{className:"text-xs text-blue-600",children:r(a.specialization)})]}),(0,d.jsx)(h.$,{variant:k>=a.purchaseCost?"success":"secondary",size:"sm",className:"w-full",disabled:k<a.purchaseCost,onClick:()=>{k>=a.purchaseCost&&j({...a,id:`bakery_${Date.now()}`,unlocked:!0})},children:k>=a.purchaseCost?l("bakeries.purchase")||"\uD83D\uDCB0 Purchase":l("bakeries.tooExpensive")||"\uD83D\uDCB8 Too Expensive"})]},b))}):(0,d.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,d.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDF89"}),(0,d.jsx)("p",{children:l("bakeries.allOwned")||"You own all available bakeries!"})]})})]}),(0,d.jsxs)("div",{className:"p-4 bg-blue-50 border-t border-gray-200",children:[(0,d.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:l("bakeries.tips")||"\uD83D\uDCA1 Bakery Tips"}),(0,d.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,d.jsxs)("li",{children:["• ",l("bakeries.tip1")||"Each bakery specializes in different products for bonus efficiency"]}),(0,d.jsxs)("li",{children:["• ",l("bakeries.tip2")||"Switch between bakeries to manage multiple locations"]}),(0,d.jsxs)("li",{children:["• ",l("bakeries.tip3")||"Specialized bakeries attract customers looking for specific items"]}),(0,d.jsxs)("li",{children:["• ",l("bakeries.tip4")||"Upgrade each bakery independently for maximum profit"]})]})]})]})})}class z{constructor(){this.saveDirectory="",this.isElectron=!1,this.isElectron=!1,this.initializeSaveDirectory()}async initializeSaveDirectory(){if(this.isElectron&&window.electronAPI)try{this.saveDirectory=await window.electronAPI.getSaveDirectory(),await this.ensureSaveDirectoryExists()}catch(a){console.error("Failed to initialize save directory:",a),this.isElectron=!1}}async ensureSaveDirectoryExists(){if(this.isElectron&&window.electronAPI)try{await window.electronAPI.ensureDirectory(this.saveDirectory)}catch(a){console.error("Failed to create save directory:",a)}}async saveToFile(a,b){if(!this.isElectron||!window.electronAPI)return this.saveToLocalStorage(a,b);try{let c=b||`save_${Date.now()}.json`,d=`${this.saveDirectory}/${c}`,e=JSON.stringify(a,null,2);return await window.electronAPI.writeFile(d,e),console.log(`Game saved to file: ${d}`),!0}catch(a){return console.error("Failed to save to file:",a),!1}}async loadFromFile(a){if(!this.isElectron||!window.electronAPI)return this.loadFromLocalStorage(a);try{let b=`${this.saveDirectory}/${a}`,c=await window.electronAPI.readFile(b);if(!c)return null;let d=JSON.parse(c);return console.log(`Game loaded from file: ${b}`),d}catch(a){return console.error("Failed to load from file:",a),null}}async getSaveFiles(){if(!this.isElectron||!window.electronAPI)return this.getLocalStorageSaves();try{let a=await window.electronAPI.listFiles(this.saveDirectory,".json"),b=[];for(let c of a)try{let a=`${this.saveDirectory}/${c.name}`,d=await window.electronAPI.readFile(a),e=JSON.parse(d);b.push({fileName:c.name,displayName:e.player.name||c.name.replace(".json",""),timestamp:e.timestamp,playerLevel:e.player.level,money:e.player.money,playTime:e.player.playTime||0,version:e.version,fileSize:c.size||0})}catch(a){console.error(`Failed to read save file ${c.name}:`,a)}return b.sort((a,b)=>b.timestamp-a.timestamp)}catch(a){return console.error("Failed to get save files:",a),[]}}async deleteSaveFile(a){if(!this.isElectron||!window.electronAPI)return this.deleteFromLocalStorage(a);try{let b=`${this.saveDirectory}/${a}`;return await window.electronAPI.deleteFile(b),console.log(`Save file deleted: ${b}`),!0}catch(a){return console.error("Failed to delete save file:",a),!1}}async exportSave(a,b){if(!this.isElectron||!window.electronAPI)return this.exportToBrowser(a,b);try{let c=b||`bake-it-out-save-${Date.now()}.json`,d=await window.electronAPI.showSaveDialog(c);if(!d)return!1;let e=JSON.stringify(a,null,2);return await window.electronAPI.writeFile(d,e),console.log(`Save exported to: ${d}`),!0}catch(a){return console.error("Failed to export save:",a),!1}}async importSave(){if(!this.isElectron||!window.electronAPI)return this.importFromBrowser();try{let a=await window.electronAPI.showOpenDialog([".json"]);if(!a)return null;let b=await window.electronAPI.readFile(a),c=JSON.parse(b);return console.log(`Save imported from: ${a}`),c}catch(a){return console.error("Failed to import save:",a),null}}async createBackup(a){if(!this.isElectron||!window.electronAPI)return!1;try{let b=`${this.saveDirectory}/${a}`,c=`${this.saveDirectory}/backups/${a}.backup.${Date.now()}`;return await window.electronAPI.ensureDirectory(`${this.saveDirectory}/backups`),await window.electronAPI.copyFile(b,c),console.log(`Backup created: ${c}`),!0}catch(a){return console.error("Failed to create backup:",a),!1}}saveToLocalStorage(a,b){try{let c=b||`save_${Date.now()}`;return localStorage.setItem(`bakeItOut_file_${c}`,JSON.stringify(a)),!0}catch(a){return console.error("Failed to save to localStorage:",a),!1}}loadFromLocalStorage(a){try{let b=localStorage.getItem(`bakeItOut_file_${a}`);return b?JSON.parse(b):null}catch(a){return console.error("Failed to load from localStorage:",a),null}}getLocalStorageSaves(){let a=[];for(let b=0;b<localStorage.length;b++){let c=localStorage.key(b);if(c?.startsWith("bakeItOut_file_"))try{let b=localStorage.getItem(c);if(b){let d=JSON.parse(b),e=c.replace("bakeItOut_file_","");a.push({fileName:e,displayName:d.player.name||e,timestamp:d.timestamp,playerLevel:d.player.level,money:d.player.money,playTime:d.player.playTime||0,version:d.version,fileSize:b.length})}}catch(a){console.error(`Failed to parse save ${c}:`,a)}}return a.sort((a,b)=>b.timestamp-a.timestamp)}deleteFromLocalStorage(a){try{return localStorage.removeItem(`bakeItOut_file_${a}`),!0}catch(a){return console.error("Failed to delete from localStorage:",a),!1}}exportToBrowser(a,b){try{let c=JSON.stringify(a,null,2),d=new Blob([c],{type:"application/json"}),e=URL.createObjectURL(d),f=document.createElement("a");return f.href=e,f.download=b||`bake-it-out-save-${Date.now()}.json`,f.click(),URL.revokeObjectURL(e),!0}catch(a){return console.error("Failed to export to browser:",a),!1}}importFromBrowser(){return new Promise(a=>{let b=document.createElement("input");b.type="file",b.accept=".json",b.onchange=b=>{let c=b.target.files?.[0];if(!c)return void a(null);let d=new FileReader;d.onload=b=>{try{let c=b.target?.result,d=JSON.parse(c);a(d)}catch(b){console.error("Failed to parse imported file:",b),a(null)}},d.readAsText(c)},b.click()})}}function A({isOpen:a,onClose:b,mode:c,onSaveSuccess:i,onLoadSuccess:j}){let{t:k}=(0,f.o)(),{player:l,saveGameState:m,loadGameState:n}=(0,g.I)(),[o,p]=(0,e.useState)([]),[q,r]=(0,e.useState)([]),[s,t]=(0,e.useState)(null),[u,v]=(0,e.useState)(null),[w,x]=(0,e.useState)(""),[y,z]=(0,e.useState)(!1),[A,B]=(0,e.useState)(!1),[C,D]=(0,e.useState)(!1),E=()=>{let a=[];for(let b=1;b<=8;b++){let c=`bakeItOut_save_slot_${b}`,d=localStorage.getItem(c);if(d)try{let c=JSON.parse(d);a.push({id:b,name:c.player.name||`Save ${b}`,timestamp:c.timestamp,playerLevel:c.player.level,money:c.player.money,bakeryName:c.bakeries?.[0]?.name||"Main Bakery",playTime:c.player.playTime||0,isEmpty:!1,data:c})}catch(c){console.error(`Failed to load save slot ${b}:`,c),a.push(F(b))}else a.push(F(b))}p(a)},F=a=>({id:a,name:`Empty Slot ${a}`,timestamp:0,playerLevel:0,money:0,bakeryName:"",playTime:0,isEmpty:!0}),G=async a=>{if(!s)return;let b=o.find(b=>b.id===a);if(!b?.isEmpty&&!A)return void B(!0);z(!0);try{await m(a,w||`Save ${a}`)&&(i?.(),E(),B(!1),x(""))}catch(a){console.error("Save failed:",a)}finally{z(!1)}},H=async a=>{let c=o.find(b=>b.id===a);if(c&&!c.isEmpty){z(!0);try{await n(a)&&(j?.(),b())}catch(a){console.error("Load failed:",a)}finally{z(!1)}}};return a?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-500 p-6 text-white",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h2",{className:"text-2xl font-bold",children:"save"===c?"\uD83D\uDCBE Save Game":"\uD83D\uDCC1 Load Game"}),(0,d.jsx)("p",{className:"text-blue-100 text-sm",children:"save"===c?k("saveLoad.saveDesc","Choose a slot to save your progress"):k("saveLoad.loadDesc","Select a save file to load")})]}),(0,d.jsx)(h.$,{variant:"secondary",size:"sm",className:"bg-white/20 hover:bg-white/30 text-white border-white/30",onClick:b,children:"✕"})]})}),C&&(0,d.jsx)("div",{className:"border-b border-gray-200",children:(0,d.jsxs)("div",{className:"flex",children:[(0,d.jsx)("button",{className:`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${!C||null!==s?"border-blue-500 text-blue-600 bg-blue-50":"border-transparent text-gray-500 hover:text-gray-700"}`,onClick:()=>{t(1),v(null)},children:"\uD83D\uDCC1 Save Slots"}),(0,d.jsx)("button",{className:`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${null!==u?"border-blue-500 text-blue-600 bg-blue-50":"border-transparent text-gray-500 hover:text-gray-700"}`,onClick:()=>{t(null),v("")},children:"\uD83D\uDCBE File System"})]})}),(0,d.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[70vh]",children:["save"===c&&s&&(0,d.jsxs)("div",{className:"mb-6 p-4 bg-blue-50 rounded-lg",children:[(0,d.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:k("saveLoad.saveName","Save Name")}),(0,d.jsx)("input",{type:"text",value:w,onChange:a=>x(a.target.value),placeholder:`Save ${s}`,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:o.map(a=>{var b;return(0,d.jsxs)("div",{className:`border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 ${s===a.id?"border-blue-500 bg-blue-50":a.isEmpty?"border-gray-200 bg-gray-50 hover:border-gray-300":"border-gray-300 bg-white hover:border-blue-300 hover:shadow-md"}`,onClick:()=>t(a.id),children:[(0,d.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-orange-400 to-yellow-400 rounded-lg flex items-center justify-center text-white font-bold text-lg",children:a.id}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h3",{className:"font-semibold text-gray-800",children:a.isEmpty?`Slot ${a.id}`:a.name}),!a.isEmpty&&(0,d.jsx)("p",{className:"text-sm text-gray-500",children:(b=a.timestamp)?new Date(b).toLocaleString():""})]})]}),!a.isEmpty&&(0,d.jsx)(h.$,{variant:"secondary",size:"sm",className:"text-red-600 hover:bg-red-50",onClick:b=>{b.stopPropagation();var c=a.id;let d=`bakeItOut_save_slot_${c}`;localStorage.removeItem(d),E()},children:"\uD83D\uDDD1️"})]}),a.isEmpty?(0,d.jsxs)("div",{className:"text-center py-8 text-gray-400",children:[(0,d.jsx)("div",{className:"text-3xl mb-2",children:"\uD83D\uDCC4"}),(0,d.jsx)("p",{className:"text-sm",children:k("saveLoad.emptySlot","Empty Slot")})]}):(0,d.jsx)("div",{className:"space-y-2",children:(0,d.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-gray-500",children:"Level:"}),(0,d.jsx)("span",{className:"ml-2 font-medium",children:a.playerLevel})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-gray-500",children:"Money:"}),(0,d.jsxs)("span",{className:"ml-2 font-medium",children:["$",a.money]})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-gray-500",children:"Bakery:"}),(0,d.jsx)("span",{className:"ml-2 font-medium",children:a.bakeryName})]}),(0,d.jsxs)("div",{children:[(0,d.jsx)("span",{className:"text-gray-500",children:"Play Time:"}),(0,d.jsx)("span",{className:"ml-2 font-medium",children:(a=>{let b=Math.floor(a/3600),c=Math.floor(a%3600/60);return`${b}h ${c}m`})(a.playTime)})]})]})})]},a.id)})})]}),(0,d.jsxs)("div",{className:"bg-gray-50 px-6 py-4 flex justify-between items-center",children:[(0,d.jsx)("div",{className:"text-sm text-gray-500",children:s&&(0,d.jsx)("span",{children:"save"===c?k("saveLoad.selectedSaveSlot",`Selected: Slot ${s}`):k("saveLoad.selectedLoadSlot",`Selected: Slot ${s}`)})}),(0,d.jsxs)("div",{className:"flex space-x-3",children:[(0,d.jsx)(h.$,{variant:"secondary",onClick:b,children:k("common.cancel","Cancel")}),"save"===c?(0,d.jsx)(h.$,{variant:"primary",onClick:()=>s&&G(s),disabled:!s||y,className:"bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600",children:y?"\uD83D\uDCBE Saving...":"\uD83D\uDCBE Save Game"}):(0,d.jsx)(h.$,{variant:"primary",onClick:()=>s&&H(s),disabled:!s||y||o.find(a=>a.id===s)?.isEmpty,className:"bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600",children:y?"\uD83D\uDCC1 Loading...":"\uD83D\uDCC1 Load Game"})]})]}),A&&(0,d.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md mx-4",children:[(0,d.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:k("saveLoad.confirmOverwrite","Overwrite Save?")}),(0,d.jsx)("p",{className:"text-gray-600 mb-6",children:k("saveLoad.overwriteWarning","This will overwrite the existing save. This action cannot be undone.")}),(0,d.jsxs)("div",{className:"flex space-x-3",children:[(0,d.jsx)(h.$,{variant:"secondary",onClick:()=>B(!1),children:k("common.cancel","Cancel")}),(0,d.jsx)(h.$,{variant:"primary",className:"bg-red-500 hover:bg-red-600",onClick:()=>s&&G(s),children:k("saveLoad.overwrite","Overwrite")})]})]})})]})}):null}function B({equipment:a,onEquipmentClick:b}){let{t:c}=(0,f.o)(),{orders:h,player:i}=(0,g.I)(),[j,k]=(0,e.useState)([]),[l,m]=(0,e.useState)("kitchen"),n=a=>{switch(a){case"oven":return"\uD83D\uDD25";case"mixer":return"\uD83E\uDD44";case"counter":return"\uD83C\uDFEA";case"auto_oven":return"\uD83E\uDD16";case"auto_mixer":return"⚙️";case"conveyor":return"\uD83D\uDD04";default:return"\uD83D\uDCE6"}},o=a=>a.isActive?"bg-green-100 border-green-400":a.automationLevel>0?"bg-blue-100 border-blue-400":"bg-gray-100 border-gray-300";return(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,d.jsxs)("h2",{className:"text-2xl font-bold text-orange-800",children:["\uD83C\uDFEA ",c("bakery.layout.title","Bakery Layout")]}),(0,d.jsxs)("div",{className:"flex space-x-2",children:[(0,d.jsxs)("button",{onClick:()=>m("kitchen"),className:`px-4 py-2 rounded-lg font-medium transition-colors ${"kitchen"===l?"bg-orange-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:["\uD83D\uDC68‍\uD83C\uDF73 ",c("bakery.kitchen","Kitchen")]}),(0,d.jsxs)("button",{onClick:()=>m("dining"),className:`px-4 py-2 rounded-lg font-medium transition-colors ${"dining"===l?"bg-orange-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:["\uD83C\uDF7D️ ",c("bakery.dining","Dining Area")]}),(0,d.jsxs)("button",{onClick:()=>m("counter"),className:`px-4 py-2 rounded-lg font-medium transition-colors ${"counter"===l?"bg-orange-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"}`,children:["\uD83D\uDED2 ",c("bakery.counter","Service Counter")]})]})]}),(0,d.jsxs)("div",{className:"relative bg-gradient-to-br from-yellow-50 to-orange-50 rounded-lg border-2 border-orange-200 min-h-[500px] overflow-hidden",children:["kitchen"===l&&(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-6",children:[(0,d.jsxs)("div",{className:"bg-red-100 rounded-lg p-4 border-2 border-red-300",children:[(0,d.jsxs)("h3",{className:"font-semibold text-red-800 mb-3",children:["\uD83D\uDD25 ",c("bakery.baking.area","Baking Area")]}),(0,d.jsx)("div",{className:"space-y-2",children:a.filter(a=>a.type.includes("oven")).map(a=>(0,d.jsxs)("div",{onClick:()=>b(a.id,a.name),className:`p-3 rounded-lg border-2 cursor-pointer transition-all hover:scale-105 ${o(a)}`,children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-xl",children:n(a.type)}),(0,d.jsx)("span",{className:"font-medium text-sm",children:a.name})]}),a.isActive&&(0,d.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"})]}),a.currentRecipe&&(0,d.jsxs)("div",{className:"text-xs text-gray-600 mt-1",children:["Making: ",a.currentRecipe]})]},a.id))})]}),(0,d.jsxs)("div",{className:"bg-blue-100 rounded-lg p-4 border-2 border-blue-300",children:[(0,d.jsxs)("h3",{className:"font-semibold text-blue-800 mb-3",children:["\uD83E\uDD44 ",c("bakery.prep.area","Prep Area")]}),(0,d.jsx)("div",{className:"space-y-2",children:a.filter(a=>a.type.includes("mixer")).map(a=>(0,d.jsx)("div",{onClick:()=>b(a.id,a.name),className:`p-3 rounded-lg border-2 cursor-pointer transition-all hover:scale-105 ${o(a)}`,children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-xl",children:n(a.type)}),(0,d.jsx)("span",{className:"font-medium text-sm",children:a.name})]}),a.isActive&&(0,d.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"})]})},a.id))})]}),(0,d.jsxs)("div",{className:"bg-purple-100 rounded-lg p-4 border-2 border-purple-300",children:[(0,d.jsxs)("h3",{className:"font-semibold text-purple-800 mb-3",children:["⚙️ ",c("bakery.automation.area","Automation")]}),(0,d.jsx)("div",{className:"space-y-2",children:a.filter(a=>a.type.includes("conveyor")||a.automationLevel>0).map(a=>(0,d.jsx)("div",{onClick:()=>b(a.id,a.name),className:`p-3 rounded-lg border-2 cursor-pointer transition-all hover:scale-105 ${o(a)}`,children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-xl",children:n(a.type)}),(0,d.jsx)("span",{className:"font-medium text-sm",children:a.name})]}),(0,d.jsxs)("div",{className:"text-xs bg-purple-200 px-2 py-1 rounded",children:["Auto Lv.",a.automationLevel]})]})},a.id))})]})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg p-4 border border-gray-300",children:[(0,d.jsxs)("h4",{className:"font-semibold text-gray-800 mb-2",children:["\uD83D\uDCCA ",c("bakery.kitchen.stats","Kitchen Stats")]}),(0,d.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-lg font-bold text-green-600",children:a.filter(a=>a.isActive).length}),(0,d.jsx)("div",{className:"text-gray-600",children:c("bakery.active.equipment","Active Equipment")})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-lg font-bold text-blue-600",children:a.filter(a=>a.automationLevel>0).length}),(0,d.jsx)("div",{className:"text-gray-600",children:c("bakery.automated.equipment","Automated Equipment")})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("div",{className:"text-lg font-bold text-purple-600",children:[Math.round(a.reduce((a,b)=>a+b.efficiency,0)/a.length*100),"%"]}),(0,d.jsx)("div",{className:"text-gray-600",children:c("bakery.efficiency","Efficiency")})]})]})]})]}),"dining"===l&&(0,d.jsxs)("div",{className:"p-6",children:[(0,d.jsx)("div",{className:"grid grid-cols-4 gap-4 mb-6",children:[1,2,3,4,5,6,7,8].map(a=>{let b=j[a-1];return(0,d.jsxs)("div",{className:"bg-white rounded-lg p-4 border-2 border-brown-300 relative",style:{backgroundColor:"#8B4513",color:"white"},children:[(0,d.jsxs)("div",{className:"text-center mb-2",children:[(0,d.jsx)("div",{className:"text-2xl",children:"\uD83E\uDE91"}),(0,d.jsxs)("div",{className:"text-xs",children:["Table ",a]})]}),b?(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-2xl mb-1",children:b.avatar}),(0,d.jsx)("div",{className:"text-xs font-medium",children:b.name}),(0,d.jsx)("div",{className:"text-xs opacity-75",children:b.order}),(0,d.jsx)("div",{className:"mt-2",children:(0,d.jsx)("div",{className:`w-full h-1 rounded ${b.satisfaction>70?"bg-green-400":b.satisfaction>40?"bg-yellow-400":"bg-red-400"}`,style:{width:`${b.satisfaction}%`}})})]}):(0,d.jsx)("div",{className:"text-center text-gray-400",children:(0,d.jsx)("div",{className:"text-xs",children:"Empty"})})]},a)})}),(0,d.jsxs)("div",{className:"bg-white rounded-lg p-4 border border-gray-300",children:[(0,d.jsxs)("h4",{className:"font-semibold text-gray-800 mb-2",children:["\uD83C\uDF7D️ ",c("bakery.dining.stats","Dining Stats")]}),(0,d.jsxs)("div",{className:"grid grid-cols-4 gap-4 text-sm",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-lg font-bold text-green-600",children:j.length}),(0,d.jsx)("div",{className:"text-gray-600",children:c("bakery.current.customers","Current Customers")})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-lg font-bold text-blue-600",children:j.filter(a=>"waiting"===a.status).length}),(0,d.jsx)("div",{className:"text-gray-600",children:c("bakery.waiting.customers","Waiting")})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-lg font-bold text-purple-600",children:j.filter(a=>"eating"===a.status).length}),(0,d.jsx)("div",{className:"text-gray-600",children:c("bakery.eating.customers","Eating")})]}),(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsxs)("div",{className:"text-lg font-bold text-orange-600",children:[j.length>0?Math.round(j.reduce((a,b)=>a+b.satisfaction,0)/j.length):100,"%"]}),(0,d.jsx)("div",{className:"text-gray-600",children:c("bakery.avg.satisfaction","Avg Satisfaction")})]})]})]})]}),"counter"===l&&(0,d.jsx)("div",{className:"p-6",children:(0,d.jsxs)("div",{className:"bg-gradient-to-r from-yellow-100 to-orange-100 rounded-lg p-6 border-2 border-orange-300",children:[(0,d.jsxs)("h3",{className:"text-xl font-bold text-orange-800 mb-4 text-center",children:["\uD83D\uDED2 ",c("bakery.service.counter","Service Counter")]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg p-4 border-2 border-gray-300 mb-4",children:[(0,d.jsxs)("h4",{className:"font-semibold text-gray-800 mb-3",children:["\uD83E\uDDC1 ",c("bakery.display.case","Display Case")]}),(0,d.jsx)("div",{className:"grid grid-cols-4 gap-3",children:["\uD83C\uDF6A","\uD83E\uDDC1","\uD83E\uDD50","\uD83C\uDF5E","\uD83E\uDD67","\uD83C\uDF70","\uD83E\uDD68","\uD83E\uDDC7"].map((a,b)=>(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3 text-center border",children:[(0,d.jsx)("div",{className:"text-2xl mb-1",children:a}),(0,d.jsx)("div",{className:"text-xs text-gray-600",children:"Fresh"})]},b))})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg p-4 border-2 border-gray-300",children:[(0,d.jsxs)("h4",{className:"font-semibold text-gray-800 mb-3",children:["\uD83D\uDCCB ",c("bakery.order.queue","Order Queue")]}),(0,d.jsx)("div",{className:"space-y-2",children:h.slice(0,3).map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-lg",children:(a=>{let b=["\uD83D\uDC68‍\uD83D\uDCBC","\uD83D\uDC69‍\uD83D\uDCBC","\uD83D\uDC68‍\uD83C\uDF93","\uD83D\uDC69‍\uD83C\uDF93","\uD83D\uDC68‍\uD83C\uDF73","\uD83D\uDC69‍\uD83C\uDF73","\uD83D\uDC68‍⚕️","\uD83D\uDC69‍⚕️","\uD83D\uDC68‍\uD83C\uDFA8","\uD83D\uDC69‍\uD83C\uDFA8"];return b[a.length%b.length]})(a.customerName)}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium text-sm",children:a.customerName}),(0,d.jsx)("div",{className:"text-xs text-gray-600",children:a.items[0]})]})]}),(0,d.jsxs)("div",{className:"text-right",children:[(0,d.jsxs)("div",{className:"text-sm font-bold text-green-600",children:["$",a.reward]}),(0,d.jsxs)("div",{className:"text-xs text-gray-500",children:[Math.floor(a.timeLimit/60),"m left"]})]})]},a.id))})]})]})})]}),(0,d.jsx)("div",{className:"mt-4 bg-gray-100 rounded-lg p-3",children:(0,d.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,d.jsxs)("div",{className:"flex space-x-4",children:[(0,d.jsxs)("span",{children:["\uD83D\uDCB0 $",i.money]}),(0,d.jsxs)("span",{children:["⭐ Level ",i.level]}),(0,d.jsxs)("span",{children:["\uD83D\uDCE6 ",h.length," Orders"]})]}),(0,d.jsxs)("div",{className:"text-gray-600",children:[c("bakery.last.updated","Last updated"),": ",new Date().toLocaleTimeString()]})]})})]})}function C({isOpen:a,onClose:b}){let{t:c}=(0,f.o)(),{orders:h,player:i,completeOrder:j}=(0,g.I)(),[k,l]=(0,e.useState)([]),[m,n]=(0,e.useState)(null),o=a=>{switch(a){case"happy":return"\uD83D\uDE0A";case"neutral":return"\uD83D\uDE10";case"impatient":return"\uD83D\uDE24";case"angry":return"\uD83D\uDE20"}},p=a=>{switch(a){case"happy":return"text-green-600 bg-green-100";case"neutral":return"text-yellow-600 bg-yellow-100";case"impatient":return"text-orange-600 bg-orange-100";case"angry":return"text-red-600 bg-red-100"}},q=a=>{switch(a){case"entering":return"\uD83D\uDEB6";case"waiting":return"⏰";case"ordering":return"\uD83D\uDCDD";case"served":return"\uD83C\uDF7D️";case"eating":return"\uD83D\uDE0B";case"leaving":return"\uD83D\uDC4B"}};return a?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-500 p-6 text-white",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h2",{className:"text-2xl font-bold",children:["\uD83D\uDC65 ",c("customers.manager.title","Customer Manager")]}),(0,d.jsx)("p",{className:"text-blue-100 text-sm",children:c("customers.manager.subtitle","Monitor and serve your customers")})]}),(0,d.jsxs)("button",{onClick:b,className:"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors",children:["✕ ",c("common.close","Close")]})]})}),(0,d.jsxs)("div",{className:"flex h-[70vh]",children:[(0,d.jsxs)("div",{className:"w-1/2 p-6 border-r border-gray-200 overflow-y-auto",children:[(0,d.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:["\uD83D\uDCCB ",c("customers.current.list","Current Customers")," (",k.length,")"]}),(0,d.jsxs)("div",{className:"space-y-3",children:[k.map(a=>(0,d.jsxs)("div",{onClick:()=>n(a),className:`p-4 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md ${m?.id===a.id?"border-blue-400 bg-blue-50":"border-gray-300 bg-white hover:border-gray-400"}`,children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,d.jsx)("span",{className:"text-2xl",children:a.avatar}),(0,d.jsxs)("div",{children:[(0,d.jsx)("h4",{className:"font-medium text-gray-800",children:a.name}),(0,d.jsxs)("p",{className:"text-sm text-gray-600",children:[c("customers.table","Table")," ",a.tableNumber]})]})]}),(0,d.jsx)("div",{className:"text-right",children:(0,d.jsxs)("div",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${p(a.mood)}`,children:[o(a.mood)," ",a.mood]})})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-lg",children:q(a.status)}),(0,d.jsx)("span",{className:"text-sm text-gray-600 capitalize",children:a.status})]}),(0,d.jsxs)("div",{className:"text-sm font-bold text-green-600",children:["$",a.orderValue]})]}),(0,d.jsxs)("div",{className:"mt-2",children:[(0,d.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mb-1",children:[(0,d.jsx)("span",{children:c("customers.patience","Patience")}),(0,d.jsxs)("span",{children:[Math.round(a.satisfaction),"%"]})]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,d.jsx)("div",{className:`h-2 rounded-full transition-all duration-300 ${a.satisfaction>60?"bg-green-500":a.satisfaction>30?"bg-yellow-500":"bg-red-500"}`,style:{width:`${a.satisfaction}%`}})})]})]},a.id)),0===k.length&&(0,d.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,d.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFEA"}),(0,d.jsx)("p",{children:c("customers.no.customers","No customers currently")}),(0,d.jsx)("p",{className:"text-sm",children:c("customers.waiting.for.orders","Waiting for new orders...")})]})]})]}),(0,d.jsx)("div",{className:"w-1/2 p-6 overflow-y-auto",children:m?(0,d.jsxs)("div",{children:[(0,d.jsxs)("div",{className:"text-center mb-6",children:[(0,d.jsx)("div",{className:"text-6xl mb-2",children:m.avatar}),(0,d.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:m.name}),(0,d.jsxs)("p",{className:"text-gray-600",children:[c("customers.table","Table")," ",m.tableNumber]})]}),(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:[(0,d.jsxs)("h4",{className:"font-semibold text-gray-800 mb-3",children:["\uD83D\uDCDD ",c("customers.order.details","Order Details")]}),(0,d.jsx)("div",{className:"space-y-2",children:m.orderItems.map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center justify-between p-2 bg-white rounded border",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{children:"\uD83E\uDDC1"}),(0,d.jsx)("span",{className:"font-medium",children:a})]}),(0,d.jsxs)("span",{className:"text-green-600 font-bold",children:["$",Math.round(m.orderValue/m.orderItems.length)]})]},b))}),(0,d.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:(0,d.jsxs)("div",{className:"flex justify-between font-bold",children:[(0,d.jsxs)("span",{children:[c("customers.total","Total"),":"]}),(0,d.jsxs)("span",{className:"text-green-600",children:["$",m.orderValue]})]})})]}),(0,d.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 mb-4",children:[(0,d.jsxs)("h4",{className:"font-semibold text-blue-800 mb-3",children:["ℹ️ ",c("customers.info","Customer Info")]}),(0,d.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsxs)("span",{children:[c("customers.mood","Mood"),":"]}),(0,d.jsxs)("span",{className:`font-medium ${p(m.mood)}`,children:[o(m.mood)," ",m.mood]})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsxs)("span",{children:[c("customers.status","Status"),":"]}),(0,d.jsxs)("span",{className:"font-medium capitalize",children:[q(m.status)," ",m.status]})]}),(0,d.jsxs)("div",{className:"flex justify-between",children:[(0,d.jsxs)("span",{children:[c("customers.patience","Patience"),":"]}),(0,d.jsxs)("span",{className:"font-medium",children:[Math.round(m.satisfaction),"%"]})]})]})]}),(0,d.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4 mb-4",children:[(0,d.jsxs)("h4",{className:"font-semibold text-purple-800 mb-3",children:["❤️ ",c("customers.preferences","Preferences")]}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:m.preferences.map((a,b)=>(0,d.jsx)("span",{className:"px-3 py-1 bg-purple-200 text-purple-800 rounded-full text-sm font-medium",children:a},b))})]}),"waiting"===m.status&&(0,d.jsx)("div",{className:"space-y-2",children:(0,d.jsxs)("button",{onClick:()=>{j(m.id),l(a=>a.map(a=>a.id===m.id?{...a,status:"served"}:a))},className:"w-full bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-4 rounded-lg transition-colors",children:["\uD83C\uDF7D️ ",c("customers.serve.order","Serve Order")]})})]}):(0,d.jsxs)("div",{className:"text-center py-12 text-gray-500",children:[(0,d.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDC46"}),(0,d.jsx)("p",{className:"text-lg",children:c("customers.select.customer","Select a customer")}),(0,d.jsx)("p",{className:"text-sm",children:c("customers.select.to.view.details","Select a customer to view details")})]})})]})]})}):null}function D({onCustomerClick:a}){let{t:b}=(0,f.o)(),{orders:c}=(0,g.I)(),[h,i]=(0,e.useState)([]),[j,k]=(0,e.useState)(!0);return(0,d.jsxs)("div",{className:"bg-gradient-to-br from-amber-50 to-orange-50 rounded-lg p-6 min-h-[600px] relative overflow-hidden",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h2",{className:"text-2xl font-bold text-orange-800",children:["\uD83C\uDF7D️ ",b("dining.room.title","Dining Room")]}),(0,d.jsx)("p",{className:"text-orange-600",children:b("dining.room.subtitle","Watch your customers enjoy their meals")})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg px-4 py-2 border border-orange-200",children:[(0,d.jsx)("div",{className:"text-sm text-gray-600",children:b("dining.occupied.tables","Occupied Tables")}),(0,d.jsxs)("div",{className:"text-xl font-bold text-orange-600",children:[h.filter(a=>a.isOccupied).length,"/",h.length]})]}),(0,d.jsx)("button",{onClick:()=>k(!j),className:`p-2 rounded-lg transition-colors ${j?"bg-green-100 text-green-600":"bg-gray-100 text-gray-600"}`,title:b("dining.ambient.sounds","Toggle ambient sounds"),children:j?"\uD83D\uDD0A":"\uD83D\uDD07"})]})]}),(0,d.jsxs)("div",{className:"relative bg-white rounded-lg border-2 border-orange-200 h-96 overflow-hidden",children:[(0,d.jsx)("div",{className:"absolute inset-0 opacity-10",children:(0,d.jsx)("div",{className:"grid grid-cols-8 grid-rows-6 h-full",children:Array.from({length:48}).map((a,b)=>(0,d.jsx)("div",{className:"border border-gray-300"},b))})}),h.map(b=>{var c;return(0,d.jsxs)("div",{className:(a=>{let b=a.seats<=2?"w-16 h-16":a.seats<=4?"w-20 h-20":"w-24 h-24",c=a.isOccupied?"bg-orange-100 border-orange-300":"bg-gray-100 border-gray-300";return`absolute transition-all duration-300 hover:scale-105 cursor-pointer ${b} ${c} border-2 rounded-lg flex flex-col items-center justify-center`})(b),style:{left:`${b.position.x}px`,top:`${b.position.y}px`},onClick:()=>b.customer&&a?.(b.customer.id),children:[(0,d.jsx)("div",{className:"text-2xl mb-1",children:(c=b.seats)<=2?"\uD83E\uDE91":c<=4?"\uD83C\uDF7D️":"\uD83C\uDFDB️"}),(0,d.jsxs)("div",{className:"text-xs font-bold text-gray-600",children:["#",b.id]}),b.customer&&(0,d.jsxs)("div",{className:"absolute -top-8 left-1/2 transform -translate-x-1/2",children:[(0,d.jsx)("div",{className:"bg-white rounded-full p-1 border-2 border-orange-300 shadow-lg",children:(0,d.jsx)("span",{className:"text-lg",children:b.customer.avatar})}),(0,d.jsx)("div",{className:"absolute -bottom-1 -right-1",children:(0,d.jsx)("div",{className:`w-3 h-3 rounded-full ${b.customer.satisfaction>80?"bg-green-500":b.customer.satisfaction>50?"bg-yellow-500":b.customer.satisfaction>20?"bg-orange-500":"bg-red-500"}`})})]}),b.customer&&(0,d.jsx)("div",{className:"absolute -bottom-6 left-1/2 transform -translate-x-1/2",children:(0,d.jsx)("div",{className:"animate-bounce text-xs",children:"\uD83C\uDF7D️"})})]},b.id)}),(0,d.jsx)("div",{className:"absolute top-4 left-4 text-2xl",children:"\uD83E\uDEB4"}),(0,d.jsx)("div",{className:"absolute top-4 right-4 text-2xl",children:"\uD83E\uDEB4"}),(0,d.jsx)("div",{className:"absolute bottom-4 left-4 text-2xl",children:"\uD83D\uDD6F️"}),(0,d.jsx)("div",{className:"absolute bottom-4 right-4 text-2xl",children:"\uD83D\uDD6F️"}),(0,d.jsx)("div",{className:"absolute bottom-0 left-1/2 transform -translate-x-1/2 bg-brown-200 rounded-t-lg p-2 border-2 border-brown-300",children:(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-lg",children:"\uD83D\uDECE️"}),(0,d.jsx)("div",{className:"text-xs font-bold",children:b("dining.service.counter","Service")})]})})]}),(0,d.jsxs)("div",{className:"mt-6 bg-white rounded-lg p-4 border border-orange-200",children:[(0,d.jsxs)("h3",{className:"font-semibold text-gray-800 mb-3",children:["\uD83D\uDC65 ",b("dining.customer.status","Customer Status")]}),h.filter(a=>a.isOccupied).length>0?(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:h.filter(a=>a.isOccupied).map(c=>{var e;return(0,d.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3 border cursor-pointer hover:bg-gray-100 transition-colors",onClick:()=>c.customer&&a?.(c.customer.id),children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)("span",{className:"text-lg",children:c.customer?.avatar}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium text-sm",children:c.customer?.name}),(0,d.jsxs)("div",{className:"text-xs text-gray-600",children:["Table ",c.id]})]})]}),(0,d.jsx)("div",{className:`text-lg ${(e=c.customer?.satisfaction||0)>80?"text-green-500":e>50?"text-yellow-500":e>20?"text-orange-500":"text-red-500"}`,children:c.customer?.satisfaction&&c.customer.satisfaction>80?"\uD83D\uDE0A":c.customer?.satisfaction&&c.customer.satisfaction>50?"\uD83D\uDE10":c.customer?.satisfaction&&c.customer.satisfaction>20?"\uD83D\uDE24":"\uD83D\uDE20"})]}),(0,d.jsxs)("div",{className:"text-xs text-gray-600 mb-2",children:[b("dining.enjoying","Enjoying"),": ",c.customer?.order]}),(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-1",children:(0,d.jsx)("div",{className:`h-1 rounded-full transition-all duration-300 ${(c.customer?.satisfaction||0)>60?"bg-green-500":(c.customer?.satisfaction||0)>30?"bg-yellow-500":"bg-red-500"}`,style:{width:`${c.customer?.satisfaction||0}%`}})})]},c.id)})}):(0,d.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,d.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFEA"}),(0,d.jsx)("p",{children:b("dining.no.customers","No customers dining currently")}),(0,d.jsx)("p",{className:"text-sm",children:b("dining.waiting.for.customers","Complete orders to see customers dining")})]})]}),j&&(0,d.jsxs)("div",{className:"absolute top-2 right-2 text-xs text-gray-500 animate-pulse",children:["\uD83C\uDFB5 ",b("dining.ambient.playing","Ambient sounds playing")]})]})}function E({player:a,onOpenMenu:b,onOpenAchievements:c,onOpenSkills:g,onOpenBakeries:i,onOpenSettings:j}){let{t:k}=(0,f.o)(),[l,m]=(0,e.useState)(!1),n=a.experience/a.maxExperience*100;return(0,d.jsx)("div",{className:"bg-white shadow-lg border-b border-gray-200 relative",children:(0,d.jsxs)("div",{className:"px-6 py-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)(h.$,{variant:"secondary",size:"sm",className:"bg-orange-100 hover:bg-orange-200 text-orange-800",onClick:b,children:["☰ ",k("toolbar.menu","Menu")]}),(0,d.jsx)("div",{className:"hidden md:block",children:(0,d.jsx)("h1",{className:"text-xl font-bold text-orange-800",children:"\uD83E\uDD56 Bake It Out"})})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,d.jsxs)("div",{className:"text-center",children:[(0,d.jsx)("div",{className:"text-sm font-medium text-gray-800",children:a.name}),(0,d.jsxs)("div",{className:"text-xs text-gray-500",children:["Level ",a.level]})]}),(0,d.jsxs)("div",{className:"hidden sm:block",children:[(0,d.jsx)("div",{className:"w-32 bg-gray-200 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300",style:{width:`${n}%`}})}),(0,d.jsxs)("div",{className:"text-xs text-center text-gray-500 mt-1",children:[a.experience,"/",a.maxExperience," XP"]})]}),(0,d.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,d.jsxs)("span",{className:"text-green-800 font-medium",children:["$",a.money]})}),a.skillPoints>0&&(0,d.jsxs)("div",{className:"bg-yellow-100 px-3 py-1 rounded-full relative",children:[(0,d.jsxs)("span",{className:"text-yellow-800 font-medium",children:["⭐ ",a.skillPoints]}),(0,d.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]})]}),(0,d.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,d.jsx)(h.$,{variant:"secondary",size:"sm",className:"md:hidden",onClick:()=>m(!l),children:"⚡"}),(0,d.jsxs)("div",{className:"hidden md:flex items-center space-x-2",children:[(0,d.jsx)(h.$,{variant:"secondary",size:"sm",onClick:i,children:"\uD83C\uDFEA"}),(0,d.jsx)(h.$,{variant:"secondary",size:"sm",onClick:c,className:"relative",children:"\uD83C\uDFC6"}),(0,d.jsxs)(h.$,{variant:"secondary",size:"sm",onClick:g,className:a.skillPoints>0?"bg-yellow-100 hover:bg-yellow-200":"",children:["\uD83C\uDF1F",a.skillPoints>0&&(0,d.jsx)("span",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]}),(0,d.jsx)(h.$,{variant:"secondary",size:"sm",onClick:j,children:"⚙️"})]})]})]}),l&&(0,d.jsxs)("div",{className:"md:hidden mt-4 pt-4 border-t border-gray-200",children:[(0,d.jsxs)("div",{className:"grid grid-cols-4 gap-2",children:[(0,d.jsxs)(h.$,{variant:"secondary",size:"sm",className:"flex flex-col items-center py-3",onClick:()=>{i(),m(!1)},children:[(0,d.jsx)("span",{className:"text-lg",children:"\uD83C\uDFEA"}),(0,d.jsx)("span",{className:"text-xs",children:k("toolbar.bakeries","Bakeries")})]}),(0,d.jsxs)(h.$,{variant:"secondary",size:"sm",className:"flex flex-col items-center py-3",onClick:()=>{c(),m(!1)},children:[(0,d.jsx)("span",{className:"text-lg",children:"\uD83C\uDFC6"}),(0,d.jsx)("span",{className:"text-xs",children:k("toolbar.achievements","Achievements")})]}),(0,d.jsxs)(h.$,{variant:"secondary",size:"sm",className:`flex flex-col items-center py-3 relative ${a.skillPoints>0?"bg-yellow-100 hover:bg-yellow-200":""}`,onClick:()=>{g(),m(!1)},children:[(0,d.jsx)("span",{className:"text-lg",children:"\uD83C\uDF1F"}),(0,d.jsx)("span",{className:"text-xs",children:k("toolbar.skills","Skills")}),a.skillPoints>0&&(0,d.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]}),(0,d.jsxs)(h.$,{variant:"secondary",size:"sm",className:"flex flex-col items-center py-3",onClick:()=>{j(),m(!1)},children:[(0,d.jsx)("span",{className:"text-lg",children:"⚙️"}),(0,d.jsx)("span",{className:"text-xs",children:k("toolbar.settings","Settings")})]})]}),(0,d.jsxs)("div",{className:"mt-3",children:[(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300",style:{width:`${n}%`}})}),(0,d.jsxs)("div",{className:"text-xs text-center text-gray-500 mt-1",children:[a.experience,"/",a.maxExperience," XP"]})]})]})]})})}function F({isOpen:a,onClose:b,onSaveGame:c,onLoadGame:e,onSettings:g,onMainMenu:i,onExit:j}){let{t:k}=(0,f.o)();return a?(0,d.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden",children:[(0,d.jsx)("div",{className:"bg-gradient-to-r from-orange-500 to-yellow-500 p-6 text-white",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsxs)("h2",{className:"text-2xl font-bold",children:["\uD83C\uDFAE ",k("gameMenu.title","Game Menu")]}),(0,d.jsx)("p",{className:"text-orange-100 text-sm",children:k("gameMenu.subtitle","Manage your game")})]}),(0,d.jsx)(h.$,{variant:"secondary",size:"sm",className:"bg-white/20 hover:bg-white/30 text-white border-white/30",onClick:b,children:"✕"})]})}),(0,d.jsxs)("div",{className:"p-6 space-y-3",children:[(0,d.jsxs)(h.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-orange-50",onClick:()=>{b()},children:[(0,d.jsx)("span",{className:"text-2xl mr-3",children:"▶️"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-semibold",children:k("gameMenu.resume","Resume Game")}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:k("gameMenu.resumeDesc","Continue playing")})]})]}),(0,d.jsxs)(h.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-green-50",onClick:()=>{c(),b()},children:[(0,d.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDCBE"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-semibold",children:k("gameMenu.save","Save Game")}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:k("gameMenu.saveDesc","Save your progress")})]})]}),(0,d.jsxs)(h.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-blue-50",onClick:()=>{e(),b()},children:[(0,d.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDCC1"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-semibold",children:k("gameMenu.load","Load Game")}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:k("gameMenu.loadDesc","Load saved progress")})]})]}),(0,d.jsxs)(h.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-purple-50",onClick:()=>{g(),b()},children:[(0,d.jsx)("span",{className:"text-2xl mr-3",children:"⚙️"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-semibold",children:k("gameMenu.settings","Settings")}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:k("gameMenu.settingsDesc","Game preferences")})]})]}),(0,d.jsxs)("div",{className:"border-t pt-3 mt-4",children:[(0,d.jsxs)(h.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-yellow-50",onClick:()=>{i(),b()},children:[(0,d.jsx)("span",{className:"text-2xl mr-3",children:"\uD83C\uDFE0"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-semibold",children:k("gameMenu.mainMenu","Main Menu")}),(0,d.jsx)("div",{className:"text-sm text-gray-500",children:k("gameMenu.mainMenuDesc","Return to main menu")})]})]}),j&&(0,d.jsxs)(h.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-red-50 text-red-600",onClick:()=>{j(),b()},children:[(0,d.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDEAA"}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-semibold",children:k("gameMenu.exit","Exit Game")}),(0,d.jsx)("div",{className:"text-sm text-red-400",children:k("gameMenu.exitDesc","Close the application")})]})]})]})]}),(0,d.jsx)("div",{className:"bg-gray-50 px-6 py-3 text-center text-sm text-gray-500",children:k("gameMenu.tip","Press ESC to open this menu anytime")})]})}):null}new z;var G=c(1100);function H(){let{t:a}=(0,f.o)(),{setGameActivity:b,setBakingActivity:c}=(0,G.l)(),{player:k,equipment:s,inventory:u,orders:x,achievements:z,skills:H,levelUpRewards:I,showLevelUp:J,updateEquipment:K,acceptOrder:L,completeOrder:M,declineOrder:N,generateNewOrder:O,upgradeSkill:P,checkAchievements:Q,dismissLevelUp:R,spendMoney:S}=(0,g.I)(),[T,U]=(0,e.useState)(!1),[V,W]=(0,e.useState)(!1),[X,Y]=(0,e.useState)(!1),[Z,$]=(0,e.useState)(!1),[_,aa]=(0,e.useState)(!1),[ab,ac]=(0,e.useState)(!1),[ad,ae]=(0,e.useState)(!1),[af,ag]=(0,e.useState)(!1),[ah,ai]=(0,e.useState)(!1),[aj,ak]=(0,e.useState)(!1),[al,am]=(0,e.useState)(!1),[an,ao]=(0,e.useState)(!1),[ap,aq]=(0,e.useState)("save"),[ar,as]=(0,e.useState)(null),[at,au]=(0,e.useState)("traditional"),[av,aw]=(0,e.useState)({language:"en",soundEnabled:!0,musicEnabled:!0,notificationsEnabled:!0,autoSaveEnabled:!0,graphicsQuality:"medium",animationSpeed:1,showTutorials:!0}),[ax,ay]=(0,e.useState)([{id:"main",name:"Downtown Delights",location:"City Center",specialization:"general",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],unlocked:!0,purchaseCost:0}]),[az,aA]=(0,e.useState)("main"),{notifications:aB,removeNotification:aC,showSuccess:aD,showError:aE,showInfo:aF}=function(){let[a,b]=(0,e.useState)([]),c=a=>{let c=Date.now().toString()+Math.random().toString(36).substr(2,9),d={...a,id:c,duration:a.duration||5e3};b(a=>[...a,d])};return{notifications:a,addNotification:c,removeNotification:a=>{b(b=>b.filter(b=>b.id!==a))},showSuccess:(a,b)=>{c({type:"success",title:a,message:b})},showError:(a,b)=>{c({type:"error",title:a,message:b})},showWarning:(a,b)=>{c({type:"warning",title:a,message:b})},showInfo:(a,b)=>{c({type:"info",title:a,message:b})}}}(),aG=(a,b)=>{as({id:a,name:b}),Y(!0)},aH=a=>{L(a),aF("Order Accepted","You have accepted a new order!")},aI=a=>{let b=x.find(b=>b.id===a);b&&(M(a),Q(),aD("Order Completed!",`You earned $${b.reward} and gained experience!`))},aJ=a=>{N(a),aF("Order Declined","Order has been removed from your queue.")};return(0,d.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-orange-50 to-yellow-50",children:[(0,d.jsx)(E,{player:k,onOpenMenu:()=>ak(!0),onOpenAchievements:()=>$(!0),onOpenSkills:()=>aa(!0),onOpenBakeries:()=>ai(!0),onOpenSettings:()=>ag(!0)}),(0,d.jsx)("div",{className:"max-w-7xl mx-auto px-6 pb-4",children:(0,d.jsxs)("div",{className:"flex justify-center space-x-2",children:[(0,d.jsxs)("button",{onClick:()=>au("traditional"),className:`px-4 py-2 rounded-lg font-medium transition-colors ${"traditional"===at?"bg-orange-500 text-white":"bg-white text-gray-700 hover:bg-gray-100 border border-gray-300"}`,children:["\uD83D\uDCCA ",a("game.view.traditional","Traditional View")]}),(0,d.jsxs)("button",{onClick:()=>au("layout"),className:`px-4 py-2 rounded-lg font-medium transition-colors ${"layout"===at?"bg-orange-500 text-white":"bg-white text-gray-700 hover:bg-gray-100 border border-gray-300"}`,children:["\uD83C\uDFEA ",a("game.view.layout","Bakery Layout")]}),(0,d.jsxs)("button",{onClick:()=>au("dining"),className:`px-4 py-2 rounded-lg font-medium transition-colors ${"dining"===at?"bg-orange-500 text-white":"bg-white text-gray-700 hover:bg-gray-100 border border-gray-300"}`,children:["\uD83C\uDF7D️ ",a("game.view.dining","Dining Room")]}),(0,d.jsxs)("button",{onClick:()=>ao(!0),className:"px-4 py-2 rounded-lg font-medium bg-blue-500 text-white hover:bg-blue-600 transition-colors",children:["\uD83D\uDC65 ",a("game.view.customers","Customer Manager")]})]})}),(0,d.jsxs)("div",{className:"max-w-7xl mx-auto p-6 grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,d.jsxs)("div",{className:"lg:col-span-3 space-y-6",children:["layout"===at&&(0,d.jsx)(B,{equipment:s,onEquipmentClick:aG}),"dining"===at&&(0,d.jsx)(D,{onCustomerClick:a=>{ao(!0)}}),"traditional"===at&&(0,d.jsxs)(d.Fragment,{children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-orange-800",children:a("kitchen.title")}),(0,d.jsxs)("div",{className:"text-sm text-gray-600",children:["Current: ",ax.find(a=>a.id===az)?.name]})]}),(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:s.map(a=>(0,d.jsx)(i.$,{equipment:a,onClick:aG},a.id))})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-orange-800 mb-4",children:a("inventory.title")}),(0,d.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:u.map(b=>(0,d.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,d.jsx)("div",{className:"text-2xl mb-1",children:b.icon}),(0,d.jsx)("div",{className:"font-medium text-gray-800",children:b.name}),(0,d.jsx)("div",{className:"text-sm text-gray-600",children:a("inventory.quantity",{qty:b.quantity.toString()})}),(0,d.jsx)("div",{className:"text-xs text-green-600",children:a("inventory.cost",{cost:b.cost.toString()})})]},b.name))})]})]})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-orange-800",children:a("orders.title")}),(0,d.jsx)(h.$,{size:"sm",variant:"primary",onClick:O,children:a("orders.newOrder")})]}),(0,d.jsx)("div",{className:"space-y-4",children:x.map(a=>(0,d.jsx)(j.p,{order:a,onAccept:aH,onDecline:aJ,onComplete:aI},a.id))})]}),(0,d.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,d.jsx)("h2",{className:"text-xl font-semibold text-orange-800 mb-4",children:a("actions.title")}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(h.$,{variant:"secondary",size:"sm",className:"w-full",onClick:()=>W(!0),children:a("actions.buyIngredients")}),(0,d.jsx)(h.$,{variant:"secondary",size:"sm",className:"w-full",onClick:()=>U(!0),children:a("actions.viewRecipes")}),(0,d.jsx)(h.$,{variant:"secondary",size:"sm",className:"w-full",onClick:()=>ae(!0),children:a("actions.equipmentShop")})]})]})]})]}),(0,d.jsx)(l,{isOpen:T,onClose:()=>U(!1)}),(0,d.jsx)(m,{isOpen:V,onClose:()=>W(!1)}),(0,d.jsx)(n,{isOpen:X,onClose:()=>Y(!1),equipmentId:ar?.id||"",equipmentName:ar?.name||""}),(0,d.jsx)(q,{isOpen:Z,onClose:()=>$(!1),achievements:z}),(0,d.jsx)(r,{isOpen:_,onClose:()=>aa(!1),skills:H,skillPoints:k.skillPoints,playerLevel:k.level,onUpgradeSkill:P}),(0,d.jsx)(p,{isOpen:J,onClose:R,newLevel:k.level,rewards:I}),(0,d.jsx)(t,{isOpen:ab,onClose:()=>ac(!1)}),(0,d.jsx)(v,{isOpen:ad,onClose:()=>ae(!1),onShowSuccess:aD}),(0,d.jsx)(w.b,{isOpen:af,onClose:()=>ag(!1),settings:av,onSettingsChange:a=>{aw(b=>({...b,...a}))}}),(0,d.jsx)(y,{isOpen:ah,onClose:()=>ai(!1),bakeries:ax,currentBakeryId:az,onSwitchBakery:a=>{aA(a),aF("Bakery Switched",`Switched to ${ax.find(b=>b.id===a)?.name}`)},onPurchaseBakery:a=>{if(S(a.purchaseCost)){let b={id:Date.now().toString(),name:a.name,location:"Downtown",specialization:"general",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],unlocked:!0,purchaseCost:a.purchaseCost};ay(a=>[...a,b]),aD("Bakery Purchased!",`You now own ${a.name}!`)}},playerMoney:k.money}),(0,d.jsx)(C,{isOpen:an,onClose:()=>ao(!1)}),(0,d.jsx)(F,{isOpen:aj,onClose:()=>ak(!1),onSaveGame:()=>{aq("save"),am(!0)},onLoadGame:()=>{aq("load"),am(!0)},onSettings:()=>ag(!0),onMainMenu:()=>{},onExit:void 0}),(0,d.jsx)(A,{isOpen:al,onClose:()=>am(!1),mode:ap,onSaveSuccess:()=>{aD("Game Saved!","Your progress has been saved successfully."),am(!1)},onLoadSuccess:()=>{aD("Game Loaded!","Your saved progress has been loaded."),am(!1)}}),(0,d.jsx)(o,{notifications:aB,onRemove:aC})]})}function I(){return(0,d.jsx)(g.S,{children:(0,d.jsx)(H,{})})}},4735:a=>{"use strict";a.exports=require("events")},5511:a=>{"use strict";a.exports=require("crypto")},5591:a=>{"use strict";a.exports=require("https")},5995:(a,b,c)=>{Promise.resolve().then(c.bind(c,4663))},6439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},6713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},6894:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(5239),e=c(8088),f=c(7220),g=c(1289),h=c(6191),i=c(4823),j=c(1998),k=c(2603),l=c(4649),m=c(2781),n=c(2602),o=c(1268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(6713),u=c(3365),v=c(1454),w=c(7778),x=c(6143),y=c(9105),z=c(8171),A=c(6439),B=c(6133),C=c.n(B),D=c(893),E=c(2836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["game",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,1105)),"C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\app\\game\\page.tsx"]}]},{metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,4431)),"C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,6133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,9868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,9615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\coding stuff\\bake it out\\bake-it-out\\src\\app\\game\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/game/page",pathname:"/game",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/game/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},7265:a=>{"use strict";a.exports=require("child_process")},7910:a=>{"use strict";a.exports=require("stream")},8354:a=>{"use strict";a.exports=require("util")},9021:a=>{"use strict";a.exports=require("fs")},9043:(a,b,c)=>{Promise.resolve().then(c.bind(c,1105))},9121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9428:a=>{"use strict";a.exports=require("buffer")},9551:a=>{"use strict";a.exports=require("url")}};var b=require("../../webpack-runtime.js");b.C(a);var c=b.X(0,[985,976,429,181],()=>b(b.s=6894));module.exports=c})();