# 🧁 Bake It Out

**The easiest bakery management game to set up and play!**

A comprehensive bakery management game with cloud saves, multiplayer, and a professional server dashboard. Built with Next.js and featuring Czech/English language support.

## ⚡ **30-Second Setup**

**Want to get started immediately? Just run this:**

```bash
# Windows - One-click setup for everything
EASY-SETUP.bat

# Or step by step:
npm install && npm run build
cd server && npm run setup-easy
```

**That's it! You'll have:**
- 🎮 **Complete game** running locally
- 🌐 **Professional server** with dashboard
- ☁️ **Cloud saves** for cross-device play
- 👥 **Multiplayer support** for friends
- 📊 **Analytics dashboard** for monitoring

---

## 🎯 **What You Get**

### 🎮 **Complete Gaming Experience**
- **Bakery Management** - Build and manage your bakery empire
- **Progressive Gameplay** - Unlock new recipes, equipment, and features
- **Achievement System** - Complete challenges and earn rewards
- **Skill Trees** - Develop your baking expertise
- **Multiple Languages** - Play in English or Czech

### ☁️ **Cloud Gaming Platform**
- **Cloud Saves** - Save your progress to the cloud
- **Cross-Device Play** - Continue your game on any device
- **User Accounts** - Secure registration and login
- **Data Sync** - Automatic synchronization across devices
- **Backup & Recovery** - Never lose your progress

### 👥 **Multiplayer Features**
- **Real-time Multiplayer** - Play with friends online
- **Game Rooms** - Create private or public rooms
- **Cooperative Mode** - Work together to build bakeries
- **Competitive Mode** - Compete for the best bakery
- **Chat System** - Communicate with other players

### 📊 **Professional Dashboard**
- **Web-based Management** - Beautiful admin interface
- **Real-time Monitoring** - Live server statistics
- **User Management** - View and manage players
- **Game Analytics** - Detailed metrics and reports
- **Room Monitoring** - Track multiplayer sessions

---

## 🚀 **Quick Start Options**

### **Option 1: Super Easy (Recommended)**
```bash
# Run the one-click installer
EASY-SETUP.bat
```
**Perfect for:** First-time users, quick testing, demonstrations

### **Option 2: Browser Version (Game Only)**
```bash
# Just want to play in browser?
PLAY-BROWSER.bat

# Or manually:
npm install && npm run build
npx serve out -p 3000
```
**Perfect for:** Single-player gaming, offline play, no installation

### **Option 3: Server Only**
```bash
# Just want the server?
cd server
npm run setup-easy
npm start
```
**Perfect for:** Hosting for others, cloud saves only

### **Option 4: Development**
```bash
# Want to modify the game?
npm install
npm run dev
```
**Perfect for:** Developers, customization, contributions

---

## 🌐 **Access Your Game**

After setup, you can access:

- 🎮 **Browser Game**: http://localhost:3000 (PLAY-BROWSER.bat)
- 📊 **Server Dashboard**: http://localhost:3001/dashboard
- 🔍 **API Health**: http://localhost:3001/health
- 📖 **API Docs**: http://localhost:3001/

---

## 🎯 **Features Overview**

### **Game Features**
✅ **Bakery Management** - Complete business simulation
✅ **Recipe System** - Unlock and master recipes
✅ **Equipment Upgrades** - Improve your bakery
✅ **Order Management** - Handle customer orders
✅ **Inventory System** - Manage ingredients and supplies
✅ **Achievement System** - Unlock rewards and titles
✅ **Skill Development** - Level up your abilities
✅ **Multilingual** - English and Czech support

### **Technical Features**
✅ **Cloud Saves** - Secure cloud storage
✅ **Multiplayer** - Real-time gaming with friends
✅ **User Accounts** - Registration and authentication
✅ **Web Dashboard** - Professional admin interface
✅ **Analytics** - Detailed game metrics
✅ **Docker Support** - Easy deployment
✅ **Mobile Responsive** - Play on any device
✅ **Offline Support** - Play without internet

---

## 📁 **Project Structure**

```
bake-it-out/
├── 🎮 Game Client (Next.js)
│   ├── src/components/     # Game components
│   ├── src/contexts/       # Game state management
│   └── src/lib/           # Utilities and helpers
├── 🖥️ Unified Server (Node.js)
│   ├── src/routes/        # API endpoints
│   ├── src/models/        # Database models
│   ├── src/socket/        # Real-time communication
│   └── src/views/         # Dashboard templates
├── 📦 Portable Distribution
│   ├── server/            # Standalone server
│   ├── out/              # Built game client
│   └── start-*.bat       # Launch scripts
└── 🛠️ Setup Scripts
    ├── EASY-SETUP.bat     # One-click installer
    └── deploy-unified.bat # Deployment script
```

---

## 🆘 **Need Help?**

### **Quick Troubleshooting**
- **Port in use?** Change PORT in server/.env
- **MongoDB issues?** Run `docker run -d -p 27017:27017 mongo:6.0`
- **Dependencies fail?** Delete node_modules and run `npm install`
- **Game won't load?** Check http://localhost:3001/health

### **Documentation**
- 📖 **Server Setup**: [server/README.md](server/README.md)
- 🌐 **Unified System**: [UNIFIED-SERVER.md](UNIFIED-SERVER.md)
- 🐳 **Docker Guide**: [docker-compose.yml](docker-compose.yml)

### **Support**
- 🐛 **Issues**: Check the logs in `server/logs/`
- 🔍 **Debug**: Run with `DEBUG=* npm start`
- 📊 **Monitor**: Use the dashboard at `/dashboard`

---

## 🎉 **Ready to Bake?**

**Get started in 30 seconds:**

1. **Download** the project
2. **Run** `EASY-SETUP.bat`
3. **Play** at http://localhost:3000
4. **Manage** at http://localhost:3001/dashboard

## 📞 **Contact & Support**

- **Discord**: `.avariss` - For questions, feedback, or multiplayer coordination
- **Issues**: Use GitHub issues for bug reports and feature requests
- **Development**: Open source project - contributions welcome!

**Happy baking! 🧁👨‍🍳**
