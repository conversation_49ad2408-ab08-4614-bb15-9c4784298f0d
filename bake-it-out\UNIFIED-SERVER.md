# 🧁 Bake It Out - Unified Server System

## 🌟 Overview

The Bake It Out Unified Server is a comprehensive gaming platform that combines cloud saves, multiplayer functionality, user management, and server administration into a single, powerful Node.js application with an integrated web dashboard.

## 🏗️ Architecture

### **Unified Server Components**
```
┌─────────────────────────────────────────────────────────────┐
│                    Bake It Out Unified Server               │
├─────────────────────────────────────────────────────────────┤
│  🎮 Game Client (Next.js)                                  │
│  ├── Cloud Save Integration                                │
│  ├── Multiplayer Support                                   │
│  ├── Dashboard Access                                      │
│  └── Real-time Communication                               │
├─────────────────────────────────────────────────────────────┤
│  🖥️ Web Dashboard (EJS + Tailwind)                         │
│  ├── User Management                                       │
│  ├── Game Room Monitoring                                  │
│  ├── Analytics & Reporting                                 │
│  └── Server Administration                                 │
├─────────────────────────────────────────────────────────────┤
│  🔧 Server Core (Express + Socket.IO)                      │
│  ├── REST API Endpoints                                    │
│  ├── Real-time Socket Communication                        │
│  ├── Authentication & Security                             │
│  └── Database Management                                   │
├─────────────────────────────────────────────────────────────┤
│  💾 Database Layer (MongoDB)                               │
│  ├── User Accounts                                         │
│  ├── Cloud Saves                                           │
│  ├── Game Rooms                                            │
│  └── Analytics Data                                        │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 Quick Start

### **Option 1: Automated Deployment**
```bash
# Run the unified deployment script
deploy-unified.bat

# Follow the prompts and instructions
```

### **Option 2: Manual Setup**
```bash
# 1. Build the client
npm run build

# 2. Install server dependencies
cd server
npm install

# 3. Setup environment
cp .env.example .env
# Edit .env with your configuration

# 4. Start the server
npm start
```

### **Option 3: Portable Distribution**
```bash
# Use the portable distribution
cd portable-dist
start-server.bat  # Start unified server
start.bat         # Start game client
```

## 🎯 Key Features

### **🎮 Gaming Platform**
- **Cloud Saves**: Cross-device game save synchronization
- **Multiplayer Rooms**: Real-time multiplayer gaming
- **User Accounts**: Secure registration and authentication
- **Game State Sync**: Real-time game state synchronization
- **Chat System**: In-game communication

### **📊 Management Dashboard**
- **Real-time Monitoring**: Live server statistics
- **User Management**: View and manage user accounts
- **Room Monitoring**: Track active multiplayer sessions
- **Analytics**: Comprehensive game metrics
- **Server Administration**: System management tools

### **🔒 Security & Performance**
- **JWT Authentication**: Secure token-based auth
- **Rate Limiting**: Protection against abuse
- **Data Validation**: Comprehensive input validation
- **Real-time Updates**: Socket.IO communication
- **Scalable Architecture**: MongoDB with optimized queries

## 🌐 Server Endpoints

### **🎮 Game API**
```
Authentication:
POST   /api/auth/register     - Register new user
POST   /api/auth/login        - User login
POST   /api/auth/refresh      - Refresh access token
GET    /api/auth/me           - Get current user
POST   /api/auth/logout       - Logout user

Cloud Saves:
GET    /api/saves             - List user's saves
GET    /api/saves/:id         - Get specific save
POST   /api/saves             - Create new save
PUT    /api/saves/:id         - Update save
DELETE /api/saves/:id         - Delete save

Multiplayer:
GET    /api/multiplayer/rooms - List game rooms
POST   /api/multiplayer/rooms - Create room
POST   /api/multiplayer/rooms/:id/join - Join room
POST   /api/multiplayer/rooms/:id/leave - Leave room
GET    /api/multiplayer/rooms/:id - Get room details

User Management:
GET    /api/users/profile     - Get user profile
PUT    /api/users/profile     - Update profile
PUT    /api/users/settings    - Update settings
GET    /api/users/stats       - Get user statistics
```

### **📊 Dashboard**
```
Dashboard Access:
GET    /dashboard             - Main dashboard
GET    /dashboard/login       - Login page
POST   /dashboard/login       - Login handler
GET    /dashboard/users       - User management
GET    /dashboard/rooms       - Room management
GET    /dashboard/saves       - Save management
GET    /dashboard/analytics   - Analytics page

Dashboard API:
GET    /dashboard/api/stats   - Real-time stats
GET    /dashboard/api/analytics - Analytics data
```

### **🔧 System**
```
Health & Info:
GET    /health               - Server health check
GET    /                     - API information
```

## 🎮 Client Integration

### **Dashboard Access**
The game client includes a dashboard button that allows authenticated users to access the server management interface:

```typescript
import { DashboardButton } from '@/components/dashboard/DashboardAccess'

// Add to your game interface
<DashboardButton />
```

### **Cloud Save Integration**
```typescript
import { useCloudSave } from '@/contexts/CloudSaveContext'

const { saveToCloud, loadFromCloud, isAuthenticated } = useCloudSave()

// Save game to cloud
const saveId = await saveToCloud('My Save', gameData)

// Load game from cloud
const gameData = await loadFromCloud(saveId)
```

### **Multiplayer Integration**
```typescript
import { useMultiplayer } from '@/contexts/MultiplayerContext'

const { createRoom, joinRoom, isConnected } = useMultiplayer()

// Create multiplayer room
const room = await createRoom('My Room', 4, 'cooperative')

// Join existing room
await joinRoom(roomId)
```

## 🔧 Configuration

### **Environment Variables**
```env
# Server Configuration
NODE_ENV=development
PORT=3001

# Database
MONGODB_URI=mongodb://localhost:27017/bake-it-out

# Authentication
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=7d

# Security
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3002
BCRYPT_ROUNDS=12

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### **Dashboard Configuration**
The dashboard is automatically configured and accessible at `/dashboard`. Authentication is handled through the same JWT system used by the game client.

## 📊 Monitoring & Analytics

### **Real-time Metrics**
- Active users and connections
- Game room statistics
- Cloud save activity
- Server performance metrics
- Memory and CPU usage

### **Analytics Dashboard**
- User growth charts
- Game activity trends
- Save usage patterns
- Top players leaderboard
- Server performance graphs

## 🐳 Docker Deployment

### **Docker Compose**
```yaml
# Use the provided docker-compose.yml
docker-compose up -d

# With admin interface
docker-compose --profile admin up -d

# Full stack with monitoring
docker-compose --profile proxy up -d
```

### **Individual Containers**
```bash
# Build and run server
docker build -t bake-it-out-server .
docker run -d -p 3001:3001 --env-file .env bake-it-out-server
```

## 🔍 Troubleshooting

### **Common Issues**

#### **Dashboard Access Issues**
- Ensure you're logged in to the game client
- Check that the server is running on port 3001
- Verify JWT token is valid

#### **Multiplayer Connection Issues**
- Check Socket.IO connection in browser console
- Verify authentication token
- Ensure firewall allows WebSocket connections

#### **Cloud Save Sync Issues**
- Check MongoDB connection
- Verify user authentication
- Check network connectivity

### **Debug Mode**
```bash
# Enable debug logging
DEBUG=* npm start

# Check server logs
tail -f logs/combined.log
```

## 📈 Performance Optimization

### **Database Optimization**
- Indexes on frequently queried fields
- Aggregation pipelines for analytics
- Connection pooling
- Query optimization

### **Real-time Performance**
- Socket.IO connection management
- Room-based event broadcasting
- Efficient state synchronization
- Memory leak prevention

## 🔒 Security Best Practices

### **Authentication Security**
- JWT token rotation
- Password hashing with bcrypt
- Rate limiting on auth endpoints
- Session management

### **Data Protection**
- Input validation and sanitization
- CORS configuration
- Helmet security headers
- Data integrity checks

## 🚀 Scaling Considerations

### **Horizontal Scaling**
- Load balancer configuration
- Session store (Redis)
- Database sharding
- CDN for static assets

### **Monitoring & Alerting**
- Health check endpoints
- Performance metrics
- Error tracking
- Uptime monitoring

## 📝 API Documentation

Complete API documentation is available at `/dashboard` when the server is running. The dashboard includes interactive API exploration and real-time testing capabilities.

## 🎉 Conclusion

The Bake It Out Unified Server provides a complete gaming platform with cloud saves, multiplayer functionality, and comprehensive management tools. The integrated dashboard makes server administration simple and efficient, while the unified architecture ensures optimal performance and scalability.

**Ready to bake? Start your unified server and let the gaming begin!** 🧁🎮
