'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { useLanguage } from '@/contexts/LanguageContext'

interface GameMenuProps {
  isOpen: boolean
  onClose: () => void
  onSaveGame: () => void
  onLoadGame: () => void
  onSettings: () => void
  onMainMenu: () => void
  onExit?: () => void
}

export function GameMenu({ 
  isOpen, 
  onClose, 
  onSaveGame, 
  onLoadGame, 
  onSettings, 
  onMainMenu,
  onExit 
}: GameMenuProps) {
  const { t } = useLanguage()

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-orange-500 to-yellow-500 p-6 text-white">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">🎮 {t('gameMenu.title', 'Game Menu')}</h2>
              <p className="text-orange-100 text-sm">
                {t('gameMenu.subtitle', 'Manage your game')}
              </p>
            </div>
            <Button
              variant="secondary"
              size="sm"
              className="bg-white/20 hover:bg-white/30 text-white border-white/30"
              onClick={onClose}
            >
              ✕
            </Button>
          </div>
        </div>

        {/* Menu Options */}
        <div className="p-6 space-y-3">
          <Button
            variant="secondary"
            size="lg"
            className="w-full justify-start text-left py-4 hover:bg-orange-50"
            onClick={() => {
              onClose()
              // Resume game automatically
            }}
          >
            <span className="text-2xl mr-3">▶️</span>
            <div>
              <div className="font-semibold">{t('gameMenu.resume', 'Resume Game')}</div>
              <div className="text-sm text-gray-500">
                {t('gameMenu.resumeDesc', 'Continue playing')}
              </div>
            </div>
          </Button>

          <Button
            variant="secondary"
            size="lg"
            className="w-full justify-start text-left py-4 hover:bg-green-50"
            onClick={() => {
              onSaveGame()
              onClose()
            }}
          >
            <span className="text-2xl mr-3">💾</span>
            <div>
              <div className="font-semibold">{t('gameMenu.save', 'Save Game')}</div>
              <div className="text-sm text-gray-500">
                {t('gameMenu.saveDesc', 'Save your progress')}
              </div>
            </div>
          </Button>

          <Button
            variant="secondary"
            size="lg"
            className="w-full justify-start text-left py-4 hover:bg-blue-50"
            onClick={() => {
              onLoadGame()
              onClose()
            }}
          >
            <span className="text-2xl mr-3">📁</span>
            <div>
              <div className="font-semibold">{t('gameMenu.load', 'Load Game')}</div>
              <div className="text-sm text-gray-500">
                {t('gameMenu.loadDesc', 'Load saved progress')}
              </div>
            </div>
          </Button>

          <Button
            variant="secondary"
            size="lg"
            className="w-full justify-start text-left py-4 hover:bg-purple-50"
            onClick={() => {
              onSettings()
              onClose()
            }}
          >
            <span className="text-2xl mr-3">⚙️</span>
            <div>
              <div className="font-semibold">{t('gameMenu.settings', 'Settings')}</div>
              <div className="text-sm text-gray-500">
                {t('gameMenu.settingsDesc', 'Game preferences')}
              </div>
            </div>
          </Button>

          <div className="border-t pt-3 mt-4">
            <Button
              variant="secondary"
              size="lg"
              className="w-full justify-start text-left py-4 hover:bg-yellow-50"
              onClick={() => {
                onMainMenu()
                onClose()
              }}
            >
              <span className="text-2xl mr-3">🏠</span>
              <div>
                <div className="font-semibold">{t('gameMenu.mainMenu', 'Main Menu')}</div>
                <div className="text-sm text-gray-500">
                  {t('gameMenu.mainMenuDesc', 'Return to main menu')}
                </div>
              </div>
            </Button>

            {onExit && (
              <Button
                variant="secondary"
                size="lg"
                className="w-full justify-start text-left py-4 hover:bg-red-50 text-red-600"
                onClick={() => {
                  onExit()
                  onClose()
                }}
              >
                <span className="text-2xl mr-3">🚪</span>
                <div>
                  <div className="font-semibold">{t('gameMenu.exit', 'Exit Game')}</div>
                  <div className="text-sm text-red-400">
                    {t('gameMenu.exitDesc', 'Close the application')}
                  </div>
                </div>
              </Button>
            )}
          </div>
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-3 text-center text-sm text-gray-500">
          {t('gameMenu.tip', 'Press ESC to open this menu anytime')}
        </div>
      </div>
    </div>
  )
}
