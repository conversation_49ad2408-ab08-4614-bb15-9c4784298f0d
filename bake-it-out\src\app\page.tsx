'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/Button'
import { useLanguage } from '@/contexts/LanguageContext'
import { MultiplayerLobby } from '@/components/multiplayer/MultiplayerLobby'
import { MultiplayerGame } from '@/components/multiplayer/MultiplayerGame'
import { useMultiplayer } from '@/contexts/MultiplayerContext'
import { MainMenu } from '@/components/menu/MainMenu'
import { CreditsModal } from '@/components/menu/CreditsModal'
import { SettingsModal } from '@/components/game/SettingsModal'
import { useDiscordRPC } from '@/contexts/DiscordRPCContext'
import dynamic from 'next/dynamic'

// Dynamically import the game component to avoid SSR issues
const GameComponent = dynamic(() => import('./game/page'), { ssr: false })

export default function Home() {
  const { language, setLanguage, t } = useLanguage()
  const { gameState } = useMultiplayer()
  const { setMenuActivity } = useDiscordRPC()
  const [showMultiplayerLobby, setShowMultiplayerLobby] = useState(false)
  const [showMultiplayerGame, setShowMultiplayerGame] = useState(false)
  const [showSinglePlayer, setShowSinglePlayer] = useState(false)
  const [showCredits, setShowCredits] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const [gameSettings, setGameSettings] = useState({
    soundEnabled: true,
    musicEnabled: true,
    notifications: true,
    autoSave: true
  })

  const handleSettingsChange = (newSettings: typeof gameSettings) => {
    setGameSettings(newSettings)
  }

  // Set Discord RPC to menu activity when on main page
  useEffect(() => {
    if (!showSinglePlayer) {
      setMenuActivity()
    }
  }, [showSinglePlayer, setMenuActivity])

  const handleExit = () => {
    if (typeof window !== 'undefined' && window.electronAPI) {
      window.electronAPI.quit()
    }
  }

  // Show single player game if selected
  if (showSinglePlayer) {
    return <GameComponent />
  }

  return (
    <>
      <MainMenu
        onStartSinglePlayer={() => setShowSinglePlayer(true)}
        onStartMultiplayer={() => setShowMultiplayerLobby(true)}
        onShowSettings={() => setShowSettings(true)}
        onShowCredits={() => setShowCredits(true)}
        onExit={typeof window !== 'undefined' && window.electronAPI ? handleExit : undefined}
      />
      {/* Modals */}
      <CreditsModal
        isOpen={showCredits}
        onClose={() => setShowCredits(false)}
      />

      <SettingsModal
        isOpen={showSettings}
        onClose={() => setShowSettings(false)}
        settings={gameSettings}
        onSettingsChange={handleSettingsChange}
      />

      {/* Multiplayer Components */}
      <MultiplayerLobby
        isOpen={showMultiplayerLobby}
        onClose={() => setShowMultiplayerLobby(false)}
      />
      <MultiplayerGame
        isOpen={showMultiplayerGame || gameState === 'playing'}
        onClose={() => setShowMultiplayerGame(false)}
      />
    </>
  );
}
