const { contextBridge, ipc<PERSON>enderer } = require('electron')

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Game controls
  newGame: () => ipcRenderer.invoke('new-game'),
  singlePlayer: () => ipcRenderer.invoke('single-player'),
  multiplayer: () => ipcRenderer.invoke('multiplayer'),
  settings: () => ipcRenderer.invoke('settings'),
  
  // Language controls
  changeLanguage: (language) => ipcRenderer.invoke('language-change', language),
  
  // App controls
  minimize: () => ipcRenderer.invoke('minimize'),
  maximize: () => ipcRenderer.invoke('maximize'),
  close: () => ipcRenderer.invoke('close'),
  
  // Event listeners
  onNewGame: (callback) => ipcRenderer.on('new-game', callback),
  onSinglePlayer: (callback) => ipcRenderer.on('single-player', callback),
  onMultiplayer: (callback) => ipcRenderer.on('multiplayer', callback),
  onSettings: (callback) => ipcRenderer.on('settings', callback),
  onLanguageChange: (callback) => ipcRenderer.on('language-change', callback),
  
  // Remove listeners
  removeAllListeners: (channel) => ipcRenderer.removeAllListeners(channel),
  
  // App info
  getVersion: () => ipcRenderer.invoke('get-version'),
  getPlatform: () => process.platform,
  
  // File system (for save games, settings, etc.)
  saveFile: (data) => ipcRenderer.invoke('save-file', data),
  loadFile: () => ipcRenderer.invoke('load-file'),

  // Advanced file operations for save system
  getSaveDirectory: () => ipcRenderer.invoke('get-save-directory'),
  ensureDirectory: (path) => ipcRenderer.invoke('ensure-directory', path),
  writeFile: (filePath, content) => ipcRenderer.invoke('write-file', filePath, content),
  readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
  deleteFile: (filePath) => ipcRenderer.invoke('delete-file', filePath),
  copyFile: (source, destination) => ipcRenderer.invoke('copy-file', source, destination),
  listFiles: (directory, extension) => ipcRenderer.invoke('list-files', directory, extension),
  showSaveDialog: (defaultName) => ipcRenderer.invoke('show-save-dialog', defaultName),
  showOpenDialog: (extensions) => ipcRenderer.invoke('show-open-dialog', extensions),

  // App controls
  quit: () => ipcRenderer.invoke('quit'),

  // Discord Rich Presence
  initDiscordRPC: (clientId) => ipcRenderer.invoke('init-discord-rpc', clientId),
  updateDiscordRPC: (activity) => ipcRenderer.invoke('update-discord-rpc', activity),
  clearDiscordRPC: () => ipcRenderer.invoke('clear-discord-rpc'),
  disconnectDiscordRPC: () => ipcRenderer.invoke('disconnect-discord-rpc'),

  // Notifications
  showNotification: (title, body) => ipcRenderer.invoke('show-notification', title, body)
})

// Expose a limited API for the game
contextBridge.exposeInMainWorld('gameAPI', {
  // Game state management
  saveGameState: (state) => ipcRenderer.invoke('save-game-state', state),
  loadGameState: () => ipcRenderer.invoke('load-game-state'),
  
  // Multiplayer events
  onPlayerJoined: (callback) => ipcRenderer.on('player-joined', callback),
  onPlayerLeft: (callback) => ipcRenderer.on('player-left', callback),
  onGameStarted: (callback) => ipcRenderer.on('game-started', callback),
  onGameEnded: (callback) => ipcRenderer.on('game-ended', callback),
  
  // Statistics
  saveStatistics: (stats) => ipcRenderer.invoke('save-statistics', stats),
  loadStatistics: () => ipcRenderer.invoke('load-statistics'),
  
  // Achievements
  unlockAchievement: (achievement) => ipcRenderer.invoke('unlock-achievement', achievement),
  getAchievements: () => ipcRenderer.invoke('get-achievements')
})

// Expose system information
contextBridge.exposeInMainWorld('systemAPI', {
  platform: process.platform,
  arch: process.arch,
  version: process.version,
  
  // Performance monitoring
  getMemoryUsage: () => process.memoryUsage(),
  getCPUUsage: () => process.cpuUsage(),
  
  // Environment
  isDev: process.env.NODE_ENV === 'development'
})

console.log('Preload script loaded successfully')
