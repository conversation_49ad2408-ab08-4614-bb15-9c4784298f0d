# Bake It Out - Cloud Save Server

A robust Node.js/Express server for handling cloud saves, user authentication, and game data management for the Bake It Out bakery management game.

## 🚀 Features

- **User Authentication**: JWT-based authentication with registration and login
- **Cloud Saves**: Secure cloud save storage with version control and integrity checks
- **Real-time Sync**: Automatic synchronization with conflict resolution
- **Data Validation**: Comprehensive input validation and sanitization
- **Security**: Rate limiting, CORS protection, and data encryption
- **Scalable**: MongoDB database with optimized queries and indexing
- **Docker Ready**: Complete containerization with Docker Compose

## 📋 Prerequisites

- Node.js 18+ 
- MongoDB 6.0+
- npm or yarn

## 🛠️ Installation

### Development Setup

1. **Clone and navigate to server directory**
```bash
cd bake-it-out/server
```

2. **Install dependencies**
```bash
npm install
```

3. **Environment configuration**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Start MongoDB** (if running locally)
```bash
# Using Docker
docker run -d -p 27017:27017 --name mongodb mongo:6.0

# Or install MongoDB locally
```

5. **Start development server**
```bash
npm run dev
```

### Production Deployment

#### Option 1: Docker Compose (Recommended)

```bash
# Copy environment file
cp .env.example .env

# Edit .env for production
nano .env

# Start all services
docker-compose up -d

# With admin interface
docker-compose --profile admin up -d

# With caching
docker-compose --profile cache up -d

# Full stack with proxy
docker-compose --profile proxy up -d
```

#### Option 2: Manual Deployment

```bash
# Install dependencies
npm ci --only=production

# Set environment variables
export NODE_ENV=production
export MONGODB_URI=mongodb://localhost:27017/bake-it-out
export JWT_SECRET=your-super-secret-key

# Start server
npm start
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `NODE_ENV` | Environment mode | `development` |
| `PORT` | Server port | `3001` |
| `MONGODB_URI` | MongoDB connection string | `mongodb://localhost:27017/bake-it-out` |
| `JWT_SECRET` | JWT signing secret | Required |
| `JWT_EXPIRES_IN` | JWT expiration time | `7d` |
| `ALLOWED_ORIGINS` | CORS allowed origins | `http://localhost:3000,http://localhost:3002` |

### Security Configuration

```env
# Strong JWT secret (generate with: openssl rand -base64 32)
JWT_SECRET=your-super-secret-jwt-key-change-in-production

# Rate limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX=10

# CORS
ALLOWED_ORIGINS=https://yourdomain.com,https://www.yourdomain.com
```

## 📡 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Refresh access token
- `GET /api/auth/me` - Get current user
- `POST /api/auth/logout` - Logout user
- `GET /api/auth/verify` - Verify token

### Cloud Saves
- `GET /api/saves` - List user's saves
- `GET /api/saves/:id` - Get specific save
- `POST /api/saves` - Create new save
- `PUT /api/saves/:id` - Update save
- `DELETE /api/saves/:id` - Delete save
- `GET /api/saves/:id/metadata` - Get save metadata

### User Management
- `GET /api/users/profile` - Get user profile
- `PUT /api/users/profile` - Update profile
- `PUT /api/users/settings` - Update settings
- `GET /api/users/stats` - Get user statistics
- `DELETE /api/users/account` - Delete account

### System
- `GET /health` - Health check
- `GET /` - API information

## 🗄️ Database Schema

### User Model
```javascript
{
  username: String,
  email: String,
  passwordHash: String,
  profile: {
    displayName: String,
    avatar: String,
    preferredLanguage: String
  },
  gameStats: {
    totalPlayTime: Number,
    highestLevel: Number,
    totalMoney: Number,
    achievementsUnlocked: Number,
    gamesPlayed: Number
  },
  settings: {
    autoSave: Boolean,
    syncFrequency: Number,
    maxSaves: Number
  }
}
```

### CloudSave Model
```javascript
{
  userId: ObjectId,
  saveName: String,
  description: String,
  gameData: Mixed,
  metadata: {
    version: Number,
    gameVersion: String,
    platform: String,
    level: Number,
    money: Number,
    playTime: Number
  },
  saveType: String, // 'manual', 'auto', 'checkpoint'
  checksum: String,
  size: Number
}
```

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication
- **Rate Limiting**: Protection against brute force attacks
- **Input Validation**: Comprehensive data validation
- **CORS Protection**: Configurable cross-origin resource sharing
- **Helmet Security**: Security headers and protection
- **Data Integrity**: Checksum validation for save files
- **Password Hashing**: bcrypt with salt rounds

## 📊 Monitoring

### Health Check
```bash
curl http://localhost:3001/health
```

### Logs
```bash
# View logs in Docker
docker-compose logs -f bake-it-out-server

# View MongoDB logs
docker-compose logs -f mongo
```

### Database Admin
Access MongoDB admin interface at `http://localhost:8081` (when using admin profile)

## 🚀 Deployment Options

### Cloud Platforms

#### Heroku
```bash
# Install Heroku CLI
heroku create bake-it-out-server
heroku addons:create mongolab:sandbox
heroku config:set JWT_SECRET=your-secret
git push heroku main
```

#### DigitalOcean App Platform
```yaml
# app.yaml
name: bake-it-out-server
services:
- name: api
  source_dir: /server
  github:
    repo: your-repo
    branch: main
  run_command: npm start
  environment_slug: node-js
  instance_count: 1
  instance_size_slug: basic-xxs
  envs:
  - key: NODE_ENV
    value: production
databases:
- name: mongodb
  engine: MONGODB
```

#### AWS ECS/Fargate
Use the provided Dockerfile with AWS ECS or Fargate for scalable deployment.

### Self-Hosted

#### VPS Deployment
```bash
# On your VPS
git clone your-repo
cd bake-it-out/server
npm ci --only=production

# Install PM2 for process management
npm install -g pm2
pm2 start src/index.js --name bake-it-out-server
pm2 startup
pm2 save
```

## 🧪 Testing

```bash
# Run tests
npm test

# Run with coverage
npm run test:coverage

# Test specific endpoint
curl -X POST http://localhost:3001/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{"username":"testuser","email":"<EMAIL>","password":"password123"}'
```

## 🔧 Troubleshooting

### Common Issues

1. **MongoDB Connection Failed**
   - Check MongoDB is running
   - Verify connection string
   - Check network connectivity

2. **JWT Token Errors**
   - Ensure JWT_SECRET is set
   - Check token expiration
   - Verify token format

3. **CORS Errors**
   - Update ALLOWED_ORIGINS
   - Check request headers
   - Verify domain configuration

4. **Rate Limiting**
   - Check rate limit settings
   - Clear rate limit cache
   - Adjust limits for production

### Debug Mode
```bash
DEBUG=* npm run dev
```

## 📈 Performance

### Optimization Tips
- Use MongoDB indexes for queries
- Implement Redis caching for sessions
- Enable gzip compression
- Use CDN for static assets
- Monitor memory usage

### Scaling
- Horizontal scaling with load balancer
- Database sharding for large datasets
- Redis cluster for session storage
- Container orchestration with Kubernetes

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Make changes
4. Add tests
5. Submit pull request

## 📄 License

MIT License - see LICENSE file for details

## 🆘 Support

- GitHub Issues: Report bugs and feature requests
- Documentation: Check API documentation
- Community: Join our Discord server

---

**Bake It Out Server** - Powering cloud saves for the ultimate bakery management experience! 🧁☁️
