'use client'

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react'
import { socket<PERSON><PERSON><PERSON>, GameRoom, Player, MultiplayerGameState } from '@/lib/socket'

interface ChatMessage {
  id: string
  playerId: string
  playerName: string
  content: string
  timestamp: number
}

interface MultiplayerContextType {
  // Connection state
  isConnected: boolean
  isInRoom: boolean
  connectionError: string | null
  
  // Room and player data
  currentRoom: GameRoom | null
  currentPlayer: Player | null
  players: Player[]
  
  // Game state
  gameState: 'waiting' | 'starting' | 'playing' | 'paused' | 'finished'
  sharedGameState: MultiplayerGameState | null
  
  // Chat
  messages: ChatMessage[]
  
  // Actions
  createRoom: (roomData: Partial<GameRoom>, playerData: Partial<Player>) => Promise<void>
  joinRoom: (roomId: string, playerData: Partial<Player>) => Promise<void>
  leaveRoom: () => void
  startGame: () => void
  sendMessage: (content: string) => void
  sendPlayerAction: (action: { type: string; data: any }) => void
  
  // Room management
  setPlayerReady: (ready: boolean) => void
  kickPlayer: (playerId: string) => void
  updateRoomSettings: (settings: any) => void
}

const MultiplayerContext = createContext<MultiplayerContextType | undefined>(undefined)

export function MultiplayerProvider({ children }: { children: React.ReactNode }) {
  // Connection state
  const [isConnected, setIsConnected] = useState(false)
  const [isInRoom, setIsInRoom] = useState(false)
  const [connectionError, setConnectionError] = useState<string | null>(null)
  
  // Room and player data
  const [currentRoom, setCurrentRoom] = useState<GameRoom | null>(null)
  const [currentPlayer, setCurrentPlayer] = useState<Player | null>(null)
  const [players, setPlayers] = useState<Player[]>([])
  
  // Game state
  const [gameState, setGameState] = useState<'waiting' | 'starting' | 'playing' | 'paused' | 'finished'>('waiting')
  const [sharedGameState, setSharedGameState] = useState<MultiplayerGameState | null>(null)
  
  // Chat
  const [messages, setMessages] = useState<ChatMessage[]>([])

  // Initialize socket connection and event listeners only when needed
  useEffect(() => {
    // Don't auto-connect to prevent errors when server isn't running
    // Connection will be initiated when multiplayer features are used
    const socket = socketManager

    // Connection events
    const handleConnect = () => {
      setIsConnected(true)
      setConnectionError(null)
    }

    const handleDisconnect = () => {
      setIsConnected(false)
      setIsInRoom(false)
      setCurrentRoom(null)
      setCurrentPlayer(null)
      setPlayers([])
    }

    const handleError = (error: any) => {
      setConnectionError(error.message || 'Connection error')
      console.error('Multiplayer error:', error)
    }

    // Room events
    const handleRoomCreated = (room: GameRoom) => {
      setCurrentRoom(room)
      setPlayers(room.players)
      setIsInRoom(true)
      setGameState(room.gameState)
      
      // Set current player as host
      const hostPlayer = room.players.find(p => p.isHost)
      if (hostPlayer) {
        setCurrentPlayer(hostPlayer)
      }
    }

    const handleRoomJoined = (room: GameRoom, player: Player) => {
      setCurrentRoom(room)
      setCurrentPlayer(player)
      setPlayers(room.players)
      setIsInRoom(true)
      setGameState(room.gameState)
    }

    const handleRoomLeft = () => {
      setCurrentRoom(null)
      setCurrentPlayer(null)
      setPlayers([])
      setIsInRoom(false)
      setGameState('waiting')
      setSharedGameState(null)
      setMessages([])
    }

    const handleRoomUpdated = (room: GameRoom) => {
      setCurrentRoom(room)
      setPlayers(room.players)
      setGameState(room.gameState)
    }

    const handlePlayerJoined = (player: Player) => {
      setPlayers(prev => [...prev, player])
      addSystemMessage(`${player.name} joined the room`)
    }

    const handlePlayerLeft = (playerId: string) => {
      setPlayers(prev => {
        const leftPlayer = prev.find(p => p.id === playerId)
        if (leftPlayer) {
          addSystemMessage(`${leftPlayer.name} left the room`)
        }
        return prev.filter(p => p.id !== playerId)
      })
    }

    // Game events
    const handleGameStarted = (gameState: MultiplayerGameState) => {
      setGameState('playing')
      setSharedGameState(gameState)
      addSystemMessage('Game started!')
    }

    const handleGameStateUpdate = (update: Partial<MultiplayerGameState>) => {
      setSharedGameState(prev => prev ? { ...prev, ...update } : null)
    }

    const handlePlayerAction = (action: any) => {
      // Handle player actions from other players
      console.log('Player action received:', action)
    }

    // Chat events
    const handleMessageReceived = (message: any) => {
      const chatMessage: ChatMessage = {
        id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
        playerId: message.playerId,
        playerName: message.playerName,
        content: message.content,
        timestamp: message.timestamp
      }
      setMessages(prev => [...prev, chatMessage])
    }

    // Register event listeners
    socket.on('connect' as any, handleConnect)
    socket.on('disconnect' as any, handleDisconnect)
    socket.on('error' as any, handleError)
    socket.on('room_created', handleRoomCreated)
    socket.on('room_joined', handleRoomJoined)
    socket.on('room_left', handleRoomLeft)
    socket.on('room_updated', handleRoomUpdated)
    socket.on('player_joined', handlePlayerJoined)
    socket.on('player_left', handlePlayerLeft)
    socket.on('game_started', handleGameStarted)
    socket.on('game_state_update', handleGameStateUpdate)
    socket.on('player_action', handlePlayerAction)
    socket.on('message_received', handleMessageReceived)

    // Check initial connection state
    setIsConnected(socket.isSocketConnected())

    // Cleanup
    return () => {
      socket.off('connect' as any, handleConnect)
      socket.off('disconnect' as any, handleDisconnect)
      socket.off('error' as any, handleError)
      socket.off('room_created', handleRoomCreated)
      socket.off('room_joined', handleRoomJoined)
      socket.off('room_left', handleRoomLeft)
      socket.off('room_updated', handleRoomUpdated)
      socket.off('player_joined', handlePlayerJoined)
      socket.off('player_left', handlePlayerLeft)
      socket.off('game_started', handleGameStarted)
      socket.off('game_state_update', handleGameStateUpdate)
      socket.off('player_action', handlePlayerAction)
      socket.off('message_received', handleMessageReceived)
    }
  }, [])

  // Helper function to add system messages
  const addSystemMessage = (content: string) => {
    const systemMessage: ChatMessage = {
      id: Date.now().toString() + Math.random().toString(36).substring(2, 11),
      playerId: 'system',
      playerName: 'System',
      content,
      timestamp: Date.now()
    }
    setMessages(prev => [...prev, systemMessage])
  }

  // Action functions
  const createRoom = useCallback(async (roomData: Partial<GameRoom>, playerData: Partial<Player>) => {
    try {
      setConnectionError(null)
      // Connect to socket server when multiplayer is actually used
      const { isConnected } = socketManager.getConnectionStatus()
      if (!isConnected) {
        socketManager.connect()
        // Wait a moment for connection
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
      await socketManager.createRoom({
        ...roomData,
        hostName: playerData.name,
        hostAvatar: playerData.avatar,
        hostLevel: playerData.level
      } as any)
      // Room created event will be handled by the event listener
    } catch (error: any) {
      setConnectionError(error.message)
      throw error
    }
  }, [])

  const joinRoom = useCallback(async (roomId: string, playerData: Partial<Player>) => {
    try {
      setConnectionError(null)
      // Connect to socket server when multiplayer is actually used
      const { isConnected } = socketManager.getConnectionStatus()
      if (!isConnected) {
        socketManager.connect()
        // Wait a moment for connection
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
      await socketManager.joinRoom(roomId, playerData)
      // Room joined event will be handled by the event listener
    } catch (error: any) {
      setConnectionError(error.message)
      throw error
    }
  }, [])

  const leaveRoom = useCallback(() => {
    socketManager.leaveRoom()
  }, [])

  const startGame = useCallback(() => {
    if (currentRoom && currentPlayer?.isHost) {
      socketManager.sendPlayerAction({
        type: 'start_game',
        data: { roomId: currentRoom.id }
      })
    }
  }, [currentRoom, currentPlayer])

  const sendMessage = useCallback((content: string) => {
    socketManager.sendMessage(content)
  }, [])

  const sendPlayerAction = useCallback((action: { type: string; data: any }) => {
    socketManager.sendPlayerAction(action)
  }, [])

  const setPlayerReady = useCallback((ready: boolean) => {
    if (currentPlayer) {
      sendPlayerAction({
        type: 'player_ready',
        data: { ready }
      })
    }
  }, [currentPlayer, sendPlayerAction])

  const kickPlayer = useCallback((playerId: string) => {
    if (currentPlayer?.isHost) {
      sendPlayerAction({
        type: 'kick_player',
        data: { playerId }
      })
    }
  }, [currentPlayer, sendPlayerAction])

  const updateRoomSettings = useCallback((settings: any) => {
    if (currentPlayer?.isHost) {
      sendPlayerAction({
        type: 'update_room_settings',
        data: { settings }
      })
    }
  }, [currentPlayer, sendPlayerAction])

  const value: MultiplayerContextType = {
    // Connection state
    isConnected,
    isInRoom,
    connectionError,
    
    // Room and player data
    currentRoom,
    currentPlayer,
    players,
    
    // Game state
    gameState,
    sharedGameState,
    
    // Chat
    messages,
    
    // Actions
    createRoom,
    joinRoom,
    leaveRoom,
    startGame,
    sendMessage,
    sendPlayerAction,
    
    // Room management
    setPlayerReady,
    kickPlayer,
    updateRoomSettings
  }

  return (
    <MultiplayerContext.Provider value={value}>
      {children}
    </MultiplayerContext.Provider>
  )
}

export function useMultiplayer() {
  const context = useContext(MultiplayerContext)
  if (context === undefined) {
    // Fallback for when context is not available
    console.warn('useMultiplayer called outside of MultiplayerProvider, using fallback')
    return {
      isConnected: false,
      connectionStatus: 'disconnected' as const,
      currentRoom: null,
      gameState: null,
      createRoom: async () => {},
      joinRoom: async () => {},
      leaveRoom: () => {},
      sendChatMessage: () => {},
      updateGameState: () => {},
      setPlayerReady: () => {}
    }
  }
  return context
}
