// File-based save system for desktop app
// Provides better save management for Electron applications

import { GameSave } from './saveSystem'

export interface FileSaveMetadata {
  fileName: string
  displayName: string
  timestamp: number
  playerLevel: number
  money: number
  playTime: number
  version: string
  fileSize: number
}

export class FileSaveSystem {
  private saveDirectory: string = ''
  private isElectron: boolean = false

  constructor() {
    this.isElectron = typeof window !== 'undefined' && window.electronAPI !== undefined
    this.initializeSaveDirectory()
  }

  private async initializeSaveDirectory() {
    if (this.isElectron && window.electronAPI) {
      try {
        // Get the user's documents folder + game saves
        this.saveDirectory = await window.electronAPI.getSaveDirectory()
        await this.ensureSaveDirectoryExists()
      } catch (error) {
        console.error('Failed to initialize save directory:', error)
        // Fallback to localStorage
        this.isElectron = false
      }
    }
  }

  private async ensureSaveDirectoryExists() {
    if (this.isElectron && window.electronAPI) {
      try {
        await window.electronAPI.ensureDirectory(this.saveDirectory)
      } catch (error) {
        console.error('Failed to create save directory:', error)
      }
    }
  }

  // Save game to file
  async saveToFile(gameData: GameSave, fileName?: string): Promise<boolean> {
    if (!this.isElectron || !window.electronAPI) {
      // Fallback to localStorage
      return this.saveToLocalStorage(gameData, fileName)
    }

    try {
      const saveFileName = fileName || `save_${Date.now()}.json`
      const filePath = `${this.saveDirectory}/${saveFileName}`
      
      const saveString = JSON.stringify(gameData, null, 2)
      await window.electronAPI.writeFile(filePath, saveString)
      
      console.log(`Game saved to file: ${filePath}`)
      return true
    } catch (error) {
      console.error('Failed to save to file:', error)
      return false
    }
  }

  // Load game from file
  async loadFromFile(fileName: string): Promise<GameSave | null> {
    if (!this.isElectron || !window.electronAPI) {
      // Fallback to localStorage
      return this.loadFromLocalStorage(fileName)
    }

    try {
      const filePath = `${this.saveDirectory}/${fileName}`
      const saveString = await window.electronAPI.readFile(filePath)
      
      if (!saveString) return null
      
      const gameData: GameSave = JSON.parse(saveString)
      console.log(`Game loaded from file: ${filePath}`)
      return gameData
    } catch (error) {
      console.error('Failed to load from file:', error)
      return null
    }
  }

  // Get list of save files
  async getSaveFiles(): Promise<FileSaveMetadata[]> {
    if (!this.isElectron || !window.electronAPI) {
      // Fallback to localStorage
      return this.getLocalStorageSaves()
    }

    try {
      const files = await window.electronAPI.listFiles(this.saveDirectory, '.json')
      const saveFiles: FileSaveMetadata[] = []

      for (const file of files) {
        try {
          const filePath = `${this.saveDirectory}/${file.name}`
          const saveString = await window.electronAPI.readFile(filePath)
          const gameData: GameSave = JSON.parse(saveString)

          saveFiles.push({
            fileName: file.name,
            displayName: gameData.player.name || file.name.replace('.json', ''),
            timestamp: gameData.timestamp,
            playerLevel: gameData.player.level,
            money: gameData.player.money,
            playTime: gameData.player.playTime || 0,
            version: gameData.version,
            fileSize: file.size || 0
          })
        } catch (error) {
          console.error(`Failed to read save file ${file.name}:`, error)
        }
      }

      return saveFiles.sort((a, b) => b.timestamp - a.timestamp)
    } catch (error) {
      console.error('Failed to get save files:', error)
      return []
    }
  }

  // Delete save file
  async deleteSaveFile(fileName: string): Promise<boolean> {
    if (!this.isElectron || !window.electronAPI) {
      // Fallback to localStorage
      return this.deleteFromLocalStorage(fileName)
    }

    try {
      const filePath = `${this.saveDirectory}/${fileName}`
      await window.electronAPI.deleteFile(filePath)
      console.log(`Save file deleted: ${filePath}`)
      return true
    } catch (error) {
      console.error('Failed to delete save file:', error)
      return false
    }
  }

  // Export save file to user-chosen location
  async exportSave(gameData: GameSave, suggestedName?: string): Promise<boolean> {
    if (!this.isElectron || !window.electronAPI) {
      // Fallback to browser download
      return this.exportToBrowser(gameData, suggestedName)
    }

    try {
      const defaultName = suggestedName || `bake-it-out-save-${Date.now()}.json`
      const filePath = await window.electronAPI.showSaveDialog(defaultName)
      
      if (!filePath) return false // User cancelled
      
      const saveString = JSON.stringify(gameData, null, 2)
      await window.electronAPI.writeFile(filePath, saveString)
      
      console.log(`Save exported to: ${filePath}`)
      return true
    } catch (error) {
      console.error('Failed to export save:', error)
      return false
    }
  }

  // Import save file from user-chosen location
  async importSave(): Promise<GameSave | null> {
    if (!this.isElectron || !window.electronAPI) {
      // Fallback to browser file input
      return this.importFromBrowser()
    }

    try {
      const filePath = await window.electronAPI.showOpenDialog(['.json'])
      
      if (!filePath) return null // User cancelled
      
      const saveString = await window.electronAPI.readFile(filePath)
      const gameData: GameSave = JSON.parse(saveString)
      
      console.log(`Save imported from: ${filePath}`)
      return gameData
    } catch (error) {
      console.error('Failed to import save:', error)
      return null
    }
  }

  // Create backup of save file
  async createBackup(fileName: string): Promise<boolean> {
    if (!this.isElectron || !window.electronAPI) {
      return false // Not supported in browser
    }

    try {
      const sourceFile = `${this.saveDirectory}/${fileName}`
      const backupFile = `${this.saveDirectory}/backups/${fileName}.backup.${Date.now()}`
      
      await window.electronAPI.ensureDirectory(`${this.saveDirectory}/backups`)
      await window.electronAPI.copyFile(sourceFile, backupFile)
      
      console.log(`Backup created: ${backupFile}`)
      return true
    } catch (error) {
      console.error('Failed to create backup:', error)
      return false
    }
  }

  // Fallback methods for browser environment
  private saveToLocalStorage(gameData: GameSave, fileName?: string): boolean {
    try {
      const key = fileName || `save_${Date.now()}`
      localStorage.setItem(`bakeItOut_file_${key}`, JSON.stringify(gameData))
      return true
    } catch (error) {
      console.error('Failed to save to localStorage:', error)
      return false
    }
  }

  private loadFromLocalStorage(fileName: string): GameSave | null {
    try {
      const saveString = localStorage.getItem(`bakeItOut_file_${fileName}`)
      return saveString ? JSON.parse(saveString) : null
    } catch (error) {
      console.error('Failed to load from localStorage:', error)
      return null
    }
  }

  private getLocalStorageSaves(): FileSaveMetadata[] {
    const saves: FileSaveMetadata[] = []
    
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i)
      if (key?.startsWith('bakeItOut_file_')) {
        try {
          const saveString = localStorage.getItem(key)
          if (saveString) {
            const gameData: GameSave = JSON.parse(saveString)
            const fileName = key.replace('bakeItOut_file_', '')
            
            saves.push({
              fileName,
              displayName: gameData.player.name || fileName,
              timestamp: gameData.timestamp,
              playerLevel: gameData.player.level,
              money: gameData.player.money,
              playTime: gameData.player.playTime || 0,
              version: gameData.version,
              fileSize: saveString.length
            })
          }
        } catch (error) {
          console.error(`Failed to parse save ${key}:`, error)
        }
      }
    }
    
    return saves.sort((a, b) => b.timestamp - a.timestamp)
  }

  private deleteFromLocalStorage(fileName: string): boolean {
    try {
      localStorage.removeItem(`bakeItOut_file_${fileName}`)
      return true
    } catch (error) {
      console.error('Failed to delete from localStorage:', error)
      return false
    }
  }

  private exportToBrowser(gameData: GameSave, suggestedName?: string): boolean {
    try {
      const saveString = JSON.stringify(gameData, null, 2)
      const blob = new Blob([saveString], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      
      const a = document.createElement('a')
      a.href = url
      a.download = suggestedName || `bake-it-out-save-${Date.now()}.json`
      a.click()
      
      URL.revokeObjectURL(url)
      return true
    } catch (error) {
      console.error('Failed to export to browser:', error)
      return false
    }
  }

  private importFromBrowser(): Promise<GameSave | null> {
    return new Promise((resolve) => {
      const input = document.createElement('input')
      input.type = 'file'
      input.accept = '.json'
      
      input.onchange = (e) => {
        const file = (e.target as HTMLInputElement).files?.[0]
        if (!file) {
          resolve(null)
          return
        }
        
        const reader = new FileReader()
        reader.onload = (e) => {
          try {
            const saveString = e.target?.result as string
            const gameData: GameSave = JSON.parse(saveString)
            resolve(gameData)
          } catch (error) {
            console.error('Failed to parse imported file:', error)
            resolve(null)
          }
        }
        reader.readAsText(file)
      }
      
      input.click()
    })
  }
}

export const fileSaveSystem = new FileSaveSystem()
