@echo off
echo Bake It Out - Cloud Save Server Startup
echo ========================================
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Node.js is not installed or not in PATH
    echo Please install Node.js 18+ from https://nodejs.org/
    echo.
    pause
    exit /b 1
)

echo Node.js version:
node --version
echo.

REM Check if server directory exists
if not exist "server" (
    echo ERROR: Server directory not found
    echo Please ensure the server files are included in the distribution
    echo.
    pause
    exit /b 1
)

cd server

REM Check if dependencies are installed
if not exist "node_modules" (
    echo Installing server dependencies...
    npm install
    if %errorlevel% neq 0 (
        echo ERROR: Failed to install dependencies
        echo.
        pause
        exit /b 1
    )
    echo Dependencies installed successfully
    echo.
)

REM Check if .env file exists, create from example if not
if not exist ".env" (
    echo Creating environment configuration...
    if exist ".env.example" (
        copy ".env.example" ".env" >nul
        echo Environment file created from template
        echo Please review and update .env file if needed
    ) else (
        echo Creating basic .env file...
        echo NODE_ENV=development > .env
        echo PORT=3001 >> .env
        echo MONGODB_URI=mongodb://localhost:27017/bake-it-out >> .env
        echo JWT_SECRET=bake-it-out-development-secret-change-in-production >> .env
        echo ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3002 >> .env
    )
    echo.
)

REM Check if MongoDB is running (optional)
echo Checking MongoDB connection...
timeout /t 2 >nul

REM Start the server
echo Starting Bake It Out Cloud Save Server...
echo Server will be available at: http://localhost:3001
echo Health check: http://localhost:3001/health
echo.
echo Press Ctrl+C to stop the server
echo.

node src/index.js

echo.
echo Server stopped
pause
