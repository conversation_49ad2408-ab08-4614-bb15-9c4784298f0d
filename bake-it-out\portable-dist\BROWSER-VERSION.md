# 🧁 Bake It Out - Browser Version

**Play the game directly in your web browser!**

## ⚡ Quick Start

### **🎯 Super Easy (Recommended)**
```bash
# Just run this!
PLAY-BROWSER.bat
```

### **🚀 Manual Method**
```bash
# 1. Build the game
npm run build

# 2. Start server (choose one):
npx serve out -p 3000        # Method 1
node serve-browser.js         # Method 2  
python -m http.server 3000    # Method 3 (from out/ folder)

# 3. Open browser
# Go to: http://localhost:3000
```

---

## 🎮 **What You Get**

✅ **Complete Game** - Full bakery management experience  
✅ **No Installation** - Runs directly in browser  
✅ **Offline Play** - Works without internet after loading  
✅ **Local Saves** - Progress saved in browser storage  
✅ **All Features** - Complete game functionality  
✅ **Responsive** - Works on desktop and mobile  

---

## 🌐 **Browser Requirements**

### **Supported Browsers:**
✅ **Chrome 90+** (Recommended)  
✅ **Firefox 88+**  
✅ **Safari 14+**  
✅ **Edge 90+**  

### **Features Needed:**
✅ **JavaScript** - Must be enabled  
✅ **Local Storage** - For saving progress  
✅ **Modern CSS** - For proper display  

---

## 🔧 **Setup Methods**

### **Method 1: NPX Serve (Easiest)**
```bash
# Install serve if needed
npm install -g serve

# Start server
npx serve out -p 3000 --single

# Open: http://localhost:3000
```

### **Method 2: Custom Node.js Server**
```bash
# Use our custom server
node serve-browser.js

# Open: http://localhost:3000
```

### **Method 3: Python Server**
```bash
# Navigate to build folder
cd out

# Start Python server
python -m http.server 3000

# Open: http://localhost:3000
```

### **Method 4: Any Static Server**
The `out/` folder contains all the files needed. You can serve it with any static file server.

---

## 🎯 **Game Features**

### **🏪 Bakery Management**
- Build and upgrade your bakery
- Manage ingredients and inventory
- Handle customer orders
- Unlock new recipes and equipment

### **🎮 Gameplay**
- Progressive difficulty
- Achievement system
- Skill development
- Multiple game modes

### **💾 Save System**
- **Local Storage** - Saves in browser
- **Export/Import** - Backup your progress
- **Multiple Slots** - Different save files

### **🌍 Languages**
- **English** - Full support
- **Czech** - Complete translation

---

## 🆘 **Troubleshooting**

### **"Game won't load"**
- Check if JavaScript is enabled
- Try a different browser
- Clear browser cache and reload
- Check browser console for errors

### **"Server won't start"**
```bash
# Try different ports
npx serve out -p 3001
npx serve out -p 8080

# Or install serve globally
npm install -g serve
```

### **"Build files missing"**
```bash
# Rebuild the game
npm run build

# Check if out/ folder exists
dir out  # Windows
ls out   # Mac/Linux
```

### **"Saves not working"**
- Check if Local Storage is enabled
- Try incognito/private mode
- Clear browser data and try again

---

## 📱 **Mobile Support**

### **Mobile Browsers:**
✅ **Chrome Mobile** - Full support  
✅ **Safari Mobile** - Full support  
✅ **Firefox Mobile** - Full support  

### **Touch Controls:**
✅ **Tap to click** - All buttons work  
✅ **Pinch to zoom** - Responsive design  
✅ **Swipe navigation** - Smooth scrolling  

---

## 🔗 **Integration with Server**

### **Cloud Saves (Optional)**
If you have the server running:
```bash
# Start server first
cd server && npm start

# Then start browser version
PLAY-BROWSER.bat

# Game will connect automatically for:
# - Cloud saves
# - Multiplayer
# - User accounts
```

### **Standalone Mode**
Without server:
- ✅ **Full game** works offline
- ✅ **Local saves** in browser
- ❌ **No cloud saves**
- ❌ **No multiplayer**

---

## 📊 **Performance Tips**

### **For Best Performance:**
- **Use Chrome** - Best performance
- **Close other tabs** - More memory available
- **Enable hardware acceleration** - Smoother graphics
- **Use desktop** - Better than mobile for complex gameplay

### **If Game is Slow:**
- Lower graphics quality in settings
- Close other applications
- Try a different browser
- Check for browser updates

---

## 🎉 **Ready to Play?**

### **Quick Start:**
1. **Run**: `PLAY-BROWSER.bat`
2. **Open**: http://localhost:3000
3. **Play**: Start your bakery empire!

### **Manual Start:**
1. **Build**: `npm run build`
2. **Serve**: `npx serve out -p 3000`
3. **Play**: http://localhost:3000

**🧁 Happy baking in your browser! 🎮**
