{"version": 3, "sources": [], "sections": [{"offset": {"line": 13, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/contexts/LanguageContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect } from 'react'\n\ntype Language = 'en' | 'cs'\n\ninterface LanguageContextType {\n  language: Language\n  setLanguage: (lang: Language) => void\n  t: (key: string, params?: Record<string, string>) => string\n}\n\nconst LanguageContext = createContext<LanguageContextType | undefined>(undefined)\n\n// Comprehensive translations object\nconst translations = {\n  en: {\n    // Main game\n    'game.title': 'Bake It Out',\n    'game.subtitle': 'Master the art of bakery management in this engaging multiplayer game. Complete orders, unlock recipes, automate your processes, and compete with friends!',\n    'game.play': '🎮 Start Playing',\n    'game.singlePlayer': '🎮 Single Player',\n    'game.singlePlayerDesc': 'Play solo and master your bakery skills',\n    'game.multiplayer': '👥 Multiplayer',\n    'game.multiplayerDesc': 'Play with friends in cooperative or competitive modes',\n    'game.english': '🇺🇸 English',\n    'game.czech': '🇨🇿 Čeština',\n    'game.home': '🏠 Home',\n    'game.close': '✕ Close',\n    'game.continue': '🚀 Continue Playing',\n\n    // Menu options\n    'menu.singlePlayer': 'Single Player',\n    'menu.multiplayer': 'Multiplayer',\n    'menu.settings': 'Settings',\n    'menu.credits': 'Credits',\n    'menu.exit': 'Exit',\n    'menu.newGame': 'New Game',\n    'menu.continueGame': 'Continue Game',\n    'menu.loadGame': 'Load Game',\n    'menu.selectLanguage': 'Select Language',\n    'menu.about': 'About',\n    'menu.help': 'Help',\n    'menu.quit': 'Quit',\n\n    // Features\n    'features.manage.title': 'Manage Your Bakery',\n    'features.manage.description': 'Take orders, bake delicious goods, and serve happy customers',\n    'features.levelup.title': 'Level Up & Automate',\n    'features.levelup.description': 'Unlock new recipes, buy equipment, and automate your processes',\n    'features.multiplayer.title': 'Play Together',\n    'features.multiplayer.description': 'Cooperative and competitive multiplayer modes with friends',\n    'status.development': '🚧 Game in Development - Phase 5: Multilayer Support! 🚧',\n\n    // Game interface\n    'ui.level': 'Level {{level}}',\n    'ui.money': '${{amount}}',\n    'ui.experience': 'XP: {{current}}/{{max}}',\n    'ui.skillPoints': 'SP: {{points}}',\n    'ui.achievements': '🏆 Achievements',\n    'ui.skills': '🌟 Skills',\n    'ui.automation': '🤖 Automation',\n\n    // Kitchen\n    'kitchen.title': '🏪 Kitchen',\n    'kitchen.clickToUse': 'Click to use',\n    'kitchen.making': 'Making: {{recipe}}',\n    'kitchen.timeRemaining': 'Time: {{time}}',\n\n    // Inventory\n    'inventory.title': '📦 Inventory',\n    'inventory.quantity': 'Qty: {{qty}}',\n    'inventory.cost': '${{cost}} each',\n\n    // Orders\n    'orders.title': '📋 Orders',\n    'orders.newOrder': '+ New Order',\n    'orders.accept': 'Accept',\n    'orders.decline': 'Decline',\n    'orders.complete': 'Complete',\n    'orders.inProgress': 'In Progress',\n    'orders.timeLimit': 'Time: {{time}}',\n    'orders.reward': '${{amount}}',\n    'orders.customer': 'Customer: {{name}}',\n\n    // Quick Actions\n    'actions.title': '⚡ Quick Actions',\n    'actions.buyIngredients': '🛒 Buy Ingredients',\n    'actions.viewRecipes': '📖 View Recipes',\n    'actions.equipmentShop': '🔧 Equipment Shop',\n\n    // Modals\n    'modal.recipes.title': '📖 Recipe Book',\n    'modal.shop.title': '🛒 Ingredient Shop',\n    'modal.baking.title': '🔥 {{equipment}} - Select Recipe',\n    'modal.achievements.title': '🏆 Achievements',\n    'modal.skills.title': '🌟 Skill Tree',\n    'modal.automation.title': '🤖 Automation Control',\n    'modal.equipmentShop.title': '🏪 Equipment Shop',\n    'modal.settings.title': '⚙️ Settings',\n    'modal.bakeries.title': '🏪 Bakery Manager',\n    'modal.levelUp.title': 'Level Up!',\n    'modal.levelUp.subtitle': 'You reached Level {{level}}!',\n\n    // Recipe Modal\n    'recipes.all': 'All',\n    'recipes.cookies': 'Cookies',\n    'recipes.cakes': 'Cakes',\n    'recipes.bread': 'Bread',\n    'recipes.pastries': 'Pastries',\n    'recipes.ingredients': 'Ingredients:',\n    'recipes.difficulty': 'Difficulty:',\n    'recipes.time': 'Time:',\n    'recipes.canCraft': '✅ Can Craft',\n    'recipes.unlockLevel': 'Unlocked at Level {{level}}',\n    'recipes.noRecipes': 'No recipes available in this category.',\n    'recipes.levelUpToUnlock': 'Level up to unlock more recipes!',\n\n    // Shop Modal\n    'shop.currentStock': 'Current stock: {{quantity}}',\n    'shop.buy': 'Buy',\n    'shop.tooExpensive': 'Too Expensive',\n    'shop.tips.title': '💡 Shopping Tips',\n    'shop.tips.bulk': '• Buy ingredients in bulk to save time',\n    'shop.tips.stock': '• Keep an eye on your stock levels',\n    'shop.tips.rare': '• Some recipes require rare ingredients',\n    'shop.tips.prices': '• Prices may vary based on availability',\n\n    // Baking Modal\n    'baking.selectRecipe': 'Select Recipe',\n    'baking.noRecipes': 'No recipes available',\n    'baking.noIngredients': 'You don\\'t have enough ingredients to craft any recipes.',\n    'baking.buyIngredients': 'Buy Ingredients',\n    'baking.startBaking': '🔥 Start Baking',\n    'baking.instructions': '📋 Baking Instructions for {{recipe}}',\n    'baking.expectedReward': 'Expected reward: ${{amount}}',\n    'baking.makesSure': 'Make sure you have all ingredients before starting!',\n    'baking.inProgress': 'Baking in progress...',\n    'baking.completed': 'Baking completed!',\n    'baking.cancelled': 'Baking cancelled',\n    'baking.timeRemaining': 'Time remaining: {{time}}',\n    'baking.clickToCollect': 'Click to collect',\n\n    // Achievements Modal\n    'achievements.completed': '{{completed}} of {{total}} achievements completed',\n    'achievements.overallProgress': 'Overall Progress',\n    'achievements.progress': 'Progress',\n    'achievements.reward': 'Reward:',\n    'achievements.noAchievements': 'No achievements in this category.',\n\n    // Skills Modal\n    'skills.availablePoints': 'Available Skill Points: {{points}}',\n    'skills.efficiency': 'Efficiency',\n    'skills.automation': 'Automation',\n    'skills.quality': 'Quality',\n    'skills.business': 'Business',\n    'skills.effects': 'Effects:',\n    'skills.requires': 'Requires: {{requirements}}',\n    'skills.requiresLevel': 'Requires Level {{level}}',\n    'skills.maxed': '✅ Maxed',\n    'skills.upgrade': '⬆️ Upgrade ({{cost}} SP)',\n    'skills.locked': '🔒 Locked',\n    'skills.noSkills': 'No skills in this category.',\n    'skills.tips.title': '💡 Skill Tips',\n    'skills.tips.earnPoints': '• Earn skill points by leveling up (1 point every 2 levels)',\n    'skills.tips.prerequisites': '• Some skills require other skills to be unlocked first',\n    'skills.tips.playstyle': '• Focus on skills that match your playstyle',\n    'skills.tips.efficiency': '• Efficiency skills help with resource management',\n\n    // Automation Modal\n    'automation.masterControl': '🎛️ Master Control',\n    'automation.enableAutomation': 'Enable Automation',\n    'automation.autoStart': 'Auto-start Equipment',\n    'automation.priorityMode': '🎯 Priority Mode',\n    'automation.efficiency': 'Efficiency (Orders First)',\n    'automation.profit': 'Profit (Highest Value)',\n    'automation.speed': 'Speed (Fastest Recipes)',\n    'automation.priorityDescription': 'How automation chooses what to bake',\n    'automation.performance': '⚡ Performance',\n    'automation.maxJobs': 'Max Concurrent Jobs: {{jobs}}',\n    'automation.safety': '🛡️ Safety',\n    'automation.stopWhenLow': 'Stop when ingredients below: {{threshold}}',\n    'automation.upgrades': '💡 Automation Upgrades',\n    'automation.upgradesDescription': 'Improve your automation efficiency, speed, and intelligence with these upgrades.',\n    'automation.purchase': 'Purchase',\n    'automation.noUpgrades': 'No upgrades available at your current level.',\n    'automation.levelUpForUpgrades': 'Level up to unlock more automation upgrades!',\n    'automation.automatedEquipment': 'Automated Equipment',\n    'automation.activeUpgrades': 'Active Upgrades',\n    'automation.automationStatus': 'Automation Status',\n    'automation.equipmentStatus': '🏭 Equipment Status',\n    'automation.running': 'Running',\n    'automation.idle': 'Idle',\n    'automation.noAutomatedEquipment': 'No automated equipment available.',\n    'automation.purchaseAutoEquipment': 'Purchase auto-equipment from the shop to get started!',\n\n    // Equipment Shop Modal\n    'equipmentShop.upgradeYourBakery': 'Upgrade your bakery with professional equipment',\n    'equipmentShop.basic': 'Basic',\n    'equipmentShop.automated': 'Automated',\n    'equipmentShop.advanced': 'Advanced',\n    'equipmentShop.efficiency': 'Efficiency: {{efficiency}}x',\n    'equipmentShop.automation': 'Automation:',\n    'equipmentShop.unlockLevel': 'Unlock Level: {{level}}',\n    'equipmentShop.purchase': '💰 Purchase',\n    'equipmentShop.noEquipment': 'No equipment available in this category.',\n    'equipmentShop.levelUpForEquipment': 'Level up to unlock more equipment!',\n    'equipmentShop.tips.title': '💡 Equipment Tips',\n    'equipmentShop.tips.automated': '• Automated equipment can run without your supervision',\n    'equipmentShop.tips.efficiency': '• Higher efficiency means faster production and better quality',\n    'equipmentShop.tips.conveyor': '• Conveyor belts connect equipment for seamless workflow',\n    'equipmentShop.tips.advanced': '• Advanced equipment unlocks at higher levels',\n\n    // Level Up Modal\n    'levelUp.levelRewards': '🎁 Level Rewards',\n    'levelUp.whatsNext': '💡 What\\'s Next?',\n    'levelUp.checkRecipes': '• Check out new recipes in your recipe book',\n    'levelUp.visitShop': '• Visit the shop for new equipment',\n    'levelUp.challengingOrders': '• Take on more challenging orders',\n    'levelUp.investSkills': '• Invest in skill upgrades',\n\n    // Settings Modal\n    'settings.title': '⚙️ Settings',\n    'settings.general': 'General',\n    'settings.audio': 'Audio',\n    'settings.graphics': 'Graphics',\n    'settings.save': 'Save & Data',\n    'settings.language': '🌍 Language',\n    'settings.gameplay': '🎮 Gameplay',\n    'settings.notifications': 'Enable Notifications',\n    'settings.tutorials': 'Show Tutorials',\n    'settings.animationSpeed': 'Animation Speed',\n    'settings.sound': 'Sound Effects',\n    'settings.music': 'Background Music',\n    'settings.quality': '🎨 Graphics Quality',\n    'settings.autoSave': '💾 Auto-Save',\n    'settings.enableAutoSave': 'Enable Auto-Save',\n    'settings.dataManagement': '📁 Data Management',\n    'settings.exportSave': '📤 Export Save',\n    'settings.importSave': '📥 Import Save',\n    'settings.cloudSync': '☁️ Cloud Sync',\n    'settings.cloudSyncDescription': 'Cloud sync allows you to save your progress online and play across multiple devices.',\n    'settings.comingSoon': 'Coming Soon',\n\n    // Bakery Manager Modal\n    'bakeries.title': '🏪 Bakery Manager',\n    'bakeries.subtitle': 'Manage your bakery empire',\n    'bakeries.owned': 'My Bakeries',\n    'bakeries.available': 'Available',\n    'bakeries.current': 'Current',\n    'bakeries.level': 'Level',\n    'bakeries.specialization': 'Specialization',\n    'bakeries.equipment': 'Equipment',\n    'bakeries.orders': 'Active Orders',\n    'bakeries.switchTo': 'Switch To',\n    'bakeries.noOwned': 'You don\\'t own any bakeries yet.',\n    'bakeries.purchase': '💰 Purchase',\n    'bakeries.tooExpensive': '💸 Too Expensive',\n    'bakeries.allOwned': 'You own all available bakeries!',\n    'bakeries.tips': '💡 Bakery Tips',\n    'bakeries.tip1': 'Each bakery specializes in different products for bonus efficiency',\n    'bakeries.tip2': 'Switch between bakeries to manage multiple locations',\n    'bakeries.tip3': 'Specialized bakeries attract customers looking for specific items',\n    'bakeries.tip4': 'Upgrade each bakery independently for maximum profit',\n\n    // Notifications\n    'notifications.orderAccepted': 'Order Accepted',\n    'notifications.orderAcceptedMessage': 'You have accepted a new order!',\n    'notifications.orderCompleted': 'Order Completed!',\n    'notifications.orderCompletedMessage': 'You earned ${{reward}} and gained experience!',\n    'notifications.orderDeclined': 'Order Declined',\n    'notifications.orderDeclinedMessage': 'Order has been removed from your queue.',\n    'notifications.bakeryPurchased': 'Bakery Purchased!',\n    'notifications.bakeryPurchasedMessage': 'You now own {{name}}!',\n    'notifications.bakerySwitched': 'Bakery Switched',\n    'notifications.bakerySwitchedMessage': 'Switched to {{name}}',\n\n    // Common buttons and actions\n    'common.accept': 'Accept',\n    'common.decline': 'Decline',\n    'common.complete': 'Complete',\n    'common.purchase': 'Purchase',\n    'common.upgrade': 'Upgrade',\n    'common.cancel': 'Cancel',\n    'common.confirm': 'Confirm',\n    'common.save': 'Save',\n    'common.load': 'Load',\n    'common.delete': 'Delete',\n    'common.edit': 'Edit',\n    'common.back': 'Back',\n    'common.next': 'Next',\n    'common.previous': 'Previous',\n    'common.yes': 'Yes',\n    'common.no': 'No',\n    'common.create': 'Create',\n    'common.join': 'Join',\n    'common.leave': 'Leave',\n    'common.start': 'Start',\n    'common.ready': 'Ready',\n    'common.notReady': 'Not Ready',\n    'common.send': 'Send',\n    'common.refresh': 'Refresh',\n    'common.retry': 'Retry',\n    'common.reset': 'Reset',\n    'common.clear': 'Clear',\n    'common.apply': 'Apply',\n    'common.warning': 'Warning',\n    'common.info': 'Information',\n    'common.success': 'Success',\n    'common.error': 'Error',\n\n    // Save/Load System\n    'saveLoad.saveDesc': 'Choose a slot to save your progress',\n    'saveLoad.loadDesc': 'Select a save file to load',\n    'saveLoad.saveName': 'Save Name',\n    'saveLoad.emptySlot': 'Empty Slot',\n    'saveLoad.selectedSaveSlot': 'Selected: Slot {{slot}}',\n    'saveLoad.selectedLoadSlot': 'Selected: Slot {{slot}}',\n    'saveLoad.confirmOverwrite': 'Overwrite Save?',\n    'saveLoad.overwriteWarning': 'This will overwrite the existing save. This action cannot be undone.',\n    'saveLoad.overwrite': 'Overwrite',\n    'saveLoad.fileSlots': 'File Slots',\n    'saveLoad.gameSlots': 'Game Slots',\n    'saveLoad.exportSave': 'Export Save',\n    'saveLoad.importSave': 'Import Save',\n    'saveLoad.deleteConfirm': 'Delete Save?',\n    'saveLoad.deleteWarning': 'This will permanently delete this save file. This action cannot be undone.',\n    'saveLoad.delete': 'Delete',\n\n    // Game Menu\n    'gameMenu.title': 'Game Menu',\n    'gameMenu.subtitle': 'Manage your game',\n    'gameMenu.resume': 'Resume Game',\n    'gameMenu.resumeDesc': 'Continue playing',\n    'gameMenu.save': 'Save Game',\n    'gameMenu.saveDesc': 'Save your progress',\n    'gameMenu.load': 'Load Game',\n    'gameMenu.loadDesc': 'Load saved progress',\n    'gameMenu.settings': 'Settings',\n    'gameMenu.settingsDesc': 'Game preferences',\n    'gameMenu.mainMenu': 'Main Menu',\n    'gameMenu.mainMenuDesc': 'Return to main menu',\n    'gameMenu.exit': 'Exit Game',\n    'gameMenu.exitDesc': 'Close the application',\n    'gameMenu.tip': 'Press ESC to open this menu anytime',\n\n    // Discord Rich Presence\n    'settings.discord': 'Discord',\n    'settings.discordRichPresence': 'Discord Rich Presence',\n    'settings.discordDescription': 'Show your current game status and activity in Discord.',\n    'settings.enableDiscordRPC': 'Enable Discord Rich Presence',\n    'settings.discordConnected': '✅ Connected to Discord',\n    'settings.discordDisconnected': '❌ Not connected to Discord',\n    'settings.discordInfo': 'What is Discord Rich Presence?',\n    'settings.discordInfoDesc1': 'Discord Rich Presence shows your friends what you\\'re doing in Bake It Out:',\n    'settings.discordFeature1': 'Your current level and money',\n    'settings.discordFeature2': 'What you\\'re currently baking',\n    'settings.discordFeature3': 'Multiplayer room information',\n    'settings.discordFeature4': 'How long you\\'ve been playing',\n    'settings.discordInfoDesc2': 'Your friends can even join your multiplayer games directly from Discord!',\n    'settings.discordTroubleshooting': 'Discord Not Connected',\n    'settings.discordTrouble1': 'Make sure Discord is running on your computer.',\n    'settings.discordTrouble2': 'Discord Rich Presence only works in the desktop version of the game.',\n    'settings.discordTrouble3': 'Try restarting both Discord and the game if the connection fails.',\n    'settings.discordPrivacy': 'Privacy Information',\n    'settings.discordPrivacyDesc1': 'Discord Rich Presence only shares:',\n    'settings.discordPrivacy1': 'Your current game activity (public)',\n    'settings.discordPrivacy2': 'Your player level and progress (public)',\n    'settings.discordPrivacy3': 'Multiplayer room codes (for joining)',\n    'settings.discordPrivacyDesc2': 'No personal information or save data is shared with Discord.',\n    'settings.discordStatus': 'Discord Status',\n    'settings.discordInitializing': '🔄 Initializing Discord RPC...',\n    'settings.discordRetrying': '🔄 Retrying connection...',\n    'settings.discordUnavailable': '❌ Discord not available',\n    'settings.discordDesktopOnly': 'ℹ️ Discord RPC only available in desktop version',\n\n    // Error messages and status\n    'error.general': 'An error occurred',\n    'error.saveLoad': 'Failed to save/load game',\n    'error.connection': 'Connection error',\n    'error.fileNotFound': 'File not found',\n    'error.invalidData': 'Invalid data format',\n    'error.permissionDenied': 'Permission denied',\n    'status.loading': 'Loading...',\n    'status.saving': 'Saving...',\n    'status.connecting': 'Connecting...',\n    'status.ready': 'Ready',\n    'status.success': 'Success!',\n    'status.failed': 'Failed',\n    'status.offline': 'Offline',\n    'status.online': 'Online',\n\n    // UI Elements\n    'ui.placeholder': 'Enter text...',\n    'ui.search': 'Search',\n    'ui.filter': 'Filter',\n    'ui.sort': 'Sort',\n    'ui.ascending': 'Ascending',\n    'ui.descending': 'Descending',\n    'ui.selectAll': 'Select All',\n    'ui.deselectAll': 'Deselect All',\n    'ui.noResults': 'No results found',\n    'ui.noData': 'No data available',\n    'ui.loading': 'Loading...',\n    'ui.saving': 'Saving...',\n    'ui.saved': 'Saved!',\n    'ui.failed': 'Failed',\n    'ui.retry': 'Retry',\n    'ui.back': 'Back',\n    'ui.forward': 'Forward',\n    'ui.home': 'Home',\n    'ui.menu': 'Menu',\n    'ui.options': 'Options',\n    'ui.preferences': 'Preferences',\n\n    // Multiplayer\n    'multiplayer.lobby': '👥 Multiplayer Lobby',\n    'multiplayer.connected': '🟢 Connected',\n    'multiplayer.disconnected': '🔴 Disconnected',\n    'multiplayer.createRoom': 'Create Room',\n    'multiplayer.joinRoom': 'Join Room',\n    'multiplayer.room': 'Room',\n    'multiplayer.yourName': 'Your Name',\n    'multiplayer.enterName': 'Enter your name',\n    'multiplayer.roomName': 'Room Name',\n    'multiplayer.enterRoomName': 'Enter room name',\n    'multiplayer.gameMode': 'Game Mode',\n    'multiplayer.cooperative': '🤝 Cooperative',\n    'multiplayer.competitive': '⚔️ Competitive',\n    'multiplayer.maxPlayers': 'Max Players: {{count}}',\n    'multiplayer.roomId': 'Room ID',\n    'multiplayer.enterRoomId': 'Enter room ID',\n    'multiplayer.players': 'Players ({{count}})',\n    'multiplayer.host': 'HOST',\n    'multiplayer.level': 'Level {{level}}',\n    'multiplayer.chat': 'Chat',\n    'multiplayer.typeMessage': 'Type a message...',\n    'multiplayer.gameTime': 'Game Time: {{time}}',\n    'multiplayer.teamStats': '📊 Team Stats',\n    'multiplayer.ordersCompleted': 'Orders Completed:',\n    'multiplayer.totalRevenue': 'Total Revenue:',\n    'multiplayer.teamExperience': 'Team Experience:',\n    'multiplayer.sharedKitchen': '🏪 Shared Kitchen',\n    'multiplayer.sharedOrders': '📋 Shared Orders',\n    'multiplayer.sharedInventory': '📦 Shared Inventory',\n    'multiplayer.contribution': 'Contribution:',\n    'multiplayer.online': '🟢 Online',\n    'multiplayer.status': 'Status:',\n    'multiplayer.you': '(You)',\n    'multiplayer.teamChat': '💬 Team Chat',\n    'multiplayer.chatPlaceholder': 'Chat messages will appear here...',\n\n    // Multiplayer game modes\n    'multiplayer.mode.cooperative.description': '🤝 Cooperative Mode: Work together to complete orders and grow your shared bakery!',\n    'multiplayer.mode.competitive.description': '⚔️ Competitive Mode: Compete against other players to complete the most orders!',\n\n    // Multiplayer game interface\n    'multiplayer.game.title': '🎮 Multiplayer Game - {{roomName}}',\n    'multiplayer.game.mode': 'Mode: {{mode}}',\n    'multiplayer.game.playersCount': 'Players: {{count}}',\n    'multiplayer.game.playing': '🟢 Playing',\n    'multiplayer.game.leaveGame': '🚪 Leave Game',\n    'multiplayer.game.tabs.game': 'Game',\n    'multiplayer.game.tabs.players': 'Players',\n    'multiplayer.game.tabs.chat': 'Chat',\n\n    // Room creation and joining\n    'multiplayer.create.title': '🏗️ Create Room',\n    'multiplayer.join.title': '🚪 Join Room',\n    'multiplayer.room.info': 'Mode: {{mode}} • Players: {{current}}/{{max}}',\n    'multiplayer.room.readyUp': '✅ Ready',\n    'multiplayer.room.notReady': '⏳ Not Ready',\n    'multiplayer.room.startGame': '🚀 Start Game',\n    'multiplayer.room.leaveRoom': '🚪 Leave',\n\n    // Connection states\n    'multiplayer.connection.connecting': 'Connecting...',\n    'multiplayer.connection.reconnecting': 'Reconnecting...',\n    'multiplayer.connection.failed': 'Connection failed',\n    'multiplayer.connection.error': '⚠️ {{error}}',\n\n    // System messages\n    'multiplayer.system.playerJoined': '{{name}} joined the room',\n    'multiplayer.system.playerLeft': '{{name}} left the room',\n    'multiplayer.system.gameStarted': 'Game started!',\n    'multiplayer.system.gameEnded': 'Game ended!',\n    'multiplayer.system.roomCreated': 'Room created successfully',\n    'multiplayer.system.roomJoined': 'Joined room successfully'\n  },\n  cs: {\n    // Main game\n    'game.title': 'Bake It Out',\n    'game.subtitle': 'Ovládněte umění řízení pekárny v této poutavé multiplayerové hře. Plňte objednávky, odemykejte recepty, automatizujte procesy a soutěžte s přáteli!',\n    'game.play': '🎮 Začít hrát',\n    'game.singlePlayer': '🎮 Jeden hráč',\n    'game.singlePlayerDesc': 'Hrajte sólo a zdokonalte své pekařské dovednosti',\n    'game.multiplayer': '👥 Multiplayer',\n    'game.multiplayerDesc': 'Hrajte s přáteli v kooperativních nebo soutěžních režimech',\n    'game.english': '🇺🇸 English',\n    'game.czech': '🇨🇿 Čeština',\n    'game.home': '🏠 Domů',\n    'game.close': '✕ Zavřít',\n    'game.continue': '🚀 Pokračovat ve hře',\n\n    // Menu options\n    'menu.singlePlayer': 'Jeden hráč',\n    'menu.multiplayer': 'Multiplayer',\n    'menu.settings': 'Nastavení',\n    'menu.credits': 'Titulky',\n    'menu.exit': 'Ukončit',\n    'menu.newGame': 'Nová hra',\n    'menu.continueGame': 'Pokračovat ve hře',\n    'menu.loadGame': 'Načíst hru',\n    'menu.selectLanguage': 'Vybrat jazyk',\n    'menu.about': 'O hře',\n    'menu.help': 'Nápověda',\n    'menu.quit': 'Ukončit',\n\n    // Features\n    'features.manage.title': 'Spravujte svou pekárnu',\n    'features.manage.description': 'Přijímejte objednávky, pečte lahodné výrobky a obsluhujte spokojené zákazníky',\n    'features.levelup.title': 'Postupujte a automatizujte',\n    'features.levelup.description': 'Odemykejte nové recepty, kupujte vybavení a automatizujte své procesy',\n    'features.multiplayer.title': 'Hrajte společně',\n    'features.multiplayer.description': 'Kooperativní a soutěžní multiplayerové režimy s přáteli',\n    'status.development': '🚧 Hra ve vývoji - Fáze 5: Vícevrstvá podpora! 🚧',\n\n    // Game interface\n    'ui.level': 'Úroveň {{level}}',\n    'ui.money': '{{amount}} Kč',\n    'ui.experience': 'XP: {{current}}/{{max}}',\n    'ui.skillPoints': 'SP: {{points}}',\n    'ui.achievements': '🏆 Úspěchy',\n    'ui.skills': '🌟 Dovednosti',\n    'ui.automation': '🤖 Automatizace',\n\n    // Kitchen\n    'kitchen.title': '🏪 Kuchyně',\n    'kitchen.clickToUse': 'Klikněte pro použití',\n    'kitchen.making': 'Připravuje: {{recipe}}',\n    'kitchen.timeRemaining': 'Čas: {{time}}',\n\n    // Inventory\n    'inventory.title': '📦 Sklad',\n    'inventory.quantity': 'Množství: {{qty}}',\n    'inventory.cost': '{{cost}} Kč za kus',\n\n    // Orders\n    'orders.title': '📋 Objednávky',\n    'orders.newOrder': '+ Nová objednávka',\n    'orders.accept': 'Přijmout',\n    'orders.decline': 'Odmítnout',\n    'orders.complete': 'Dokončit',\n    'orders.inProgress': 'Probíhá',\n    'orders.timeLimit': 'Čas: {{time}}',\n    'orders.reward': '{{amount}} Kč',\n    'orders.customer': 'Zákazník: {{name}}',\n\n    // Quick Actions\n    'actions.title': '⚡ Rychlé akce',\n    'actions.buyIngredients': '🛒 Koupit suroviny',\n    'actions.viewRecipes': '📖 Zobrazit recepty',\n    'actions.equipmentShop': '🔧 Obchod s vybavením',\n\n    // Modals\n    'modal.recipes.title': '📖 Kniha receptů',\n    'modal.shop.title': '🛒 Obchod se surovinami',\n    'modal.baking.title': '🔥 {{equipment}} - Vyberte recept',\n    'modal.achievements.title': '🏆 Úspěchy',\n    'modal.skills.title': '🌟 Strom dovedností',\n    'modal.automation.title': '🤖 Ovládání automatizace',\n    'modal.equipmentShop.title': '🏪 Obchod s vybavením',\n    'modal.settings.title': '⚙️ Nastavení',\n    'modal.bakeries.title': '🏪 Správce pekáren',\n    'modal.levelUp.title': 'Postup na vyšší úroveň!',\n    'modal.levelUp.subtitle': 'Dosáhli jste úrovně {{level}}!',\n\n    // Recipe Modal\n    'recipes.all': 'Vše',\n    'recipes.cookies': 'Sušenky',\n    'recipes.cakes': 'Dorty',\n    'recipes.bread': 'Chléb',\n    'recipes.pastries': 'Pečivo',\n    'recipes.ingredients': 'Suroviny:',\n    'recipes.difficulty': 'Obtížnost:',\n    'recipes.time': 'Čas:',\n    'recipes.canCraft': '✅ Lze vyrobit',\n    'recipes.unlockLevel': 'Odemčeno na úrovni {{level}}',\n    'recipes.noRecipes': 'V této kategorii nejsou k dispozici žádné recepty.',\n    'recipes.levelUpToUnlock': 'Postupte na vyšší úroveň pro odemčení dalších receptů!',\n\n    // Shop Modal\n    'shop.currentStock': 'Aktuální zásoba: {{quantity}}',\n    'shop.buy': 'Koupit',\n    'shop.tooExpensive': 'Příliš drahé',\n    'shop.tips.title': '💡 Tipy pro nakupování',\n    'shop.tips.bulk': '• Kupujte suroviny ve velkém množství pro úsporu času',\n    'shop.tips.stock': '• Sledujte úroveň svých zásob',\n    'shop.tips.rare': '• Některé recepty vyžadují vzácné suroviny',\n    'shop.tips.prices': '• Ceny se mohou lišit podle dostupnosti',\n\n    // Baking Modal\n    'baking.selectRecipe': 'Vyberte recept',\n    'baking.noRecipes': 'Žádné recepty k dispozici',\n    'baking.noIngredients': 'Nemáte dostatek surovin pro výrobu jakéhokoli receptu.',\n    'baking.buyIngredients': 'Koupit suroviny',\n    'baking.startBaking': '🔥 Začít péct',\n    'baking.instructions': '📋 Pokyny pro pečení {{recipe}}',\n    'baking.expectedReward': 'Očekávaná odměna: {{amount}} Kč',\n    'baking.makesSure': 'Ujistěte se, že máte všechny suroviny před začátkem!',\n    'baking.inProgress': 'Pečení probíhá...',\n    'baking.completed': 'Pečení dokončeno!',\n    'baking.cancelled': 'Pečení zrušeno',\n    'baking.timeRemaining': 'Zbývající čas: {{time}}',\n    'baking.clickToCollect': 'Klikněte pro vyzvednutí',\n\n    // Achievements Modal\n    'achievements.completed': '{{completed}} z {{total}} úspěchů dokončeno',\n    'achievements.overallProgress': 'Celkový pokrok',\n    'achievements.progress': 'Pokrok',\n    'achievements.reward': 'Odměna:',\n    'achievements.noAchievements': 'V této kategorii nejsou žádné úspěchy.',\n\n    // Skills Modal\n    'skills.availablePoints': 'Dostupné body dovedností: {{points}}',\n    'skills.efficiency': 'Efektivita',\n    'skills.automation': 'Automatizace',\n    'skills.quality': 'Kvalita',\n    'skills.business': 'Podnikání',\n    'skills.effects': 'Efekty:',\n    'skills.requires': 'Vyžaduje: {{requirements}}',\n    'skills.requiresLevel': 'Vyžaduje úroveň {{level}}',\n    'skills.maxed': '✅ Maximální',\n    'skills.upgrade': '⬆️ Vylepšit ({{cost}} SP)',\n    'skills.locked': '🔒 Uzamčeno',\n    'skills.noSkills': 'V této kategorii nejsou žádné dovednosti.',\n    'skills.tips.title': '💡 Tipy pro dovednosti',\n    'skills.tips.earnPoints': '• Získávejte body dovedností postupem na vyšší úroveň (1 bod každé 2 úrovně)',\n    'skills.tips.prerequisites': '• Některé dovednosti vyžadují nejprve odemčení jiných dovedností',\n    'skills.tips.playstyle': '• Zaměřte se na dovednosti, které odpovídají vašemu stylu hry',\n    'skills.tips.efficiency': '• Dovednosti efektivity pomáhají se správou zdrojů',\n\n    // Automation Modal\n    'automation.masterControl': '🎛️ Hlavní ovládání',\n    'automation.enableAutomation': 'Povolit automatizaci',\n    'automation.autoStart': 'Automatické spuštění vybavení',\n    'automation.priorityMode': '🎯 Režim priority',\n    'automation.efficiency': 'Efektivita (objednávky první)',\n    'automation.profit': 'Zisk (nejvyšší hodnota)',\n    'automation.speed': 'Rychlost (nejrychlejší recepty)',\n    'automation.priorityDescription': 'Jak automatizace vybírá, co péct',\n    'automation.performance': '⚡ Výkon',\n    'automation.maxJobs': 'Max současných úloh: {{jobs}}',\n    'automation.safety': '🛡️ Bezpečnost',\n    'automation.stopWhenLow': 'Zastavit, když suroviny klesnou pod: {{threshold}}',\n    'automation.upgrades': '💡 Vylepšení automatizace',\n    'automation.upgradesDescription': 'Vylepšete efektivitu, rychlost a inteligenci vaší automatizace.',\n    'automation.purchase': 'Koupit',\n    'automation.noUpgrades': 'Na vaší současné úrovni nejsou k dispozici žádná vylepšení.',\n    'automation.levelUpForUpgrades': 'Postupte na vyšší úroveň pro odemčení dalších vylepšení automatizace!',\n    'automation.automatedEquipment': 'Automatizované vybavení',\n    'automation.activeUpgrades': 'Aktivní vylepšení',\n    'automation.automationStatus': 'Stav automatizace',\n    'automation.equipmentStatus': '🏭 Stav vybavení',\n    'automation.running': 'Běží',\n    'automation.idle': 'Nečinné',\n    'automation.noAutomatedEquipment': 'Žádné automatizované vybavení k dispozici.',\n    'automation.purchaseAutoEquipment': 'Kupte si auto-vybavení z obchodu pro začátek!',\n\n    // Equipment Shop Modal\n    'equipmentShop.upgradeYourBakery': 'Vylepšete svou pekárnu profesionálním vybavením',\n    'equipmentShop.basic': 'Základní',\n    'equipmentShop.automated': 'Automatizované',\n    'equipmentShop.advanced': 'Pokročilé',\n    'equipmentShop.efficiency': 'Efektivita: {{efficiency}}x',\n    'equipmentShop.automation': 'Automatizace:',\n    'equipmentShop.unlockLevel': 'Úroveň odemčení: {{level}}',\n    'equipmentShop.purchase': '💰 Koupit',\n    'equipmentShop.noEquipment': 'V této kategorii není k dispozici žádné vybavení.',\n    'equipmentShop.levelUpForEquipment': 'Postupte na vyšší úroveň pro odemčení dalšího vybavení!',\n    'equipmentShop.tips.title': '💡 Tipy pro vybavení',\n    'equipmentShop.tips.automated': '• Automatizované vybavení může běžet bez vašeho dohledu',\n    'equipmentShop.tips.efficiency': '• Vyšší efektivita znamená rychlejší výrobu a lepší kvalitu',\n    'equipmentShop.tips.conveyor': '• Dopravní pásy spojují vybavení pro bezproblémový pracovní tok',\n    'equipmentShop.tips.advanced': '• Pokročilé vybavení se odemyká na vyšších úrovních',\n\n    // Level Up Modal\n    'levelUp.levelRewards': '🎁 Odměny za úroveň',\n    'levelUp.whatsNext': '💡 Co dál?',\n    'levelUp.checkRecipes': '• Podívejte se na nové recepty ve své knize receptů',\n    'levelUp.visitShop': '• Navštivte obchod pro nové vybavení',\n    'levelUp.challengingOrders': '• Přijměte náročnější objednávky',\n    'levelUp.investSkills': '• Investujte do vylepšení dovedností',\n\n    // Settings Modal\n    'settings.title': '⚙️ Nastavení',\n    'settings.general': 'Obecné',\n    'settings.audio': 'Zvuk',\n    'settings.graphics': 'Grafika',\n    'settings.save': 'Uložení a data',\n    'settings.language': '🌍 Jazyk',\n    'settings.gameplay': '🎮 Hratelnost',\n    'settings.notifications': 'Povolit oznámení',\n    'settings.tutorials': 'Zobrazit návody',\n    'settings.animationSpeed': 'Rychlost animace',\n    'settings.sound': 'Zvukové efekty',\n    'settings.music': 'Hudba na pozadí',\n    'settings.quality': '🎨 Kvalita grafiky',\n    'settings.autoSave': '💾 Automatické ukládání',\n    'settings.enableAutoSave': 'Povolit automatické ukládání',\n    'settings.dataManagement': '📁 Správa dat',\n    'settings.exportSave': '📤 Exportovat uložení',\n    'settings.importSave': '📥 Importovat uložení',\n    'settings.cloudSync': '☁️ Cloudová synchronizace',\n    'settings.cloudSyncDescription': 'Cloudová synchronizace vám umožňuje uložit pokrok online a hrát na více zařízeních.',\n    'settings.comingSoon': 'Již brzy',\n\n    // Bakery Manager Modal\n    'bakeries.title': '🏪 Správce pekáren',\n    'bakeries.subtitle': 'Spravujte své pekárenské impérium',\n    'bakeries.owned': 'Moje pekárny',\n    'bakeries.available': 'Dostupné',\n    'bakeries.current': 'Aktuální',\n    'bakeries.level': 'Úroveň',\n    'bakeries.specialization': 'Specializace',\n    'bakeries.equipment': 'Vybavení',\n    'bakeries.orders': 'Aktivní objednávky',\n    'bakeries.switchTo': 'Přepnout na',\n    'bakeries.noOwned': 'Ještě nevlastníte žádné pekárny.',\n    'bakeries.purchase': '💰 Koupit',\n    'bakeries.tooExpensive': '💸 Příliš drahé',\n    'bakeries.allOwned': 'Vlastníte všechny dostupné pekárny!',\n    'bakeries.tips': '💡 Tipy pro pekárny',\n    'bakeries.tip1': 'Každá pekárna se specializuje na různé produkty pro bonusovou efektivitu',\n    'bakeries.tip2': 'Přepínejte mezi pekárnami pro správu více lokalit',\n    'bakeries.tip3': 'Specializované pekárny přitahují zákazníky hledající konkrétní položky',\n    'bakeries.tip4': 'Vylepšujte každou pekárnu nezávisle pro maximální zisk',\n\n    // Notifications\n    'notifications.orderAccepted': 'Objednávka přijata',\n    'notifications.orderAcceptedMessage': 'Přijali jste novou objednávku!',\n    'notifications.orderCompleted': 'Objednávka dokončena!',\n    'notifications.orderCompletedMessage': 'Získali jste {{reward}} Kč a zkušenosti!',\n    'notifications.orderDeclined': 'Objednávka odmítnuta',\n    'notifications.orderDeclinedMessage': 'Objednávka byla odstraněna z vaší fronty.',\n    'notifications.bakeryPurchased': 'Pekárna zakoupena!',\n    'notifications.bakeryPurchasedMessage': 'Nyní vlastníte {{name}}!',\n    'notifications.bakerySwitched': 'Pekárna přepnuta',\n    'notifications.bakerySwitchedMessage': 'Přepnuto na {{name}}',\n\n    // Common buttons and actions\n    'common.accept': 'Přijmout',\n    'common.decline': 'Odmítnout',\n    'common.complete': 'Dokončit',\n    'common.purchase': 'Koupit',\n    'common.upgrade': 'Vylepšit',\n    'common.cancel': 'Zrušit',\n    'common.confirm': 'Potvrdit',\n    'common.save': 'Uložit',\n    'common.load': 'Načíst',\n    'common.delete': 'Smazat',\n    'common.edit': 'Upravit',\n    'common.back': 'Zpět',\n    'common.next': 'Další',\n    'common.previous': 'Předchozí',\n    'common.yes': 'Ano',\n    'common.no': 'Ne',\n    'common.create': 'Vytvořit',\n    'common.join': 'Připojit se',\n    'common.leave': 'Odejít',\n    'common.start': 'Začít',\n    'common.ready': 'Připraven',\n    'common.notReady': 'Nepřipraven',\n    'common.send': 'Odeslat',\n    'common.refresh': 'Obnovit',\n    'common.retry': 'Zkusit znovu',\n    'common.reset': 'Resetovat',\n    'common.clear': 'Vymazat',\n    'common.apply': 'Použít',\n    'common.warning': 'Varování',\n    'common.info': 'Informace',\n    'common.success': 'Úspěch',\n    'common.error': 'Chyba',\n\n    // Save/Load System\n    'saveLoad.saveDesc': 'Vyberte slot pro uložení vašeho postupu',\n    'saveLoad.loadDesc': 'Vyberte soubor uložení k načtení',\n    'saveLoad.saveName': 'Název uložení',\n    'saveLoad.emptySlot': 'Prázdný slot',\n    'saveLoad.selectedSaveSlot': 'Vybrán: Slot {{slot}}',\n    'saveLoad.selectedLoadSlot': 'Vybrán: Slot {{slot}}',\n    'saveLoad.confirmOverwrite': 'Přepsat uložení?',\n    'saveLoad.overwriteWarning': 'Toto přepíše existující uložení. Tuto akci nelze vrátit zpět.',\n    'saveLoad.overwrite': 'Přepsat',\n    'saveLoad.fileSlots': 'Souborové sloty',\n    'saveLoad.gameSlots': 'Herní sloty',\n    'saveLoad.exportSave': 'Exportovat uložení',\n    'saveLoad.importSave': 'Importovat uložení',\n    'saveLoad.deleteConfirm': 'Smazat uložení?',\n    'saveLoad.deleteWarning': 'Toto trvale smaže tento soubor uložení. Tuto akci nelze vrátit zpět.',\n    'saveLoad.delete': 'Smazat',\n\n    // Game Menu\n    'gameMenu.title': 'Herní menu',\n    'gameMenu.subtitle': 'Spravujte svou hru',\n    'gameMenu.resume': 'Pokračovat ve hře',\n    'gameMenu.resumeDesc': 'Pokračovat v hraní',\n    'gameMenu.save': 'Uložit hru',\n    'gameMenu.saveDesc': 'Uložit váš postup',\n    'gameMenu.load': 'Načíst hru',\n    'gameMenu.loadDesc': 'Načíst uložený postup',\n    'gameMenu.settings': 'Nastavení',\n    'gameMenu.settingsDesc': 'Herní předvolby',\n    'gameMenu.mainMenu': 'Hlavní menu',\n    'gameMenu.mainMenuDesc': 'Návrat do hlavního menu',\n    'gameMenu.exit': 'Ukončit hru',\n    'gameMenu.exitDesc': 'Zavřít aplikaci',\n    'gameMenu.tip': 'Stiskněte ESC pro otevření tohoto menu kdykoli',\n\n    // Discord Rich Presence\n    'settings.discord': 'Discord',\n    'settings.discordRichPresence': 'Discord Rich Presence',\n    'settings.discordDescription': 'Zobrazit váš aktuální herní stav a aktivitu v Discordu.',\n    'settings.enableDiscordRPC': 'Povolit Discord Rich Presence',\n    'settings.discordConnected': '✅ Připojeno k Discordu',\n    'settings.discordDisconnected': '❌ Nepřipojeno k Discordu',\n    'settings.discordInfo': 'Co je Discord Rich Presence?',\n    'settings.discordInfoDesc1': 'Discord Rich Presence ukazuje vašim přátelům, co děláte v Bake It Out:',\n    'settings.discordFeature1': 'Vaši aktuální úroveň a peníze',\n    'settings.discordFeature2': 'Co právě pečete',\n    'settings.discordFeature3': 'Informace o multiplayer místnosti',\n    'settings.discordFeature4': 'Jak dlouho hrajete',\n    'settings.discordInfoDesc2': 'Vaši přátelé se mohou připojit k vašim multiplayer hrám přímo z Discordu!',\n    'settings.discordTroubleshooting': 'Discord není připojen',\n    'settings.discordTrouble1': 'Ujistěte se, že Discord běží na vašem počítači.',\n    'settings.discordTrouble2': 'Discord Rich Presence funguje pouze v desktopové verzi hry.',\n    'settings.discordTrouble3': 'Zkuste restartovat Discord i hru, pokud se připojení nezdaří.',\n    'settings.discordPrivacy': 'Informace o soukromí',\n    'settings.discordPrivacyDesc1': 'Discord Rich Presence sdílí pouze:',\n    'settings.discordPrivacy1': 'Vaši aktuální herní aktivitu (veřejné)',\n    'settings.discordPrivacy2': 'Vaši úroveň hráče a postup (veřejné)',\n    'settings.discordPrivacy3': 'Kódy multiplayer místností (pro připojení)',\n    'settings.discordPrivacyDesc2': 'Žádné osobní informace nebo uložená data nejsou sdílena s Discordem.',\n    'settings.discordStatus': 'Stav Discordu',\n    'settings.discordInitializing': '🔄 Inicializace Discord RPC...',\n    'settings.discordRetrying': '🔄 Opakování připojení...',\n    'settings.discordUnavailable': '❌ Discord není dostupný',\n    'settings.discordDesktopOnly': 'ℹ️ Discord RPC dostupný pouze v desktopové verzi',\n\n    // Error messages and status\n    'error.general': 'Došlo k chybě',\n    'error.saveLoad': 'Nepodařilo se uložit/načíst hru',\n    'error.connection': 'Chyba připojení',\n    'error.fileNotFound': 'Soubor nenalezen',\n    'error.invalidData': 'Neplatný formát dat',\n    'error.permissionDenied': 'Přístup odepřen',\n    'status.loading': 'Načítání...',\n    'status.saving': 'Ukládání...',\n    'status.connecting': 'Připojování...',\n    'status.ready': 'Připraven',\n    'status.success': 'Úspěch!',\n    'status.failed': 'Neúspěšné',\n    'status.offline': 'Offline',\n    'status.online': 'Online',\n\n    // UI Elements\n    'ui.placeholder': 'Zadejte text...',\n    'ui.search': 'Hledat',\n    'ui.filter': 'Filtrovat',\n    'ui.sort': 'Seřadit',\n    'ui.ascending': 'Vzestupně',\n    'ui.descending': 'Sestupně',\n    'ui.selectAll': 'Vybrat vše',\n    'ui.deselectAll': 'Zrušit výběr',\n    'ui.noResults': 'Žádné výsledky',\n    'ui.noData': 'Žádná data k dispozici',\n    'ui.loading': 'Načítání...',\n    'ui.saving': 'Ukládání...',\n    'ui.saved': 'Uloženo!',\n    'ui.failed': 'Neúspěšné',\n    'ui.retry': 'Zkusit znovu',\n    'ui.back': 'Zpět',\n    'ui.forward': 'Vpřed',\n    'ui.home': 'Domů',\n    'ui.menu': 'Menu',\n    'ui.options': 'Možnosti',\n    'ui.preferences': 'Předvolby',\n\n    // Multiplayer\n    'multiplayer.lobby': '👥 Multiplayerová lobby',\n    'multiplayer.connected': '🟢 Připojeno',\n    'multiplayer.disconnected': '🔴 Odpojeno',\n    'multiplayer.createRoom': 'Vytvořit místnost',\n    'multiplayer.joinRoom': 'Připojit se k místnosti',\n    'multiplayer.room': 'Místnost',\n    'multiplayer.yourName': 'Vaše jméno',\n    'multiplayer.enterName': 'Zadejte své jméno',\n    'multiplayer.roomName': 'Název místnosti',\n    'multiplayer.enterRoomName': 'Zadejte název místnosti',\n    'multiplayer.gameMode': 'Herní režim',\n    'multiplayer.cooperative': '🤝 Kooperativní',\n    'multiplayer.competitive': '⚔️ Soutěžní',\n    'multiplayer.maxPlayers': 'Max hráčů: {{count}}',\n    'multiplayer.roomId': 'ID místnosti',\n    'multiplayer.enterRoomId': 'Zadejte ID místnosti',\n    'multiplayer.players': 'Hráči ({{count}})',\n    'multiplayer.host': 'HOSTITEL',\n    'multiplayer.level': 'Úroveň {{level}}',\n    'multiplayer.chat': 'Chat',\n    'multiplayer.typeMessage': 'Napište zprávu...',\n    'multiplayer.gameTime': 'Herní čas: {{time}}',\n    'multiplayer.teamStats': '📊 Týmové statistiky',\n    'multiplayer.ordersCompleted': 'Dokončené objednávky:',\n    'multiplayer.totalRevenue': 'Celkový příjem:',\n    'multiplayer.teamExperience': 'Týmové zkušenosti:',\n    'multiplayer.sharedKitchen': '🏪 Sdílená kuchyně',\n    'multiplayer.sharedOrders': '📋 Sdílené objednávky',\n    'multiplayer.sharedInventory': '📦 Sdílený sklad',\n    'multiplayer.contribution': 'Příspěvek:',\n    'multiplayer.online': '🟢 Online',\n    'multiplayer.status': 'Stav:',\n    'multiplayer.you': '(Vy)',\n    'multiplayer.teamChat': '💬 Týmový chat',\n    'multiplayer.chatPlaceholder': 'Zde se zobrazí zprávy chatu...',\n\n    // Multiplayer game modes\n    'multiplayer.mode.cooperative.description': '🤝 Kooperativní režim: Spolupracujte na dokončování objednávek a rozvoji sdílené pekárny!',\n    'multiplayer.mode.competitive.description': '⚔️ Soutěžní režim: Soutěžte s ostatními hráči o dokončení nejvíce objednávek!',\n\n    // Multiplayer game interface\n    'multiplayer.game.title': '🎮 Multiplayerová hra - {{roomName}}',\n    'multiplayer.game.mode': 'Režim: {{mode}}',\n    'multiplayer.game.playersCount': 'Hráči: {{count}}',\n    'multiplayer.game.playing': '🟢 Hraje se',\n    'multiplayer.game.leaveGame': '🚪 Opustit hru',\n    'multiplayer.game.tabs.game': 'Hra',\n    'multiplayer.game.tabs.players': 'Hráči',\n    'multiplayer.game.tabs.chat': 'Chat',\n\n    // Room creation and joining\n    'multiplayer.create.title': '🏗️ Vytvořit místnost',\n    'multiplayer.join.title': '🚪 Připojit se k místnosti',\n    'multiplayer.room.info': 'Režim: {{mode}} • Hráči: {{current}}/{{max}}',\n    'multiplayer.room.readyUp': '✅ Připraven',\n    'multiplayer.room.notReady': '⏳ Nepřipraven',\n    'multiplayer.room.startGame': '🚀 Začít hru',\n    'multiplayer.room.leaveRoom': '🚪 Opustit',\n\n    // Connection states\n    'multiplayer.connection.connecting': 'Připojování...',\n    'multiplayer.connection.reconnecting': 'Znovu se připojuje...',\n    'multiplayer.connection.failed': 'Připojení selhalo',\n    'multiplayer.connection.error': '⚠️ {{error}}',\n\n    // System messages\n    'multiplayer.system.playerJoined': '{{name}} se připojil do místnosti',\n    'multiplayer.system.playerLeft': '{{name}} opustil místnost',\n    'multiplayer.system.gameStarted': 'Hra začala!',\n    'multiplayer.system.gameEnded': 'Hra skončila!',\n    'multiplayer.system.roomCreated': 'Místnost byla úspěšně vytvořena',\n    'multiplayer.system.roomJoined': 'Úspěšně jste se připojili do místnosti'\n  }\n}\n\nexport function LanguageProvider({ children }: { children: React.ReactNode }) {\n  const [language, setLanguage] = useState<Language>('en')\n  const [mounted, setMounted] = useState(false)\n\n  useEffect(() => {\n    // Mark as mounted to prevent hydration mismatch\n    setMounted(true)\n  }, [])\n\n  useEffect(() => {\n    // Only access localStorage after component is mounted on client side\n    if (!mounted) return\n\n    const savedLanguage = localStorage.getItem('language') as Language\n    if (savedLanguage && (savedLanguage === 'en' || savedLanguage === 'cs')) {\n      setLanguage(savedLanguage)\n    }\n  }, [mounted])\n\n  const handleSetLanguage = (lang: Language) => {\n    setLanguage(lang)\n    // Only save to localStorage if we're on the client side\n    if (mounted && typeof window !== 'undefined') {\n      localStorage.setItem('language', lang)\n    }\n  }\n\n  const t = (key: string, params?: Record<string, string>) => {\n    let translation = translations[language][key as keyof typeof translations[typeof language]] || key\n    \n    if (params) {\n      Object.entries(params).forEach(([param, value]) => {\n        translation = translation.replace(`{{${param}}}`, value)\n      })\n    }\n    \n    return translation\n  }\n\n  return (\n    <LanguageContext.Provider value={{ language, setLanguage: handleSetLanguage, t }}>\n      {children}\n    </LanguageContext.Provider>\n  )\n}\n\nexport function useLanguage() {\n  const context = useContext(LanguageContext)\n  if (context === undefined) {\n    // Fallback for when context is not available\n    console.warn('useLanguage called outside of LanguageProvider, using fallback')\n    return {\n      language: 'en' as Language,\n      setLanguage: () => {},\n      t: (key: string) => key\n    }\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAYA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAmC;AAEvE,oCAAoC;AACpC,MAAM,eAAe;IACnB,IAAI;QACF,YAAY;QACZ,cAAc;QACd,iBAAiB;QACjB,aAAa;QACb,qBAAqB;QACrB,yBAAyB;QACzB,oBAAoB;QACpB,wBAAwB;QACxB,gBAAgB;QAChB,cAAc;QACd,aAAa;QACb,cAAc;QACd,iBAAiB;QAEjB,eAAe;QACf,qBAAqB;QACrB,oBAAoB;QACpB,iBAAiB;QACjB,gBAAgB;QAChB,aAAa;QACb,gBAAgB;QAChB,qBAAqB;QACrB,iBAAiB;QACjB,uBAAuB;QACvB,cAAc;QACd,aAAa;QACb,aAAa;QAEb,WAAW;QACX,yBAAyB;QACzB,+BAA+B;QAC/B,0BAA0B;QAC1B,gCAAgC;QAChC,8BAA8B;QAC9B,oCAAoC;QACpC,sBAAsB;QAEtB,iBAAiB;QACjB,YAAY;QACZ,YAAY;QACZ,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,aAAa;QACb,iBAAiB;QAEjB,UAAU;QACV,iBAAiB;QACjB,sBAAsB;QACtB,kBAAkB;QAClB,yBAAyB;QAEzB,YAAY;QACZ,mBAAmB;QACnB,sBAAsB;QACtB,kBAAkB;QAElB,SAAS;QACT,gBAAgB;QAChB,mBAAmB;QACnB,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,qBAAqB;QACrB,oBAAoB;QACpB,iBAAiB;QACjB,mBAAmB;QAEnB,gBAAgB;QAChB,iBAAiB;QACjB,0BAA0B;QAC1B,uBAAuB;QACvB,yBAAyB;QAEzB,SAAS;QACT,uBAAuB;QACvB,oBAAoB;QACpB,sBAAsB;QACtB,4BAA4B;QAC5B,sBAAsB;QACtB,0BAA0B;QAC1B,6BAA6B;QAC7B,wBAAwB;QACxB,wBAAwB;QACxB,uBAAuB;QACvB,0BAA0B;QAE1B,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,iBAAiB;QACjB,iBAAiB;QACjB,oBAAoB;QACpB,uBAAuB;QACvB,sBAAsB;QACtB,gBAAgB;QAChB,oBAAoB;QACpB,uBAAuB;QACvB,qBAAqB;QACrB,2BAA2B;QAE3B,aAAa;QACb,qBAAqB;QACrB,YAAY;QACZ,qBAAqB;QACrB,mBAAmB;QACnB,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,oBAAoB;QAEpB,eAAe;QACf,uBAAuB;QACvB,oBAAoB;QACpB,wBAAwB;QACxB,yBAAyB;QACzB,sBAAsB;QACtB,uBAAuB;QACvB,yBAAyB;QACzB,oBAAoB;QACpB,qBAAqB;QACrB,oBAAoB;QACpB,oBAAoB;QACpB,wBAAwB;QACxB,yBAAyB;QAEzB,qBAAqB;QACrB,0BAA0B;QAC1B,gCAAgC;QAChC,yBAAyB;QACzB,uBAAuB;QACvB,+BAA+B;QAE/B,eAAe;QACf,0BAA0B;QAC1B,qBAAqB;QACrB,qBAAqB;QACrB,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,mBAAmB;QACnB,wBAAwB;QACxB,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QACjB,mBAAmB;QACnB,qBAAqB;QACrB,0BAA0B;QAC1B,6BAA6B;QAC7B,yBAAyB;QACzB,0BAA0B;QAE1B,mBAAmB;QACnB,4BAA4B;QAC5B,+BAA+B;QAC/B,wBAAwB;QACxB,2BAA2B;QAC3B,yBAAyB;QACzB,qBAAqB;QACrB,oBAAoB;QACpB,kCAAkC;QAClC,0BAA0B;QAC1B,sBAAsB;QACtB,qBAAqB;QACrB,0BAA0B;QAC1B,uBAAuB;QACvB,kCAAkC;QAClC,uBAAuB;QACvB,yBAAyB;QACzB,iCAAiC;QACjC,iCAAiC;QACjC,6BAA6B;QAC7B,+BAA+B;QAC/B,8BAA8B;QAC9B,sBAAsB;QACtB,mBAAmB;QACnB,mCAAmC;QACnC,oCAAoC;QAEpC,uBAAuB;QACvB,mCAAmC;QACnC,uBAAuB;QACvB,2BAA2B;QAC3B,0BAA0B;QAC1B,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,0BAA0B;QAC1B,6BAA6B;QAC7B,qCAAqC;QACrC,4BAA4B;QAC5B,gCAAgC;QAChC,iCAAiC;QACjC,+BAA+B;QAC/B,+BAA+B;QAE/B,iBAAiB;QACjB,wBAAwB;QACxB,qBAAqB;QACrB,wBAAwB;QACxB,qBAAqB;QACrB,6BAA6B;QAC7B,wBAAwB;QAExB,iBAAiB;QACjB,kBAAkB;QAClB,oBAAoB;QACpB,kBAAkB;QAClB,qBAAqB;QACrB,iBAAiB;QACjB,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,sBAAsB;QACtB,2BAA2B;QAC3B,kBAAkB;QAClB,kBAAkB;QAClB,oBAAoB;QACpB,qBAAqB;QACrB,2BAA2B;QAC3B,2BAA2B;QAC3B,uBAAuB;QACvB,uBAAuB;QACvB,sBAAsB;QACtB,iCAAiC;QACjC,uBAAuB;QAEvB,uBAAuB;QACvB,kBAAkB;QAClB,qBAAqB;QACrB,kBAAkB;QAClB,sBAAsB;QACtB,oBAAoB;QACpB,kBAAkB;QAClB,2BAA2B;QAC3B,sBAAsB;QACtB,mBAAmB;QACnB,qBAAqB;QACrB,oBAAoB;QACpB,qBAAqB;QACrB,yBAAyB;QACzB,qBAAqB;QACrB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QAEjB,gBAAgB;QAChB,+BAA+B;QAC/B,sCAAsC;QACtC,gCAAgC;QAChC,uCAAuC;QACvC,+BAA+B;QAC/B,sCAAsC;QACtC,iCAAiC;QACjC,wCAAwC;QACxC,gCAAgC;QAChC,uCAAuC;QAEvC,6BAA6B;QAC7B,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,kBAAkB;QAClB,eAAe;QACf,eAAe;QACf,iBAAiB;QACjB,eAAe;QACf,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,cAAc;QACd,aAAa;QACb,iBAAiB;QACjB,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAChB,mBAAmB;QACnB,eAAe;QACf,kBAAkB;QAClB,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;QAClB,eAAe;QACf,kBAAkB;QAClB,gBAAgB;QAEhB,mBAAmB;QACnB,qBAAqB;QACrB,qBAAqB;QACrB,qBAAqB;QACrB,sBAAsB;QACtB,6BAA6B;QAC7B,6BAA6B;QAC7B,6BAA6B;QAC7B,6BAA6B;QAC7B,sBAAsB;QACtB,sBAAsB;QACtB,sBAAsB;QACtB,uBAAuB;QACvB,uBAAuB;QACvB,0BAA0B;QAC1B,0BAA0B;QAC1B,mBAAmB;QAEnB,YAAY;QACZ,kBAAkB;QAClB,qBAAqB;QACrB,mBAAmB;QACnB,uBAAuB;QACvB,iBAAiB;QACjB,qBAAqB;QACrB,iBAAiB;QACjB,qBAAqB;QACrB,qBAAqB;QACrB,yBAAyB;QACzB,qBAAqB;QACrB,yBAAyB;QACzB,iBAAiB;QACjB,qBAAqB;QACrB,gBAAgB;QAEhB,wBAAwB;QACxB,oBAAoB;QACpB,gCAAgC;QAChC,+BAA+B;QAC/B,6BAA6B;QAC7B,6BAA6B;QAC7B,gCAAgC;QAChC,wBAAwB;QACxB,6BAA6B;QAC7B,4BAA4B;QAC5B,4BAA4B;QAC5B,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,mCAAmC;QACnC,4BAA4B;QAC5B,4BAA4B;QAC5B,4BAA4B;QAC5B,2BAA2B;QAC3B,gCAAgC;QAChC,4BAA4B;QAC5B,4BAA4B;QAC5B,4BAA4B;QAC5B,gCAAgC;QAChC,0BAA0B;QAC1B,gCAAgC;QAChC,4BAA4B;QAC5B,+BAA+B;QAC/B,+BAA+B;QAE/B,4BAA4B;QAC5B,iBAAiB;QACjB,kBAAkB;QAClB,oBAAoB;QACpB,sBAAsB;QACtB,qBAAqB;QACrB,0BAA0B;QAC1B,kBAAkB;QAClB,iBAAiB;QACjB,qBAAqB;QACrB,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QACjB,kBAAkB;QAClB,iBAAiB;QAEjB,cAAc;QACd,kBAAkB;QAClB,aAAa;QACb,aAAa;QACb,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QACjB,gBAAgB;QAChB,kBAAkB;QAClB,gBAAgB;QAChB,aAAa;QACb,cAAc;QACd,aAAa;QACb,YAAY;QACZ,aAAa;QACb,YAAY;QACZ,WAAW;QACX,cAAc;QACd,WAAW;QACX,WAAW;QACX,cAAc;QACd,kBAAkB;QAElB,cAAc;QACd,qBAAqB;QACrB,yBAAyB;QACzB,4BAA4B;QAC5B,0BAA0B;QAC1B,wBAAwB;QACxB,oBAAoB;QACpB,wBAAwB;QACxB,yBAAyB;QACzB,wBAAwB;QACxB,6BAA6B;QAC7B,wBAAwB;QACxB,2BAA2B;QAC3B,2BAA2B;QAC3B,0BAA0B;QAC1B,sBAAsB;QACtB,2BAA2B;QAC3B,uBAAuB;QACvB,oBAAoB;QACpB,qBAAqB;QACrB,oBAAoB;QACpB,2BAA2B;QAC3B,wBAAwB;QACxB,yBAAyB;QACzB,+BAA+B;QAC/B,4BAA4B;QAC5B,8BAA8B;QAC9B,6BAA6B;QAC7B,4BAA4B;QAC5B,+BAA+B;QAC/B,4BAA4B;QAC5B,sBAAsB;QACtB,sBAAsB;QACtB,mBAAmB;QACnB,wBAAwB;QACxB,+BAA+B;QAE/B,yBAAyB;QACzB,4CAA4C;QAC5C,4CAA4C;QAE5C,6BAA6B;QAC7B,0BAA0B;QAC1B,yBAAyB;QACzB,iCAAiC;QACjC,4BAA4B;QAC5B,8BAA8B;QAC9B,8BAA8B;QAC9B,iCAAiC;QACjC,8BAA8B;QAE9B,4BAA4B;QAC5B,4BAA4B;QAC5B,0BAA0B;QAC1B,yBAAyB;QACzB,4BAA4B;QAC5B,6BAA6B;QAC7B,8BAA8B;QAC9B,8BAA8B;QAE9B,oBAAoB;QACpB,qCAAqC;QACrC,uCAAuC;QACvC,iCAAiC;QACjC,gCAAgC;QAEhC,kBAAkB;QAClB,mCAAmC;QACnC,iCAAiC;QACjC,kCAAkC;QAClC,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;IACnC;IACA,IAAI;QACF,YAAY;QACZ,cAAc;QACd,iBAAiB;QACjB,aAAa;QACb,qBAAqB;QACrB,yBAAyB;QACzB,oBAAoB;QACpB,wBAAwB;QACxB,gBAAgB;QAChB,cAAc;QACd,aAAa;QACb,cAAc;QACd,iBAAiB;QAEjB,eAAe;QACf,qBAAqB;QACrB,oBAAoB;QACpB,iBAAiB;QACjB,gBAAgB;QAChB,aAAa;QACb,gBAAgB;QAChB,qBAAqB;QACrB,iBAAiB;QACjB,uBAAuB;QACvB,cAAc;QACd,aAAa;QACb,aAAa;QAEb,WAAW;QACX,yBAAyB;QACzB,+BAA+B;QAC/B,0BAA0B;QAC1B,gCAAgC;QAChC,8BAA8B;QAC9B,oCAAoC;QACpC,sBAAsB;QAEtB,iBAAiB;QACjB,YAAY;QACZ,YAAY;QACZ,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,aAAa;QACb,iBAAiB;QAEjB,UAAU;QACV,iBAAiB;QACjB,sBAAsB;QACtB,kBAAkB;QAClB,yBAAyB;QAEzB,YAAY;QACZ,mBAAmB;QACnB,sBAAsB;QACtB,kBAAkB;QAElB,SAAS;QACT,gBAAgB;QAChB,mBAAmB;QACnB,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,qBAAqB;QACrB,oBAAoB;QACpB,iBAAiB;QACjB,mBAAmB;QAEnB,gBAAgB;QAChB,iBAAiB;QACjB,0BAA0B;QAC1B,uBAAuB;QACvB,yBAAyB;QAEzB,SAAS;QACT,uBAAuB;QACvB,oBAAoB;QACpB,sBAAsB;QACtB,4BAA4B;QAC5B,sBAAsB;QACtB,0BAA0B;QAC1B,6BAA6B;QAC7B,wBAAwB;QACxB,wBAAwB;QACxB,uBAAuB;QACvB,0BAA0B;QAE1B,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,iBAAiB;QACjB,iBAAiB;QACjB,oBAAoB;QACpB,uBAAuB;QACvB,sBAAsB;QACtB,gBAAgB;QAChB,oBAAoB;QACpB,uBAAuB;QACvB,qBAAqB;QACrB,2BAA2B;QAE3B,aAAa;QACb,qBAAqB;QACrB,YAAY;QACZ,qBAAqB;QACrB,mBAAmB;QACnB,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,oBAAoB;QAEpB,eAAe;QACf,uBAAuB;QACvB,oBAAoB;QACpB,wBAAwB;QACxB,yBAAyB;QACzB,sBAAsB;QACtB,uBAAuB;QACvB,yBAAyB;QACzB,oBAAoB;QACpB,qBAAqB;QACrB,oBAAoB;QACpB,oBAAoB;QACpB,wBAAwB;QACxB,yBAAyB;QAEzB,qBAAqB;QACrB,0BAA0B;QAC1B,gCAAgC;QAChC,yBAAyB;QACzB,uBAAuB;QACvB,+BAA+B;QAE/B,eAAe;QACf,0BAA0B;QAC1B,qBAAqB;QACrB,qBAAqB;QACrB,kBAAkB;QAClB,mBAAmB;QACnB,kBAAkB;QAClB,mBAAmB;QACnB,wBAAwB;QACxB,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QACjB,mBAAmB;QACnB,qBAAqB;QACrB,0BAA0B;QAC1B,6BAA6B;QAC7B,yBAAyB;QACzB,0BAA0B;QAE1B,mBAAmB;QACnB,4BAA4B;QAC5B,+BAA+B;QAC/B,wBAAwB;QACxB,2BAA2B;QAC3B,yBAAyB;QACzB,qBAAqB;QACrB,oBAAoB;QACpB,kCAAkC;QAClC,0BAA0B;QAC1B,sBAAsB;QACtB,qBAAqB;QACrB,0BAA0B;QAC1B,uBAAuB;QACvB,kCAAkC;QAClC,uBAAuB;QACvB,yBAAyB;QACzB,iCAAiC;QACjC,iCAAiC;QACjC,6BAA6B;QAC7B,+BAA+B;QAC/B,8BAA8B;QAC9B,sBAAsB;QACtB,mBAAmB;QACnB,mCAAmC;QACnC,oCAAoC;QAEpC,uBAAuB;QACvB,mCAAmC;QACnC,uBAAuB;QACvB,2BAA2B;QAC3B,0BAA0B;QAC1B,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,0BAA0B;QAC1B,6BAA6B;QAC7B,qCAAqC;QACrC,4BAA4B;QAC5B,gCAAgC;QAChC,iCAAiC;QACjC,+BAA+B;QAC/B,+BAA+B;QAE/B,iBAAiB;QACjB,wBAAwB;QACxB,qBAAqB;QACrB,wBAAwB;QACxB,qBAAqB;QACrB,6BAA6B;QAC7B,wBAAwB;QAExB,iBAAiB;QACjB,kBAAkB;QAClB,oBAAoB;QACpB,kBAAkB;QAClB,qBAAqB;QACrB,iBAAiB;QACjB,qBAAqB;QACrB,qBAAqB;QACrB,0BAA0B;QAC1B,sBAAsB;QACtB,2BAA2B;QAC3B,kBAAkB;QAClB,kBAAkB;QAClB,oBAAoB;QACpB,qBAAqB;QACrB,2BAA2B;QAC3B,2BAA2B;QAC3B,uBAAuB;QACvB,uBAAuB;QACvB,sBAAsB;QACtB,iCAAiC;QACjC,uBAAuB;QAEvB,uBAAuB;QACvB,kBAAkB;QAClB,qBAAqB;QACrB,kBAAkB;QAClB,sBAAsB;QACtB,oBAAoB;QACpB,kBAAkB;QAClB,2BAA2B;QAC3B,sBAAsB;QACtB,mBAAmB;QACnB,qBAAqB;QACrB,oBAAoB;QACpB,qBAAqB;QACrB,yBAAyB;QACzB,qBAAqB;QACrB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QAEjB,gBAAgB;QAChB,+BAA+B;QAC/B,sCAAsC;QACtC,gCAAgC;QAChC,uCAAuC;QACvC,+BAA+B;QAC/B,sCAAsC;QACtC,iCAAiC;QACjC,wCAAwC;QACxC,gCAAgC;QAChC,uCAAuC;QAEvC,6BAA6B;QAC7B,iBAAiB;QACjB,kBAAkB;QAClB,mBAAmB;QACnB,mBAAmB;QACnB,kBAAkB;QAClB,iBAAiB;QACjB,kBAAkB;QAClB,eAAe;QACf,eAAe;QACf,iBAAiB;QACjB,eAAe;QACf,eAAe;QACf,eAAe;QACf,mBAAmB;QACnB,cAAc;QACd,aAAa;QACb,iBAAiB;QACjB,eAAe;QACf,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAChB,mBAAmB;QACnB,eAAe;QACf,kBAAkB;QAClB,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAChB,gBAAgB;QAChB,kBAAkB;QAClB,eAAe;QACf,kBAAkB;QAClB,gBAAgB;QAEhB,mBAAmB;QACnB,qBAAqB;QACrB,qBAAqB;QACrB,qBAAqB;QACrB,sBAAsB;QACtB,6BAA6B;QAC7B,6BAA6B;QAC7B,6BAA6B;QAC7B,6BAA6B;QAC7B,sBAAsB;QACtB,sBAAsB;QACtB,sBAAsB;QACtB,uBAAuB;QACvB,uBAAuB;QACvB,0BAA0B;QAC1B,0BAA0B;QAC1B,mBAAmB;QAEnB,YAAY;QACZ,kBAAkB;QAClB,qBAAqB;QACrB,mBAAmB;QACnB,uBAAuB;QACvB,iBAAiB;QACjB,qBAAqB;QACrB,iBAAiB;QACjB,qBAAqB;QACrB,qBAAqB;QACrB,yBAAyB;QACzB,qBAAqB;QACrB,yBAAyB;QACzB,iBAAiB;QACjB,qBAAqB;QACrB,gBAAgB;QAEhB,wBAAwB;QACxB,oBAAoB;QACpB,gCAAgC;QAChC,+BAA+B;QAC/B,6BAA6B;QAC7B,6BAA6B;QAC7B,gCAAgC;QAChC,wBAAwB;QACxB,6BAA6B;QAC7B,4BAA4B;QAC5B,4BAA4B;QAC5B,4BAA4B;QAC5B,4BAA4B;QAC5B,6BAA6B;QAC7B,mCAAmC;QACnC,4BAA4B;QAC5B,4BAA4B;QAC5B,4BAA4B;QAC5B,2BAA2B;QAC3B,gCAAgC;QAChC,4BAA4B;QAC5B,4BAA4B;QAC5B,4BAA4B;QAC5B,gCAAgC;QAChC,0BAA0B;QAC1B,gCAAgC;QAChC,4BAA4B;QAC5B,+BAA+B;QAC/B,+BAA+B;QAE/B,4BAA4B;QAC5B,iBAAiB;QACjB,kBAAkB;QAClB,oBAAoB;QACpB,sBAAsB;QACtB,qBAAqB;QACrB,0BAA0B;QAC1B,kBAAkB;QAClB,iBAAiB;QACjB,qBAAqB;QACrB,gBAAgB;QAChB,kBAAkB;QAClB,iBAAiB;QACjB,kBAAkB;QAClB,iBAAiB;QAEjB,cAAc;QACd,kBAAkB;QAClB,aAAa;QACb,aAAa;QACb,WAAW;QACX,gBAAgB;QAChB,iBAAiB;QACjB,gBAAgB;QAChB,kBAAkB;QAClB,gBAAgB;QAChB,aAAa;QACb,cAAc;QACd,aAAa;QACb,YAAY;QACZ,aAAa;QACb,YAAY;QACZ,WAAW;QACX,cAAc;QACd,WAAW;QACX,WAAW;QACX,cAAc;QACd,kBAAkB;QAElB,cAAc;QACd,qBAAqB;QACrB,yBAAyB;QACzB,4BAA4B;QAC5B,0BAA0B;QAC1B,wBAAwB;QACxB,oBAAoB;QACpB,wBAAwB;QACxB,yBAAyB;QACzB,wBAAwB;QACxB,6BAA6B;QAC7B,wBAAwB;QACxB,2BAA2B;QAC3B,2BAA2B;QAC3B,0BAA0B;QAC1B,sBAAsB;QACtB,2BAA2B;QAC3B,uBAAuB;QACvB,oBAAoB;QACpB,qBAAqB;QACrB,oBAAoB;QACpB,2BAA2B;QAC3B,wBAAwB;QACxB,yBAAyB;QACzB,+BAA+B;QAC/B,4BAA4B;QAC5B,8BAA8B;QAC9B,6BAA6B;QAC7B,4BAA4B;QAC5B,+BAA+B;QAC/B,4BAA4B;QAC5B,sBAAsB;QACtB,sBAAsB;QACtB,mBAAmB;QACnB,wBAAwB;QACxB,+BAA+B;QAE/B,yBAAyB;QACzB,4CAA4C;QAC5C,4CAA4C;QAE5C,6BAA6B;QAC7B,0BAA0B;QAC1B,yBAAyB;QACzB,iCAAiC;QACjC,4BAA4B;QAC5B,8BAA8B;QAC9B,8BAA8B;QAC9B,iCAAiC;QACjC,8BAA8B;QAE9B,4BAA4B;QAC5B,4BAA4B;QAC5B,0BAA0B;QAC1B,yBAAyB;QACzB,4BAA4B;QAC5B,6BAA6B;QAC7B,8BAA8B;QAC9B,8BAA8B;QAE9B,oBAAoB;QACpB,qCAAqC;QACrC,uCAAuC;QACvC,iCAAiC;QACjC,gCAAgC;QAEhC,kBAAkB;QAClB,mCAAmC;QACnC,iCAAiC;QACjC,kCAAkC;QAClC,gCAAgC;QAChC,kCAAkC;QAClC,iCAAiC;IACnC;AACF;AAEO,SAAS,iBAAiB,EAAE,QAAQ,EAAiC;IAC1E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gDAAgD;QAChD,WAAW;IACb,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qEAAqE;QACrE,IAAI,CAAC,SAAS;QAEd,MAAM,gBAAgB,aAAa,OAAO,CAAC;QAC3C,IAAI,iBAAiB,CAAC,kBAAkB,QAAQ,kBAAkB,IAAI,GAAG;YACvE,YAAY;QACd;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,oBAAoB,CAAC;QACzB,YAAY;QACZ,wDAAwD;QACxD;;IAGF;IAEA,MAAM,IAAI,CAAC,KAAa;QACtB,IAAI,cAAc,YAAY,CAAC,SAAS,CAAC,IAAkD,IAAI;QAE/F,IAAI,QAAQ;YACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,OAAO,MAAM;gBAC5C,cAAc,YAAY,OAAO,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,EAAE;YACpD;QACF;QAEA,OAAO;IACT;IAEA,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;YAAU,aAAa;YAAmB;QAAE;kBAC5E;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,6CAA6C;QAC7C,QAAQ,IAAI,CAAC;QACb,OAAO;YACL,UAAU;YACV,aAAa,KAAO;YACpB,GAAG,CAAC,MAAgB;QACtB;IACF;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1092, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/lib/socket.ts"], "sourcesContent": ["// Socket.IO client setup for multiplayer functionality\n\nimport { io, Socket } from 'socket.io-client'\n\nexport interface Player {\n  id: string\n  name: string\n  avatar: string\n  level: number\n  isHost: boolean\n  isReady: boolean\n  bakeryId?: string\n}\n\nexport interface GameRoom {\n  id: string\n  name: string\n  mode: 'cooperative' | 'competitive' | 'sandbox'\n  maxPlayers: number\n  currentPlayers: number\n  players: Player[]\n  gameState: 'waiting' | 'starting' | 'playing' | 'paused' | 'finished'\n  settings: {\n    gameMode: 'cooperative' | 'competitive'\n    timeLimit?: number\n    targetScore?: number\n    difficulty: 'easy' | 'medium' | 'hard'\n    allowSpectators: boolean\n  }\n  createdAt: number\n  startedAt?: number\n}\n\nexport interface MultiplayerGameState {\n  roomId: string\n  players: Record<string, {\n    playerId: string\n    name: string\n    level: number\n    money: number\n    experience: number\n    skillPoints: number\n    bakeryId: string\n    isOnline: boolean\n    lastActivity: number\n  }>\n  sharedResources: {\n    inventory: Record<string, number>\n    orders: any[]\n    equipment: any[]\n    automationJobs: any[]\n  }\n  gameStats: {\n    totalOrders: number\n    totalRevenue: number\n    totalExperience: number\n    gameStartTime: number\n    gameEndTime?: number\n  }\n  events: {\n    id: string\n    type: 'order_completed' | 'equipment_purchased' | 'player_joined' | 'player_left' | 'game_started' | 'game_ended'\n    playerId?: string\n    data: any\n    timestamp: number\n  }[]\n}\n\nexport interface SocketEvents {\n  // Room management\n  'create_room': (roomData: Partial<GameRoom>) => void\n  'join_room': (roomId: string, playerData: Partial<Player>) => void\n  'leave_room': (roomId: string) => void\n  'room_created': (room: GameRoom) => void\n  'room_joined': (room: GameRoom, player: Player) => void\n  'room_left': (roomId: string) => void\n  'room_updated': (room: GameRoom) => void\n  'player_joined': (player: Player) => void\n  'player_left': (playerId: string) => void\n  'player_updated': (player: Player) => void\n  \n  // Game state synchronization\n  'game_state_update': (gameState: Partial<MultiplayerGameState>) => void\n  'game_state_sync': (gameState: MultiplayerGameState) => void\n  'player_action': (action: {\n    type: string\n    playerId: string\n    data: any\n    timestamp: number\n  }) => void\n  \n  // Game events\n  'game_start': (roomId: string) => void\n  'game_pause': (roomId: string) => void\n  'game_resume': (roomId: string) => void\n  'game_end': (roomId: string, results: any) => void\n  'game_started': (gameState: MultiplayerGameState) => void\n  'game_paused': () => void\n  'game_resumed': () => void\n  'game_ended': (results: any) => void\n  \n  // Chat and communication\n  'send_message': (message: {\n    playerId: string\n    playerName: string\n    content: string\n    timestamp: number\n  }) => void\n  'message_received': (message: {\n    playerId: string\n    playerName: string\n    content: string\n    timestamp: number\n  }) => void\n  \n  // Error handling\n  'error': (error: {\n    code: string\n    message: string\n    details?: any\n  }) => void\n  'disconnect': () => void\n  'reconnect': () => void\n}\n\nclass SocketManager {\n  private socket: Socket | null = null\n  private isConnected = false\n  private reconnectAttempts = 0\n  private maxReconnectAttempts = 5\n  private currentRoom: GameRoom | null = null\n  private currentPlayer: Player | null = null\n\n  constructor() {\n    // Don't auto-connect to prevent errors when server isn't running\n    // Connection will be initiated when multiplayer features are used\n  }\n\n  connect() {\n    if (this.socket?.connected) return\n\n    try {\n      this.socket = io(process.env.NEXT_PUBLIC_SOCKET_URL || 'http://localhost:3001', {\n        transports: ['websocket', 'polling'],\n        timeout: 10000,\n        forceNew: true,\n        autoConnect: false // Don't auto-connect to prevent immediate errors\n      })\n\n      this.setupEventListeners()\n      this.socket.connect() // Manually connect after setting up listeners\n    } catch (error) {\n      console.warn('Failed to initialize socket connection:', error)\n    }\n  }\n\n  private setupEventListeners() {\n    if (!this.socket) return\n\n    this.socket.on('connect', () => {\n      console.log('Connected to multiplayer server')\n      this.isConnected = true\n      this.reconnectAttempts = 0\n    })\n\n    this.socket.on('disconnect', (reason) => {\n      console.log('Disconnected from multiplayer server:', reason)\n      this.isConnected = false\n      \n      if (reason === 'io server disconnect') {\n        // Server disconnected, try to reconnect\n        this.handleReconnect()\n      }\n    })\n\n    this.socket.on('connect_error', (error) => {\n      console.warn('Multiplayer server not available:', error.message)\n      this.isConnected = false\n      // Don't attempt reconnection immediately to avoid spam\n    })\n\n    this.socket.on('error', (error) => {\n      console.warn('Socket error:', error.message)\n    })\n  }\n\n  private handleReconnect() {\n    if (this.reconnectAttempts < this.maxReconnectAttempts) {\n      this.reconnectAttempts++\n      console.log(`Attempting to reconnect (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`)\n\n      setTimeout(() => {\n        this.connect()\n      }, Math.pow(2, this.reconnectAttempts) * 1000) // Exponential backoff\n    } else {\n      console.error('Max reconnection attempts reached')\n    }\n  }\n\n  // Public method to check connection status\n  getConnectionStatus() {\n    return {\n      isConnected: this.isConnected,\n      socket: this.socket\n    }\n  }\n\n  // Room management methods\n  createRoom(roomData: Partial<GameRoom>): Promise<GameRoom> {\n    return new Promise((resolve, reject) => {\n      if (!this.socket?.connected) {\n        reject(new Error('Not connected to server'))\n        return\n      }\n\n      this.socket.emit('create_room', roomData)\n      \n      this.socket.once('room_created', (room: GameRoom) => {\n        this.currentRoom = room\n        resolve(room)\n      })\n\n      this.socket.once('error', (error) => {\n        reject(new Error(error.message))\n      })\n    })\n  }\n\n  joinRoom(roomId: string, playerData: Partial<Player>): Promise<{ room: GameRoom, player: Player }> {\n    return new Promise((resolve, reject) => {\n      if (!this.socket?.connected) {\n        reject(new Error('Not connected to server'))\n        return\n      }\n\n      this.socket.emit('join_room', roomId, playerData)\n      \n      this.socket.once('room_joined', (room: GameRoom, player: Player) => {\n        this.currentRoom = room\n        this.currentPlayer = player\n        resolve({ room, player })\n      })\n\n      this.socket.once('error', (error) => {\n        reject(new Error(error.message))\n      })\n    })\n  }\n\n  leaveRoom() {\n    if (this.socket?.connected && this.currentRoom) {\n      this.socket.emit('leave_room', this.currentRoom.id)\n      this.currentRoom = null\n      this.currentPlayer = null\n    }\n  }\n\n  // Game state methods\n  sendPlayerAction(action: {\n    type: string\n    data: any\n  }) {\n    if (this.socket?.connected && this.currentPlayer) {\n      this.socket.emit('player_action', {\n        ...action,\n        playerId: this.currentPlayer.id,\n        timestamp: Date.now()\n      })\n    }\n  }\n\n  sendMessage(content: string) {\n    if (this.socket?.connected && this.currentPlayer) {\n      this.socket.emit('send_message', {\n        playerId: this.currentPlayer.id,\n        playerName: this.currentPlayer.name,\n        content,\n        timestamp: Date.now()\n      })\n    }\n  }\n\n  // Event subscription methods\n  on<K extends keyof SocketEvents>(event: K, callback: SocketEvents[K]) {\n    this.socket?.on(event, callback as any)\n  }\n\n  off<K extends keyof SocketEvents>(event: K, callback?: SocketEvents[K]) {\n    this.socket?.off(event, callback as any)\n  }\n\n  once<K extends keyof SocketEvents>(event: K, callback: SocketEvents[K]) {\n    this.socket?.once(event, callback as any)\n  }\n\n  // Utility methods\n  isSocketConnected(): boolean {\n    return this.isConnected && this.socket?.connected === true\n  }\n\n  getCurrentRoom(): GameRoom | null {\n    return this.currentRoom\n  }\n\n  getCurrentPlayer(): Player | null {\n    return this.currentPlayer\n  }\n\n  disconnect() {\n    if (this.socket) {\n      this.socket.disconnect()\n      this.socket = null\n      this.isConnected = false\n      this.currentRoom = null\n      this.currentPlayer = null\n    }\n  }\n}\n\n// Singleton instance\nexport const socketManager = new SocketManager()\n\n// React hook for using socket in components\nexport function useSocket() {\n  return socketManager\n}\n"], "names": [], "mappings": "AAAA,uDAAuD;;;;;AAEvD;AAAA;;AA2HA,MAAM;IACI,SAAwB,KAAI;IAC5B,cAAc,MAAK;IACnB,oBAAoB,EAAC;IACrB,uBAAuB,EAAC;IACxB,cAA+B,KAAI;IACnC,gBAA+B,KAAI;IAE3C,aAAc;IACZ,iEAAiE;IACjE,kEAAkE;IACpE;IAEA,UAAU;QACR,IAAI,IAAI,CAAC,MAAM,EAAE,WAAW;QAE5B,IAAI;YACF,IAAI,CAAC,MAAM,GAAG,CAAA,GAAA,wLAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,GAAG,CAAC,sBAAsB,IAAI,yBAAyB;gBAC9E,YAAY;oBAAC;oBAAa;iBAAU;gBACpC,SAAS;gBACT,UAAU;gBACV,aAAa,MAAM,iDAAiD;YACtE;YAEA,IAAI,CAAC,mBAAmB;YACxB,IAAI,CAAC,MAAM,CAAC,OAAO,IAAG,8CAA8C;QACtE,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,2CAA2C;QAC1D;IACF;IAEQ,sBAAsB;QAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAElB,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,WAAW;YACxB,QAAQ,GAAG,CAAC;YACZ,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,iBAAiB,GAAG;QAC3B;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,cAAc,CAAC;YAC5B,QAAQ,GAAG,CAAC,yCAAyC;YACrD,IAAI,CAAC,WAAW,GAAG;YAEnB,IAAI,WAAW,wBAAwB;gBACrC,wCAAwC;gBACxC,IAAI,CAAC,eAAe;YACtB;QACF;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,iBAAiB,CAAC;YAC/B,QAAQ,IAAI,CAAC,qCAAqC,MAAM,OAAO;YAC/D,IAAI,CAAC,WAAW,GAAG;QACnB,uDAAuD;QACzD;QAEA,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC;YACvB,QAAQ,IAAI,CAAC,iBAAiB,MAAM,OAAO;QAC7C;IACF;IAEQ,kBAAkB;QACxB,IAAI,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,oBAAoB,EAAE;YACtD,IAAI,CAAC,iBAAiB;YACtB,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,IAAI,CAAC,iBAAiB,CAAC,CAAC,EAAE,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC;YAEjG,WAAW;gBACT,IAAI,CAAC,OAAO;YACd,GAAG,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC,iBAAiB,IAAI,OAAM,sBAAsB;QACvE,OAAO;YACL,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,2CAA2C;IAC3C,sBAAsB;QACpB,OAAO;YACL,aAAa,IAAI,CAAC,WAAW;YAC7B,QAAQ,IAAI,CAAC,MAAM;QACrB;IACF;IAEA,0BAA0B;IAC1B,WAAW,QAA2B,EAAqB;QACzD,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW;gBAC3B,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe;YAEhC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB,CAAC;gBAChC,IAAI,CAAC,WAAW,GAAG;gBACnB,QAAQ;YACV;YAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;gBACzB,OAAO,IAAI,MAAM,MAAM,OAAO;YAChC;QACF;IACF;IAEA,SAAS,MAAc,EAAE,UAA2B,EAA+C;QACjG,OAAO,IAAI,QAAQ,CAAC,SAAS;YAC3B,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,WAAW;gBAC3B,OAAO,IAAI,MAAM;gBACjB;YACF;YAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa,QAAQ;YAEtC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,MAAgB;gBAC/C,IAAI,CAAC,WAAW,GAAG;gBACnB,IAAI,CAAC,aAAa,GAAG;gBACrB,QAAQ;oBAAE;oBAAM;gBAAO;YACzB;YAEA,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC;gBACzB,OAAO,IAAI,MAAM,MAAM,OAAO;YAChC;QACF;IACF;IAEA,YAAY;QACV,IAAI,IAAI,CAAC,MAAM,EAAE,aAAa,IAAI,CAAC,WAAW,EAAE;YAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,WAAW,CAAC,EAAE;YAClD,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,aAAa,GAAG;QACvB;IACF;IAEA,qBAAqB;IACrB,iBAAiB,MAGhB,EAAE;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,aAAa,IAAI,CAAC,aAAa,EAAE;YAChD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,iBAAiB;gBAChC,GAAG,MAAM;gBACT,UAAU,IAAI,CAAC,aAAa,CAAC,EAAE;gBAC/B,WAAW,KAAK,GAAG;YACrB;QACF;IACF;IAEA,YAAY,OAAe,EAAE;QAC3B,IAAI,IAAI,CAAC,MAAM,EAAE,aAAa,IAAI,CAAC,aAAa,EAAE;YAChD,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB;gBAC/B,UAAU,IAAI,CAAC,aAAa,CAAC,EAAE;gBAC/B,YAAY,IAAI,CAAC,aAAa,CAAC,IAAI;gBACnC;gBACA,WAAW,KAAK,GAAG;YACrB;QACF;IACF;IAEA,6BAA6B;IAC7B,GAAiC,KAAQ,EAAE,QAAyB,EAAE;QACpE,IAAI,CAAC,MAAM,EAAE,GAAG,OAAO;IACzB;IAEA,IAAkC,KAAQ,EAAE,QAA0B,EAAE;QACtE,IAAI,CAAC,MAAM,EAAE,IAAI,OAAO;IAC1B;IAEA,KAAmC,KAAQ,EAAE,QAAyB,EAAE;QACtE,IAAI,CAAC,MAAM,EAAE,KAAK,OAAO;IAC3B;IAEA,kBAAkB;IAClB,oBAA6B;QAC3B,OAAO,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,MAAM,EAAE,cAAc;IACxD;IAEA,iBAAkC;QAChC,OAAO,IAAI,CAAC,WAAW;IACzB;IAEA,mBAAkC;QAChC,OAAO,IAAI,CAAC,aAAa;IAC3B;IAEA,aAAa;QACX,IAAI,IAAI,CAAC,MAAM,EAAE;YACf,IAAI,CAAC,MAAM,CAAC,UAAU;YACtB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,aAAa,GAAG;QACvB;IACF;AACF;AAGO,MAAM,gBAAgB,IAAI;AAG1B,SAAS;IACd,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1273, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/contexts/MultiplayerContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect, useCallback } from 'react'\nimport { socket<PERSON><PERSON><PERSON>, GameRoom, Player, MultiplayerGameState } from '@/lib/socket'\n\ninterface ChatMessage {\n  id: string\n  playerId: string\n  playerName: string\n  content: string\n  timestamp: number\n}\n\ninterface MultiplayerContextType {\n  // Connection state\n  isConnected: boolean\n  isInRoom: boolean\n  connectionError: string | null\n  \n  // Room and player data\n  currentRoom: GameRoom | null\n  currentPlayer: Player | null\n  players: Player[]\n  \n  // Game state\n  gameState: 'waiting' | 'starting' | 'playing' | 'paused' | 'finished'\n  sharedGameState: MultiplayerGameState | null\n  \n  // Chat\n  messages: ChatMessage[]\n  \n  // Actions\n  createRoom: (roomData: Partial<GameRoom>, playerData: Partial<Player>) => Promise<void>\n  joinRoom: (roomId: string, playerData: Partial<Player>) => Promise<void>\n  leaveRoom: () => void\n  startGame: () => void\n  sendMessage: (content: string) => void\n  sendPlayerAction: (action: { type: string; data: any }) => void\n  \n  // Room management\n  setPlayerReady: (ready: boolean) => void\n  kickPlayer: (playerId: string) => void\n  updateRoomSettings: (settings: any) => void\n}\n\nconst MultiplayerContext = createContext<MultiplayerContextType | undefined>(undefined)\n\nexport function MultiplayerProvider({ children }: { children: React.ReactNode }) {\n  // Connection state\n  const [isConnected, setIsConnected] = useState(false)\n  const [isInRoom, setIsInRoom] = useState(false)\n  const [connectionError, setConnectionError] = useState<string | null>(null)\n  \n  // Room and player data\n  const [currentRoom, setCurrentRoom] = useState<GameRoom | null>(null)\n  const [currentPlayer, setCurrentPlayer] = useState<Player | null>(null)\n  const [players, setPlayers] = useState<Player[]>([])\n  \n  // Game state\n  const [gameState, setGameState] = useState<'waiting' | 'starting' | 'playing' | 'paused' | 'finished'>('waiting')\n  const [sharedGameState, setSharedGameState] = useState<MultiplayerGameState | null>(null)\n  \n  // Chat\n  const [messages, setMessages] = useState<ChatMessage[]>([])\n\n  // Initialize socket connection and event listeners only when needed\n  useEffect(() => {\n    // Don't auto-connect to prevent errors when server isn't running\n    // Connection will be initiated when multiplayer features are used\n    const socket = socketManager\n\n    // Connection events\n    const handleConnect = () => {\n      setIsConnected(true)\n      setConnectionError(null)\n    }\n\n    const handleDisconnect = () => {\n      setIsConnected(false)\n      setIsInRoom(false)\n      setCurrentRoom(null)\n      setCurrentPlayer(null)\n      setPlayers([])\n    }\n\n    const handleError = (error: any) => {\n      setConnectionError(error.message || 'Connection error')\n      console.error('Multiplayer error:', error)\n    }\n\n    // Room events\n    const handleRoomCreated = (room: GameRoom) => {\n      setCurrentRoom(room)\n      setPlayers(room.players)\n      setIsInRoom(true)\n      setGameState(room.gameState)\n      \n      // Set current player as host\n      const hostPlayer = room.players.find(p => p.isHost)\n      if (hostPlayer) {\n        setCurrentPlayer(hostPlayer)\n      }\n    }\n\n    const handleRoomJoined = (room: GameRoom, player: Player) => {\n      setCurrentRoom(room)\n      setCurrentPlayer(player)\n      setPlayers(room.players)\n      setIsInRoom(true)\n      setGameState(room.gameState)\n    }\n\n    const handleRoomLeft = () => {\n      setCurrentRoom(null)\n      setCurrentPlayer(null)\n      setPlayers([])\n      setIsInRoom(false)\n      setGameState('waiting')\n      setSharedGameState(null)\n      setMessages([])\n    }\n\n    const handleRoomUpdated = (room: GameRoom) => {\n      setCurrentRoom(room)\n      setPlayers(room.players)\n      setGameState(room.gameState)\n    }\n\n    const handlePlayerJoined = (player: Player) => {\n      setPlayers(prev => [...prev, player])\n      addSystemMessage(`${player.name} joined the room`)\n    }\n\n    const handlePlayerLeft = (playerId: string) => {\n      setPlayers(prev => {\n        const leftPlayer = prev.find(p => p.id === playerId)\n        if (leftPlayer) {\n          addSystemMessage(`${leftPlayer.name} left the room`)\n        }\n        return prev.filter(p => p.id !== playerId)\n      })\n    }\n\n    // Game events\n    const handleGameStarted = (gameState: MultiplayerGameState) => {\n      setGameState('playing')\n      setSharedGameState(gameState)\n      addSystemMessage('Game started!')\n    }\n\n    const handleGameStateUpdate = (update: Partial<MultiplayerGameState>) => {\n      setSharedGameState(prev => prev ? { ...prev, ...update } : null)\n    }\n\n    const handlePlayerAction = (action: any) => {\n      // Handle player actions from other players\n      console.log('Player action received:', action)\n    }\n\n    // Chat events\n    const handleMessageReceived = (message: any) => {\n      const chatMessage: ChatMessage = {\n        id: Date.now().toString() + Math.random().toString(36).substring(2, 11),\n        playerId: message.playerId,\n        playerName: message.playerName,\n        content: message.content,\n        timestamp: message.timestamp\n      }\n      setMessages(prev => [...prev, chatMessage])\n    }\n\n    // Register event listeners\n    socket.on('connect' as any, handleConnect)\n    socket.on('disconnect' as any, handleDisconnect)\n    socket.on('error' as any, handleError)\n    socket.on('room_created', handleRoomCreated)\n    socket.on('room_joined', handleRoomJoined)\n    socket.on('room_left', handleRoomLeft)\n    socket.on('room_updated', handleRoomUpdated)\n    socket.on('player_joined', handlePlayerJoined)\n    socket.on('player_left', handlePlayerLeft)\n    socket.on('game_started', handleGameStarted)\n    socket.on('game_state_update', handleGameStateUpdate)\n    socket.on('player_action', handlePlayerAction)\n    socket.on('message_received', handleMessageReceived)\n\n    // Check initial connection state\n    setIsConnected(socket.isSocketConnected())\n\n    // Cleanup\n    return () => {\n      socket.off('connect' as any, handleConnect)\n      socket.off('disconnect' as any, handleDisconnect)\n      socket.off('error' as any, handleError)\n      socket.off('room_created', handleRoomCreated)\n      socket.off('room_joined', handleRoomJoined)\n      socket.off('room_left', handleRoomLeft)\n      socket.off('room_updated', handleRoomUpdated)\n      socket.off('player_joined', handlePlayerJoined)\n      socket.off('player_left', handlePlayerLeft)\n      socket.off('game_started', handleGameStarted)\n      socket.off('game_state_update', handleGameStateUpdate)\n      socket.off('player_action', handlePlayerAction)\n      socket.off('message_received', handleMessageReceived)\n    }\n  }, [])\n\n  // Helper function to add system messages\n  const addSystemMessage = (content: string) => {\n    const systemMessage: ChatMessage = {\n      id: Date.now().toString() + Math.random().toString(36).substring(2, 11),\n      playerId: 'system',\n      playerName: 'System',\n      content,\n      timestamp: Date.now()\n    }\n    setMessages(prev => [...prev, systemMessage])\n  }\n\n  // Action functions\n  const createRoom = useCallback(async (roomData: Partial<GameRoom>, playerData: Partial<Player>) => {\n    try {\n      setConnectionError(null)\n      // Connect to socket server when multiplayer is actually used\n      const { isConnected } = socketManager.getConnectionStatus()\n      if (!isConnected) {\n        socketManager.connect()\n        // Wait a moment for connection\n        await new Promise(resolve => setTimeout(resolve, 1000))\n      }\n      await socketManager.createRoom({\n        ...roomData,\n        hostName: playerData.name,\n        hostAvatar: playerData.avatar,\n        hostLevel: playerData.level\n      } as any)\n      // Room created event will be handled by the event listener\n    } catch (error: any) {\n      setConnectionError(error.message)\n      throw error\n    }\n  }, [])\n\n  const joinRoom = useCallback(async (roomId: string, playerData: Partial<Player>) => {\n    try {\n      setConnectionError(null)\n      // Connect to socket server when multiplayer is actually used\n      const { isConnected } = socketManager.getConnectionStatus()\n      if (!isConnected) {\n        socketManager.connect()\n        // Wait a moment for connection\n        await new Promise(resolve => setTimeout(resolve, 1000))\n      }\n      await socketManager.joinRoom(roomId, playerData)\n      // Room joined event will be handled by the event listener\n    } catch (error: any) {\n      setConnectionError(error.message)\n      throw error\n    }\n  }, [])\n\n  const leaveRoom = useCallback(() => {\n    socketManager.leaveRoom()\n  }, [])\n\n  const startGame = useCallback(() => {\n    if (currentRoom && currentPlayer?.isHost) {\n      socketManager.sendPlayerAction({\n        type: 'start_game',\n        data: { roomId: currentRoom.id }\n      })\n    }\n  }, [currentRoom, currentPlayer])\n\n  const sendMessage = useCallback((content: string) => {\n    socketManager.sendMessage(content)\n  }, [])\n\n  const sendPlayerAction = useCallback((action: { type: string; data: any }) => {\n    socketManager.sendPlayerAction(action)\n  }, [])\n\n  const setPlayerReady = useCallback((ready: boolean) => {\n    if (currentPlayer) {\n      sendPlayerAction({\n        type: 'player_ready',\n        data: { ready }\n      })\n    }\n  }, [currentPlayer, sendPlayerAction])\n\n  const kickPlayer = useCallback((playerId: string) => {\n    if (currentPlayer?.isHost) {\n      sendPlayerAction({\n        type: 'kick_player',\n        data: { playerId }\n      })\n    }\n  }, [currentPlayer, sendPlayerAction])\n\n  const updateRoomSettings = useCallback((settings: any) => {\n    if (currentPlayer?.isHost) {\n      sendPlayerAction({\n        type: 'update_room_settings',\n        data: { settings }\n      })\n    }\n  }, [currentPlayer, sendPlayerAction])\n\n  const value: MultiplayerContextType = {\n    // Connection state\n    isConnected,\n    isInRoom,\n    connectionError,\n    \n    // Room and player data\n    currentRoom,\n    currentPlayer,\n    players,\n    \n    // Game state\n    gameState,\n    sharedGameState,\n    \n    // Chat\n    messages,\n    \n    // Actions\n    createRoom,\n    joinRoom,\n    leaveRoom,\n    startGame,\n    sendMessage,\n    sendPlayerAction,\n    \n    // Room management\n    setPlayerReady,\n    kickPlayer,\n    updateRoomSettings\n  }\n\n  return (\n    <MultiplayerContext.Provider value={value}>\n      {children}\n    </MultiplayerContext.Provider>\n  )\n}\n\nexport function useMultiplayer() {\n  const context = useContext(MultiplayerContext)\n  if (context === undefined) {\n    // Fallback for when context is not available\n    console.warn('useMultiplayer called outside of MultiplayerProvider, using fallback')\n    return {\n      isConnected: false,\n      connectionStatus: 'disconnected' as const,\n      currentRoom: null,\n      gameState: null,\n      createRoom: async () => {},\n      joinRoom: async () => {},\n      leaveRoom: () => {},\n      sendChatMessage: () => {},\n      updateGameState: () => {},\n      setPlayerReady: () => {}\n    }\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AA6CA,MAAM,mCAAqB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAsC;AAEtE,SAAS,oBAAoB,EAAE,QAAQ,EAAiC;IAC7E,mBAAmB;IACnB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtE,uBAAuB;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;IAChE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAEnD,aAAa;IACb,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA8D;IACvG,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA+B;IAEpF,OAAO;IACP,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAE1D,oEAAoE;IACpE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iEAAiE;QACjE,kEAAkE;QAClE,MAAM,SAAS,oHAAA,CAAA,gBAAa;QAE5B,oBAAoB;QACpB,MAAM,gBAAgB;YACpB,eAAe;YACf,mBAAmB;QACrB;QAEA,MAAM,mBAAmB;YACvB,eAAe;YACf,YAAY;YACZ,eAAe;YACf,iBAAiB;YACjB,WAAW,EAAE;QACf;QAEA,MAAM,cAAc,CAAC;YACnB,mBAAmB,MAAM,OAAO,IAAI;YACpC,QAAQ,KAAK,CAAC,sBAAsB;QACtC;QAEA,cAAc;QACd,MAAM,oBAAoB,CAAC;YACzB,eAAe;YACf,WAAW,KAAK,OAAO;YACvB,YAAY;YACZ,aAAa,KAAK,SAAS;YAE3B,6BAA6B;YAC7B,MAAM,aAAa,KAAK,OAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM;YAClD,IAAI,YAAY;gBACd,iBAAiB;YACnB;QACF;QAEA,MAAM,mBAAmB,CAAC,MAAgB;YACxC,eAAe;YACf,iBAAiB;YACjB,WAAW,KAAK,OAAO;YACvB,YAAY;YACZ,aAAa,KAAK,SAAS;QAC7B;QAEA,MAAM,iBAAiB;YACrB,eAAe;YACf,iBAAiB;YACjB,WAAW,EAAE;YACb,YAAY;YACZ,aAAa;YACb,mBAAmB;YACnB,YAAY,EAAE;QAChB;QAEA,MAAM,oBAAoB,CAAC;YACzB,eAAe;YACf,WAAW,KAAK,OAAO;YACvB,aAAa,KAAK,SAAS;QAC7B;QAEA,MAAM,qBAAqB,CAAC;YAC1B,WAAW,CAAA,OAAQ;uBAAI;oBAAM;iBAAO;YACpC,iBAAiB,GAAG,OAAO,IAAI,CAAC,gBAAgB,CAAC;QACnD;QAEA,MAAM,mBAAmB,CAAC;YACxB,WAAW,CAAA;gBACT,MAAM,aAAa,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC3C,IAAI,YAAY;oBACd,iBAAiB,GAAG,WAAW,IAAI,CAAC,cAAc,CAAC;gBACrD;gBACA,OAAO,KAAK,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YACnC;QACF;QAEA,cAAc;QACd,MAAM,oBAAoB,CAAC;YACzB,aAAa;YACb,mBAAmB;YACnB,iBAAiB;QACnB;QAEA,MAAM,wBAAwB,CAAC;YAC7B,mBAAmB,CAAA,OAAQ,OAAO;oBAAE,GAAG,IAAI;oBAAE,GAAG,MAAM;gBAAC,IAAI;QAC7D;QAEA,MAAM,qBAAqB,CAAC;YAC1B,2CAA2C;YAC3C,QAAQ,GAAG,CAAC,2BAA2B;QACzC;QAEA,cAAc;QACd,MAAM,wBAAwB,CAAC;YAC7B,MAAM,cAA2B;gBAC/B,IAAI,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;gBACpE,UAAU,QAAQ,QAAQ;gBAC1B,YAAY,QAAQ,UAAU;gBAC9B,SAAS,QAAQ,OAAO;gBACxB,WAAW,QAAQ,SAAS;YAC9B;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAY;QAC5C;QAEA,2BAA2B;QAC3B,OAAO,EAAE,CAAC,WAAkB;QAC5B,OAAO,EAAE,CAAC,cAAqB;QAC/B,OAAO,EAAE,CAAC,SAAgB;QAC1B,OAAO,EAAE,CAAC,gBAAgB;QAC1B,OAAO,EAAE,CAAC,eAAe;QACzB,OAAO,EAAE,CAAC,aAAa;QACvB,OAAO,EAAE,CAAC,gBAAgB;QAC1B,OAAO,EAAE,CAAC,iBAAiB;QAC3B,OAAO,EAAE,CAAC,eAAe;QACzB,OAAO,EAAE,CAAC,gBAAgB;QAC1B,OAAO,EAAE,CAAC,qBAAqB;QAC/B,OAAO,EAAE,CAAC,iBAAiB;QAC3B,OAAO,EAAE,CAAC,oBAAoB;QAE9B,iCAAiC;QACjC,eAAe,OAAO,iBAAiB;QAEvC,UAAU;QACV,OAAO;YACL,OAAO,GAAG,CAAC,WAAkB;YAC7B,OAAO,GAAG,CAAC,cAAqB;YAChC,OAAO,GAAG,CAAC,SAAgB;YAC3B,OAAO,GAAG,CAAC,gBAAgB;YAC3B,OAAO,GAAG,CAAC,eAAe;YAC1B,OAAO,GAAG,CAAC,aAAa;YACxB,OAAO,GAAG,CAAC,gBAAgB;YAC3B,OAAO,GAAG,CAAC,iBAAiB;YAC5B,OAAO,GAAG,CAAC,eAAe;YAC1B,OAAO,GAAG,CAAC,gBAAgB;YAC3B,OAAO,GAAG,CAAC,qBAAqB;YAChC,OAAO,GAAG,CAAC,iBAAiB;YAC5B,OAAO,GAAG,CAAC,oBAAoB;QACjC;IACF,GAAG,EAAE;IAEL,yCAAyC;IACzC,MAAM,mBAAmB,CAAC;QACxB,MAAM,gBAA6B;YACjC,IAAI,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;YACpE,UAAU;YACV,YAAY;YACZ;YACA,WAAW,KAAK,GAAG;QACrB;QACA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAc;IAC9C;IAEA,mBAAmB;IACnB,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,UAA6B;QACjE,IAAI;YACF,mBAAmB;YACnB,6DAA6D;YAC7D,MAAM,EAAE,WAAW,EAAE,GAAG,oHAAA,CAAA,gBAAa,CAAC,mBAAmB;YACzD,IAAI,CAAC,aAAa;gBAChB,oHAAA,CAAA,gBAAa,CAAC,OAAO;gBACrB,+BAA+B;gBAC/B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;YACA,MAAM,oHAAA,CAAA,gBAAa,CAAC,UAAU,CAAC;gBAC7B,GAAG,QAAQ;gBACX,UAAU,WAAW,IAAI;gBACzB,YAAY,WAAW,MAAM;gBAC7B,WAAW,WAAW,KAAK;YAC7B;QACA,2DAA2D;QAC7D,EAAE,OAAO,OAAY;YACnB,mBAAmB,MAAM,OAAO;YAChC,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO,QAAgB;QAClD,IAAI;YACF,mBAAmB;YACnB,6DAA6D;YAC7D,MAAM,EAAE,WAAW,EAAE,GAAG,oHAAA,CAAA,gBAAa,CAAC,mBAAmB;YACzD,IAAI,CAAC,aAAa;gBAChB,oHAAA,CAAA,gBAAa,CAAC,OAAO;gBACrB,+BAA+B;gBAC/B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YACnD;YACA,MAAM,oHAAA,CAAA,gBAAa,CAAC,QAAQ,CAAC,QAAQ;QACrC,0DAA0D;QAC5D,EAAE,OAAO,OAAY;YACnB,mBAAmB,MAAM,OAAO;YAChC,MAAM;QACR;IACF,GAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,oHAAA,CAAA,gBAAa,CAAC,SAAS;IACzB,GAAG,EAAE;IAEL,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,eAAe,eAAe,QAAQ;YACxC,oHAAA,CAAA,gBAAa,CAAC,gBAAgB,CAAC;gBAC7B,MAAM;gBACN,MAAM;oBAAE,QAAQ,YAAY,EAAE;gBAAC;YACjC;QACF;IACF,GAAG;QAAC;QAAa;KAAc;IAE/B,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC/B,oHAAA,CAAA,gBAAa,CAAC,WAAW,CAAC;IAC5B,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACpC,oHAAA,CAAA,gBAAa,CAAC,gBAAgB,CAAC;IACjC,GAAG,EAAE;IAEL,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,IAAI,eAAe;YACjB,iBAAiB;gBACf,MAAM;gBACN,MAAM;oBAAE;gBAAM;YAChB;QACF;IACF,GAAG;QAAC;QAAe;KAAiB;IAEpC,MAAM,aAAa,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC9B,IAAI,eAAe,QAAQ;YACzB,iBAAiB;gBACf,MAAM;gBACN,MAAM;oBAAE;gBAAS;YACnB;QACF;IACF,GAAG;QAAC;QAAe;KAAiB;IAEpC,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,IAAI,eAAe,QAAQ;YACzB,iBAAiB;gBACf,MAAM;gBACN,MAAM;oBAAE;gBAAS;YACnB;QACF;IACF,GAAG;QAAC;QAAe;KAAiB;IAEpC,MAAM,QAAgC;QACpC,mBAAmB;QACnB;QACA;QACA;QAEA,uBAAuB;QACvB;QACA;QACA;QAEA,aAAa;QACb;QACA;QAEA,OAAO;QACP;QAEA,UAAU;QACV;QACA;QACA;QACA;QACA;QACA;QAEA,kBAAkB;QAClB;QACA;QACA;IACF;IAEA,qBACE,8OAAC,mBAAmB,QAAQ;QAAC,OAAO;kBACjC;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,6CAA6C;QAC7C,QAAQ,IAAI,CAAC;QACb,OAAO;YACL,aAAa;YACb,kBAAkB;YAClB,aAAa;YACb,WAAW;YACX,YAAY,WAAa;YACzB,UAAU,WAAa;YACvB,WAAW,KAAO;YAClB,iBAAiB,KAAO;YACxB,iBAAiB,KAAO;YACxB,gBAAgB,KAAO;QACzB;IACF;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1606, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/lib/discordRPC.ts"], "sourcesContent": ["// Discord Rich Presence integration for Bake It Out\n// Shows current game status, activity, and multiplayer information\n\n// For Electron, we'll use IPC to communicate with the main process\n// which will handle the actual Discord RPC connection\n\nexport interface GameActivity {\n  state: string\n  details: string\n  largeImageKey?: string\n  largeImageText?: string\n  smallImageKey?: string\n  smallImageText?: string\n  startTimestamp?: number\n  endTimestamp?: number\n  partyId?: string\n  partySize?: number\n  partyMax?: number\n  joinSecret?: string\n  spectateSecret?: string\n  matchSecret?: string\n  buttons?: Array<{\n    label: string\n    url: string\n  }>\n}\n\nexport interface PlayerStatus {\n  level: number\n  money: number\n  currentActivity: 'menu' | 'baking' | 'managing' | 'multiplayer' | 'idle'\n  bakeryName?: string\n  currentOrder?: string\n  multiplayerRoom?: {\n    id: string\n    playerCount: number\n    maxPlayers: number\n  }\n  playTime?: number\n}\n\nclass DiscordRPCService {\n  private isConnected: boolean = false\n  private isEnabled: boolean = true\n  private startTime: number = Date.now()\n  private currentActivity: GameActivity | null = null\n  private isElectron: boolean = false\n\n  // Discord Application ID for Bake It Out\n  private readonly CLIENT_ID = '1234567890123456789' // This would be your actual Discord app ID\n\n  constructor() {\n    // Check if we're in Electron environment\n    this.isElectron = typeof window !== 'undefined' && window.electronAPI !== undefined\n\n    if (this.isElectron) {\n      this.initializeRPC()\n    } else {\n      console.log('Discord RPC only available in desktop version')\n    }\n  }\n\n  private async initializeRPC() {\n    if (!this.isEnabled || !this.isElectron) return\n\n    try {\n      // Use Electron IPC to initialize Discord RPC in main process\n      if (window.electronAPI && window.electronAPI.initDiscordRPC) {\n        const success = await window.electronAPI.initDiscordRPC(this.CLIENT_ID)\n        if (success) {\n          console.log('Discord RPC initialized successfully')\n          this.isConnected = true\n\n          // Set initial activity with a delay to ensure connection is stable\n          setTimeout(async () => {\n            await this.updateActivity({\n              state: 'In Main Menu',\n              details: 'Starting the bakery adventure',\n              largeImageKey: 'bake_it_out_logo',\n              largeImageText: 'Bake It Out - Bakery Management Game',\n              startTimestamp: this.startTime\n            })\n          }, 1000)\n        } else {\n          console.log('Discord RPC initialization failed')\n          this.isConnected = false\n        }\n      } else {\n        console.log('Discord RPC not available - missing Electron API')\n        this.isConnected = false\n      }\n    } catch (error) {\n      console.log('Discord RPC initialization error:', error.message || error)\n      this.isConnected = false\n    }\n  }\n\n  public async updatePlayerStatus(playerStatus: PlayerStatus) {\n    if (!this.isConnected || !this.client) return\n\n    const activity = this.createActivityFromPlayerStatus(playerStatus)\n    await this.updateActivity(activity)\n  }\n\n  private createActivityFromPlayerStatus(status: PlayerStatus): GameActivity {\n    const baseActivity: GameActivity = {\n      state: '',\n      details: '',\n      largeImageKey: 'bake_it_out_logo',\n      largeImageText: 'Bake It Out - Bakery Management Game',\n      startTimestamp: this.startTime\n    }\n\n    switch (status.currentActivity) {\n      case 'menu':\n        return {\n          ...baseActivity,\n          state: 'In Main Menu',\n          details: 'Choosing game mode',\n          smallImageKey: 'menu_icon',\n          smallImageText: 'Main Menu'\n        }\n\n      case 'baking':\n        return {\n          ...baseActivity,\n          state: `Level ${status.level} Baker`,\n          details: status.currentOrder \n            ? `Baking: ${status.currentOrder}`\n            : 'Managing the bakery',\n          smallImageKey: 'baking_icon',\n          smallImageText: 'Baking',\n          buttons: [\n            {\n              label: 'Play Bake It Out',\n              url: 'https://bakeitout.game'\n            }\n          ]\n        }\n\n      case 'managing':\n        return {\n          ...baseActivity,\n          state: `Level ${status.level} - $${status.money}`,\n          details: status.bakeryName \n            ? `Managing ${status.bakeryName}`\n            : 'Managing bakery',\n          smallImageKey: 'management_icon',\n          smallImageText: 'Bakery Management'\n        }\n\n      case 'multiplayer':\n        const multiplayerActivity = {\n          ...baseActivity,\n          state: `Level ${status.level} Baker`,\n          details: 'Playing with friends',\n          smallImageKey: 'multiplayer_icon',\n          smallImageText: 'Multiplayer',\n          buttons: [\n            {\n              label: 'Join Game',\n              url: 'https://bakeitout.game/join'\n            }\n          ]\n        }\n\n        if (status.multiplayerRoom) {\n          multiplayerActivity.partyId = status.multiplayerRoom.id\n          multiplayerActivity.partySize = status.multiplayerRoom.playerCount\n          multiplayerActivity.partyMax = status.multiplayerRoom.maxPlayers\n          multiplayerActivity.details = `Multiplayer Bakery (${status.multiplayerRoom.playerCount}/${status.multiplayerRoom.maxPlayers})`\n        }\n\n        return multiplayerActivity\n\n      case 'idle':\n        return {\n          ...baseActivity,\n          state: `Level ${status.level} Baker`,\n          details: 'Taking a break',\n          smallImageKey: 'idle_icon',\n          smallImageText: 'Idle'\n        }\n\n      default:\n        return {\n          ...baseActivity,\n          state: 'Playing Bake It Out',\n          details: 'Bakery Management Game'\n        }\n    }\n  }\n\n  public async updateActivity(activity: GameActivity) {\n    if (!this.isConnected || !this.isElectron) return\n\n    try {\n      if (window.electronAPI && window.electronAPI.updateDiscordRPC) {\n        await window.electronAPI.updateDiscordRPC(activity)\n        this.currentActivity = activity\n        console.log('Discord RPC activity updated:', activity.details)\n      }\n    } catch (error) {\n      console.error('Failed to update Discord RPC activity:', error)\n    }\n  }\n\n  public async setMenuActivity() {\n    await this.updateActivity({\n      state: 'In Main Menu',\n      details: 'Choosing game mode',\n      largeImageKey: 'bake_it_out_logo',\n      largeImageText: 'Bake It Out - Bakery Management Game',\n      smallImageKey: 'menu_icon',\n      smallImageText: 'Main Menu',\n      startTimestamp: this.startTime\n    })\n  }\n\n  public async setGameActivity(level: number, money: number, activity?: string) {\n    await this.updateActivity({\n      state: `Level ${level} - $${money}`,\n      details: activity || 'Managing bakery',\n      largeImageKey: 'bake_it_out_logo',\n      largeImageText: 'Bake It Out - Bakery Management Game',\n      smallImageKey: 'baking_icon',\n      smallImageText: 'In Game',\n      startTimestamp: this.startTime,\n      buttons: [\n        {\n          label: 'Play Bake It Out',\n          url: 'https://bakeitout.game'\n        }\n      ]\n    })\n  }\n\n  public async setMultiplayerActivity(roomId: string, playerCount: number, maxPlayers: number) {\n    await this.updateActivity({\n      state: 'Multiplayer Bakery',\n      details: `Playing with friends (${playerCount}/${maxPlayers})`,\n      largeImageKey: 'bake_it_out_logo',\n      largeImageText: 'Bake It Out - Bakery Management Game',\n      smallImageKey: 'multiplayer_icon',\n      smallImageText: 'Multiplayer',\n      startTimestamp: this.startTime,\n      partyId: roomId,\n      partySize: playerCount,\n      partyMax: maxPlayers,\n      buttons: [\n        {\n          label: 'Join Game',\n          url: `https://bakeitout.game/join/${roomId}`\n        }\n      ]\n    })\n  }\n\n  public async setBakingActivity(level: number, currentOrder: string) {\n    await this.updateActivity({\n      state: `Level ${level} Baker`,\n      details: `Baking: ${currentOrder}`,\n      largeImageKey: 'bake_it_out_logo',\n      largeImageText: 'Bake It Out - Bakery Management Game',\n      smallImageKey: 'baking_icon',\n      smallImageText: 'Baking',\n      startTimestamp: this.startTime\n    })\n  }\n\n  public async clearActivity() {\n    if (!this.isConnected || !this.isElectron) return\n\n    try {\n      if (window.electronAPI && window.electronAPI.clearDiscordRPC) {\n        await window.electronAPI.clearDiscordRPC()\n        this.currentActivity = null\n        console.log('Discord RPC activity cleared')\n      }\n    } catch (error) {\n      console.error('Failed to clear Discord RPC activity:', error)\n    }\n  }\n\n  public setEnabled(enabled: boolean) {\n    this.isEnabled = enabled\n    \n    if (!enabled && this.isConnected) {\n      this.clearActivity()\n      this.disconnect()\n    } else if (enabled && !this.isConnected) {\n      this.initializeRPC()\n    }\n  }\n\n  public isRPCEnabled(): boolean {\n    return this.isEnabled\n  }\n\n  public isRPCConnected(): boolean {\n    return this.isConnected\n  }\n\n  public getCurrentActivity(): GameActivity | null {\n    return this.currentActivity\n  }\n\n  public async disconnect() {\n    if (this.isConnected && this.isElectron) {\n      try {\n        if (window.electronAPI && window.electronAPI.disconnectDiscordRPC) {\n          await window.electronAPI.disconnectDiscordRPC()\n          console.log('Discord RPC disconnected')\n        }\n      } catch (error) {\n        console.error('Error disconnecting Discord RPC:', error)\n      }\n    }\n\n    this.isConnected = false\n    this.currentActivity = null\n  }\n\n  // Cleanup method for when the app closes\n  public async cleanup() {\n    await this.clearActivity()\n    await this.disconnect()\n  }\n}\n\n// Export singleton instance\nexport const discordRPC = new DiscordRPCService()\n\n// Export for use in components\nexport default discordRPC\n"], "names": [], "mappings": "AAAA,oDAAoD;AACpD,mEAAmE;AAEnE,mEAAmE;AACnE,sDAAsD;;;;;AAqCtD,MAAM;IACI,cAAuB,MAAK;IAC5B,YAAqB,KAAI;IACzB,YAAoB,KAAK,GAAG,GAAE;IAC9B,kBAAuC,KAAI;IAC3C,aAAsB,MAAK;IAEnC,yCAAyC;IACxB,YAAY,sBAAsB,2CAA2C;KAA5C;IAElD,aAAc;QACZ,yCAAyC;QACzC,IAAI,CAAC,UAAU,GAAG,gBAAkB,eAAe,OAAO,WAAW,KAAK;QAE1E,IAAI,IAAI,CAAC,UAAU,EAAE;YACnB,IAAI,CAAC,aAAa;QACpB,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;IACF;IAEA,MAAc,gBAAgB;QAC5B,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;QAEzC,IAAI;YACF,6DAA6D;YAC7D,IAAI,OAAO,WAAW,IAAI,OAAO,WAAW,CAAC,cAAc,EAAE;gBAC3D,MAAM,UAAU,MAAM,OAAO,WAAW,CAAC,cAAc,CAAC,IAAI,CAAC,SAAS;gBACtE,IAAI,SAAS;oBACX,QAAQ,GAAG,CAAC;oBACZ,IAAI,CAAC,WAAW,GAAG;oBAEnB,mEAAmE;oBACnE,WAAW;wBACT,MAAM,IAAI,CAAC,cAAc,CAAC;4BACxB,OAAO;4BACP,SAAS;4BACT,eAAe;4BACf,gBAAgB;4BAChB,gBAAgB,IAAI,CAAC,SAAS;wBAChC;oBACF,GAAG;gBACL,OAAO;oBACL,QAAQ,GAAG,CAAC;oBACZ,IAAI,CAAC,WAAW,GAAG;gBACrB;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,IAAI,CAAC,WAAW,GAAG;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,GAAG,CAAC,qCAAqC,MAAM,OAAO,IAAI;YAClE,IAAI,CAAC,WAAW,GAAG;QACrB;IACF;IAEA,MAAa,mBAAmB,YAA0B,EAAE;QAC1D,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;QAEvC,MAAM,WAAW,IAAI,CAAC,8BAA8B,CAAC;QACrD,MAAM,IAAI,CAAC,cAAc,CAAC;IAC5B;IAEQ,+BAA+B,MAAoB,EAAgB;QACzE,MAAM,eAA6B;YACjC,OAAO;YACP,SAAS;YACT,eAAe;YACf,gBAAgB;YAChB,gBAAgB,IAAI,CAAC,SAAS;QAChC;QAEA,OAAQ,OAAO,eAAe;YAC5B,KAAK;gBACH,OAAO;oBACL,GAAG,YAAY;oBACf,OAAO;oBACP,SAAS;oBACT,eAAe;oBACf,gBAAgB;gBAClB;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,YAAY;oBACf,OAAO,CAAC,MAAM,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC;oBACpC,SAAS,OAAO,YAAY,GACxB,CAAC,QAAQ,EAAE,OAAO,YAAY,EAAE,GAChC;oBACJ,eAAe;oBACf,gBAAgB;oBAChB,SAAS;wBACP;4BACE,OAAO;4BACP,KAAK;wBACP;qBACD;gBACH;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,YAAY;oBACf,OAAO,CAAC,MAAM,EAAE,OAAO,KAAK,CAAC,IAAI,EAAE,OAAO,KAAK,EAAE;oBACjD,SAAS,OAAO,UAAU,GACtB,CAAC,SAAS,EAAE,OAAO,UAAU,EAAE,GAC/B;oBACJ,eAAe;oBACf,gBAAgB;gBAClB;YAEF,KAAK;gBACH,MAAM,sBAAsB;oBAC1B,GAAG,YAAY;oBACf,OAAO,CAAC,MAAM,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC;oBACpC,SAAS;oBACT,eAAe;oBACf,gBAAgB;oBAChB,SAAS;wBACP;4BACE,OAAO;4BACP,KAAK;wBACP;qBACD;gBACH;gBAEA,IAAI,OAAO,eAAe,EAAE;oBAC1B,oBAAoB,OAAO,GAAG,OAAO,eAAe,CAAC,EAAE;oBACvD,oBAAoB,SAAS,GAAG,OAAO,eAAe,CAAC,WAAW;oBAClE,oBAAoB,QAAQ,GAAG,OAAO,eAAe,CAAC,UAAU;oBAChE,oBAAoB,OAAO,GAAG,CAAC,oBAAoB,EAAE,OAAO,eAAe,CAAC,WAAW,CAAC,CAAC,EAAE,OAAO,eAAe,CAAC,UAAU,CAAC,CAAC,CAAC;gBACjI;gBAEA,OAAO;YAET,KAAK;gBACH,OAAO;oBACL,GAAG,YAAY;oBACf,OAAO,CAAC,MAAM,EAAE,OAAO,KAAK,CAAC,MAAM,CAAC;oBACpC,SAAS;oBACT,eAAe;oBACf,gBAAgB;gBAClB;YAEF;gBACE,OAAO;oBACL,GAAG,YAAY;oBACf,OAAO;oBACP,SAAS;gBACX;QACJ;IACF;IAEA,MAAa,eAAe,QAAsB,EAAE;QAClD,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;QAE3C,IAAI;YACF,IAAI,OAAO,WAAW,IAAI,OAAO,WAAW,CAAC,gBAAgB,EAAE;gBAC7D,MAAM,OAAO,WAAW,CAAC,gBAAgB,CAAC;gBAC1C,IAAI,CAAC,eAAe,GAAG;gBACvB,QAAQ,GAAG,CAAC,iCAAiC,SAAS,OAAO;YAC/D;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;QAC1D;IACF;IAEA,MAAa,kBAAkB;QAC7B,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,OAAO;YACP,SAAS;YACT,eAAe;YACf,gBAAgB;YAChB,eAAe;YACf,gBAAgB;YAChB,gBAAgB,IAAI,CAAC,SAAS;QAChC;IACF;IAEA,MAAa,gBAAgB,KAAa,EAAE,KAAa,EAAE,QAAiB,EAAE;QAC5E,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,OAAO,CAAC,MAAM,EAAE,MAAM,IAAI,EAAE,OAAO;YACnC,SAAS,YAAY;YACrB,eAAe;YACf,gBAAgB;YAChB,eAAe;YACf,gBAAgB;YAChB,gBAAgB,IAAI,CAAC,SAAS;YAC9B,SAAS;gBACP;oBACE,OAAO;oBACP,KAAK;gBACP;aACD;QACH;IACF;IAEA,MAAa,uBAAuB,MAAc,EAAE,WAAmB,EAAE,UAAkB,EAAE;QAC3F,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,OAAO;YACP,SAAS,CAAC,sBAAsB,EAAE,YAAY,CAAC,EAAE,WAAW,CAAC,CAAC;YAC9D,eAAe;YACf,gBAAgB;YAChB,eAAe;YACf,gBAAgB;YAChB,gBAAgB,IAAI,CAAC,SAAS;YAC9B,SAAS;YACT,WAAW;YACX,UAAU;YACV,SAAS;gBACP;oBACE,OAAO;oBACP,KAAK,CAAC,4BAA4B,EAAE,QAAQ;gBAC9C;aACD;QACH;IACF;IAEA,MAAa,kBAAkB,KAAa,EAAE,YAAoB,EAAE;QAClE,MAAM,IAAI,CAAC,cAAc,CAAC;YACxB,OAAO,CAAC,MAAM,EAAE,MAAM,MAAM,CAAC;YAC7B,SAAS,CAAC,QAAQ,EAAE,cAAc;YAClC,eAAe;YACf,gBAAgB;YAChB,eAAe;YACf,gBAAgB;YAChB,gBAAgB,IAAI,CAAC,SAAS;QAChC;IACF;IAEA,MAAa,gBAAgB;QAC3B,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;QAE3C,IAAI;YACF,IAAI,OAAO,WAAW,IAAI,OAAO,WAAW,CAAC,eAAe,EAAE;gBAC5D,MAAM,OAAO,WAAW,CAAC,eAAe;gBACxC,IAAI,CAAC,eAAe,GAAG;gBACvB,QAAQ,GAAG,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;QACzD;IACF;IAEO,WAAW,OAAgB,EAAE;QAClC,IAAI,CAAC,SAAS,GAAG;QAEjB,IAAI,CAAC,WAAW,IAAI,CAAC,WAAW,EAAE;YAChC,IAAI,CAAC,aAAa;YAClB,IAAI,CAAC,UAAU;QACjB,OAAO,IAAI,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE;YACvC,IAAI,CAAC,aAAa;QACpB;IACF;IAEO,eAAwB;QAC7B,OAAO,IAAI,CAAC,SAAS;IACvB;IAEO,iBAA0B;QAC/B,OAAO,IAAI,CAAC,WAAW;IACzB;IAEO,qBAA0C;QAC/C,OAAO,IAAI,CAAC,eAAe;IAC7B;IAEA,MAAa,aAAa;QACxB,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,UAAU,EAAE;YACvC,IAAI;gBACF,IAAI,OAAO,WAAW,IAAI,OAAO,WAAW,CAAC,oBAAoB,EAAE;oBACjE,MAAM,OAAO,WAAW,CAAC,oBAAoB;oBAC7C,QAAQ,GAAG,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF;QAEA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,eAAe,GAAG;IACzB;IAEA,yCAAyC;IACzC,MAAa,UAAU;QACrB,MAAM,IAAI,CAAC,aAAa;QACxB,MAAM,IAAI,CAAC,UAAU;IACvB;AACF;AAGO,MAAM,aAAa,IAAI;uCAGf", "debugId": null}}, {"offset": {"line": 1872, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/lib/gameLogic.ts"], "sourcesContent": ["// Game logic and data structures\n\nexport interface Recipe {\n  id: string\n  name: string\n  ingredients: { name: string; quantity: number }[]\n  bakingTime: number // in seconds\n  difficulty: number // 1-5\n  unlockLevel: number\n  basePrice: number\n  category: 'cookies' | 'cakes' | 'bread' | 'pastries'\n}\n\nexport const RECIPES: Recipe[] = [\n  {\n    id: 'chocolate_chip_cookies',\n    name: 'Chocolate Chip Cookies',\n    ingredients: [\n      { name: 'Flour', quantity: 2 },\n      { name: 'Sugar', quantity: 1 },\n      { name: 'Butter', quantity: 1 },\n      { name: 'Chocolate Chips', quantity: 1 }\n    ],\n    bakingTime: 45,\n    difficulty: 1,\n    unlockLevel: 1,\n    basePrice: 25,\n    category: 'cookies'\n  },\n  {\n    id: 'vanilla_muffins',\n    name: 'Vanilla Muffins',\n    ingredients: [\n      { name: 'Flour', quantity: 2 },\n      { name: 'Sugar', quantity: 1 },\n      { name: 'Eggs', quantity: 1 },\n      { name: 'Vanilla', quantity: 1 }\n    ],\n    bakingTime: 60,\n    difficulty: 1,\n    unlockLevel: 1,\n    basePrice: 20,\n    category: 'cakes'\n  },\n  {\n    id: 'cinnamon_rolls',\n    name: 'Cinnamon Rolls',\n    ingredients: [\n      { name: 'Flour', quantity: 3 },\n      { name: 'Sugar', quantity: 2 },\n      { name: 'Butter', quantity: 2 },\n      { name: 'Eggs', quantity: 1 }\n    ],\n    bakingTime: 90,\n    difficulty: 2,\n    unlockLevel: 2,\n    basePrice: 35,\n    category: 'pastries'\n  },\n  {\n    id: 'chocolate_brownies',\n    name: 'Chocolate Brownies',\n    ingredients: [\n      { name: 'Flour', quantity: 2 },\n      { name: 'Sugar', quantity: 2 },\n      { name: 'Butter', quantity: 1 },\n      { name: 'Chocolate Chips', quantity: 2 }\n    ],\n    bakingTime: 75,\n    difficulty: 2,\n    unlockLevel: 2,\n    basePrice: 30,\n    category: 'cakes'\n  },\n  {\n    id: 'sourdough_bread',\n    name: 'Sourdough Bread',\n    ingredients: [\n      { name: 'Flour', quantity: 4 },\n      { name: 'Salt', quantity: 1 }\n    ],\n    bakingTime: 180,\n    difficulty: 3,\n    unlockLevel: 3,\n    basePrice: 45,\n    category: 'bread'\n  }\n]\n\nexport const CUSTOMER_NAMES = [\n  'Alice Johnson', 'Bob Smith', 'Carol Davis', 'David Wilson',\n  'Emma Brown', 'Frank Miller', 'Grace Taylor', 'Henry Anderson',\n  'Ivy Thomas', 'Jack Martinez', 'Kate Garcia', 'Liam Rodriguez',\n  'Mia Lopez', 'Noah Gonzalez', 'Olivia Hernandez', 'Paul Perez',\n  'Quinn Turner', 'Ruby Phillips', 'Sam Campbell', 'Tina Parker'\n]\n\nexport function generateRandomOrder(playerLevel: number): {\n  id: string\n  customerName: string\n  items: string[]\n  timeLimit: number\n  reward: number\n  status: 'pending'\n  difficulty: number\n} {\n  // Filter recipes based on player level\n  const availableRecipes = RECIPES.filter(recipe => recipe.unlockLevel <= playerLevel)\n  \n  if (availableRecipes.length === 0) {\n    // Fallback to basic recipe\n    availableRecipes.push(RECIPES[0])\n  }\n\n  // Select random recipe(s)\n  const numItems = Math.random() < 0.7 ? 1 : Math.random() < 0.9 ? 2 : 3\n  const selectedRecipes: Recipe[] = []\n  \n  for (let i = 0; i < numItems; i++) {\n    const recipe = availableRecipes[Math.floor(Math.random() * availableRecipes.length)]\n    selectedRecipes.push(recipe)\n  }\n\n  // Calculate order properties\n  const totalDifficulty = selectedRecipes.reduce((sum, recipe) => sum + recipe.difficulty, 0)\n  const avgDifficulty = Math.ceil(totalDifficulty / selectedRecipes.length)\n  const totalBasePrice = selectedRecipes.reduce((sum, recipe) => sum + recipe.basePrice, 0)\n  \n  // Add some randomness to price and time\n  const priceMultiplier = 0.8 + Math.random() * 0.4 // 0.8 to 1.2\n  const timeMultiplier = 1.5 + Math.random() * 1.0 // 1.5 to 2.5\n  \n  const reward = Math.floor(totalBasePrice * priceMultiplier)\n  const baseTime = selectedRecipes.reduce((sum, recipe) => sum + recipe.bakingTime, 0)\n  const timeLimit = Math.floor(baseTime * timeMultiplier)\n\n  return {\n    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n    customerName: CUSTOMER_NAMES[Math.floor(Math.random() * CUSTOMER_NAMES.length)],\n    items: selectedRecipes.map(recipe => recipe.name),\n    timeLimit,\n    reward,\n    status: 'pending',\n    difficulty: Math.min(5, avgDifficulty)\n  }\n}\n\nexport function calculateExperienceReward(difficulty: number, timeBonus: boolean = false): number {\n  const baseExp = difficulty * 10\n  const bonus = timeBonus ? Math.floor(baseExp * 0.5) : 0\n  return baseExp + bonus\n}\n\nexport function calculateLevelRequirement(level: number): number {\n  return level * 100 + (level - 1) * 50\n}\n\nexport function canCraftRecipe(recipe: Recipe, inventory: { name: string; quantity: number }[]): boolean {\n  return recipe.ingredients.every(ingredient => {\n    const inventoryItem = inventory.find(item => item.name === ingredient.name)\n    return inventoryItem && inventoryItem.quantity >= ingredient.quantity\n  })\n}\n\nexport function getRecipeById(id: string): Recipe | undefined {\n  return RECIPES.find(recipe => recipe.id === id)\n}\n\nexport function getAvailableRecipes(playerLevel: number): Recipe[] {\n  return RECIPES.filter(recipe => recipe.unlockLevel <= playerLevel)\n}\n"], "names": [], "mappings": "AAAA,iCAAiC;;;;;;;;;;;AAa1B,MAAM,UAAoB;IAC/B;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YACX;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAU,UAAU;YAAE;YAC9B;gBAAE,MAAM;gBAAmB,UAAU;YAAE;SACxC;QACD,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YACX;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAQ,UAAU;YAAE;YAC5B;gBAAE,MAAM;gBAAW,UAAU;YAAE;SAChC;QACD,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YACX;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAU,UAAU;YAAE;YAC9B;gBAAE,MAAM;gBAAQ,UAAU;YAAE;SAC7B;QACD,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YACX;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAU,UAAU;YAAE;YAC9B;gBAAE,MAAM;gBAAmB,UAAU;YAAE;SACxC;QACD,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,WAAW;QACX,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;YACX;gBAAE,MAAM;gBAAS,UAAU;YAAE;YAC7B;gBAAE,MAAM;gBAAQ,UAAU;YAAE;SAC7B;QACD,YAAY;QACZ,YAAY;QACZ,aAAa;QACb,WAAW;QACX,UAAU;IACZ;CACD;AAEM,MAAM,iBAAiB;IAC5B;IAAiB;IAAa;IAAe;IAC7C;IAAc;IAAgB;IAAgB;IAC9C;IAAc;IAAiB;IAAe;IAC9C;IAAa;IAAiB;IAAoB;IAClD;IAAgB;IAAiB;IAAgB;CAClD;AAEM,SAAS,oBAAoB,WAAmB;IASrD,uCAAuC;IACvC,MAAM,mBAAmB,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,WAAW,IAAI;IAExE,IAAI,iBAAiB,MAAM,KAAK,GAAG;QACjC,2BAA2B;QAC3B,iBAAiB,IAAI,CAAC,OAAO,CAAC,EAAE;IAClC;IAEA,0BAA0B;IAC1B,MAAM,WAAW,KAAK,MAAM,KAAK,MAAM,IAAI,KAAK,MAAM,KAAK,MAAM,IAAI;IACrE,MAAM,kBAA4B,EAAE;IAEpC,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,IAAK;QACjC,MAAM,SAAS,gBAAgB,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,iBAAiB,MAAM,EAAE;QACpF,gBAAgB,IAAI,CAAC;IACvB;IAEA,6BAA6B;IAC7B,MAAM,kBAAkB,gBAAgB,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,UAAU,EAAE;IACzF,MAAM,gBAAgB,KAAK,IAAI,CAAC,kBAAkB,gBAAgB,MAAM;IACxE,MAAM,iBAAiB,gBAAgB,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,SAAS,EAAE;IAEvF,wCAAwC;IACxC,MAAM,kBAAkB,MAAM,KAAK,MAAM,KAAK,IAAI,aAAa;;IAC/D,MAAM,iBAAiB,MAAM,KAAK,MAAM,KAAK,IAAI,aAAa;;IAE9D,MAAM,SAAS,KAAK,KAAK,CAAC,iBAAiB;IAC3C,MAAM,WAAW,gBAAgB,MAAM,CAAC,CAAC,KAAK,SAAW,MAAM,OAAO,UAAU,EAAE;IAClF,MAAM,YAAY,KAAK,KAAK,CAAC,WAAW;IAExC,OAAO;QACL,IAAI,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QACjE,cAAc,cAAc,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,eAAe,MAAM,EAAE;QAC/E,OAAO,gBAAgB,GAAG,CAAC,CAAA,SAAU,OAAO,IAAI;QAChD;QACA;QACA,QAAQ;QACR,YAAY,KAAK,GAAG,CAAC,GAAG;IAC1B;AACF;AAEO,SAAS,0BAA0B,UAAkB,EAAE,YAAqB,KAAK;IACtF,MAAM,UAAU,aAAa;IAC7B,MAAM,QAAQ,YAAY,KAAK,KAAK,CAAC,UAAU,OAAO;IACtD,OAAO,UAAU;AACnB;AAEO,SAAS,0BAA0B,KAAa;IACrD,OAAO,QAAQ,MAAM,CAAC,QAAQ,CAAC,IAAI;AACrC;AAEO,SAAS,eAAe,MAAc,EAAE,SAA+C;IAC5F,OAAO,OAAO,WAAW,CAAC,KAAK,CAAC,CAAA;QAC9B,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,IAAI;QAC1E,OAAO,iBAAiB,cAAc,QAAQ,IAAI,WAAW,QAAQ;IACvE;AACF;AAEO,SAAS,cAAc,EAAU;IACtC,OAAO,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,EAAE,KAAK;AAC9C;AAEO,SAAS,oBAAoB,WAAmB;IACrD,OAAO,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,WAAW,IAAI;AACxD", "debugId": null}}, {"offset": {"line": 2094, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/lib/progressionSystem.ts"], "sourcesContent": ["// Advanced progression system for Bake It Out\n\nexport interface LevelReward {\n  type: 'recipe' | 'equipment' | 'money' | 'skill_point' | 'achievement'\n  id: string\n  name: string\n  description: string\n  value?: number\n}\n\nexport interface PlayerLevel {\n  level: number\n  experience: number\n  experienceRequired: number\n  totalExperience: number\n  rewards: LevelReward[]\n}\n\nexport interface Achievement {\n  id: string\n  name: string\n  description: string\n  icon: string\n  category: 'baking' | 'business' | 'efficiency' | 'collection' | 'special'\n  requirements: {\n    type: 'orders_completed' | 'money_earned' | 'recipes_unlocked' | 'level_reached' | 'items_baked' | 'equipment_owned'\n    target: number\n    current?: number\n  }[]\n  reward: LevelReward\n  unlocked: boolean\n  completed: boolean\n}\n\nexport interface SkillTree {\n  id: string\n  name: string\n  description: string\n  icon: string\n  category: 'efficiency' | 'automation' | 'quality' | 'business'\n  level: number\n  maxLevel: number\n  cost: number\n  requirements: {\n    playerLevel?: number\n    skills?: string[]\n    achievements?: string[]\n  }\n  effects: {\n    type: 'baking_speed' | 'money_multiplier' | 'xp_multiplier' | 'ingredient_efficiency' | 'automation_unlock'\n    value: number\n  }[]\n}\n\n// Experience calculation with exponential growth\nexport function calculateExperienceRequired(level: number): number {\n  if (level <= 1) return 0\n  return Math.floor(100 * Math.pow(1.15, level - 1))\n}\n\nexport function calculateTotalExperienceForLevel(level: number): number {\n  let total = 0\n  for (let i = 1; i <= level; i++) {\n    total += calculateExperienceRequired(i)\n  }\n  return total\n}\n\nexport function getLevelFromExperience(experience: number): PlayerLevel {\n  let level = 1\n  let totalExp = 0\n  \n  while (true) {\n    const expRequired = calculateExperienceRequired(level + 1)\n    if (totalExp + expRequired > experience) {\n      break\n    }\n    totalExp += expRequired\n    level++\n  }\n  \n  const expRequired = calculateExperienceRequired(level + 1)\n  const currentLevelExp = experience - totalExp\n  \n  return {\n    level,\n    experience: currentLevelExp,\n    experienceRequired: expRequired,\n    totalExperience: experience,\n    rewards: getLevelRewards(level)\n  }\n}\n\nexport function getLevelRewards(level: number): LevelReward[] {\n  const rewards: LevelReward[] = []\n  \n  // Money rewards every level\n  rewards.push({\n    type: 'money',\n    id: `money_${level}`,\n    name: 'Level Bonus',\n    description: `Bonus money for reaching level ${level}`,\n    value: level * 25\n  })\n  \n  // Recipe unlocks at specific levels\n  const recipeUnlocks: Record<number, string[]> = {\n    2: ['cinnamon_rolls'],\n    3: ['chocolate_brownies', 'sourdough_bread'],\n    4: ['croissants'],\n    5: ['cheesecake'],\n    6: ['macarons'],\n    7: ['honey_glazed_donuts'],\n    8: ['sourdough_bread'],\n    9: ['chocolate_souffle'],\n    10: ['croquembouche'],\n    12: ['opera_cake'],\n    15: ['artisan_pizza_dough']\n  }\n  \n  if (recipeUnlocks[level]) {\n    recipeUnlocks[level].forEach(recipeId => {\n      rewards.push({\n        type: 'recipe',\n        id: recipeId,\n        name: 'New Recipe Unlocked',\n        description: `You can now bake ${recipeId.replace(/_/g, ' ')}`\n      })\n    })\n  }\n  \n  // Equipment unlocks\n  const equipmentUnlocks: Record<number, string[]> = {\n    3: ['professional_oven'],\n    4: ['auto_mixer'],\n    5: ['stand_mixer'],\n    6: ['auto_oven'],\n    7: ['conveyor_belt'],\n    8: ['advanced_auto_mixer'],\n    10: ['industrial_oven'],\n    12: ['smart_conveyor_system']\n  }\n  \n  if (equipmentUnlocks[level]) {\n    equipmentUnlocks[level].forEach(equipmentId => {\n      rewards.push({\n        type: 'equipment',\n        id: equipmentId,\n        name: 'New Equipment Available',\n        description: `${equipmentId.replace(/_/g, ' ')} is now available for purchase`\n      })\n    })\n  }\n  \n  // Skill points every 2 levels\n  if (level % 2 === 0) {\n    rewards.push({\n      type: 'skill_point',\n      id: `skill_point_${level}`,\n      name: 'Skill Point',\n      description: 'Use this to upgrade your skills in the technology tree',\n      value: 1\n    })\n  }\n  \n  return rewards\n}\n\nexport const ACHIEVEMENTS: Achievement[] = [\n  // Baking Achievements\n  {\n    id: 'first_order',\n    name: 'First Customer',\n    description: 'Complete your first order',\n    icon: '🎯',\n    category: 'baking',\n    requirements: [{ type: 'orders_completed', target: 1 }],\n    reward: { type: 'money', id: 'first_order_bonus', name: 'First Order Bonus', description: 'Bonus for first order', value: 50 },\n    unlocked: true,\n    completed: false\n  },\n  {\n    id: 'baker_apprentice',\n    name: 'Baker Apprentice',\n    description: 'Complete 10 orders',\n    icon: '👨‍🍳',\n    category: 'baking',\n    requirements: [{ type: 'orders_completed', target: 10 }],\n    reward: { type: 'skill_point', id: 'apprentice_skill', name: 'Skill Point', description: 'Gain 1 skill point', value: 1 },\n    unlocked: true,\n    completed: false\n  },\n  {\n    id: 'baker_journeyman',\n    name: 'Baker Journeyman',\n    description: 'Complete 50 orders',\n    icon: '👨‍🍳',\n    category: 'baking',\n    requirements: [{ type: 'orders_completed', target: 50 }],\n    reward: { type: 'money', id: 'journeyman_bonus', name: 'Journeyman Bonus', description: 'Large money bonus', value: 500 },\n    unlocked: true,\n    completed: false\n  },\n  {\n    id: 'master_baker',\n    name: 'Master Baker',\n    description: 'Complete 100 orders',\n    icon: '🏆',\n    category: 'baking',\n    requirements: [{ type: 'orders_completed', target: 100 }],\n    reward: { type: 'skill_point', id: 'master_skill', name: 'Master Skill Points', description: 'Gain 3 skill points', value: 3 },\n    unlocked: true,\n    completed: false\n  },\n  {\n    id: 'speed_baker',\n    name: 'Speed Baker',\n    description: 'Bake 100 items',\n    icon: '⚡',\n    category: 'efficiency',\n    requirements: [{ type: 'items_baked', target: 100 }],\n    reward: { type: 'skill_point', id: 'speed_skill', name: 'Speed Skill Point', description: 'Gain 1 skill point', value: 1 },\n    unlocked: true,\n    completed: false\n  },\n  {\n    id: 'money_maker',\n    name: 'Money Maker',\n    description: 'Earn $1000 total',\n    icon: '💰',\n    category: 'business',\n    requirements: [{ type: 'money_earned', target: 1000 }],\n    reward: { type: 'skill_point', id: 'money_maker_skill', name: 'Business Skill Point', description: 'Extra skill point for business success', value: 1 },\n    unlocked: true,\n    completed: false\n  },\n  {\n    id: 'recipe_collector',\n    name: 'Recipe Collector',\n    description: 'Unlock 5 different recipes',\n    icon: '📚',\n    category: 'collection',\n    requirements: [{ type: 'recipes_unlocked', target: 5 }],\n    reward: { type: 'money', id: 'recipe_bonus', name: 'Recipe Collection Bonus', description: 'Bonus for collecting recipes', value: 200 },\n    unlocked: true,\n    completed: false\n  },\n  {\n    id: 'level_master',\n    name: 'Level Master',\n    description: 'Reach level 10',\n    icon: '⭐',\n    category: 'special',\n    requirements: [{ type: 'level_reached', target: 10 }],\n    reward: { type: 'skill_point', id: 'level_master_skill', name: 'Master Level Bonus', description: 'Gain 2 skill points', value: 2 },\n    unlocked: true,\n    completed: false\n  },\n\n  // Additional Business Achievements\n  {\n    id: 'first_hundred',\n    name: 'First Hundred',\n    description: 'Earn $100 total',\n    icon: '💵',\n    category: 'business',\n    requirements: [{ type: 'money_earned', target: 100 }],\n    reward: { type: 'money', id: 'first_hundred_bonus', name: 'Business Bonus', description: 'Small business milestone bonus', value: 25 },\n    unlocked: true,\n    completed: false\n  },\n  {\n    id: 'entrepreneur',\n    name: 'Entrepreneur',\n    description: 'Earn $5000 total',\n    icon: '🏢',\n    category: 'business',\n    requirements: [{ type: 'money_earned', target: 5000 }],\n    reward: { type: 'skill_point', id: 'entrepreneur_skill', name: 'Business Skill Points', description: 'Gain 2 skill points', value: 2 },\n    unlocked: true,\n    completed: false\n  },\n\n  // Equipment Achievements\n  {\n    id: 'equipment_enthusiast',\n    name: 'Equipment Enthusiast',\n    description: 'Own 3 pieces of equipment',\n    icon: '⚙️',\n    category: 'collection',\n    requirements: [{ type: 'equipment_owned', target: 3 }],\n    reward: { type: 'money', id: 'equipment_bonus', name: 'Equipment Bonus', description: 'Equipment investment bonus', value: 300 },\n    unlocked: true,\n    completed: false\n  },\n\n  // Level Achievements\n  {\n    id: 'rising_star',\n    name: 'Rising Star',\n    description: 'Reach level 5',\n    icon: '🌟',\n    category: 'special',\n    requirements: [{ type: 'level_reached', target: 5 }],\n    reward: { type: 'skill_point', id: 'rising_star_skill', name: 'Rising Star Bonus', description: 'Gain 1 skill point', value: 1 },\n    unlocked: true,\n    completed: false\n  },\n  {\n    id: 'legendary_baker',\n    name: 'Legendary Baker',\n    description: 'Reach level 20',\n    icon: '👑',\n    category: 'special',\n    requirements: [{ type: 'level_reached', target: 20 }],\n    reward: { type: 'skill_point', id: 'legendary_skill', name: 'Legendary Bonus', description: 'Gain 5 skill points', value: 5 },\n    unlocked: true,\n    completed: false\n  }\n]\n\nexport const SKILL_TREE: SkillTree[] = [\n  {\n    id: 'baking_speed_1',\n    name: 'Quick Hands',\n    description: 'Increase baking speed by 10%',\n    icon: '⚡',\n    category: 'efficiency',\n    level: 0,\n    maxLevel: 3,\n    cost: 1,\n    requirements: { playerLevel: 2 },\n    effects: [{ type: 'baking_speed', value: 0.1 }]\n  },\n  {\n    id: 'money_bonus_1',\n    name: 'Business Sense',\n    description: 'Increase money earned by 15%',\n    icon: '💼',\n    category: 'business',\n    level: 0,\n    maxLevel: 3,\n    cost: 1,\n    requirements: { playerLevel: 3 },\n    effects: [{ type: 'money_multiplier', value: 0.15 }]\n  },\n  {\n    id: 'xp_bonus_1',\n    name: 'Fast Learner',\n    description: 'Increase experience gained by 20%',\n    icon: '📈',\n    category: 'efficiency',\n    level: 0,\n    maxLevel: 2,\n    cost: 2,\n    requirements: { playerLevel: 4 },\n    effects: [{ type: 'xp_multiplier', value: 0.2 }]\n  },\n  {\n    id: 'ingredient_efficiency_1',\n    name: 'Efficient Baker',\n    description: 'Use 10% fewer ingredients',\n    icon: '🌾',\n    category: 'efficiency',\n    level: 0,\n    maxLevel: 2,\n    cost: 2,\n    requirements: { playerLevel: 5, skills: ['baking_speed_1'] },\n    effects: [{ type: 'ingredient_efficiency', value: 0.1 }]\n  },\n  {\n    id: 'automation_unlock_1',\n    name: 'Automation Expert',\n    description: 'Unlock advanced automation features',\n    icon: '🤖',\n    category: 'automation',\n    level: 0,\n    maxLevel: 1,\n    cost: 3,\n    requirements: { playerLevel: 8, achievements: ['baker_apprentice'] },\n    effects: [{ type: 'automation_unlock', value: 1 }]\n  }\n]\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;;;;;AAuDvC,SAAS,4BAA4B,KAAa;IACvD,IAAI,SAAS,GAAG,OAAO;IACvB,OAAO,KAAK,KAAK,CAAC,MAAM,KAAK,GAAG,CAAC,MAAM,QAAQ;AACjD;AAEO,SAAS,iCAAiC,KAAa;IAC5D,IAAI,QAAQ;IACZ,IAAK,IAAI,IAAI,GAAG,KAAK,OAAO,IAAK;QAC/B,SAAS,4BAA4B;IACvC;IACA,OAAO;AACT;AAEO,SAAS,uBAAuB,UAAkB;IACvD,IAAI,QAAQ;IACZ,IAAI,WAAW;IAEf,MAAO,KAAM;QACX,MAAM,cAAc,4BAA4B,QAAQ;QACxD,IAAI,WAAW,cAAc,YAAY;YACvC;QACF;QACA,YAAY;QACZ;IACF;IAEA,MAAM,cAAc,4BAA4B,QAAQ;IACxD,MAAM,kBAAkB,aAAa;IAErC,OAAO;QACL;QACA,YAAY;QACZ,oBAAoB;QACpB,iBAAiB;QACjB,SAAS,gBAAgB;IAC3B;AACF;AAEO,SAAS,gBAAgB,KAAa;IAC3C,MAAM,UAAyB,EAAE;IAEjC,4BAA4B;IAC5B,QAAQ,IAAI,CAAC;QACX,MAAM;QACN,IAAI,CAAC,MAAM,EAAE,OAAO;QACpB,MAAM;QACN,aAAa,CAAC,+BAA+B,EAAE,OAAO;QACtD,OAAO,QAAQ;IACjB;IAEA,oCAAoC;IACpC,MAAM,gBAA0C;QAC9C,GAAG;YAAC;SAAiB;QACrB,GAAG;YAAC;YAAsB;SAAkB;QAC5C,GAAG;YAAC;SAAa;QACjB,GAAG;YAAC;SAAa;QACjB,GAAG;YAAC;SAAW;QACf,GAAG;YAAC;SAAsB;QAC1B,GAAG;YAAC;SAAkB;QACtB,GAAG;YAAC;SAAoB;QACxB,IAAI;YAAC;SAAgB;QACrB,IAAI;YAAC;SAAa;QAClB,IAAI;YAAC;SAAsB;IAC7B;IAEA,IAAI,aAAa,CAAC,MAAM,EAAE;QACxB,aAAa,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;YAC3B,QAAQ,IAAI,CAAC;gBACX,MAAM;gBACN,IAAI;gBACJ,MAAM;gBACN,aAAa,CAAC,iBAAiB,EAAE,SAAS,OAAO,CAAC,MAAM,MAAM;YAChE;QACF;IACF;IAEA,oBAAoB;IACpB,MAAM,mBAA6C;QACjD,GAAG;YAAC;SAAoB;QACxB,GAAG;YAAC;SAAa;QACjB,GAAG;YAAC;SAAc;QAClB,GAAG;YAAC;SAAY;QAChB,GAAG;YAAC;SAAgB;QACpB,GAAG;YAAC;SAAsB;QAC1B,IAAI;YAAC;SAAkB;QACvB,IAAI;YAAC;SAAwB;IAC/B;IAEA,IAAI,gBAAgB,CAAC,MAAM,EAAE;QAC3B,gBAAgB,CAAC,MAAM,CAAC,OAAO,CAAC,CAAA;YAC9B,QAAQ,IAAI,CAAC;gBACX,MAAM;gBACN,IAAI;gBACJ,MAAM;gBACN,aAAa,GAAG,YAAY,OAAO,CAAC,MAAM,KAAK,8BAA8B,CAAC;YAChF;QACF;IACF;IAEA,8BAA8B;IAC9B,IAAI,QAAQ,MAAM,GAAG;QACnB,QAAQ,IAAI,CAAC;YACX,MAAM;YACN,IAAI,CAAC,YAAY,EAAE,OAAO;YAC1B,MAAM;YACN,aAAa;YACb,OAAO;QACT;IACF;IAEA,OAAO;AACT;AAEO,MAAM,eAA8B;IACzC,sBAAsB;IACtB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAoB,QAAQ;YAAE;SAAE;QACvD,QAAQ;YAAE,MAAM;YAAS,IAAI;YAAqB,MAAM;YAAqB,aAAa;YAAyB,OAAO;QAAG;QAC7H,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAoB,QAAQ;YAAG;SAAE;QACxD,QAAQ;YAAE,MAAM;YAAe,IAAI;YAAoB,MAAM;YAAe,aAAa;YAAsB,OAAO;QAAE;QACxH,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAoB,QAAQ;YAAG;SAAE;QACxD,QAAQ;YAAE,MAAM;YAAS,IAAI;YAAoB,MAAM;YAAoB,aAAa;YAAqB,OAAO;QAAI;QACxH,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAoB,QAAQ;YAAI;SAAE;QACzD,QAAQ;YAAE,MAAM;YAAe,IAAI;YAAgB,MAAM;YAAuB,aAAa;YAAuB,OAAO;QAAE;QAC7H,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAe,QAAQ;YAAI;SAAE;QACpD,QAAQ;YAAE,MAAM;YAAe,IAAI;YAAe,MAAM;YAAqB,aAAa;YAAsB,OAAO;QAAE;QACzH,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAgB,QAAQ;YAAK;SAAE;QACtD,QAAQ;YAAE,MAAM;YAAe,IAAI;YAAqB,MAAM;YAAwB,aAAa;YAA0C,OAAO;QAAE;QACtJ,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAoB,QAAQ;YAAE;SAAE;QACvD,QAAQ;YAAE,MAAM;YAAS,IAAI;YAAgB,MAAM;YAA2B,aAAa;YAAgC,OAAO;QAAI;QACtI,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAiB,QAAQ;YAAG;SAAE;QACrD,QAAQ;YAAE,MAAM;YAAe,IAAI;YAAsB,MAAM;YAAsB,aAAa;YAAuB,OAAO;QAAE;QAClI,UAAU;QACV,WAAW;IACb;IAEA,mCAAmC;IACnC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAgB,QAAQ;YAAI;SAAE;QACrD,QAAQ;YAAE,MAAM;YAAS,IAAI;YAAuB,MAAM;YAAkB,aAAa;YAAkC,OAAO;QAAG;QACrI,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAgB,QAAQ;YAAK;SAAE;QACtD,QAAQ;YAAE,MAAM;YAAe,IAAI;YAAsB,MAAM;YAAyB,aAAa;YAAuB,OAAO;QAAE;QACrI,UAAU;QACV,WAAW;IACb;IAEA,yBAAyB;IACzB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAmB,QAAQ;YAAE;SAAE;QACtD,QAAQ;YAAE,MAAM;YAAS,IAAI;YAAmB,MAAM;YAAmB,aAAa;YAA8B,OAAO;QAAI;QAC/H,UAAU;QACV,WAAW;IACb;IAEA,qBAAqB;IACrB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAiB,QAAQ;YAAE;SAAE;QACpD,QAAQ;YAAE,MAAM;YAAe,IAAI;YAAqB,MAAM;YAAqB,aAAa;YAAsB,OAAO;QAAE;QAC/H,UAAU;QACV,WAAW;IACb;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,cAAc;YAAC;gBAAE,MAAM;gBAAiB,QAAQ;YAAG;SAAE;QACrD,QAAQ;YAAE,MAAM;YAAe,IAAI;YAAmB,MAAM;YAAmB,aAAa;YAAuB,OAAO;QAAE;QAC5H,UAAU;QACV,WAAW;IACb;CACD;AAEM,MAAM,aAA0B;IACrC;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,MAAM;QACN,cAAc;YAAE,aAAa;QAAE;QAC/B,SAAS;YAAC;gBAAE,MAAM;gBAAgB,OAAO;YAAI;SAAE;IACjD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,MAAM;QACN,cAAc;YAAE,aAAa;QAAE;QAC/B,SAAS;YAAC;gBAAE,MAAM;gBAAoB,OAAO;YAAK;SAAE;IACtD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,MAAM;QACN,cAAc;YAAE,aAAa;QAAE;QAC/B,SAAS;YAAC;gBAAE,MAAM;gBAAiB,OAAO;YAAI;SAAE;IAClD;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,MAAM;QACN,cAAc;YAAE,aAAa;YAAG,QAAQ;gBAAC;aAAiB;QAAC;QAC3D,SAAS;YAAC;gBAAE,MAAM;gBAAyB,OAAO;YAAI;SAAE;IAC1D;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,UAAU;QACV,OAAO;QACP,UAAU;QACV,MAAM;QACN,cAAc;YAAE,aAAa;YAAG,cAAc;gBAAC;aAAmB;QAAC;QACnE,SAAS;YAAC;gBAAE,MAAM;gBAAqB,OAAO;YAAE;SAAE;IACpD;CACD", "debugId": null}}, {"offset": {"line": 2640, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/lib/automationSystem.ts"], "sourcesContent": ["// Automation system for Bake It Out\n\nexport interface AutomationSettings {\n  enabled: boolean\n  autoStart: boolean\n  preferredRecipes: string[]\n  maxConcurrentJobs: number\n  priorityMode: 'speed' | 'efficiency' | 'profit'\n  ingredientThreshold: number // Minimum ingredients before stopping\n}\n\nexport interface AutomationJob {\n  id: string\n  equipmentId: string\n  recipeId: string\n  startTime: number\n  duration: number\n  status: 'queued' | 'running' | 'completed' | 'failed'\n  ingredients: { name: string; quantity: number }[]\n  efficiency: number\n}\n\nexport interface ConveyorBelt {\n  id: string\n  name: string\n  level: number\n  speed: number // items per minute\n  capacity: number // max items on belt\n  connections: string[] // connected equipment IDs\n  items: ConveyorItem[]\n  isActive: boolean\n}\n\nexport interface ConveyorItem {\n  id: string\n  recipeId: string\n  position: number // 0-1 along the belt\n  targetEquipmentId: string\n}\n\nexport interface AutomationUpgrade {\n  id: string\n  name: string\n  description: string\n  type: 'speed' | 'efficiency' | 'capacity' | 'intelligence'\n  cost: number\n  unlockLevel: number\n  effects: {\n    speedMultiplier?: number\n    efficiencyBonus?: number\n    capacityIncrease?: number\n    autoQueueing?: boolean\n    smartPrioritization?: boolean\n  }\n}\n\nexport const AUTOMATION_UPGRADES: AutomationUpgrade[] = [\n  {\n    id: 'auto_queue_basic',\n    name: 'Basic Auto-Queue',\n    description: 'Equipment automatically starts the next recipe when finished',\n    type: 'intelligence',\n    cost: 500,\n    unlockLevel: 4,\n    effects: { autoQueueing: true }\n  },\n  {\n    id: 'efficiency_boost_1',\n    name: 'Efficiency Boost I',\n    description: 'Automated equipment uses 10% fewer ingredients',\n    type: 'efficiency',\n    cost: 750,\n    unlockLevel: 5,\n    effects: { efficiencyBonus: 0.1 }\n  },\n  {\n    id: 'speed_boost_1',\n    name: 'Speed Boost I',\n    description: 'Automated equipment works 15% faster',\n    type: 'speed',\n    cost: 1000,\n    unlockLevel: 6,\n    effects: { speedMultiplier: 1.15 }\n  },\n  {\n    id: 'smart_prioritization',\n    name: 'Smart Prioritization',\n    description: 'Automation prioritizes orders based on profit and urgency',\n    type: 'intelligence',\n    cost: 1500,\n    unlockLevel: 8,\n    effects: { smartPrioritization: true }\n  },\n  {\n    id: 'efficiency_boost_2',\n    name: 'Efficiency Boost II',\n    description: 'Automated equipment uses 20% fewer ingredients',\n    type: 'efficiency',\n    cost: 2000,\n    unlockLevel: 10,\n    effects: { efficiencyBonus: 0.2 }\n  },\n  {\n    id: 'speed_boost_2',\n    name: 'Speed Boost II',\n    description: 'Automated equipment works 30% faster',\n    type: 'speed',\n    cost: 2500,\n    unlockLevel: 12,\n    effects: { speedMultiplier: 1.3 }\n  }\n]\n\nexport function calculateAutomationEfficiency(\n  baseEfficiency: number,\n  automationLevel: number,\n  upgrades: string[],\n  skillBonuses: number = 0\n): number {\n  let efficiency = baseEfficiency\n\n  // Automation level bonus\n  efficiency *= (1 + automationLevel * 0.1)\n\n  // Upgrade bonuses\n  upgrades.forEach(upgradeId => {\n    const upgrade = AUTOMATION_UPGRADES.find(u => u.id === upgradeId)\n    if (upgrade?.effects.efficiencyBonus) {\n      efficiency *= (1 + upgrade.effects.efficiencyBonus)\n    }\n  })\n\n  // Skill bonuses\n  efficiency *= (1 + skillBonuses)\n\n  return Math.min(efficiency, 2.0) // Cap at 200% efficiency\n}\n\nexport function calculateAutomationSpeed(\n  baseSpeed: number,\n  automationLevel: number,\n  upgrades: string[],\n  skillBonuses: number = 0\n): number {\n  let speed = baseSpeed\n\n  // Automation level bonus\n  speed *= (1 + automationLevel * 0.05)\n\n  // Upgrade bonuses\n  upgrades.forEach(upgradeId => {\n    const upgrade = AUTOMATION_UPGRADES.find(u => u.id === upgradeId)\n    if (upgrade?.effects.speedMultiplier) {\n      speed *= upgrade.effects.speedMultiplier\n    }\n  })\n\n  // Skill bonuses\n  speed *= (1 + skillBonuses)\n\n  return speed\n}\n\nexport function canAutomate(equipmentType: string, automationLevel: number): boolean {\n  const automationRequirements: Record<string, number> = {\n    'oven': 1,\n    'mixer': 1,\n    'counter': 2,\n    'auto_oven': 0,\n    'auto_mixer': 0,\n    'conveyor': 0\n  }\n\n  return automationLevel >= (automationRequirements[equipmentType] || 999)\n}\n\nexport function generateAutomationJob(\n  equipmentId: string,\n  recipeId: string,\n  recipe: any,\n  efficiency: number\n): AutomationJob {\n  const adjustedDuration = Math.floor(recipe.bakingTime / efficiency)\n  const adjustedIngredients = recipe.ingredients.map((ing: any) => ({\n    ...ing,\n    quantity: Math.ceil(ing.quantity * (1 - (efficiency - 1) * 0.1))\n  }))\n\n  return {\n    id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n    equipmentId,\n    recipeId,\n    startTime: Date.now(),\n    duration: adjustedDuration,\n    status: 'queued',\n    ingredients: adjustedIngredients,\n    efficiency\n  }\n}\n\nexport function selectOptimalRecipe(\n  availableRecipes: any[],\n  inventory: any[],\n  priorityMode: 'speed' | 'efficiency' | 'profit',\n  currentOrders: any[]\n): string | null {\n  const craftableRecipes = availableRecipes.filter(recipe =>\n    recipe.ingredients.every((ingredient: any) => {\n      const inventoryItem = inventory.find(item => item.name === ingredient.name)\n      return inventoryItem && inventoryItem.quantity >= ingredient.quantity\n    })\n  )\n\n  if (craftableRecipes.length === 0) return null\n\n  switch (priorityMode) {\n    case 'speed':\n      return craftableRecipes.reduce((fastest, recipe) =>\n        recipe.bakingTime < fastest.bakingTime ? recipe : fastest\n      ).id\n\n    case 'profit':\n      return craftableRecipes.reduce((mostProfitable, recipe) =>\n        recipe.basePrice > mostProfitable.basePrice ? recipe : mostProfitable\n      ).id\n\n    case 'efficiency':\n      // Prioritize recipes needed for current orders\n      const neededRecipes = currentOrders.flatMap(order => order.items)\n      const neededCraftable = craftableRecipes.filter(recipe =>\n        neededRecipes.includes(recipe.name)\n      )\n      \n      if (neededCraftable.length > 0) {\n        return neededCraftable[0].id\n      }\n      \n      return craftableRecipes[0].id\n\n    default:\n      return craftableRecipes[0].id\n  }\n}\n\nexport function updateConveyorBelt(\n  belt: ConveyorBelt,\n  deltaTime: number\n): ConveyorBelt {\n  const speed = belt.speed / 60 // convert to items per second\n  const moveDistance = speed * deltaTime\n\n  const updatedItems = belt.items.map(item => ({\n    ...item,\n    position: Math.min(1, item.position + moveDistance)\n  }))\n\n  // Remove items that reached the end\n  const activeItems = updatedItems.filter(item => item.position < 1)\n\n  return {\n    ...belt,\n    items: activeItems\n  }\n}\n\nexport function addItemToConveyor(\n  belt: ConveyorBelt,\n  item: Omit<ConveyorItem, 'position'>\n): ConveyorBelt {\n  if (belt.items.length >= belt.capacity) {\n    return belt // Belt is full\n  }\n\n  const newItem: ConveyorItem = {\n    ...item,\n    position: 0\n  }\n\n  return {\n    ...belt,\n    items: [...belt.items, newItem]\n  }\n}\n"], "names": [], "mappings": "AAAA,oCAAoC;;;;;;;;;;;AAwD7B,MAAM,sBAA2C;IACtD;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,aAAa;QACb,SAAS;YAAE,cAAc;QAAK;IAChC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,aAAa;QACb,SAAS;YAAE,iBAAiB;QAAI;IAClC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,aAAa;QACb,SAAS;YAAE,iBAAiB;QAAK;IACnC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,aAAa;QACb,SAAS;YAAE,qBAAqB;QAAK;IACvC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,aAAa;QACb,SAAS;YAAE,iBAAiB;QAAI;IAClC;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,MAAM;QACN,MAAM;QACN,aAAa;QACb,SAAS;YAAE,iBAAiB;QAAI;IAClC;CACD;AAEM,SAAS,8BACd,cAAsB,EACtB,eAAuB,EACvB,QAAkB,EAClB,eAAuB,CAAC;IAExB,IAAI,aAAa;IAEjB,yBAAyB;IACzB,cAAe,IAAI,kBAAkB;IAErC,kBAAkB;IAClB,SAAS,OAAO,CAAC,CAAA;QACf,MAAM,UAAU,oBAAoB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACvD,IAAI,SAAS,QAAQ,iBAAiB;YACpC,cAAe,IAAI,QAAQ,OAAO,CAAC,eAAe;QACpD;IACF;IAEA,gBAAgB;IAChB,cAAe,IAAI;IAEnB,OAAO,KAAK,GAAG,CAAC,YAAY,KAAK,yBAAyB;;AAC5D;AAEO,SAAS,yBACd,SAAiB,EACjB,eAAuB,EACvB,QAAkB,EAClB,eAAuB,CAAC;IAExB,IAAI,QAAQ;IAEZ,yBAAyB;IACzB,SAAU,IAAI,kBAAkB;IAEhC,kBAAkB;IAClB,SAAS,OAAO,CAAC,CAAA;QACf,MAAM,UAAU,oBAAoB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACvD,IAAI,SAAS,QAAQ,iBAAiB;YACpC,SAAS,QAAQ,OAAO,CAAC,eAAe;QAC1C;IACF;IAEA,gBAAgB;IAChB,SAAU,IAAI;IAEd,OAAO;AACT;AAEO,SAAS,YAAY,aAAqB,EAAE,eAAuB;IACxE,MAAM,yBAAiD;QACrD,QAAQ;QACR,SAAS;QACT,WAAW;QACX,aAAa;QACb,cAAc;QACd,YAAY;IACd;IAEA,OAAO,mBAAmB,CAAC,sBAAsB,CAAC,cAAc,IAAI,GAAG;AACzE;AAEO,SAAS,sBACd,WAAmB,EACnB,QAAgB,EAChB,MAAW,EACX,UAAkB;IAElB,MAAM,mBAAmB,KAAK,KAAK,CAAC,OAAO,UAAU,GAAG;IACxD,MAAM,sBAAsB,OAAO,WAAW,CAAC,GAAG,CAAC,CAAC,MAAa,CAAC;YAChE,GAAG,GAAG;YACN,UAAU,KAAK,IAAI,CAAC,IAAI,QAAQ,GAAG,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI,GAAG;QAChE,CAAC;IAED,OAAO;QACL,IAAI,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;QACjE;QACA;QACA,WAAW,KAAK,GAAG;QACnB,UAAU;QACV,QAAQ;QACR,aAAa;QACb;IACF;AACF;AAEO,SAAS,oBACd,gBAAuB,EACvB,SAAgB,EAChB,YAA+C,EAC/C,aAAoB;IAEpB,MAAM,mBAAmB,iBAAiB,MAAM,CAAC,CAAA,SAC/C,OAAO,WAAW,CAAC,KAAK,CAAC,CAAC;YACxB,MAAM,gBAAgB,UAAU,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,WAAW,IAAI;YAC1E,OAAO,iBAAiB,cAAc,QAAQ,IAAI,WAAW,QAAQ;QACvE;IAGF,IAAI,iBAAiB,MAAM,KAAK,GAAG,OAAO;IAE1C,OAAQ;QACN,KAAK;YACH,OAAO,iBAAiB,MAAM,CAAC,CAAC,SAAS,SACvC,OAAO,UAAU,GAAG,QAAQ,UAAU,GAAG,SAAS,SAClD,EAAE;QAEN,KAAK;YACH,OAAO,iBAAiB,MAAM,CAAC,CAAC,gBAAgB,SAC9C,OAAO,SAAS,GAAG,eAAe,SAAS,GAAG,SAAS,gBACvD,EAAE;QAEN,KAAK;YACH,+CAA+C;YAC/C,MAAM,gBAAgB,cAAc,OAAO,CAAC,CAAA,QAAS,MAAM,KAAK;YAChE,MAAM,kBAAkB,iBAAiB,MAAM,CAAC,CAAA,SAC9C,cAAc,QAAQ,CAAC,OAAO,IAAI;YAGpC,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,OAAO,eAAe,CAAC,EAAE,CAAC,EAAE;YAC9B;YAEA,OAAO,gBAAgB,CAAC,EAAE,CAAC,EAAE;QAE/B;YACE,OAAO,gBAAgB,CAAC,EAAE,CAAC,EAAE;IACjC;AACF;AAEO,SAAS,mBACd,IAAkB,EAClB,SAAiB;IAEjB,MAAM,QAAQ,KAAK,KAAK,GAAG,GAAG,8BAA8B;;IAC5D,MAAM,eAAe,QAAQ;IAE7B,MAAM,eAAe,KAAK,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;YAC3C,GAAG,IAAI;YACP,UAAU,KAAK,GAAG,CAAC,GAAG,KAAK,QAAQ,GAAG;QACxC,CAAC;IAED,oCAAoC;IACpC,MAAM,cAAc,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,GAAG;IAEhE,OAAO;QACL,GAAG,IAAI;QACP,OAAO;IACT;AACF;AAEO,SAAS,kBACd,IAAkB,EAClB,IAAoC;IAEpC,IAAI,KAAK,KAAK,CAAC,MAAM,IAAI,KAAK,QAAQ,EAAE;QACtC,OAAO,KAAK,eAAe;;IAC7B;IAEA,MAAM,UAAwB;QAC5B,GAAG,IAAI;QACP,UAAU;IACZ;IAEA,OAAO;QACL,GAAG,IAAI;QACP,OAAO;eAAI,KAAK,KAAK;YAAE;SAAQ;IACjC;AACF", "debugId": null}}, {"offset": {"line": 2837, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/lib/saveSystem.ts"], "sourcesContent": ["// Save/Load system for Bake It Out\n\nexport interface GameSave {\n  version: string\n  timestamp: number\n  player: {\n    level: number\n    experience: number\n    money: number\n    skillPoints: number\n    totalMoneyEarned: number\n    totalOrdersCompleted: number\n    totalItemsBaked: number\n    unlockedRecipes: string[]\n    automationUpgrades: string[]\n  }\n  equipment: any[]\n  inventory: any[]\n  achievements: any[]\n  skills: any[]\n  automationSettings: any\n  gameSettings: {\n    language: string\n    soundEnabled: boolean\n    musicEnabled: boolean\n    notificationsEnabled: boolean\n    autoSaveEnabled: boolean\n  }\n  bakeries: BakeryLocation[]\n  currentBakeryId: string\n}\n\nexport interface BakeryLocation {\n  id: string\n  name: string\n  location: string\n  specialization: 'general' | 'cookies' | 'cakes' | 'bread' | 'pastries'\n  level: number\n  equipment: any[]\n  inventory: any[]\n  orders: any[]\n  automationJobs: any[]\n  conveyorBelts: any[]\n  unlocked: boolean\n  purchaseCost: number\n}\n\nexport interface CloudSaveMetadata {\n  id: string\n  userId: string\n  deviceId: string\n  lastModified: number\n  gameVersion: string\n  bakeryCount: number\n  playerLevel: number\n}\n\nconst SAVE_VERSION = '1.0.0'\nconst LOCAL_STORAGE_KEY = 'bakeItOut_gameSave'\nconst AUTO_SAVE_INTERVAL = 30000 // 30 seconds\n\nexport class SaveSystem {\n  private autoSaveInterval: NodeJS.Timeout | null = null\n  private cloudSyncEnabled = false\n\n  constructor() {\n    this.initializeAutoSave()\n  }\n\n  // Local Storage Operations\n  saveToLocal(gameData: Partial<GameSave>): boolean {\n    try {\n      const save: GameSave = {\n        version: SAVE_VERSION,\n        timestamp: Date.now(),\n        ...gameData\n      } as GameSave\n\n      const saveString = JSON.stringify(save)\n      localStorage.setItem(LOCAL_STORAGE_KEY, saveString)\n      \n      console.log('Game saved to local storage')\n      return true\n    } catch (error) {\n      console.error('Failed to save game to local storage:', error)\n      return false\n    }\n  }\n\n  loadFromLocal(): GameSave | null {\n    try {\n      const saveString = localStorage.getItem(LOCAL_STORAGE_KEY)\n      if (!saveString) return null\n\n      const save: GameSave = JSON.parse(saveString)\n      \n      // Version compatibility check\n      if (save.version !== SAVE_VERSION) {\n        console.warn('Save version mismatch, attempting migration')\n        return this.migrateSave(save)\n      }\n\n      console.log('Game loaded from local storage')\n      return save\n    } catch (error) {\n      console.error('Failed to load game from local storage:', error)\n      return null\n    }\n  }\n\n  deleteLocalSave(): boolean {\n    try {\n      localStorage.removeItem(LOCAL_STORAGE_KEY)\n      console.log('Local save deleted')\n      return true\n    } catch (error) {\n      console.error('Failed to delete local save:', error)\n      return false\n    }\n  }\n\n  // Auto-save functionality\n  initializeAutoSave() {\n    if (typeof window !== 'undefined') {\n      this.autoSaveInterval = setInterval(() => {\n        this.triggerAutoSave()\n      }, AUTO_SAVE_INTERVAL)\n    }\n  }\n\n  private triggerAutoSave() {\n    // This would be called by the game context to auto-save\n    const event = new CustomEvent('autoSave')\n    window.dispatchEvent(event)\n  }\n\n  stopAutoSave() {\n    if (this.autoSaveInterval) {\n      clearInterval(this.autoSaveInterval)\n      this.autoSaveInterval = null\n    }\n  }\n\n  // Cloud Save Operations (placeholder for future implementation)\n  async saveToCloud(gameData: GameSave, userId: string): Promise<boolean> {\n    try {\n      // This would integrate with a cloud service like Firebase, Supabase, etc.\n      const metadata: CloudSaveMetadata = {\n        id: `${userId}_${Date.now()}`,\n        userId,\n        deviceId: this.getDeviceId(),\n        lastModified: Date.now(),\n        gameVersion: SAVE_VERSION,\n        bakeryCount: gameData.bakeries?.length || 1,\n        playerLevel: gameData.player.level\n      }\n\n      // Placeholder for cloud save implementation\n      console.log('Cloud save would be implemented here', { gameData, metadata })\n      return true\n    } catch (error) {\n      console.error('Failed to save to cloud:', error)\n      return false\n    }\n  }\n\n  async loadFromCloud(userId: string): Promise<GameSave | null> {\n    try {\n      // Placeholder for cloud load implementation\n      console.log('Cloud load would be implemented here', { userId })\n      return null\n    } catch (error) {\n      console.error('Failed to load from cloud:', error)\n      return null\n    }\n  }\n\n  async syncWithCloud(localSave: GameSave, userId: string): Promise<GameSave> {\n    try {\n      const cloudSave = await this.loadFromCloud(userId)\n      \n      if (!cloudSave) {\n        // No cloud save exists, upload local save\n        await this.saveToCloud(localSave, userId)\n        return localSave\n      }\n\n      // Compare timestamps and merge\n      if (cloudSave.timestamp > localSave.timestamp) {\n        console.log('Cloud save is newer, using cloud data')\n        return cloudSave\n      } else {\n        console.log('Local save is newer, uploading to cloud')\n        await this.saveToCloud(localSave, userId)\n        return localSave\n      }\n    } catch (error) {\n      console.error('Failed to sync with cloud:', error)\n      return localSave\n    }\n  }\n\n  // Save migration for version compatibility\n  private migrateSave(oldSave: any): GameSave | null {\n    try {\n      // Handle migration from older save versions\n      const migratedSave: GameSave = {\n        version: SAVE_VERSION,\n        timestamp: oldSave.timestamp || Date.now(),\n        player: {\n          level: oldSave.player?.level || 1,\n          experience: oldSave.player?.experience || 0,\n          money: oldSave.player?.money || 100,\n          skillPoints: oldSave.player?.skillPoints || 0,\n          totalMoneyEarned: oldSave.player?.totalMoneyEarned || 0,\n          totalOrdersCompleted: oldSave.player?.totalOrdersCompleted || 0,\n          totalItemsBaked: oldSave.player?.totalItemsBaked || 0,\n          unlockedRecipes: oldSave.player?.unlockedRecipes || ['chocolate_chip_cookies', 'vanilla_muffins'],\n          automationUpgrades: oldSave.player?.automationUpgrades || []\n        },\n        equipment: oldSave.equipment || [],\n        inventory: oldSave.inventory || [],\n        achievements: oldSave.achievements || [],\n        skills: oldSave.skills || [],\n        automationSettings: oldSave.automationSettings || {},\n        gameSettings: {\n          language: oldSave.gameSettings?.language || 'en',\n          soundEnabled: oldSave.gameSettings?.soundEnabled ?? true,\n          musicEnabled: oldSave.gameSettings?.musicEnabled ?? true,\n          notificationsEnabled: oldSave.gameSettings?.notificationsEnabled ?? true,\n          autoSaveEnabled: oldSave.gameSettings?.autoSaveEnabled ?? true\n        },\n        bakeries: oldSave.bakeries || [],\n        currentBakeryId: oldSave.currentBakeryId || 'main'\n      }\n\n      console.log('Save migrated successfully')\n      return migratedSave\n    } catch (error) {\n      console.error('Failed to migrate save:', error)\n      return null\n    }\n  }\n\n  // Utility functions\n  private getDeviceId(): string {\n    let deviceId = localStorage.getItem('deviceId')\n    if (!deviceId) {\n      deviceId = 'device_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)\n      localStorage.setItem('deviceId', deviceId)\n    }\n    return deviceId\n  }\n\n  exportSave(gameData: GameSave): string {\n    return JSON.stringify(gameData, null, 2)\n  }\n\n  importSave(saveString: string): GameSave | null {\n    try {\n      const save = JSON.parse(saveString)\n      return this.migrateSave(save)\n    } catch (error) {\n      console.error('Failed to import save:', error)\n      return null\n    }\n  }\n\n  // Backup management\n  createBackup(gameData: GameSave): boolean {\n    try {\n      const backupKey = `${LOCAL_STORAGE_KEY}_backup_${Date.now()}`\n      localStorage.setItem(backupKey, JSON.stringify(gameData))\n      \n      // Keep only the 5 most recent backups\n      this.cleanupOldBackups()\n      return true\n    } catch (error) {\n      console.error('Failed to create backup:', error)\n      return false\n    }\n  }\n\n  private cleanupOldBackups() {\n    const backupKeys = Object.keys(localStorage)\n      .filter(key => key.startsWith(`${LOCAL_STORAGE_KEY}_backup_`))\n      .sort()\n\n    while (backupKeys.length > 5) {\n      const oldestKey = backupKeys.shift()\n      if (oldestKey) {\n        localStorage.removeItem(oldestKey)\n      }\n    }\n  }\n\n  getBackups(): Array<{ key: string; timestamp: number; data: GameSave }> {\n    const backups: Array<{ key: string; timestamp: number; data: GameSave }> = []\n    \n    Object.keys(localStorage).forEach(key => {\n      if (key.startsWith(`${LOCAL_STORAGE_KEY}_backup_`)) {\n        try {\n          const data = JSON.parse(localStorage.getItem(key) || '{}')\n          const timestamp = parseInt(key.split('_').pop() || '0')\n          backups.push({ key, timestamp, data })\n        } catch (error) {\n          console.error('Failed to parse backup:', error)\n        }\n      }\n    })\n\n    return backups.sort((a, b) => b.timestamp - a.timestamp)\n  }\n}\n\nexport const saveSystem = new SaveSystem()\n"], "names": [], "mappings": "AAAA,mCAAmC;;;;;AAyDnC,MAAM,eAAe;AACrB,MAAM,oBAAoB;AAC1B,MAAM,qBAAqB,MAAM,aAAa;;AAEvC,MAAM;IACH,mBAA0C,KAAI;IAC9C,mBAAmB,MAAK;IAEhC,aAAc;QACZ,IAAI,CAAC,kBAAkB;IACzB;IAEA,2BAA2B;IAC3B,YAAY,QAA2B,EAAW;QAChD,IAAI;YACF,MAAM,OAAiB;gBACrB,SAAS;gBACT,WAAW,KAAK,GAAG;gBACnB,GAAG,QAAQ;YACb;YAEA,MAAM,aAAa,KAAK,SAAS,CAAC;YAClC,aAAa,OAAO,CAAC,mBAAmB;YAExC,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;QACT;IACF;IAEA,gBAAiC;QAC/B,IAAI;YACF,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,CAAC,YAAY,OAAO;YAExB,MAAM,OAAiB,KAAK,KAAK,CAAC;YAElC,8BAA8B;YAC9B,IAAI,KAAK,OAAO,KAAK,cAAc;gBACjC,QAAQ,IAAI,CAAC;gBACb,OAAO,IAAI,CAAC,WAAW,CAAC;YAC1B;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;YACzD,OAAO;QACT;IACF;IAEA,kBAA2B;QACzB,IAAI;YACF,aAAa,UAAU,CAAC;YACxB,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;QACT;IACF;IAEA,0BAA0B;IAC1B,qBAAqB;QACnB;;IAKF;IAEQ,kBAAkB;QACxB,wDAAwD;QACxD,MAAM,QAAQ,IAAI,YAAY;QAC9B,OAAO,aAAa,CAAC;IACvB;IAEA,eAAe;QACb,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACzB,cAAc,IAAI,CAAC,gBAAgB;YACnC,IAAI,CAAC,gBAAgB,GAAG;QAC1B;IACF;IAEA,gEAAgE;IAChE,MAAM,YAAY,QAAkB,EAAE,MAAc,EAAoB;QACtE,IAAI;YACF,0EAA0E;YAC1E,MAAM,WAA8B;gBAClC,IAAI,GAAG,OAAO,CAAC,EAAE,KAAK,GAAG,IAAI;gBAC7B;gBACA,UAAU,IAAI,CAAC,WAAW;gBAC1B,cAAc,KAAK,GAAG;gBACtB,aAAa;gBACb,aAAa,SAAS,QAAQ,EAAE,UAAU;gBAC1C,aAAa,SAAS,MAAM,CAAC,KAAK;YACpC;YAEA,4CAA4C;YAC5C,QAAQ,GAAG,CAAC,wCAAwC;gBAAE;gBAAU;YAAS;YACzE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF;IAEA,MAAM,cAAc,MAAc,EAA4B;QAC5D,IAAI;YACF,4CAA4C;YAC5C,QAAQ,GAAG,CAAC,wCAAwC;gBAAE;YAAO;YAC7D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;QACT;IACF;IAEA,MAAM,cAAc,SAAmB,EAAE,MAAc,EAAqB;QAC1E,IAAI;YACF,MAAM,YAAY,MAAM,IAAI,CAAC,aAAa,CAAC;YAE3C,IAAI,CAAC,WAAW;gBACd,0CAA0C;gBAC1C,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW;gBAClC,OAAO;YACT;YAEA,+BAA+B;YAC/B,IAAI,UAAU,SAAS,GAAG,UAAU,SAAS,EAAE;gBAC7C,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACT,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW;gBAClC,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO;QACT;IACF;IAEA,2CAA2C;IACnC,YAAY,OAAY,EAAmB;QACjD,IAAI;YACF,4CAA4C;YAC5C,MAAM,eAAyB;gBAC7B,SAAS;gBACT,WAAW,QAAQ,SAAS,IAAI,KAAK,GAAG;gBACxC,QAAQ;oBACN,OAAO,QAAQ,MAAM,EAAE,SAAS;oBAChC,YAAY,QAAQ,MAAM,EAAE,cAAc;oBAC1C,OAAO,QAAQ,MAAM,EAAE,SAAS;oBAChC,aAAa,QAAQ,MAAM,EAAE,eAAe;oBAC5C,kBAAkB,QAAQ,MAAM,EAAE,oBAAoB;oBACtD,sBAAsB,QAAQ,MAAM,EAAE,wBAAwB;oBAC9D,iBAAiB,QAAQ,MAAM,EAAE,mBAAmB;oBACpD,iBAAiB,QAAQ,MAAM,EAAE,mBAAmB;wBAAC;wBAA0B;qBAAkB;oBACjG,oBAAoB,QAAQ,MAAM,EAAE,sBAAsB,EAAE;gBAC9D;gBACA,WAAW,QAAQ,SAAS,IAAI,EAAE;gBAClC,WAAW,QAAQ,SAAS,IAAI,EAAE;gBAClC,cAAc,QAAQ,YAAY,IAAI,EAAE;gBACxC,QAAQ,QAAQ,MAAM,IAAI,EAAE;gBAC5B,oBAAoB,QAAQ,kBAAkB,IAAI,CAAC;gBACnD,cAAc;oBACZ,UAAU,QAAQ,YAAY,EAAE,YAAY;oBAC5C,cAAc,QAAQ,YAAY,EAAE,gBAAgB;oBACpD,cAAc,QAAQ,YAAY,EAAE,gBAAgB;oBACpD,sBAAsB,QAAQ,YAAY,EAAE,wBAAwB;oBACpE,iBAAiB,QAAQ,YAAY,EAAE,mBAAmB;gBAC5D;gBACA,UAAU,QAAQ,QAAQ,IAAI,EAAE;gBAChC,iBAAiB,QAAQ,eAAe,IAAI;YAC9C;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,OAAO;QACT;IACF;IAEA,oBAAoB;IACZ,cAAsB;QAC5B,IAAI,WAAW,aAAa,OAAO,CAAC;QACpC,IAAI,CAAC,UAAU;YACb,WAAW,YAAY,KAAK,GAAG,KAAK,MAAM,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YAC/E,aAAa,OAAO,CAAC,YAAY;QACnC;QACA,OAAO;IACT;IAEA,WAAW,QAAkB,EAAU;QACrC,OAAO,KAAK,SAAS,CAAC,UAAU,MAAM;IACxC;IAEA,WAAW,UAAkB,EAAmB;QAC9C,IAAI;YACF,MAAM,OAAO,KAAK,KAAK,CAAC;YACxB,OAAO,IAAI,CAAC,WAAW,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,aAAa,QAAkB,EAAW;QACxC,IAAI;YACF,MAAM,YAAY,GAAG,kBAAkB,QAAQ,EAAE,KAAK,GAAG,IAAI;YAC7D,aAAa,OAAO,CAAC,WAAW,KAAK,SAAS,CAAC;YAE/C,sCAAsC;YACtC,IAAI,CAAC,iBAAiB;YACtB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,OAAO;QACT;IACF;IAEQ,oBAAoB;QAC1B,MAAM,aAAa,OAAO,IAAI,CAAC,cAC5B,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,CAAC,GAAG,kBAAkB,QAAQ,CAAC,GAC3D,IAAI;QAEP,MAAO,WAAW,MAAM,GAAG,EAAG;YAC5B,MAAM,YAAY,WAAW,KAAK;YAClC,IAAI,WAAW;gBACb,aAAa,UAAU,CAAC;YAC1B;QACF;IACF;IAEA,aAAwE;QACtE,MAAM,UAAqE,EAAE;QAE7E,OAAO,IAAI,CAAC,cAAc,OAAO,CAAC,CAAA;YAChC,IAAI,IAAI,UAAU,CAAC,GAAG,kBAAkB,QAAQ,CAAC,GAAG;gBAClD,IAAI;oBACF,MAAM,OAAO,KAAK,KAAK,CAAC,aAAa,OAAO,CAAC,QAAQ;oBACrD,MAAM,YAAY,SAAS,IAAI,KAAK,CAAC,KAAK,GAAG,MAAM;oBACnD,QAAQ,IAAI,CAAC;wBAAE;wBAAK;wBAAW;oBAAK;gBACtC,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,2BAA2B;gBAC3C;YACF;QACF;QAEA,OAAO,QAAQ,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,SAAS,GAAG,EAAE,SAAS;IACzD;AACF;AAEO,MAAM,aAAa,IAAI", "debugId": null}}, {"offset": {"line": 3081, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/contexts/GameContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect } from 'react'\nimport { EquipmentData } from '@/components/game/Equipment'\nimport { OrderData } from '@/components/game/Order'\nimport { generateRandomOrder, calculateExperienceReward, canCraftRecipe, getRecipeById, getAvailableRecipes } from '@/lib/gameLogic'\nimport {\n  getLevelFromExperience,\n  LevelReward,\n  Achievement,\n  SkillTree,\n  ACHIEVEMENTS,\n  SKILL_TREE\n} from '@/lib/progressionSystem'\nimport {\n  AutomationSettings,\n  AutomationJob,\n  ConveyorBelt,\n  AUTOMATION_UPGRADES,\n  calculateAutomationEfficiency,\n  calculateAutomationSpeed,\n  selectOptimalRecipe,\n  generateAutomationJob\n} from '@/lib/automationSystem'\nimport { saveSystem, GameSave } from '@/lib/saveSystem'\n\nexport interface Player {\n  level: number\n  experience: number\n  money: number\n  maxExperience: number\n  skillPoints: number\n  totalMoneyEarned: number\n  totalOrdersCompleted: number\n  totalItemsBaked: number\n  unlockedRecipes: string[]\n  automationUpgrades: string[]\n  name?: string\n  playTime?: number\n}\n\nexport interface Ingredient {\n  name: string\n  quantity: number\n  cost: number\n  icon: string\n}\n\ninterface GameContextType {\n  player: Player\n  equipment: EquipmentData[]\n  inventory: Ingredient[]\n  orders: OrderData[]\n  achievements: Achievement[]\n  skills: SkillTree[]\n  levelUpRewards: LevelReward[]\n  showLevelUp: boolean\n  automationSettings: AutomationSettings\n  automationJobs: AutomationJob[]\n  conveyorBelts: ConveyorBelt[]\n  updatePlayer: (updates: Partial<Player>) => void\n  updateEquipment: (equipmentId: string, updates: Partial<EquipmentData>) => void\n  addEquipment: (equipment: Omit<EquipmentData, 'id'>) => void\n  addExperience: (amount: number) => void\n  addMoney: (amount: number) => void\n  spendMoney: (amount: number) => boolean\n  useIngredient: (name: string, quantity: number) => boolean\n  addIngredient: (name: string, quantity: number) => void\n  acceptOrder: (orderId: string) => void\n  completeOrder: (orderId: string) => void\n  declineOrder: (orderId: string) => void\n  generateNewOrder: () => void\n  upgradeSkill: (skillId: string) => void\n  checkAchievements: () => void\n  dismissLevelUp: () => void\n  updateAutomationSettings: (updates: Partial<AutomationSettings>) => void\n  purchaseAutomationUpgrade: (upgradeId: string) => void\n  startAutomationJob: (equipmentId: string) => void\n  saveGameState: (slotNumber?: number, saveName?: string) => Promise<boolean>\n  loadGameState: (slotNumber: number) => Promise<boolean>\n  quickSave: () => Promise<boolean>\n  quickLoad: () => Promise<boolean>\n  autoSave: () => Promise<boolean>\n}\n\nconst GameContext = createContext<GameContextType | undefined>(undefined)\n\nconst RECIPES = [\n  'Chocolate Chip Cookies',\n  'Vanilla Muffins',\n  'Cinnamon Rolls',\n  'Brownies',\n  'Croissants',\n  'Bread Loaf',\n  'Cupcakes',\n  'Apple Pie'\n]\n\nconst CUSTOMER_NAMES = [\n  'Alice Johnson', 'Bob Smith', 'Carol Davis', 'David Wilson',\n  'Emma Brown', 'Frank Miller', 'Grace Taylor', 'Henry Anderson',\n  'Ivy Thomas', 'Jack Martinez', 'Kate Garcia', 'Liam Rodriguez'\n]\n\nexport function GameProvider({ children }: { children: React.ReactNode }) {\n  const [player, setPlayer] = useState<Player>({\n    level: 1,\n    experience: 0,\n    money: 100,\n    maxExperience: 100,\n    skillPoints: 0,\n    totalMoneyEarned: 0,\n    totalOrdersCompleted: 0,\n    totalItemsBaked: 0,\n    unlockedRecipes: ['chocolate_chip_cookies', 'vanilla_muffins'],\n    automationUpgrades: []\n  })\n\n  const [equipment, setEquipment] = useState<EquipmentData[]>([\n    { id: 'oven1', name: 'Basic Oven', type: 'oven', isActive: false, level: 1, efficiency: 1.0, automationLevel: 0 },\n    { id: 'mixer1', name: 'Hand Mixer', type: 'mixer', isActive: false, level: 1, efficiency: 1.0, automationLevel: 0 },\n    { id: 'counter1', name: 'Work Counter', type: 'counter', isActive: false, level: 1, efficiency: 1.0, automationLevel: 0 }\n  ])\n\n  const [inventory, setInventory] = useState<Ingredient[]>([\n    { name: 'Flour', quantity: 15, cost: 5, icon: '🌾' },\n    { name: 'Sugar', quantity: 12, cost: 8, icon: '🍯' },\n    { name: 'Eggs', quantity: 10, cost: 12, icon: '🥚' },\n    { name: 'Butter', quantity: 8, cost: 15, icon: '🧈' },\n    { name: 'Chocolate Chips', quantity: 6, cost: 20, icon: '🍫' },\n    { name: 'Vanilla', quantity: 5, cost: 25, icon: '🌿' },\n    { name: 'Salt', quantity: 10, cost: 3, icon: '🧂' }\n  ])\n\n  const [orders, setOrders] = useState<OrderData[]>([\n    {\n      id: '1',\n      customerName: 'Alice Johnson',\n      items: ['Chocolate Chip Cookies'],\n      timeLimit: 300,\n      reward: 25,\n      status: 'pending',\n      difficulty: 1\n    }\n  ])\n\n  const [achievements, setAchievements] = useState<Achievement[]>(ACHIEVEMENTS)\n  const [skills, setSkills] = useState<SkillTree[]>(SKILL_TREE)\n  const [levelUpRewards, setLevelUpRewards] = useState<LevelReward[]>([])\n  const [showLevelUp, setShowLevelUp] = useState(false)\n\n  const [automationSettings, setAutomationSettings] = useState<AutomationSettings>({\n    enabled: false,\n    autoStart: false,\n    preferredRecipes: [],\n    maxConcurrentJobs: 2,\n    priorityMode: 'efficiency',\n    ingredientThreshold: 5\n  })\n\n  const [automationJobs, setAutomationJobs] = useState<AutomationJob[]>([])\n  const [conveyorBelts, setConveyorBelts] = useState<ConveyorBelt[]>([])\n\n  // Equipment timer effect\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setEquipment(prev => prev.map(eq => {\n        if (eq.isActive && eq.timeRemaining && eq.timeRemaining > 0) {\n          return { ...eq, timeRemaining: eq.timeRemaining - 1 }\n        } else if (eq.isActive && eq.timeRemaining === 0) {\n          // Baking completed - could add notification here\n          return { ...eq, isActive: false, timeRemaining: undefined, currentRecipe: undefined }\n        }\n        return eq\n      }))\n    }, 1000)\n\n    return () => clearInterval(interval)\n  }, [])\n\n  // Order timer effect\n  useEffect(() => {\n    const interval = setInterval(() => {\n      setOrders(prev => prev.map(order => {\n        if ((order.status === 'accepted' || order.status === 'in_progress') && order.timeLimit > 0) {\n          const newTimeLimit = order.timeLimit - 1\n          if (newTimeLimit === 0) {\n            return { ...order, status: 'failed', timeLimit: 0 }\n          }\n          return { ...order, timeLimit: newTimeLimit }\n        }\n        return order\n      }))\n    }, 1000)\n\n    return () => clearInterval(interval)\n  }, [])\n\n  const updatePlayer = (updates: Partial<Player>) => {\n    setPlayer(prev => ({ ...prev, ...updates }))\n  }\n\n  const updateEquipment = (equipmentId: string, updates: Partial<EquipmentData>) => {\n    setEquipment(prev => prev.map(eq =>\n      eq.id === equipmentId ? { ...eq, ...updates } : eq\n    ))\n  }\n\n  const addEquipment = (equipmentData: Omit<EquipmentData, 'id'>) => {\n    const newEquipment = {\n      ...equipmentData,\n      id: Date.now().toString() + Math.random().toString(36).substring(2, 11)\n    }\n    setEquipment(prev => [...prev, newEquipment])\n\n    // Check achievements after equipment purchase\n    setTimeout(() => checkAchievements(), 100)\n  }\n\n  const addExperience = (amount: number) => {\n    setPlayer(prev => {\n      const newTotalExp = prev.experience + amount\n      const levelData = getLevelFromExperience(newTotalExp)\n      const leveledUp = levelData.level > prev.level\n\n      if (leveledUp) {\n        setLevelUpRewards(levelData.rewards)\n        setShowLevelUp(true)\n\n        // Add skill points for level up\n        const skillPointsGained = levelData.level % 2 === 0 ? 1 : 0\n\n        // Check achievements after level up\n        setTimeout(() => checkAchievements(), 100)\n\n        return {\n          ...prev,\n          level: levelData.level,\n          experience: newTotalExp,\n          maxExperience: levelData.experienceRequired,\n          skillPoints: prev.skillPoints + skillPointsGained\n        }\n      }\n\n      return {\n        ...prev,\n        experience: newTotalExp,\n        maxExperience: levelData.experienceRequired\n      }\n    })\n  }\n\n  const addMoney = (amount: number) => {\n    setPlayer(prev => ({\n      ...prev,\n      money: prev.money + amount,\n      totalMoneyEarned: prev.totalMoneyEarned + amount\n    }))\n\n    // Check achievements after money update\n    setTimeout(() => checkAchievements(), 100)\n  }\n\n  const spendMoney = (amount: number): boolean => {\n    if (player.money >= amount) {\n      setPlayer(prev => ({ ...prev, money: prev.money - amount }))\n      return true\n    }\n    return false\n  }\n\n  const useIngredient = (name: string, quantity: number): boolean => {\n    const ingredient = inventory.find(ing => ing.name === name)\n    if (ingredient && ingredient.quantity >= quantity) {\n      setInventory(prev => prev.map(ing => \n        ing.name === name \n          ? { ...ing, quantity: ing.quantity - quantity }\n          : ing\n      ))\n      return true\n    }\n    return false\n  }\n\n  const addIngredient = (name: string, quantity: number) => {\n    setInventory(prev => prev.map(ing => \n      ing.name === name \n        ? { ...ing, quantity: ing.quantity + quantity }\n        : ing\n    ))\n  }\n\n  const acceptOrder = (orderId: string) => {\n    setOrders(prev => prev.map(order => \n      order.id === orderId \n        ? { ...order, status: 'accepted' }\n        : order\n    ))\n  }\n\n  const completeOrder = (orderId: string) => {\n    const order = orders.find(o => o.id === orderId)\n    if (order) {\n      // Check if player has required ingredients\n      const canComplete = order.items.every(itemName => {\n        const recipe = getRecipeById(itemName.toLowerCase().replace(/\\s+/g, '_'))\n        return recipe ? canCraftRecipe(recipe, inventory) : false\n      })\n\n      if (canComplete) {\n        // Consume ingredients\n        order.items.forEach(itemName => {\n          const recipe = getRecipeById(itemName.toLowerCase().replace(/\\s+/g, '_'))\n          if (recipe) {\n            recipe.ingredients.forEach(ingredient => {\n              useIngredient(ingredient.name, ingredient.quantity)\n            })\n          }\n        })\n\n        // Complete order\n        setOrders(prev => prev.map(o =>\n          o.id === orderId\n            ? { ...o, status: 'completed' }\n            : o\n        ))\n\n        // Update player stats\n        setPlayer(prev => ({\n          ...prev,\n          totalOrdersCompleted: prev.totalOrdersCompleted + 1,\n          totalItemsBaked: prev.totalItemsBaked + order.items.length\n        }))\n\n        // Calculate rewards\n        const timeBonus = order.timeLimit > 60 // Bonus if completed with time to spare\n        const expReward = calculateExperienceReward(order.difficulty, timeBonus)\n\n        addMoney(order.reward)\n        addExperience(expReward)\n\n        // Check for achievement progress\n        setTimeout(() => checkAchievements(), 100)\n      }\n    }\n  }\n\n  const declineOrder = (orderId: string) => {\n    setOrders(prev => prev.filter(order => order.id !== orderId))\n  }\n\n  const generateNewOrder = () => {\n    const newOrder = generateRandomOrder(player.level)\n    setOrders(prev => [...prev, newOrder])\n  }\n\n  const upgradeSkill = (skillId: string) => {\n    const skill = skills.find(s => s.id === skillId)\n    if (!skill || skill.level >= skill.maxLevel || player.skillPoints < skill.cost) {\n      return\n    }\n\n    setSkills(prev => prev.map(s =>\n      s.id === skillId\n        ? { ...s, level: s.level + 1 }\n        : s\n    ))\n\n    setPlayer(prev => ({\n      ...prev,\n      skillPoints: prev.skillPoints - skill.cost\n    }))\n  }\n\n  const checkAchievements = () => {\n    setAchievements(prev => {\n      const newAchievements = prev.map(achievement => {\n        if (achievement.completed) return achievement\n\n        // Check all requirements\n        const updatedRequirements = achievement.requirements.map(requirement => {\n          let current = 0\n\n          switch (requirement.type) {\n            case 'orders_completed':\n              current = player.totalOrdersCompleted\n              break\n            case 'money_earned':\n              current = player.totalMoneyEarned\n              break\n            case 'recipes_unlocked':\n              current = player.unlockedRecipes.length\n              break\n            case 'level_reached':\n              current = player.level\n              break\n            case 'items_baked':\n              current = player.totalItemsBaked\n              break\n            case 'equipment_owned':\n              current = equipment.length\n              break\n          }\n\n          return { ...requirement, current }\n        })\n\n        // Check if all requirements are met\n        const completed = updatedRequirements.every(req => req.current >= req.target)\n\n        // If newly completed, show notification and apply reward\n        if (completed && !achievement.completed) {\n          showSuccess('Achievement Unlocked!', `🏆 ${achievement.name}`)\n\n          // Apply achievement reward\n          if (achievement.reward.type === 'money' && achievement.reward.value) {\n            addMoney(achievement.reward.value)\n          } else if (achievement.reward.type === 'skill_point' && achievement.reward.value) {\n            setPlayer(prev => ({\n              ...prev,\n              skillPoints: prev.skillPoints + achievement.reward.value!\n            }))\n          }\n        }\n\n        return {\n          ...achievement,\n          requirements: updatedRequirements,\n          completed\n        }\n      })\n\n      return newAchievements\n    })\n  }\n\n  const dismissLevelUp = () => {\n    setShowLevelUp(false)\n    setLevelUpRewards([])\n  }\n\n  const updateAutomationSettings = (updates: Partial<AutomationSettings>) => {\n    setAutomationSettings(prev => ({ ...prev, ...updates }))\n  }\n\n  const purchaseAutomationUpgrade = (upgradeId: string) => {\n    const upgrade = AUTOMATION_UPGRADES.find(u => u.id === upgradeId)\n    if (!upgrade || player.money < upgrade.cost) return\n\n    if (spendMoney(upgrade.cost)) {\n      setPlayer(prev => ({\n        ...prev,\n        automationUpgrades: [...prev.automationUpgrades, upgradeId]\n      }))\n    }\n  }\n\n  const startAutomationJob = (equipmentId: string) => {\n    if (!automationSettings.enabled) return\n\n    const targetEquipment = equipment.find(eq => eq.id === equipmentId)\n    if (!targetEquipment || targetEquipment.isActive || targetEquipment.automationLevel === 0) return\n\n    const availableRecipes = getAvailableRecipes(player.level)\n    const optimalRecipeId = selectOptimalRecipe(\n      availableRecipes,\n      inventory,\n      automationSettings.priorityMode,\n      orders\n    )\n\n    if (!optimalRecipeId) return\n\n    const recipe = getRecipeById(optimalRecipeId)\n    if (!recipe || !canCraftRecipe(recipe, inventory)) return\n\n    const efficiency = calculateAutomationEfficiency(\n      targetEquipment.efficiency,\n      targetEquipment.automationLevel,\n      player.automationUpgrades\n    )\n\n    const job = generateAutomationJob(equipmentId, optimalRecipeId, recipe, efficiency)\n\n    // Start the job\n    setAutomationJobs(prev => [...prev, { ...job, status: 'running' }])\n    updateEquipment(equipmentId, {\n      isActive: true,\n      timeRemaining: job.duration,\n      currentRecipe: recipe.name\n    })\n\n    // Consume ingredients\n    job.ingredients.forEach(ingredient => {\n      useIngredient(ingredient.name, ingredient.quantity)\n    })\n  }\n\n  // Save/Load functionality\n  const saveGameState = async (slotNumber?: number, saveName?: string): Promise<boolean> => {\n    try {\n      const gameData: GameSave = {\n        version: '1.0.0',\n        timestamp: Date.now(),\n        player: {\n          level: player.level,\n          experience: player.experience,\n          money: player.money,\n          skillPoints: player.skillPoints,\n          totalMoneyEarned: player.totalMoneyEarned || 0,\n          totalOrdersCompleted: player.totalOrdersCompleted || 0,\n          totalItemsBaked: player.totalItemsBaked || 0,\n          unlockedRecipes: player.unlockedRecipes || [],\n          automationUpgrades: player.automationUpgrades || [],\n          name: saveName || `Save ${slotNumber || 1}`,\n          playTime: player.playTime || 0\n        },\n        equipment,\n        inventory,\n        achievements,\n        skills,\n        automationSettings,\n        gameSettings: {\n          language: 'en',\n          soundEnabled: true,\n          musicEnabled: true,\n          notificationsEnabled: true,\n          autoSaveEnabled: true\n        },\n        bakeries: [],\n        currentBakeryId: 'main'\n      }\n\n      if (slotNumber) {\n        // Save to specific slot\n        const slotKey = `bakeItOut_save_slot_${slotNumber}`\n        localStorage.setItem(slotKey, JSON.stringify(gameData))\n      } else {\n        // Save to default location\n        saveSystem.saveToLocal(gameData)\n      }\n\n      return true\n    } catch (error) {\n      console.error('Failed to save game:', error)\n      return false\n    }\n  }\n\n  const loadGameState = async (slotNumber: number): Promise<boolean> => {\n    try {\n      const slotKey = `bakeItOut_save_slot_${slotNumber}`\n      const saveData = localStorage.getItem(slotKey)\n\n      if (!saveData) return false\n\n      const gameData: GameSave = JSON.parse(saveData)\n\n      // Load player data\n      setPlayer(prev => ({\n        ...prev,\n        level: gameData.player.level,\n        experience: gameData.player.experience,\n        money: gameData.player.money,\n        skillPoints: gameData.player.skillPoints,\n        totalMoneyEarned: gameData.player.totalMoneyEarned || 0,\n        totalOrdersCompleted: gameData.player.totalOrdersCompleted || 0,\n        totalItemsBaked: gameData.player.totalItemsBaked || 0,\n        unlockedRecipes: gameData.player.unlockedRecipes || [],\n        automationUpgrades: gameData.player.automationUpgrades || [],\n        playTime: gameData.player.playTime || 0\n      }))\n\n      // Load other game state\n      setEquipment(gameData.equipment || [])\n      setInventory(gameData.inventory || [])\n      setAchievements(gameData.achievements || [])\n      setSkills(gameData.skills || [])\n      setAutomationSettings(gameData.automationSettings || {\n        enabled: false,\n        efficiency: 1,\n        speed: 1,\n        qualityBonus: 0\n      })\n\n      return true\n    } catch (error) {\n      console.error('Failed to load game:', error)\n      return false\n    }\n  }\n\n  const quickSave = async (): Promise<boolean> => {\n    return await saveGameState(0, 'Quick Save')\n  }\n\n  const quickLoad = async (): Promise<boolean> => {\n    return await loadGameState(0)\n  }\n\n  const autoSave = async (): Promise<boolean> => {\n    return await saveGameState(-1, 'Auto Save')\n  }\n\n  // Auto-save every 2 minutes\n  useEffect(() => {\n    const autoSaveInterval = setInterval(() => {\n      autoSave()\n    }, 120000) // 2 minutes\n\n    return () => clearInterval(autoSaveInterval)\n  }, [])\n\n  return (\n    <GameContext.Provider value={{\n      player,\n      equipment,\n      inventory,\n      orders,\n      achievements,\n      skills,\n      levelUpRewards,\n      showLevelUp,\n      automationSettings,\n      automationJobs,\n      conveyorBelts,\n      updatePlayer,\n      updateEquipment,\n      addEquipment,\n      addExperience,\n      addMoney,\n      spendMoney,\n      useIngredient,\n      addIngredient,\n      acceptOrder,\n      completeOrder,\n      declineOrder,\n      generateNewOrder,\n      upgradeSkill,\n      checkAchievements,\n      dismissLevelUp,\n      updateAutomationSettings,\n      purchaseAutomationUpgrade,\n      startAutomationJob,\n      saveGameState,\n      loadGameState,\n      quickSave,\n      quickLoad,\n      autoSave\n    }}>\n      {children}\n    </GameContext.Provider>\n  )\n}\n\nexport function useGame() {\n  const context = useContext(GameContext)\n  if (context === undefined) {\n    // Return a fallback object for SSR/build compatibility\n    return {\n      player: { level: 1, experience: 0, money: 100, maxExperience: 100, skillPoints: 0, totalMoneyEarned: 0, totalOrdersCompleted: 0, totalItemsBaked: 0, unlockedRecipes: [], automationUpgrades: [] },\n      equipment: [],\n      inventory: [],\n      orders: [],\n      achievements: [],\n      skills: [],\n      levelUpRewards: [],\n      showLevelUp: false,\n      updateEquipment: () => {},\n      acceptOrder: () => {},\n      completeOrder: () => {},\n      declineOrder: () => {},\n      generateNewOrder: () => {},\n      upgradeSkill: () => {},\n      checkAchievements: () => {},\n      dismissLevelUp: () => {},\n      spendMoney: () => false,\n      addMoney: () => {},\n      addExperience: () => {},\n      addEquipment: () => {},\n      removeEquipment: () => {},\n      addInventoryItem: () => {},\n      removeInventoryItem: () => {},\n      updateAutomationSettings: () => {},\n      purchaseAutomationUpgrade: () => {},\n      startAutomationJob: () => {},\n      saveGameState: async () => false,\n      loadGameState: async () => false,\n      quickSave: async () => false,\n      quickLoad: async () => false,\n      autoSave: async () => false\n    }\n  }\n  return context\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAGA;AACA;AAQA;AAUA;AAxBA;;;;;;;AAqFA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAE/D,MAAM,UAAU;IACd;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,iBAAiB;IACrB;IAAiB;IAAa;IAAe;IAC7C;IAAc;IAAgB;IAAgB;IAC9C;IAAc;IAAiB;IAAe;CAC/C;AAEM,SAAS,aAAa,EAAE,QAAQ,EAAiC;IACtE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;QAC3C,OAAO;QACP,YAAY;QACZ,OAAO;QACP,eAAe;QACf,aAAa;QACb,kBAAkB;QAClB,sBAAsB;QACtB,iBAAiB;QACjB,iBAAiB;YAAC;YAA0B;SAAkB;QAC9D,oBAAoB,EAAE;IACxB;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB;QAC1D;YAAE,IAAI;YAAS,MAAM;YAAc,MAAM;YAAQ,UAAU;YAAO,OAAO;YAAG,YAAY;YAAK,iBAAiB;QAAE;QAChH;YAAE,IAAI;YAAU,MAAM;YAAc,MAAM;YAAS,UAAU;YAAO,OAAO;YAAG,YAAY;YAAK,iBAAiB;QAAE;QAClH;YAAE,IAAI;YAAY,MAAM;YAAgB,MAAM;YAAW,UAAU;YAAO,OAAO;YAAG,YAAY;YAAK,iBAAiB;QAAE;KACzH;IAED,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB;QACvD;YAAE,MAAM;YAAS,UAAU;YAAI,MAAM;YAAG,MAAM;QAAK;QACnD;YAAE,MAAM;YAAS,UAAU;YAAI,MAAM;YAAG,MAAM;QAAK;QACnD;YAAE,MAAM;YAAQ,UAAU;YAAI,MAAM;YAAI,MAAM;QAAK;QACnD;YAAE,MAAM;YAAU,UAAU;YAAG,MAAM;YAAI,MAAM;QAAK;QACpD;YAAE,MAAM;YAAmB,UAAU;YAAG,MAAM;YAAI,MAAM;QAAK;QAC7D;YAAE,MAAM;YAAW,UAAU;YAAG,MAAM;YAAI,MAAM;QAAK;QACrD;YAAE,MAAM;YAAQ,UAAU;YAAI,MAAM;YAAG,MAAM;QAAK;KACnD;IAED,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;QAChD;YACE,IAAI;YACJ,cAAc;YACd,OAAO;gBAAC;aAAyB;YACjC,WAAW;YACX,QAAQ;YACR,QAAQ;YACR,YAAY;QACd;KACD;IAED,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,+HAAA,CAAA,eAAY;IAC5E,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,+HAAA,CAAA,aAAU;IAC5D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB;QAC/E,SAAS;QACT,WAAW;QACX,kBAAkB,EAAE;QACpB,mBAAmB;QACnB,cAAc;QACd,qBAAqB;IACvB;IAEA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB,EAAE;IAErE,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,aAAa,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;oBAC5B,IAAI,GAAG,QAAQ,IAAI,GAAG,aAAa,IAAI,GAAG,aAAa,GAAG,GAAG;wBAC3D,OAAO;4BAAE,GAAG,EAAE;4BAAE,eAAe,GAAG,aAAa,GAAG;wBAAE;oBACtD,OAAO,IAAI,GAAG,QAAQ,IAAI,GAAG,aAAa,KAAK,GAAG;wBAChD,iDAAiD;wBACjD,OAAO;4BAAE,GAAG,EAAE;4BAAE,UAAU;4BAAO,eAAe;4BAAW,eAAe;wBAAU;oBACtF;oBACA,OAAO;gBACT;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,YAAY;YAC3B,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA;oBACzB,IAAI,CAAC,MAAM,MAAM,KAAK,cAAc,MAAM,MAAM,KAAK,aAAa,KAAK,MAAM,SAAS,GAAG,GAAG;wBAC1F,MAAM,eAAe,MAAM,SAAS,GAAG;wBACvC,IAAI,iBAAiB,GAAG;4BACtB,OAAO;gCAAE,GAAG,KAAK;gCAAE,QAAQ;gCAAU,WAAW;4BAAE;wBACpD;wBACA,OAAO;4BAAE,GAAG,KAAK;4BAAE,WAAW;wBAAa;oBAC7C;oBACA,OAAO;gBACT;QACF,GAAG;QAEH,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,MAAM,eAAe,CAAC;QACpB,UAAU,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC,CAAC;IAC5C;IAEA,MAAM,kBAAkB,CAAC,aAAqB;QAC5C,aAAa,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,KAC5B,GAAG,EAAE,KAAK,cAAc;oBAAE,GAAG,EAAE;oBAAE,GAAG,OAAO;gBAAC,IAAI;IAEpD;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,eAAe;YACnB,GAAG,aAAa;YAChB,IAAI,KAAK,GAAG,GAAG,QAAQ,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG;QACtE;QACA,aAAa,CAAA,OAAQ;mBAAI;gBAAM;aAAa;QAE5C,8CAA8C;QAC9C,WAAW,IAAM,qBAAqB;IACxC;IAEA,MAAM,gBAAgB,CAAC;QACrB,UAAU,CAAA;YACR,MAAM,cAAc,KAAK,UAAU,GAAG;YACtC,MAAM,YAAY,CAAA,GAAA,+HAAA,CAAA,yBAAsB,AAAD,EAAE;YACzC,MAAM,YAAY,UAAU,KAAK,GAAG,KAAK,KAAK;YAE9C,IAAI,WAAW;gBACb,kBAAkB,UAAU,OAAO;gBACnC,eAAe;gBAEf,gCAAgC;gBAChC,MAAM,oBAAoB,UAAU,KAAK,GAAG,MAAM,IAAI,IAAI;gBAE1D,oCAAoC;gBACpC,WAAW,IAAM,qBAAqB;gBAEtC,OAAO;oBACL,GAAG,IAAI;oBACP,OAAO,UAAU,KAAK;oBACtB,YAAY;oBACZ,eAAe,UAAU,kBAAkB;oBAC3C,aAAa,KAAK,WAAW,GAAG;gBAClC;YACF;YAEA,OAAO;gBACL,GAAG,IAAI;gBACP,YAAY;gBACZ,eAAe,UAAU,kBAAkB;YAC7C;QACF;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,UAAU,CAAA,OAAQ,CAAC;gBACjB,GAAG,IAAI;gBACP,OAAO,KAAK,KAAK,GAAG;gBACpB,kBAAkB,KAAK,gBAAgB,GAAG;YAC5C,CAAC;QAED,wCAAwC;QACxC,WAAW,IAAM,qBAAqB;IACxC;IAEA,MAAM,aAAa,CAAC;QAClB,IAAI,OAAO,KAAK,IAAI,QAAQ;YAC1B,UAAU,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,OAAO,KAAK,KAAK,GAAG;gBAAO,CAAC;YAC1D,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC,MAAc;QACnC,MAAM,aAAa,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;QACtD,IAAI,cAAc,WAAW,QAAQ,IAAI,UAAU;YACjD,aAAa,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MAC5B,IAAI,IAAI,KAAK,OACT;wBAAE,GAAG,GAAG;wBAAE,UAAU,IAAI,QAAQ,GAAG;oBAAS,IAC5C;YAEN,OAAO;QACT;QACA,OAAO;IACT;IAEA,MAAM,gBAAgB,CAAC,MAAc;QACnC,aAAa,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,MAC5B,IAAI,IAAI,KAAK,OACT;oBAAE,GAAG,GAAG;oBAAE,UAAU,IAAI,QAAQ,GAAG;gBAAS,IAC5C;IAER;IAEA,MAAM,cAAc,CAAC;QACnB,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,QACzB,MAAM,EAAE,KAAK,UACT;oBAAE,GAAG,KAAK;oBAAE,QAAQ;gBAAW,IAC/B;IAER;IAEA,MAAM,gBAAgB,CAAC;QACrB,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACxC,IAAI,OAAO;YACT,2CAA2C;YAC3C,MAAM,cAAc,MAAM,KAAK,CAAC,KAAK,CAAC,CAAA;gBACpC,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,WAAW,GAAG,OAAO,CAAC,QAAQ;gBACpE,OAAO,SAAS,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,aAAa;YACtD;YAEA,IAAI,aAAa;gBACf,sBAAsB;gBACtB,MAAM,KAAK,CAAC,OAAO,CAAC,CAAA;oBAClB,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,WAAW,GAAG,OAAO,CAAC,QAAQ;oBACpE,IAAI,QAAQ;wBACV,OAAO,WAAW,CAAC,OAAO,CAAC,CAAA;4BACzB,cAAc,WAAW,IAAI,EAAE,WAAW,QAAQ;wBACpD;oBACF;gBACF;gBAEA,iBAAiB;gBACjB,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACzB,EAAE,EAAE,KAAK,UACL;4BAAE,GAAG,CAAC;4BAAE,QAAQ;wBAAY,IAC5B;gBAGN,sBAAsB;gBACtB,UAAU,CAAA,OAAQ,CAAC;wBACjB,GAAG,IAAI;wBACP,sBAAsB,KAAK,oBAAoB,GAAG;wBAClD,iBAAiB,KAAK,eAAe,GAAG,MAAM,KAAK,CAAC,MAAM;oBAC5D,CAAC;gBAED,oBAAoB;gBACpB,MAAM,YAAY,MAAM,SAAS,GAAG,GAAG,wCAAwC;;gBAC/E,MAAM,YAAY,CAAA,GAAA,uHAAA,CAAA,4BAAyB,AAAD,EAAE,MAAM,UAAU,EAAE;gBAE9D,SAAS,MAAM,MAAM;gBACrB,cAAc;gBAEd,iCAAiC;gBACjC,WAAW,IAAM,qBAAqB;YACxC;QACF;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,UAAU,CAAA,OAAQ,KAAK,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;IACtD;IAEA,MAAM,mBAAmB;QACvB,MAAM,WAAW,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,KAAK;QACjD,UAAU,CAAA,OAAQ;mBAAI;gBAAM;aAAS;IACvC;IAEA,MAAM,eAAe,CAAC;QACpB,MAAM,QAAQ,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACxC,IAAI,CAAC,SAAS,MAAM,KAAK,IAAI,MAAM,QAAQ,IAAI,OAAO,WAAW,GAAG,MAAM,IAAI,EAAE;YAC9E;QACF;QAEA,UAAU,CAAA,OAAQ,KAAK,GAAG,CAAC,CAAA,IACzB,EAAE,EAAE,KAAK,UACL;oBAAE,GAAG,CAAC;oBAAE,OAAO,EAAE,KAAK,GAAG;gBAAE,IAC3B;QAGN,UAAU,CAAA,OAAQ,CAAC;gBACjB,GAAG,IAAI;gBACP,aAAa,KAAK,WAAW,GAAG,MAAM,IAAI;YAC5C,CAAC;IACH;IAEA,MAAM,oBAAoB;QACxB,gBAAgB,CAAA;YACd,MAAM,kBAAkB,KAAK,GAAG,CAAC,CAAA;gBAC/B,IAAI,YAAY,SAAS,EAAE,OAAO;gBAElC,yBAAyB;gBACzB,MAAM,sBAAsB,YAAY,YAAY,CAAC,GAAG,CAAC,CAAA;oBACvD,IAAI,UAAU;oBAEd,OAAQ,YAAY,IAAI;wBACtB,KAAK;4BACH,UAAU,OAAO,oBAAoB;4BACrC;wBACF,KAAK;4BACH,UAAU,OAAO,gBAAgB;4BACjC;wBACF,KAAK;4BACH,UAAU,OAAO,eAAe,CAAC,MAAM;4BACvC;wBACF,KAAK;4BACH,UAAU,OAAO,KAAK;4BACtB;wBACF,KAAK;4BACH,UAAU,OAAO,eAAe;4BAChC;wBACF,KAAK;4BACH,UAAU,UAAU,MAAM;4BAC1B;oBACJ;oBAEA,OAAO;wBAAE,GAAG,WAAW;wBAAE;oBAAQ;gBACnC;gBAEA,oCAAoC;gBACpC,MAAM,YAAY,oBAAoB,KAAK,CAAC,CAAA,MAAO,IAAI,OAAO,IAAI,IAAI,MAAM;gBAE5E,yDAAyD;gBACzD,IAAI,aAAa,CAAC,YAAY,SAAS,EAAE;oBACvC,YAAY,yBAAyB,CAAC,GAAG,EAAE,YAAY,IAAI,EAAE;oBAE7D,2BAA2B;oBAC3B,IAAI,YAAY,MAAM,CAAC,IAAI,KAAK,WAAW,YAAY,MAAM,CAAC,KAAK,EAAE;wBACnE,SAAS,YAAY,MAAM,CAAC,KAAK;oBACnC,OAAO,IAAI,YAAY,MAAM,CAAC,IAAI,KAAK,iBAAiB,YAAY,MAAM,CAAC,KAAK,EAAE;wBAChF,UAAU,CAAA,OAAQ,CAAC;gCACjB,GAAG,IAAI;gCACP,aAAa,KAAK,WAAW,GAAG,YAAY,MAAM,CAAC,KAAK;4BAC1D,CAAC;oBACH;gBACF;gBAEA,OAAO;oBACL,GAAG,WAAW;oBACd,cAAc;oBACd;gBACF;YACF;YAEA,OAAO;QACT;IACF;IAEA,MAAM,iBAAiB;QACrB,eAAe;QACf,kBAAkB,EAAE;IACtB;IAEA,MAAM,2BAA2B,CAAC;QAChC,sBAAsB,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,GAAG,OAAO;YAAC,CAAC;IACxD;IAEA,MAAM,4BAA4B,CAAC;QACjC,MAAM,UAAU,8HAAA,CAAA,sBAAmB,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACvD,IAAI,CAAC,WAAW,OAAO,KAAK,GAAG,QAAQ,IAAI,EAAE;QAE7C,IAAI,WAAW,QAAQ,IAAI,GAAG;YAC5B,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,oBAAoB;2BAAI,KAAK,kBAAkB;wBAAE;qBAAU;gBAC7D,CAAC;QACH;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,IAAI,CAAC,mBAAmB,OAAO,EAAE;QAEjC,MAAM,kBAAkB,UAAU,IAAI,CAAC,CAAA,KAAM,GAAG,EAAE,KAAK;QACvD,IAAI,CAAC,mBAAmB,gBAAgB,QAAQ,IAAI,gBAAgB,eAAe,KAAK,GAAG;QAE3F,MAAM,mBAAmB,CAAA,GAAA,uHAAA,CAAA,sBAAmB,AAAD,EAAE,OAAO,KAAK;QACzD,MAAM,kBAAkB,CAAA,GAAA,8HAAA,CAAA,sBAAmB,AAAD,EACxC,kBACA,WACA,mBAAmB,YAAY,EAC/B;QAGF,IAAI,CAAC,iBAAiB;QAEtB,MAAM,SAAS,CAAA,GAAA,uHAAA,CAAA,gBAAa,AAAD,EAAE;QAC7B,IAAI,CAAC,UAAU,CAAC,CAAA,GAAA,uHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,YAAY;QAEnD,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,gCAA6B,AAAD,EAC7C,gBAAgB,UAAU,EAC1B,gBAAgB,eAAe,EAC/B,OAAO,kBAAkB;QAG3B,MAAM,MAAM,CAAA,GAAA,8HAAA,CAAA,wBAAqB,AAAD,EAAE,aAAa,iBAAiB,QAAQ;QAExE,gBAAgB;QAChB,kBAAkB,CAAA,OAAQ;mBAAI;gBAAM;oBAAE,GAAG,GAAG;oBAAE,QAAQ;gBAAU;aAAE;QAClE,gBAAgB,aAAa;YAC3B,UAAU;YACV,eAAe,IAAI,QAAQ;YAC3B,eAAe,OAAO,IAAI;QAC5B;QAEA,sBAAsB;QACtB,IAAI,WAAW,CAAC,OAAO,CAAC,CAAA;YACtB,cAAc,WAAW,IAAI,EAAE,WAAW,QAAQ;QACpD;IACF;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,OAAO,YAAqB;QAChD,IAAI;YACF,MAAM,WAAqB;gBACzB,SAAS;gBACT,WAAW,KAAK,GAAG;gBACnB,QAAQ;oBACN,OAAO,OAAO,KAAK;oBACnB,YAAY,OAAO,UAAU;oBAC7B,OAAO,OAAO,KAAK;oBACnB,aAAa,OAAO,WAAW;oBAC/B,kBAAkB,OAAO,gBAAgB,IAAI;oBAC7C,sBAAsB,OAAO,oBAAoB,IAAI;oBACrD,iBAAiB,OAAO,eAAe,IAAI;oBAC3C,iBAAiB,OAAO,eAAe,IAAI,EAAE;oBAC7C,oBAAoB,OAAO,kBAAkB,IAAI,EAAE;oBACnD,MAAM,YAAY,CAAC,KAAK,EAAE,cAAc,GAAG;oBAC3C,UAAU,OAAO,QAAQ,IAAI;gBAC/B;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,cAAc;oBACZ,UAAU;oBACV,cAAc;oBACd,cAAc;oBACd,sBAAsB;oBACtB,iBAAiB;gBACnB;gBACA,UAAU,EAAE;gBACZ,iBAAiB;YACnB;YAEA,IAAI,YAAY;gBACd,wBAAwB;gBACxB,MAAM,UAAU,CAAC,oBAAoB,EAAE,YAAY;gBACnD,aAAa,OAAO,CAAC,SAAS,KAAK,SAAS,CAAC;YAC/C,OAAO;gBACL,2BAA2B;gBAC3B,wHAAA,CAAA,aAAU,CAAC,WAAW,CAAC;YACzB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;QACT;IACF;IAEA,MAAM,gBAAgB,OAAO;QAC3B,IAAI;YACF,MAAM,UAAU,CAAC,oBAAoB,EAAE,YAAY;YACnD,MAAM,WAAW,aAAa,OAAO,CAAC;YAEtC,IAAI,CAAC,UAAU,OAAO;YAEtB,MAAM,WAAqB,KAAK,KAAK,CAAC;YAEtC,mBAAmB;YACnB,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,OAAO,SAAS,MAAM,CAAC,KAAK;oBAC5B,YAAY,SAAS,MAAM,CAAC,UAAU;oBACtC,OAAO,SAAS,MAAM,CAAC,KAAK;oBAC5B,aAAa,SAAS,MAAM,CAAC,WAAW;oBACxC,kBAAkB,SAAS,MAAM,CAAC,gBAAgB,IAAI;oBACtD,sBAAsB,SAAS,MAAM,CAAC,oBAAoB,IAAI;oBAC9D,iBAAiB,SAAS,MAAM,CAAC,eAAe,IAAI;oBACpD,iBAAiB,SAAS,MAAM,CAAC,eAAe,IAAI,EAAE;oBACtD,oBAAoB,SAAS,MAAM,CAAC,kBAAkB,IAAI,EAAE;oBAC5D,UAAU,SAAS,MAAM,CAAC,QAAQ,IAAI;gBACxC,CAAC;YAED,wBAAwB;YACxB,aAAa,SAAS,SAAS,IAAI,EAAE;YACrC,aAAa,SAAS,SAAS,IAAI,EAAE;YACrC,gBAAgB,SAAS,YAAY,IAAI,EAAE;YAC3C,UAAU,SAAS,MAAM,IAAI,EAAE;YAC/B,sBAAsB,SAAS,kBAAkB,IAAI;gBACnD,SAAS;gBACT,YAAY;gBACZ,OAAO;gBACP,cAAc;YAChB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;QACT;IACF;IAEA,MAAM,YAAY;QAChB,OAAO,MAAM,cAAc,GAAG;IAChC;IAEA,MAAM,YAAY;QAChB,OAAO,MAAM,cAAc;IAC7B;IAEA,MAAM,WAAW;QACf,OAAO,MAAM,cAAc,CAAC,GAAG;IACjC;IAEA,4BAA4B;IAC5B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB,YAAY;YACnC;QACF,GAAG,QAAQ,YAAY;;QAEvB,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,qBACE,8OAAC,YAAY,QAAQ;QAAC,OAAO;YAC3B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACF;kBACG;;;;;;AAGP;AAEO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,uDAAuD;QACvD,OAAO;YACL,QAAQ;gBAAE,OAAO;gBAAG,YAAY;gBAAG,OAAO;gBAAK,eAAe;gBAAK,aAAa;gBAAG,kBAAkB;gBAAG,sBAAsB;gBAAG,iBAAiB;gBAAG,iBAAiB,EAAE;gBAAE,oBAAoB,EAAE;YAAC;YACjM,WAAW,EAAE;YACb,WAAW,EAAE;YACb,QAAQ,EAAE;YACV,cAAc,EAAE;YAChB,QAAQ,EAAE;YACV,gBAAgB,EAAE;YAClB,aAAa;YACb,iBAAiB,KAAO;YACxB,aAAa,KAAO;YACpB,eAAe,KAAO;YACtB,cAAc,KAAO;YACrB,kBAAkB,KAAO;YACzB,cAAc,KAAO;YACrB,mBAAmB,KAAO;YAC1B,gBAAgB,KAAO;YACvB,YAAY,IAAM;YAClB,UAAU,KAAO;YACjB,eAAe,KAAO;YACtB,cAAc,KAAO;YACrB,iBAAiB,KAAO;YACxB,kBAAkB,KAAO;YACzB,qBAAqB,KAAO;YAC5B,0BAA0B,KAAO;YACjC,2BAA2B,KAAO;YAClC,oBAAoB,KAAO;YAC3B,eAAe,UAAY;YAC3B,eAAe,UAAY;YAC3B,WAAW,UAAY;YACvB,WAAW,UAAY;YACvB,UAAU,UAAY;QACxB;IACF;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 3751, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/coding%20stuff/bake%20it%20out/bake-it-out/src/contexts/DiscordRPCContext.tsx"], "sourcesContent": ["'use client'\n\nimport { createContext, useContext, useEffect, useState, ReactNode } from 'react'\nimport { discordRPC, PlayerStatus, GameActivity } from '@/lib/discordRPC'\nimport { useGame } from './GameContext'\nimport { useMultiplayer } from './MultiplayerContext'\n\ninterface DiscordRPCContextType {\n  isEnabled: boolean\n  isConnected: boolean\n  currentActivity: GameActivity | null\n  setEnabled: (enabled: boolean) => void\n  updateActivity: (activity: GameActivity) => Promise<void>\n  setMenuActivity: () => Promise<void>\n  setGameActivity: (level: number, money: number, activity?: string) => Promise<void>\n  setMultiplayerActivity: (roomId: string, playerCount: number, maxPlayers: number) => Promise<void>\n  setBakingActivity: (level: number, currentOrder: string) => Promise<void>\n  clearActivity: () => Promise<void>\n}\n\nconst DiscordRPCContext = createContext<DiscordRPCContextType | undefined>(undefined)\n\nexport function useDiscordRPC() {\n  const context = useContext(DiscordRPCContext)\n  if (context === undefined) {\n    // Return a fallback object instead of throwing an error\n    return {\n      isEnabled: false,\n      isConnected: false,\n      currentActivity: null,\n      setEnabled: () => {},\n      updateActivity: async () => {},\n      setMenuActivity: async () => {},\n      setGameActivity: async () => {},\n      setMultiplayerActivity: async () => {},\n      setBakingActivity: async () => {},\n      clearActivity: async () => {}\n    }\n  }\n  return context\n}\n\ninterface DiscordRPCProviderProps {\n  children: ReactNode\n}\n\nexport function DiscordRPCProvider({ children }: DiscordRPCProviderProps) {\n  const [isEnabled, setIsEnabled] = useState(false) // Start disabled to prevent SSR issues\n  const [isConnected, setIsConnected] = useState(false)\n  const [currentActivity, setCurrentActivity] = useState<GameActivity | null>(null)\n  const [isClient, setIsClient] = useState(false)\n  \n  // Get game state for automatic updates\n  const gameContext = useGame()\n  const multiplayerContext = useMultiplayer()\n\n  // Initialize client-side state\n  useEffect(() => {\n    setIsClient(true)\n\n    // Load saved preference\n    const saved = localStorage.getItem('discordRPCEnabled')\n    if (saved !== null) {\n      const enabled = saved === 'true'\n      setIsEnabled(enabled)\n    } else {\n      // Default to enabled if in Electron environment\n      const shouldEnable = window.electronAPI !== undefined\n      setIsEnabled(shouldEnable)\n    }\n  }, [])\n\n  // Initialize Discord RPC\n  useEffect(() => {\n    if (!isClient) return\n\n    // Check if Discord RPC should be enabled (only in Electron environment)\n    const shouldEnable = typeof window !== 'undefined' &&\n                        window.electronAPI !== undefined &&\n                        isEnabled\n\n    if (shouldEnable) {\n      discordRPC.setEnabled(true)\n\n      // Check connection status periodically\n      const checkConnection = () => {\n        setIsConnected(discordRPC.isRPCConnected())\n        setCurrentActivity(discordRPC.getCurrentActivity())\n      }\n\n      const interval = setInterval(checkConnection, 2000) // Less frequent to reduce load\n      checkConnection() // Initial check\n\n      return () => {\n        clearInterval(interval)\n      }\n    } else {\n      discordRPC.setEnabled(false)\n    }\n  }, [isClient, isEnabled])\n\n  // Auto-update Discord RPC based on game state\n  useEffect(() => {\n    if (!isClient || !isEnabled || !isConnected || !gameContext) return\n\n    const updateRPCFromGameState = async () => {\n      try {\n        const { player, currentOrders } = gameContext\n        \n        // Determine current activity\n        let activity: 'menu' | 'baking' | 'managing' | 'multiplayer' | 'idle' = 'managing'\n        let currentOrder: string | undefined\n        \n        if (currentOrders && currentOrders.length > 0) {\n          activity = 'baking'\n          currentOrder = currentOrders[0].items[0]?.name || 'Unknown item'\n        }\n        \n        // Check if in multiplayer\n        if (multiplayerContext?.gameState === 'playing') {\n          activity = 'multiplayer'\n        }\n        \n        const playerStatus: PlayerStatus = {\n          level: player.level,\n          money: player.money,\n          currentActivity: activity,\n          currentOrder,\n          playTime: player.playTime\n        }\n        \n        // Update Discord RPC\n        await discordRPC.updatePlayerStatus(playerStatus)\n        \n      } catch (error) {\n        console.error('Failed to update Discord RPC from game state:', error)\n      }\n    }\n\n    // Update immediately and then every 30 seconds\n    updateRPCFromGameState()\n    const interval = setInterval(updateRPCFromGameState, 30000)\n    \n    return () => clearInterval(interval)\n  }, [gameContext?.player, gameContext?.currentOrders, multiplayerContext?.gameState, isClient, isEnabled, isConnected])\n\n  // Auto-update for multiplayer state\n  useEffect(() => {\n    if (!isClient || !isEnabled || !isConnected || !multiplayerContext) return\n\n    const updateMultiplayerRPC = async () => {\n      try {\n        const { gameState, currentRoom, players } = multiplayerContext\n        \n        if (gameState === 'playing' && currentRoom) {\n          await discordRPC.setMultiplayerActivity(\n            currentRoom.id,\n            players.length,\n            currentRoom.maxPlayers || 4\n          )\n        }\n      } catch (error) {\n        console.error('Failed to update multiplayer Discord RPC:', error)\n      }\n    }\n\n    updateMultiplayerRPC()\n  }, [multiplayerContext?.gameState, multiplayerContext?.currentRoom, multiplayerContext?.players, isClient, isEnabled, isConnected])\n\n  const handleSetEnabled = (enabled: boolean) => {\n    setIsEnabled(enabled)\n    discordRPC.setEnabled(enabled)\n\n    // Save preference to localStorage\n    if (isClient) {\n      localStorage.setItem('discordRPCEnabled', enabled.toString())\n    }\n  }\n\n  const updateActivity = async (activity: GameActivity) => {\n    await discordRPC.updateActivity(activity)\n    setCurrentActivity(activity)\n  }\n\n  const setMenuActivity = async () => {\n    await discordRPC.setMenuActivity()\n    setCurrentActivity(discordRPC.getCurrentActivity())\n  }\n\n  const setGameActivity = async (level: number, money: number, activity?: string) => {\n    await discordRPC.setGameActivity(level, money, activity)\n    setCurrentActivity(discordRPC.getCurrentActivity())\n  }\n\n  const setMultiplayerActivity = async (roomId: string, playerCount: number, maxPlayers: number) => {\n    await discordRPC.setMultiplayerActivity(roomId, playerCount, maxPlayers)\n    setCurrentActivity(discordRPC.getCurrentActivity())\n  }\n\n  const setBakingActivity = async (level: number, currentOrder: string) => {\n    await discordRPC.setBakingActivity(level, currentOrder)\n    setCurrentActivity(discordRPC.getCurrentActivity())\n  }\n\n  const clearActivity = async () => {\n    await discordRPC.clearActivity()\n    setCurrentActivity(null)\n  }\n\n  // This effect is now handled in the client-side initialization above\n\n  // Cleanup on unmount\n  useEffect(() => {\n    return () => {\n      discordRPC.cleanup()\n    }\n  }, [])\n\n  const value: DiscordRPCContextType = {\n    isEnabled,\n    isConnected,\n    currentActivity,\n    setEnabled: handleSetEnabled,\n    updateActivity,\n    setMenuActivity,\n    setGameActivity,\n    setMultiplayerActivity,\n    setBakingActivity,\n    clearActivity\n  }\n\n  return (\n    <DiscordRPCContext.Provider value={value}>\n      {children}\n    </DiscordRPCContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAoBA,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAqC;AAEpE,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,wDAAwD;QACxD,OAAO;YACL,WAAW;YACX,aAAa;YACb,iBAAiB;YACjB,YAAY,KAAO;YACnB,gBAAgB,WAAa;YAC7B,iBAAiB,WAAa;YAC9B,iBAAiB,WAAa;YAC9B,wBAAwB,WAAa;YACrC,mBAAmB,WAAa;YAChC,eAAe,WAAa;QAC9B;IACF;IACA,OAAO;AACT;AAMO,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,uCAAuC;;IACzF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;IAC5E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,uCAAuC;IACvC,MAAM,cAAc,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,qBAAqB,CAAA,GAAA,sIAAA,CAAA,iBAAc,AAAD;IAExC,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;QAEZ,wBAAwB;QACxB,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,UAAU,MAAM;YAClB,MAAM,UAAU,UAAU;YAC1B,aAAa;QACf,OAAO;YACL,gDAAgD;YAChD,MAAM,eAAe,OAAO,WAAW,KAAK;YAC5C,aAAa;QACf;IACF,GAAG,EAAE;IAEL,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,UAAU;QAEf,wEAAwE;QACxE,MAAM,eAAe,gBAAkB,eACnB,OAAO,WAAW,KAAK,aACvB;QAEpB;;aAeO;YACL,wHAAA,CAAA,aAAU,CAAC,UAAU,CAAC;QACxB;IACF,GAAG;QAAC;QAAU;KAAU;IAExB,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,eAAe,CAAC,aAAa;QAE7D,MAAM,yBAAyB;YAC7B,IAAI;gBACF,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG;gBAElC,6BAA6B;gBAC7B,IAAI,WAAoE;gBACxE,IAAI;gBAEJ,IAAI,iBAAiB,cAAc,MAAM,GAAG,GAAG;oBAC7C,WAAW;oBACX,eAAe,aAAa,CAAC,EAAE,CAAC,KAAK,CAAC,EAAE,EAAE,QAAQ;gBACpD;gBAEA,0BAA0B;gBAC1B,IAAI,oBAAoB,cAAc,WAAW;oBAC/C,WAAW;gBACb;gBAEA,MAAM,eAA6B;oBACjC,OAAO,OAAO,KAAK;oBACnB,OAAO,OAAO,KAAK;oBACnB,iBAAiB;oBACjB;oBACA,UAAU,OAAO,QAAQ;gBAC3B;gBAEA,qBAAqB;gBACrB,MAAM,wHAAA,CAAA,aAAU,CAAC,kBAAkB,CAAC;YAEtC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iDAAiD;YACjE;QACF;QAEA,+CAA+C;QAC/C;QACA,MAAM,WAAW,YAAY,wBAAwB;QAErD,OAAO,IAAM,cAAc;IAC7B,GAAG;QAAC,aAAa;QAAQ,aAAa;QAAe,oBAAoB;QAAW;QAAU;QAAW;KAAY;IAErH,oCAAoC;IACpC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,eAAe,CAAC,oBAAoB;QAEpE,MAAM,uBAAuB;YAC3B,IAAI;gBACF,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG;gBAE5C,IAAI,cAAc,aAAa,aAAa;oBAC1C,MAAM,wHAAA,CAAA,aAAU,CAAC,sBAAsB,CACrC,YAAY,EAAE,EACd,QAAQ,MAAM,EACd,YAAY,UAAU,IAAI;gBAE9B;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6CAA6C;YAC7D;QACF;QAEA;IACF,GAAG;QAAC,oBAAoB;QAAW,oBAAoB;QAAa,oBAAoB;QAAS;QAAU;QAAW;KAAY;IAElI,MAAM,mBAAmB,CAAC;QACxB,aAAa;QACb,wHAAA,CAAA,aAAU,CAAC,UAAU,CAAC;QAEtB,kCAAkC;QAClC,IAAI,UAAU;YACZ,aAAa,OAAO,CAAC,qBAAqB,QAAQ,QAAQ;QAC5D;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,MAAM,wHAAA,CAAA,aAAU,CAAC,cAAc,CAAC;QAChC,mBAAmB;IACrB;IAEA,MAAM,kBAAkB;QACtB,MAAM,wHAAA,CAAA,aAAU,CAAC,eAAe;QAChC,mBAAmB,wHAAA,CAAA,aAAU,CAAC,kBAAkB;IAClD;IAEA,MAAM,kBAAkB,OAAO,OAAe,OAAe;QAC3D,MAAM,wHAAA,CAAA,aAAU,CAAC,eAAe,CAAC,OAAO,OAAO;QAC/C,mBAAmB,wHAAA,CAAA,aAAU,CAAC,kBAAkB;IAClD;IAEA,MAAM,yBAAyB,OAAO,QAAgB,aAAqB;QACzE,MAAM,wHAAA,CAAA,aAAU,CAAC,sBAAsB,CAAC,QAAQ,aAAa;QAC7D,mBAAmB,wHAAA,CAAA,aAAU,CAAC,kBAAkB;IAClD;IAEA,MAAM,oBAAoB,OAAO,OAAe;QAC9C,MAAM,wHAAA,CAAA,aAAU,CAAC,iBAAiB,CAAC,OAAO;QAC1C,mBAAmB,wHAAA,CAAA,aAAU,CAAC,kBAAkB;IAClD;IAEA,MAAM,gBAAgB;QACpB,MAAM,wHAAA,CAAA,aAAU,CAAC,aAAa;QAC9B,mBAAmB;IACrB;IAEA,qEAAqE;IAErE,qBAAqB;IACrB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,OAAO;YACL,wHAAA,CAAA,aAAU,CAAC,OAAO;QACpB;IACF,GAAG,EAAE;IAEL,MAAM,QAA+B;QACnC;QACA;QACA;QACA,YAAY;QACZ;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,8OAAC,kBAAkB,QAAQ;QAAC,OAAO;kBAChC;;;;;;AAGP", "debugId": null}}]}