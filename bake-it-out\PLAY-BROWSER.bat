@echo off
title Bake It Out - Browser Version

echo.
echo ===============================================
echo        BAKE IT OUT - Browser Version
echo ===============================================
echo.

REM Check if build exists
if not exist "out\index.html" (
    echo Building the game...
    npm run build
    if %errorlevel% neq 0 (
        echo ERROR: Build failed
        echo Make sure you have run: npm install
        pause
        exit /b 1
    )
    echo SUCCESS: Game built!
    echo.
)

echo Starting browser version...
echo.
echo The game will be available at:
echo   http://localhost:3000
echo.
echo Opening in your default browser...
echo Press Ctrl+C to stop the server
echo.

REM Start the server and open browser
echo Starting server...
start /B npx serve out -p 3000 --single >nul 2>&1

REM Wait a moment for server to start
timeout /t 3 >nul

REM Try to open browser
start http://localhost:3000

echo.
echo ===============================================
echo                SERVER STARTED!
echo ===============================================
echo.
echo Your game is now running at:
echo   http://localhost:3000
echo.
echo If the browser didn't open automatically,
echo copy and paste the URL above into your browser.
echo.
echo Press Ctrl+C to stop the server when done playing.
echo.
echo Enjoy your bakery management adventure!
echo.

REM Keep the window open
pause

pause
