'use client'

import { useState, useEffect } from 'react'
import { useLanguage } from '@/contexts/LanguageContext'
import { useCloudSave } from '@/contexts/CloudSaveContext'
import { Button } from '@/components/ui/Button'
import { CloudAuthModal } from './CloudAuthModal'

interface CloudSaveModalProps {
  isOpen: boolean
  onClose: () => void
  mode: 'save' | 'load'
  currentGameData?: any
  onLoadGame?: (gameData: any) => void
}

export function CloudSaveModal({ 
  isOpen, 
  onClose, 
  mode, 
  currentGameData, 
  onLoadGame 
}: CloudSaveModalProps) {
  const { t } = useLanguage()
  const {
    isAuthenticated,
    user,
    cloudSaves,
    isLoadingSaves,
    saveToCloud,
    loadFromCloud,
    deleteCloudSave,
    refreshCloudSaves,
    syncStatus,
    lastSyncTime,
    error,
    clearError
  } = useCloudSave()

  const [showAuthModal, setShowAuthModal] = useState(false)
  const [authMode, setAuthMode] = useState<'login' | 'register'>('login')
  const [saveName, setSaveName] = useState('')
  const [selectedSaveId, setSelectedSaveId] = useState<string | null>(null)
  const [isProcessing, setIsProcessing] = useState(false)

  useEffect(() => {
    if (isOpen && isAuthenticated) {
      refreshCloudSaves()
    }
  }, [isOpen, isAuthenticated])

  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        clearError()
      }, 5000)
      return () => clearTimeout(timer)
    }
  }, [error, clearError])

  if (!isOpen) return null

  const handleSave = async () => {
    if (!saveName.trim() || !currentGameData) return

    setIsProcessing(true)
    try {
      const saveId = await saveToCloud(saveName, currentGameData)
      if (saveId) {
        setSaveName('')
        onClose()
      }
    } catch (error) {
      console.error('Save failed:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleLoad = async () => {
    if (!selectedSaveId || !onLoadGame) return

    setIsProcessing(true)
    try {
      const gameData = await loadFromCloud(selectedSaveId)
      if (gameData) {
        onLoadGame(gameData)
        onClose()
      }
    } catch (error) {
      console.error('Load failed:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  const handleDelete = async (saveId: string) => {
    if (!confirm(t('cloud.save.confirm_delete', 'Are you sure you want to delete this save?'))) {
      return
    }

    const success = await deleteCloudSave(saveId)
    if (success && selectedSaveId === saveId) {
      setSelectedSaveId(null)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  if (!isAuthenticated) {
    return (
      <>
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">
                {t('cloud.save.auth_required', 'Cloud Save Account Required')}
              </h2>
              <p className="text-gray-600 mb-6">
                {t('cloud.save.auth_description', 'You need to login or create an account to use cloud saves.')}
              </p>
              <div className="flex space-x-3">
                <Button
                  variant="primary"
                  onClick={() => {
                    setAuthMode('login')
                    setShowAuthModal(true)
                  }}
                  className="flex-1"
                >
                  {t('cloud.auth.login', 'Login')}
                </Button>
                <Button
                  variant="secondary"
                  onClick={() => {
                    setAuthMode('register')
                    setShowAuthModal(true)
                  }}
                  className="flex-1"
                >
                  {t('cloud.auth.register', 'Register')}
                </Button>
              </div>
              <Button
                variant="secondary"
                onClick={onClose}
                className="w-full mt-3"
              >
                {t('cloud.auth.cancel', 'Cancel')}
              </Button>
            </div>
          </div>
        </div>

        <CloudAuthModal
          isOpen={showAuthModal}
          onClose={() => setShowAuthModal(false)}
          mode={authMode}
          onModeChange={setAuthMode}
        />
      </>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden flex flex-col">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h2 className="text-2xl font-bold text-gray-800">
              {mode === 'save' 
                ? t('cloud.save.save_title', 'Save to Cloud')
                : t('cloud.save.load_title', 'Load from Cloud')
              }
            </h2>
            <p className="text-sm text-gray-600">
              {t('cloud.save.logged_in_as', 'Logged in as')}: {user?.username}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 text-2xl"
          >
            ×
          </button>
        </div>

        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
            {error}
          </div>
        )}

        {/* Sync Status */}
        <div className="flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-2">
            <div className={`w-3 h-3 rounded-full ${
              syncStatus === 'syncing' ? 'bg-yellow-500 animate-pulse' :
              syncStatus === 'success' ? 'bg-green-500' :
              syncStatus === 'error' ? 'bg-red-500' : 'bg-gray-400'
            }`} />
            <span className="text-sm text-gray-600">
              {syncStatus === 'syncing' && t('cloud.save.syncing', 'Syncing...')}
              {syncStatus === 'success' && t('cloud.save.sync_success', 'Synced')}
              {syncStatus === 'error' && t('cloud.save.sync_error', 'Sync Error')}
              {syncStatus === 'idle' && t('cloud.save.sync_idle', 'Ready')}
            </span>
          </div>
          {lastSyncTime && (
            <span className="text-xs text-gray-500">
              {t('cloud.save.last_sync', 'Last sync')}: {formatDate(lastSyncTime.toISOString())}
            </span>
          )}
        </div>

        {mode === 'save' && (
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              {t('cloud.save.save_name', 'Save Name')}
            </label>
            <input
              type="text"
              value={saveName}
              onChange={(e) => setSaveName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
              placeholder={t('cloud.save.save_name_placeholder', 'Enter a name for your save')}
              maxLength={100}
            />
          </div>
        )}

        {/* Cloud Saves List */}
        <div className="flex-1 overflow-hidden">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-gray-800">
              {t('cloud.save.your_saves', 'Your Cloud Saves')} ({cloudSaves.length})
            </h3>
            <Button
              variant="secondary"
              size="sm"
              onClick={refreshCloudSaves}
              disabled={isLoadingSaves}
            >
              {isLoadingSaves ? t('cloud.save.refreshing', 'Refreshing...') : t('cloud.save.refresh', 'Refresh')}
            </Button>
          </div>

          <div className="overflow-y-auto max-h-96 border border-gray-200 rounded-lg">
            {isLoadingSaves ? (
              <div className="p-8 text-center text-gray-500">
                {t('cloud.save.loading_saves', 'Loading saves...')}
              </div>
            ) : cloudSaves.length === 0 ? (
              <div className="p-8 text-center text-gray-500">
                {t('cloud.save.no_saves', 'No cloud saves found')}
              </div>
            ) : (
              <div className="divide-y divide-gray-200">
                {cloudSaves.map((save) => (
                  <div
                    key={save.id}
                    className={`p-4 hover:bg-gray-50 cursor-pointer ${
                      selectedSaveId === save.id ? 'bg-orange-50 border-l-4 border-orange-500' : ''
                    }`}
                    onClick={() => setSelectedSaveId(save.id)}
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-800">{save.saveName}</h4>
                        <div className="text-sm text-gray-600 mt-1">
                          <div className="flex items-center space-x-4">
                            <span>
                              {t('cloud.save.level', 'Level')} {save.metadata.level}
                            </span>
                            <span>
                              ${save.metadata.money}
                            </span>
                            <span className={`px-2 py-1 rounded text-xs ${
                              save.saveType === 'manual' ? 'bg-blue-100 text-blue-800' :
                              save.saveType === 'auto' ? 'bg-green-100 text-green-800' :
                              'bg-purple-100 text-purple-800'
                            }`}>
                              {save.saveType}
                            </span>
                          </div>
                          <div className="mt-1 text-xs text-gray-500">
                            {formatDate(save.updatedAt)} • {formatFileSize(save.size)}
                          </div>
                        </div>
                      </div>
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          handleDelete(save.id)
                        }}
                        className="text-red-500 hover:text-red-700 ml-4"
                        title={t('cloud.save.delete', 'Delete')}
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-3 mt-6">
          <Button
            variant="secondary"
            onClick={onClose}
            disabled={isProcessing}
          >
            {t('cloud.save.cancel', 'Cancel')}
          </Button>
          
          {mode === 'save' ? (
            <Button
              variant="primary"
              onClick={handleSave}
              disabled={!saveName.trim() || isProcessing}
            >
              {isProcessing 
                ? t('cloud.save.saving', 'Saving...')
                : t('cloud.save.save_button', 'Save to Cloud')
              }
            </Button>
          ) : (
            <Button
              variant="primary"
              onClick={handleLoad}
              disabled={!selectedSaveId || isProcessing}
            >
              {isProcessing 
                ? t('cloud.save.loading', 'Loading...')
                : t('cloud.save.load_button', 'Load Game')
              }
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}
