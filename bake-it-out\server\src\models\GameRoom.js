const mongoose = require('mongoose');

const gameRoomSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 50
  },
  host: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true,
    index: true
  },
  players: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  maxPlayers: {
    type: Number,
    required: true,
    min: 2,
    max: 8,
    default: 4
  },
  isPrivate: {
    type: Boolean,
    default: false
  },
  roomCode: {
    type: String,
    required: true,
    unique: true,
    uppercase: true,
    length: 6
  },
  gameMode: {
    type: String,
    enum: ['cooperative', 'competitive', 'sandbox'],
    default: 'cooperative'
  },
  status: {
    type: String,
    enum: ['waiting', 'playing', 'paused', 'finished'],
    default: 'waiting'
  },
  gameState: {
    type: mongoose.Schema.Types.Mixed,
    default: {}
  },
  settings: {
    difficulty: {
      type: String,
      enum: ['easy', 'medium', 'hard'],
      default: 'medium'
    },
    timeLimit: {
      type: Number,
      default: 0 // 0 = no time limit
    },
    autoSave: {
      type: Boolean,
      default: true
    },
    allowSpectators: {
      type: Boolean,
      default: false
    }
  },
  statistics: {
    gamesPlayed: {
      type: Number,
      default: 0
    },
    totalPlayTime: {
      type: Number,
      default: 0
    },
    averageScore: {
      type: Number,
      default: 0
    },
    bestScore: {
      type: Number,
      default: 0
    }
  },
  isActive: {
    type: Boolean,
    default: true
  },
  lastActivity: {
    type: Date,
    default: Date.now
  }
}, {
  timestamps: true,
  toJSON: {
    transform: function(doc, ret) {
      delete ret.__v;
      return ret;
    }
  }
});

// Indexes for performance
gameRoomSchema.index({ host: 1, isActive: 1 });
gameRoomSchema.index({ isActive: 1, createdAt: -1 });
gameRoomSchema.index({ roomCode: 1 });
gameRoomSchema.index({ lastActivity: 1 });

// Auto-cleanup inactive rooms (older than 24 hours)
gameRoomSchema.index({ lastActivity: 1 }, { expireAfterSeconds: 86400 });

// Virtual for player count
gameRoomSchema.virtual('playerCount').get(function() {
  return this.players.length;
});

// Virtual for is full
gameRoomSchema.virtual('isFull').get(function() {
  return this.players.length >= this.maxPlayers;
});

// Static method to get active rooms
gameRoomSchema.statics.getActiveRooms = function(options = {}) {
  const {
    limit = 20,
    skip = 0,
    gameMode = null,
    includePrivate = false
  } = options;

  const query = { isActive: true };
  if (gameMode) query.gameMode = gameMode;
  if (!includePrivate) query.isPrivate = false;

  return this.find(query)
    .populate('host', 'username profile.displayName')
    .populate('players', 'username profile.displayName')
    .sort({ lastActivity: -1 })
    .limit(limit)
    .skip(skip);
};

// Static method to find room by code
gameRoomSchema.statics.findByCode = function(roomCode) {
  return this.findOne({ roomCode: roomCode.toUpperCase(), isActive: true })
    .populate('host', 'username profile.displayName')
    .populate('players', 'username profile.displayName');
};

// Instance method to add player
gameRoomSchema.methods.addPlayer = function(userId) {
  if (this.players.includes(userId)) {
    throw new Error('Player already in room');
  }
  
  if (this.players.length >= this.maxPlayers) {
    throw new Error('Room is full');
  }
  
  this.players.push(userId);
  this.lastActivity = new Date();
  return this.save();
};

// Instance method to remove player
gameRoomSchema.methods.removePlayer = function(userId) {
  this.players = this.players.filter(playerId => !playerId.equals(userId));
  
  // If host leaves, transfer to another player or deactivate
  if (this.host.equals(userId)) {
    if (this.players.length > 0) {
      this.host = this.players[0];
    } else {
      this.isActive = false;
    }
  }
  
  this.lastActivity = new Date();
  return this.save();
};

// Instance method to update game state
gameRoomSchema.methods.updateGameState = function(newState) {
  this.gameState = { ...this.gameState, ...newState };
  this.lastActivity = new Date();
  return this.save();
};

// Instance method to start game
gameRoomSchema.methods.startGame = function() {
  if (this.players.length < 2) {
    throw new Error('Need at least 2 players to start');
  }
  
  this.status = 'playing';
  this.lastActivity = new Date();
  this.statistics.gamesPlayed += 1;
  return this.save();
};

// Instance method to end game
gameRoomSchema.methods.endGame = function(finalScore = 0) {
  this.status = 'finished';
  this.lastActivity = new Date();
  
  // Update statistics
  if (finalScore > this.statistics.bestScore) {
    this.statistics.bestScore = finalScore;
  }
  
  // Calculate average score
  const totalGames = this.statistics.gamesPlayed;
  this.statistics.averageScore = 
    (this.statistics.averageScore * (totalGames - 1) + finalScore) / totalGames;
  
  return this.save();
};

// Instance method to get room info for client
gameRoomSchema.methods.getClientInfo = function() {
  return {
    id: this._id,
    name: this.name,
    host: this.host,
    players: this.players,
    maxPlayers: this.maxPlayers,
    playerCount: this.playerCount,
    isFull: this.isFull,
    isPrivate: this.isPrivate,
    gameMode: this.gameMode,
    status: this.status,
    settings: this.settings,
    statistics: this.statistics,
    createdAt: this.createdAt,
    lastActivity: this.lastActivity
  };
};

// Pre-save middleware to update lastActivity
gameRoomSchema.pre('save', function(next) {
  if (this.isModified() && !this.isModified('lastActivity')) {
    this.lastActivity = new Date();
  }
  next();
});

module.exports = mongoose.model('GameRoom', gameRoomSchema);
