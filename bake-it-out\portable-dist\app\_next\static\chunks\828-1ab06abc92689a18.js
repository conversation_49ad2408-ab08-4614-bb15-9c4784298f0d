"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[828],{828:(e,s,a)=>{a.r(s),a.d(s,{default:()=>J});var t=a(5155),r=a(2115),l=a(9283),i=a(2517),n=a(3741),c=a(9419),d=a(2163),o=a(4983);function m(e,s){return e.name.startsWith("recipe.")?s(e.name,e.name.replace("recipe.","").replace(/_/g," ")):e.name}function x(e,s){return e.name.startsWith("ingredient.")?s(e.name,e.name.replace("ingredient.","").replace(/_/g," ")):e.name}function u(e){let{isOpen:s,onClose:a}=e,{player:c,inventory:d}=(0,i.I)(),{t:u}=(0,l.o)(),[h,g]=(0,r.useState)("all");if(!s)return null;let p=(0,o.x0)(c.level),v="all"===h?p:p.filter(e=>e.category===h),b=e=>e.ingredients.every(e=>{let s=d.find(s=>s.name===e.name);return s&&s.quantity>=e.quantity}),y=[{id:"all",name:u("recipes.all"),icon:"\uD83C\uDF7D️"},{id:"cookies",name:u("recipes.cookies"),icon:"\uD83C\uDF6A"},{id:"cakes",name:u("recipes.cakes"),icon:"\uD83E\uDDC1"},{id:"bread",name:u("recipes.bread"),icon:"\uD83C\uDF5E"},{id:"pastries",name:u("recipes.pastries"),icon:"\uD83E\uDD50"}];return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:u("modal.recipes.title")}),(0,t.jsx)(n.$,{variant:"secondary",onClick:a,children:u("game.close")})]})}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:y.map(e=>(0,t.jsxs)(n.$,{variant:h===e.id?"primary":"secondary",size:"sm",onClick:()=>g(e.id),children:[e.icon," ",e.name]},e.id))}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto",children:v.map(e=>{let s;return(0,t.jsxs)("div",{className:"p-4 rounded-lg border-2 ".concat(b(e)?"border-green-300 bg-green-50":"border-gray-300 bg-gray-50"),children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800",children:m(e,u)}),(0,t.jsxs)("span",{className:"text-sm text-green-600",children:["$",e.basePrice]})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mb-2",children:[(s=e.difficulty,"⭐".repeat(s)+"☆".repeat(5-s))," • ⏱️ ",(e=>{let s=Math.floor(e/60);return"".concat(s,":").concat((e%60).toString().padStart(2,"0"))})(e.bakingTime)]}),(0,t.jsxs)("div",{className:"space-y-1 mb-3",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-700",children:u("recipes.ingredients")}),e.ingredients.map((e,s)=>{let a=d.find(s=>s.name===e.name),r=a&&a.quantity>=e.quantity;return(0,t.jsxs)("div",{className:"text-xs flex justify-between ".concat(r?"text-green-600":"text-red-600"),children:[(0,t.jsx)("span",{children:x(e,u)}),(0,t.jsxs)("span",{children:[e.quantity,a&&(0,t.jsxs)("span",{className:"ml-1",children:["(",a.quantity," available)"]})]})]},s)})]}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:u("recipes.unlockLevel",{level:e.unlockLevel.toString()})}),b(e)&&(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsx)(n.$,{size:"sm",variant:"success",className:"w-full",children:u("recipes.canCraft")})})]},e.id)})}),0===v.length&&(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)("div",{className:"text-4xl mb-2",children:"\uD83D\uDCDD"}),(0,t.jsx)("p",{children:u("recipes.noRecipes")}),(0,t.jsx)("p",{className:"text-sm",children:u("recipes.levelUpToUnlock")})]})]})]})})}function h(e){let{isOpen:s,onClose:a}=e,{player:c,inventory:d,spendMoney:o,addIngredient:m}=(0,i.I)(),{t:u}=(0,l.o)(),[h,g]=(0,r.useState)({});if(!s)return null;let p=(e,s)=>{g(a=>({...a,[e]:Math.max(0,s)}))},v=(e,s)=>s*(h[e]||1),b=(e,s)=>c.money>=v(e,s);return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:u("modal.shop.title")}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,t.jsx)("span",{className:"text-green-800 font-medium",children:u("ui.money",{amount:c.money.toString()})})}),(0,t.jsx)(n.$,{variant:"secondary",onClick:a,children:u("game.close")})]})]})}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("div",{className:"space-y-4 max-h-[60vh] overflow-y-auto",children:d.map(e=>{let s=h[e.name]||1,a=v(e.name,e.cost),r=b(e.name,e.cost);return(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"text-2xl",children:e.icon}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-800",children:x({name:e.name,quantity:0},u)}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:u("shop.currentStock",{quantity:e.quantity.toString()})}),(0,t.jsx)("p",{className:"text-sm text-green-600",children:u("inventory.cost",{cost:e.cost.toString()})})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(n.$,{size:"sm",variant:"secondary",onClick:()=>p(e.name,s-1),disabled:s<=1,children:"-"}),(0,t.jsx)("span",{className:"w-12 text-center font-mono",children:s}),(0,t.jsx)(n.$,{size:"sm",variant:"secondary",onClick:()=>p(e.name,s+1),disabled:!b(e.name,e.cost)&&s>=1,children:"+"})]}),(0,t.jsxs)("div",{className:"text-right min-w-[80px]",children:[(0,t.jsxs)("div",{className:"font-medium ".concat(r?"text-green-600":"text-red-600"),children:["$",a]}),(0,t.jsx)(n.$,{size:"sm",variant:r?"success":"secondary",onClick:()=>((e,s)=>{let a=h[e]||1;o(s*a)&&(m(e,a),g(s=>({...s,[e]:0})))})(e.name,e.cost),disabled:!r,className:"mt-1",children:r?u("shop.buy"):u("shop.tooExpensive")})]})]})]},e.name)})}),(0,t.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:u("shop.tips.title")}),(0,t.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,t.jsx)("li",{children:u("shop.tips.bulk")}),(0,t.jsx)("li",{children:u("shop.tips.stock")}),(0,t.jsx)("li",{children:u("shop.tips.rare")}),(0,t.jsx)("li",{children:u("shop.tips.prices")})]})]})]})]})})}function g(e){let{isOpen:s,onClose:a,equipmentId:c,equipmentName:d}=e,{player:u,inventory:h,updateEquipment:g,useIngredient:p}=(0,i.I)(),{t:v}=(0,l.o)(),[b,y]=(0,r.useState)(null);if(!s)return null;let j=(0,o.x0)(u.level).filter(e=>(0,o.hF)(e,h)),f=e=>{let s=Math.floor(e/60);return"".concat(s,":").concat((e%60).toString().padStart(2,"0"))},N=e=>"⭐".repeat(e)+"☆".repeat(5-e);return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-orange-800",children:["\uD83D\uDD25 ",d," - Select Recipe"]}),(0,t.jsx)(n.$,{variant:"secondary",onClick:a,children:"✕ Close"})]})}),(0,t.jsxs)("div",{className:"p-6",children:[0===j.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDE14"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-2",children:"No recipes available"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have enough ingredients to craft any recipes."}),(0,t.jsx)(n.$,{variant:"primary",onClick:a,children:"Buy Ingredients"})]}):(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[60vh] overflow-y-auto",children:j.map(e=>(0,t.jsxs)("div",{className:"p-4 rounded-lg border-2 cursor-pointer transition-all ".concat((null==b?void 0:b.id)===e.id?"border-orange-400 bg-orange-50":"border-gray-300 bg-gray-50 hover:border-orange-300"),onClick:()=>y(e),children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:(e=>{switch(e){case"cookies":return"\uD83C\uDF6A";case"cakes":return"\uD83E\uDDC1";case"bread":return"\uD83C\uDF5E";case"pastries":return"\uD83E\uDD50";default:return"\uD83C\uDF7D️"}})(e.category)}),(0,t.jsx)("h3",{className:"font-semibold text-gray-800",children:m(e,v)})]}),(0,t.jsxs)("span",{className:"text-sm text-green-600",children:["$",e.basePrice]})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mb-2",children:[N(e.difficulty)," • ⏱️ ",f(e.bakingTime)]}),(0,t.jsxs)("div",{className:"space-y-1 mb-3",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-700",children:"Ingredients:"}),e.ingredients.map((e,s)=>{let a=h.find(s=>s.name===e.name);return(0,t.jsxs)("div",{className:"text-xs flex justify-between text-green-600",children:[(0,t.jsx)("span",{children:x(e,v)}),(0,t.jsxs)("span",{children:[e.quantity,(0,t.jsxs)("span",{className:"ml-1",children:["(",(null==a?void 0:a.quantity)||0," available)"]})]})]},s)})]}),(null==b?void 0:b.id)===e.id&&(0,t.jsx)(n.$,{variant:"success",size:"sm",className:"w-full",onClick:()=>{var s;return s=e,void((0,o.hF)(s,h)&&s.ingredients.every(e=>{let s=h.find(s=>s.name===e.name);return s&&s.quantity>=e.quantity})&&(s.ingredients.forEach(e=>{p(e.name,e.quantity)}),g(c,{isActive:!0,timeRemaining:s.bakingTime,currentRecipe:s.name}),a()))},children:"\uD83D\uDD25 Start Baking"})]},e.id))}),b&&j.length>0&&(0,t.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,t.jsxs)("h3",{className:"font-medium text-blue-800 mb-2",children:["\uD83D\uDCCB Baking Instructions for ",b.name]}),(0,t.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,t.jsxs)("p",{children:["• Baking time: ",f(b.bakingTime)]}),(0,t.jsxs)("p",{children:["• Difficulty: ",N(b.difficulty)]}),(0,t.jsxs)("p",{children:["• Expected reward: $",b.basePrice]}),(0,t.jsx)("p",{children:"• Make sure you have all ingredients before starting!"})]})]})]})]})})}function p(e){let{notifications:s,onRemove:a}=e;return((0,r.useEffect)(()=>{s.forEach(e=>{if(e.duration){let s=setTimeout(()=>{a(e.id)},e.duration);return()=>clearTimeout(s)}})},[s,a]),0===s.length)?null:(0,t.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2 max-w-sm",children:s.map(e=>(0,t.jsx)("div",{className:"p-4 rounded-lg border-l-4 shadow-lg transition-all duration-300 ".concat((e=>{switch(e){case"success":return"bg-green-100 border-green-400 text-green-800";case"error":return"bg-red-100 border-red-400 text-red-800";case"warning":return"bg-yellow-100 border-yellow-400 text-yellow-800";case"info":return"bg-blue-100 border-blue-400 text-blue-800";default:return"bg-gray-100 border-gray-400 text-gray-800"}})(e.type)),children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,t.jsx)("span",{className:"text-lg",children:(e=>{switch(e){case"success":return"✅";case"error":return"❌";case"warning":return"⚠️";case"info":return"ℹ️";default:return"\uD83D\uDCE2"}})(e.type)}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:e.title}),(0,t.jsx)("p",{className:"text-sm opacity-90",children:e.message})]})]}),(0,t.jsx)("button",{onClick:()=>a(e.id),className:"text-lg opacity-60 hover:opacity-100 transition-opacity",children:"\xd7"})]})},e.id))})}function v(e){let{isOpen:s,onClose:a,newLevel:r,rewards:l}=e;return s?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full overflow-hidden",children:[(0,t.jsxs)("div",{className:"bg-gradient-to-r from-yellow-400 to-orange-500 p-6 text-center",children:[(0,t.jsx)("div",{className:"text-6xl mb-2",children:"\uD83C\uDF89"}),(0,t.jsx)("h2",{className:"text-3xl font-bold text-white mb-2",children:"Level Up!"}),(0,t.jsxs)("p",{className:"text-xl text-yellow-100",children:["You reached Level ",r,"!"]})]}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"\uD83C\uDF81 Level Rewards"}),(0,t.jsx)("div",{className:"space-y-3 mb-6",children:l.map((e,s)=>(0,t.jsx)("div",{className:"p-3 rounded-lg border ".concat((e=>{switch(e){case"recipe":return"bg-blue-50 border-blue-300 text-blue-800";case"equipment":return"bg-purple-50 border-purple-300 text-purple-800";case"money":return"bg-green-50 border-green-300 text-green-800";case"skill_point":return"bg-yellow-50 border-yellow-300 text-yellow-800";case"achievement":return"bg-orange-50 border-orange-300 text-orange-800";default:return"bg-gray-50 border-gray-300 text-gray-800"}})(e.type)),children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("span",{className:"text-2xl",children:(e=>{switch(e){case"recipe":return"\uD83D\uDCD6";case"equipment":return"⚙️";case"money":return"\uD83D\uDCB0";case"skill_point":return"⭐";case"achievement":return"\uD83C\uDFC6";default:return"\uD83C\uDF81"}})(e.type)}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"font-medium",children:e.name}),(0,t.jsx)("p",{className:"text-sm opacity-80",children:e.description}),e.value&&(0,t.jsx)("p",{className:"text-sm font-semibold",children:"money"===e.type?"$".concat(e.value):"+".concat(e.value)})]})]})},s))}),(0,t.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg mb-6",children:[(0,t.jsx)("h4",{className:"font-medium text-blue-800 mb-2",children:"\uD83D\uDCA1 What's Next?"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,t.jsx)("li",{children:"• Check out new recipes in your recipe book"}),(0,t.jsx)("li",{children:"• Visit the shop for new equipment"}),(0,t.jsx)("li",{children:"• Take on more challenging orders"}),(0,t.jsx)("li",{children:"• Invest in skill upgrades"})]})]}),(0,t.jsx)(n.$,{variant:"primary",size:"lg",className:"w-full",onClick:a,children:"\uD83D\uDE80 Continue Playing"})]})]})}):null}function b(e){let{isOpen:s,onClose:a,achievements:l}=e,[i,c]=(0,r.useState)("all");if(!s)return null;let d="all"===i?l:l.filter(e=>e.category===i),o=l.filter(e=>e.completed).length,m=l.length;return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:"\uD83C\uDFC6 Achievements"}),(0,t.jsxs)("p",{className:"text-gray-600",children:[o," of ",m," achievements completed"]})]}),(0,t.jsx)(n.$,{variant:"secondary",onClick:a,children:"✕ Close"})]})}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Overall Progress"}),(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:[Math.round(o/m*100),"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,t.jsx)("div",{className:"bg-gradient-to-r from-yellow-400 to-orange-500 h-3 rounded-full transition-all duration-500",style:{width:"".concat(o/m*100,"%")}})})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:[{id:"all",name:"All",icon:"\uD83C\uDFC6"},{id:"baking",name:"Baking",icon:"\uD83D\uDC68‍\uD83C\uDF73"},{id:"business",name:"Business",icon:"\uD83D\uDCBC"},{id:"efficiency",name:"Efficiency",icon:"⚡"},{id:"collection",name:"Collection",icon:"\uD83D\uDCDA"},{id:"special",name:"Special",icon:"⭐"}].map(e=>(0,t.jsxs)(n.$,{variant:i===e.id?"primary":"secondary",size:"sm",onClick:()=>c(e.id),children:[e.icon," ",e.name]},e.id))}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[50vh] overflow-y-auto",children:d.map(e=>{let s=e.completed?100:Math.min(...e.requirements.map(e=>e.current?Math.min(100,e.current/e.target*100):0)),a=e.completed,r=e.unlocked;return(0,t.jsx)("div",{className:"p-4 rounded-lg border-2 ".concat(a?"border-green-400 bg-green-50":r?"border-gray-300 bg-white":"border-gray-200 bg-gray-50 opacity-60"),children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"text-3xl ".concat(a?"grayscale-0":"grayscale"),children:e.icon}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("h3",{className:"font-semibold ".concat(a?"text-green-800":"text-gray-800"),children:[e.name,a&&(0,t.jsx)("span",{className:"ml-2",children:"✅"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:e.description}),r&&!a&&(0,t.jsxs)("div",{className:"mb-2",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,t.jsx)("span",{className:"text-xs text-gray-500",children:"Progress"}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:(e=>{if(e.completed)return"Completed!";let s=e.requirements[0];return"".concat(s.current||0," / ").concat(s.target)})(e)})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(s,"%")}})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,t.jsx)("span",{className:"text-gray-500",children:"Reward:"}),(0,t.jsx)("span",{className:"text-lg",children:(e=>{switch(e){case"recipe":return"\uD83D\uDCD6";case"equipment":return"⚙️";case"money":return"\uD83D\uDCB0";case"skill_point":return"⭐";default:return"\uD83C\uDF81"}})(e.reward.type)}),(0,t.jsx)("span",{className:"text-gray-700",children:e.reward.name}),e.reward.value&&(0,t.jsx)("span",{className:"text-green-600 font-medium",children:"money"===e.reward.type?"$".concat(e.reward.value):"+".concat(e.reward.value)})]})]})]})},e.id)})}),0===d.length&&(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFC6"}),(0,t.jsx)("p",{children:"No achievements in this category."})]})]})]})})}function y(e){let{isOpen:s,onClose:a,skills:l,skillPoints:i,playerLevel:c,onUpgradeSkill:d}=e,[o,m]=(0,r.useState)("all");if(!s)return null;let x="all"===o?l:l.filter(e=>e.category===o),u=e=>!(e.level>=e.maxLevel)&&!(i<e.cost)&&(!e.requirements.playerLevel||!(c<e.requirements.playerLevel))&&(!e.requirements.skills||e.requirements.skills.every(e=>{let s=l.find(s=>s.id===e);return s&&s.level>0}));return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:"\uD83C\uDF1F Skill Tree"}),(0,t.jsxs)("p",{className:"text-gray-600",children:["Available Skill Points: ",(0,t.jsx)("span",{className:"font-semibold text-blue-600",children:i})]})]}),(0,t.jsx)(n.$,{variant:"secondary",onClick:a,children:"✕ Close"})]})}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:[{id:"all",name:"All",icon:"\uD83C\uDF1F"},{id:"efficiency",name:"Efficiency",icon:"⚡"},{id:"automation",name:"Automation",icon:"\uD83E\uDD16"},{id:"quality",name:"Quality",icon:"\uD83D\uDC8E"},{id:"business",name:"Business",icon:"\uD83D\uDCBC"}].map(e=>(0,t.jsxs)(n.$,{variant:o===e.id?"primary":"secondary",size:"sm",onClick:()=>m(e.id),children:[e.icon," ",e.name]},e.id))}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto",children:x.map(e=>{let s=e.level>=e.maxLevel?"maxed":u(e)?"available":"locked",a=u(e);return(0,t.jsxs)("div",{className:"p-4 rounded-lg border-2 ".concat((e=>{switch(e){case"maxed":return"border-green-400 bg-green-50";case"available":return"border-blue-400 bg-blue-50";default:return"border-gray-300 bg-gray-50"}})(s)),children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:e.icon}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name}),(0,t.jsx)("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:e.category})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("div",{className:"text-sm font-medium text-gray-700",children:["Level ",e.level,"/",e.maxLevel]}),"maxed"!==s&&(0,t.jsxs)("div",{className:"text-xs text-blue-600",children:["Cost: ",e.cost," SP"]})]})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,t.jsxs)("div",{className:"mb-3",children:[(0,t.jsx)("h4",{className:"text-xs font-medium text-gray-700 mb-1",children:"Effects:"}),e.effects.map((e,s)=>(0,t.jsxs)("div",{className:"text-xs text-green-600",children:["• ",(e=>{let s=Math.round(100*e.value);switch(e.type){case"baking_speed":return"+".concat(s,"% baking speed");case"money_multiplier":return"+".concat(s,"% money earned");case"xp_multiplier":return"+".concat(s,"% experience gained");case"ingredient_efficiency":return"".concat(s,"% less ingredients used");case"automation_unlock":return"Unlock automation features";default:return"+".concat(s,"% bonus")}})(e)]},s))]}),e.requirements.playerLevel&&c<e.requirements.playerLevel&&(0,t.jsx)("div",{className:"mb-3",children:(0,t.jsxs)("div",{className:"text-xs text-red-600",children:["Requires Level ",e.requirements.playerLevel]})}),e.requirements.skills&&(0,t.jsx)("div",{className:"mb-3",children:(0,t.jsxs)("div",{className:"text-xs text-gray-600",children:["Requires: ",e.requirements.skills.map(e=>{let s=l.find(s=>s.id===e);return null==s?void 0:s.name}).join(", ")]})}),(0,t.jsx)("div",{className:"mb-3",children:(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(e.level/e.maxLevel*100,"%")}})})}),"maxed"===s?(0,t.jsx)(n.$,{variant:"success",size:"sm",className:"w-full",disabled:!0,children:"✅ Maxed"}):a?(0,t.jsxs)(n.$,{variant:"primary",size:"sm",className:"w-full",onClick:()=>d(e.id),children:["⬆️ Upgrade (",e.cost," SP)"]}):(0,t.jsx)(n.$,{variant:"secondary",size:"sm",className:"w-full",disabled:!0,children:"\uD83D\uDD12 Locked"})]},e.id)})}),0===x.length&&(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDF1F"}),(0,t.jsx)("p",{children:"No skills in this category."})]}),(0,t.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"\uD83D\uDCA1 Skill Tips"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,t.jsx)("li",{children:"• Earn skill points by leveling up (1 point every 2 levels)"}),(0,t.jsx)("li",{children:"• Some skills require other skills to be unlocked first"}),(0,t.jsx)("li",{children:"• Focus on skills that match your playstyle"}),(0,t.jsx)("li",{children:"• Efficiency skills help with resource management"})]})]})]})]})})}var j=a(7871);function f(e){var s;let{isOpen:a,onClose:l}=e,{player:c,equipment:d,automationSettings:o,updateAutomationSettings:m,purchaseAutomationUpgrade:x}=(0,i.I)(),[u,h]=(0,r.useState)("settings");if(!a)return null;let g=d.filter(e=>e.automationLevel>0),p=j.sA.filter(e=>{var s;return c.level>=e.unlockLevel&&!(null==(s=c.automationUpgrades)?void 0:s.includes(e.id))}),v=(e,s)=>{m({[e]:s})};return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:"\uD83E\uDD16 Automation Control"}),(0,t.jsx)(n.$,{variant:"secondary",onClick:l,children:"✕ Close"})]})}),(0,t.jsx)("div",{className:"border-b border-gray-200",children:(0,t.jsx)("div",{className:"flex space-x-0",children:[{id:"settings",name:"Settings",icon:"⚙️"},{id:"upgrades",name:"Upgrades",icon:"\uD83D\uDD27"},{id:"status",name:"Status",icon:"\uD83D\uDCCA"}].map(e=>(0,t.jsxs)("button",{onClick:()=>h(e.id),className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat(u===e.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[e.icon," ",e.name]},e.id))})}),(0,t.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["settings"===u&&(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"\uD83C\uDF9B️ Master Control"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"checkbox",checked:(null==o?void 0:o.enabled)||!1,onChange:e=>v("enabled",e.target.checked),className:"rounded"}),(0,t.jsx)("span",{className:"text-sm",children:"Enable Automation"})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"checkbox",checked:(null==o?void 0:o.autoStart)||!1,onChange:e=>v("autoStart",e.target.checked),className:"rounded"}),(0,t.jsx)("span",{className:"text-sm",children:"Auto-start Equipment"})]})]})]}),(0,t.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,t.jsx)("h3",{className:"font-semibold text-green-800 mb-3",children:"\uD83C\uDFAF Priority Mode"}),(0,t.jsxs)("select",{value:(null==o?void 0:o.priorityMode)||"efficiency",onChange:e=>v("priorityMode",e.target.value),className:"w-full p-2 border rounded-lg",children:[(0,t.jsx)("option",{value:"efficiency",children:"Efficiency (Orders First)"}),(0,t.jsx)("option",{value:"profit",children:"Profit (Highest Value)"}),(0,t.jsx)("option",{value:"speed",children:"Speed (Fastest Recipes)"})]}),(0,t.jsx)("p",{className:"text-xs text-green-600 mt-1",children:"How automation chooses what to bake"})]}),(0,t.jsxs)("div",{className:"bg-purple-50 p-4 rounded-lg",children:[(0,t.jsx)("h3",{className:"font-semibold text-purple-800 mb-3",children:"⚡ Performance"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"block text-sm",children:["Max Concurrent Jobs: ",(null==o?void 0:o.maxConcurrentJobs)||2]}),(0,t.jsx)("input",{type:"range",min:"1",max:"5",value:(null==o?void 0:o.maxConcurrentJobs)||2,onChange:e=>v("maxConcurrentJobs",parseInt(e.target.value)),className:"w-full"})]})]}),(0,t.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,t.jsx)("h3",{className:"font-semibold text-yellow-800 mb-3",children:"\uD83D\uDEE1️ Safety"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"block text-sm",children:["Stop when ingredients below: ",(null==o?void 0:o.ingredientThreshold)||5]}),(0,t.jsx)("input",{type:"range",min:"0",max:"20",value:(null==o?void 0:o.ingredientThreshold)||5,onChange:e=>v("ingredientThreshold",parseInt(e.target.value)),className:"w-full"})]})]})]})}),"upgrades"===u&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg mb-4",children:[(0,t.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"\uD83D\uDCA1 Automation Upgrades"}),(0,t.jsx)("p",{className:"text-sm text-blue-700",children:"Improve your automation efficiency, speed, and intelligence with these upgrades."})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:p.map(e=>(0,t.jsxs)("div",{className:"p-4 border rounded-lg bg-white hover:bg-gray-50 transition-colors",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-800",children:e.name}),(0,t.jsxs)("span",{className:"text-sm text-green-600 font-medium",children:["$",e.cost]})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-xs text-gray-500 uppercase tracking-wide",children:e.type}),(0,t.jsx)(n.$,{size:"sm",variant:c.money>=e.cost?"primary":"secondary",disabled:c.money<e.cost,onClick:()=>x(e.id),children:c.money>=e.cost?"Purchase":"Too Expensive"})]})]},e.id))}),0===p.length&&(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)("div",{className:"text-4xl mb-2",children:"\uD83D\uDD27"}),(0,t.jsx)("p",{children:"No upgrades available at your current level."}),(0,t.jsx)("p",{className:"text-sm",children:"Level up to unlock more automation upgrades!"})]})]}),"status"===u&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg text-center",children:[(0,t.jsx)("div",{className:"text-2xl text-green-600 mb-1",children:g.length}),(0,t.jsx)("div",{className:"text-sm text-green-800",children:"Automated Equipment"})]}),(0,t.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg text-center",children:[(0,t.jsx)("div",{className:"text-2xl text-blue-600 mb-1",children:(null==o?void 0:o.enabled)?"✅":"❌"}),(0,t.jsx)("div",{className:"text-sm text-blue-800",children:"Automation Status"})]}),(0,t.jsxs)("div",{className:"bg-purple-50 p-4 rounded-lg text-center",children:[(0,t.jsx)("div",{className:"text-2xl text-purple-600 mb-1",children:(null==(s=c.automationUpgrades)?void 0:s.length)||0}),(0,t.jsx)("div",{className:"text-sm text-purple-800",children:"Active Upgrades"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800",children:"\uD83C\uDFED Equipment Status"}),g.length>0?(0,t.jsx)("div",{className:"space-y-2",children:g.map(e=>(0,t.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:e.name}),(0,t.jsxs)("span",{className:"text-sm text-gray-500 ml-2",children:["Level ",e.automationLevel," • ",e.efficiency,"x efficiency"]})]}),(0,t.jsx)("div",{className:"px-2 py-1 rounded text-xs ".concat(e.isActive?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"),children:e.isActive?"Running":"Idle"})]},e.id))}):(0,t.jsxs)("div",{className:"text-center py-4 text-gray-500",children:[(0,t.jsx)("p",{children:"No automated equipment available."}),(0,t.jsx)("p",{className:"text-sm",children:"Purchase auto-equipment from the shop to get started!"})]})]})]})]})]})})}let N=[{id:"professional_oven",name:"Professional Oven",type:"oven",description:"Faster and more efficient than basic oven",cost:500,unlockLevel:3,automationLevel:0,efficiency:1.3,icon:"\uD83D\uDD25",category:"basic"},{id:"auto_oven",name:"Automated Oven",type:"auto_oven",description:"Fully automated oven that can run without supervision",cost:1500,unlockLevel:5,automationLevel:2,efficiency:1.5,icon:"\uD83E\uDD16",category:"automated"},{id:"industrial_mixer",name:"Industrial Mixer",type:"mixer",description:"High-capacity mixer for large batches",cost:750,unlockLevel:4,automationLevel:0,efficiency:1.4,icon:"\uD83E\uDD44",category:"basic"},{id:"auto_mixer",name:"Automated Mixer",type:"auto_mixer",description:"Self-operating mixer with ingredient dispensing",cost:2e3,unlockLevel:6,automationLevel:2,efficiency:1.6,icon:"\uD83E\uDD16",category:"automated"},{id:"conveyor_belt_basic",name:"Basic Conveyor Belt",type:"conveyor",description:"Moves items between equipment automatically",cost:1e3,unlockLevel:7,automationLevel:1,efficiency:1.2,icon:"\uD83D\uDD04",category:"automated"},{id:"smart_conveyor",name:"Smart Conveyor System",type:"conveyor",description:"Intelligent conveyor with sorting and routing",cost:3e3,unlockLevel:10,automationLevel:3,efficiency:1.8,icon:"\uD83E\uDDE0",category:"advanced"},{id:"master_oven",name:"Master Oven",type:"oven",description:"The ultimate baking machine with AI assistance",cost:5e3,unlockLevel:12,automationLevel:3,efficiency:2,icon:"\uD83D\uDC51",category:"advanced"}];function w(e){let{isOpen:s,onClose:a,onShowSuccess:l}=e,{player:c,equipment:d,spendMoney:o,addEquipment:m}=(0,i.I)(),[x,u]=(0,r.useState)("all");if(!s)return null;let h=N.filter(e=>c.level>=e.unlockLevel&&("all"===x||e.category===x));return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:"\uD83C\uDFEA Equipment Shop"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Upgrade your bakery with professional equipment"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,t.jsxs)("span",{className:"text-green-800 font-medium",children:["$",c.money]})}),(0,t.jsx)(n.$,{variant:"secondary",onClick:a,children:"✕ Close"})]})]})}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:[{id:"all",name:"All",icon:"\uD83C\uDFEA"},{id:"basic",name:"Basic",icon:"\uD83D\uDD27"},{id:"automated",name:"Automated",icon:"\uD83E\uDD16"},{id:"advanced",name:"Advanced",icon:"⚡"}].map(e=>(0,t.jsxs)(n.$,{variant:x===e.id?"primary":"secondary",size:"sm",onClick:()=>u(e.id),children:[e.icon," ",e.name]},e.id))}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto",children:h.map(e=>{var s;return(0,t.jsxs)("div",{className:"p-4 rounded-lg border-2 ".concat((e=>{switch(e){case"basic":return"border-gray-300 bg-gray-50";case"automated":return"border-blue-300 bg-blue-50";case"advanced":return"border-purple-300 bg-purple-50";default:return"border-gray-300 bg-white"}})(e.category)),children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:e.icon}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name}),(0,t.jsx)("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:e.category})]})]}),(0,t.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["$",e.cost]})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,t.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Efficiency:"}),(0,t.jsxs)("span",{className:"font-medium",children:[e.efficiency,"x"]})]}),e.automationLevel>0&&(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Automation:"}),0===(s=e.automationLevel)?null:(0,t.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:["\uD83E\uDD16 Auto Level ",s]})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Unlock Level:"}),(0,t.jsx)("span",{className:"font-medium",children:e.unlockLevel})]})]}),(0,t.jsx)(n.$,{variant:c.money>=e.cost?"success":"secondary",size:"sm",className:"w-full",disabled:c.money<e.cost,onClick:()=>{!(c.money<e.cost)&&o(e.cost)&&(m({name:e.name,type:e.type,isActive:!1,level:1,efficiency:e.efficiency,automationLevel:e.automationLevel}),l&&l("Equipment Purchased!","You bought ".concat(e.name,"!")))},children:c.money>=e.cost?"\uD83D\uDCB0 Purchase":"\uD83D\uDCB8 Too Expensive"})]},e.id)})}),0===h.length&&(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFEA"}),(0,t.jsx)("p",{children:"No equipment available in this category."}),(0,t.jsx)("p",{className:"text-sm",children:"Level up to unlock more equipment!"})]}),(0,t.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"\uD83D\uDCA1 Equipment Tips"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,t.jsx)("li",{children:"• Automated equipment can run without your supervision"}),(0,t.jsx)("li",{children:"• Higher efficiency means faster production and better quality"}),(0,t.jsx)("li",{children:"• Conveyor belts connect equipment for seamless workflow"}),(0,t.jsx)("li",{children:"• Advanced equipment unlocks at higher levels"})]})]})]})]})})}var k=a(2785);let D=[{name:"Downtown Delights",location:"City Center",specialization:"general",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:0},{name:"Cookie Corner",location:"Shopping Mall",specialization:"cookies",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:2500},{name:"Cake Castle",location:"Wedding District",specialization:"cakes",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:3500},{name:"Bread Basket",location:"Farmers Market",specialization:"bread",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:3e3},{name:"Pastry Palace",location:"French Quarter",specialization:"pastries",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:4e3}];function C(e){let{isOpen:s,onClose:a,bakeries:i,currentBakeryId:c,onSwitchBakery:d,onPurchaseBakery:o,playerMoney:m}=e,{t:x}=(0,l.o)(),[u,h]=(0,r.useState)("owned");if(!s)return null;let g=i.filter(e=>e.unlocked),p=D.filter(e=>!i.some(s=>s.name===e.name&&s.unlocked)),v=e=>{switch(e){case"cookies":return"\uD83C\uDF6A";case"cakes":return"\uD83E\uDDC1";case"bread":return"\uD83C\uDF5E";case"pastries":return"\uD83E\uDD50";default:return"\uD83C\uDFEA"}},b=e=>{switch(e){case"cookies":return"+20% Cookie Production Speed";case"cakes":return"+25% Cake Profit Margin";case"bread":return"+15% Bread Ingredient Efficiency";case"pastries":return"+30% Pastry Experience Gain";default:return"Balanced Production"}},y=[{id:"owned",name:x("bakeries.owned")||"My Bakeries",icon:"\uD83C\uDFEA"},{id:"available",name:x("bakeries.available")||"Available",icon:"\uD83D\uDED2"}];return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:x("bakeries.title")||"\uD83C\uDFEA Bakery Manager"}),(0,t.jsx)("p",{className:"text-gray-600",children:x("bakeries.subtitle")||"Manage your bakery empire"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,t.jsxs)("span",{className:"text-green-800 font-medium",children:["$",m]})}),(0,t.jsx)(n.$,{variant:"secondary",onClick:a,children:x("game.close")||"✕ Close"})]})]})}),(0,t.jsx)("div",{className:"border-b border-gray-200",children:(0,t.jsx)("div",{className:"flex space-x-0",children:y.map(e=>(0,t.jsxs)("button",{onClick:()=>h(e.id),className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat(u===e.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[e.icon," ",e.name]},e.id))})}),(0,t.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["owned"===u&&(0,t.jsx)("div",{className:"space-y-4",children:g.length>0?(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:g.map(e=>(0,t.jsxs)("div",{className:"p-4 rounded-lg border-2 cursor-pointer transition-all ".concat(e.id===c?"border-orange-400 bg-orange-50":"border-gray-300 bg-white hover:border-orange-300"),onClick:()=>d(e.id),children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:v(e.specialization)}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.location})]})]}),e.id===c&&(0,t.jsx)("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full",children:x("bakeries.current")||"Current"})]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("span",{className:"text-gray-600",children:[x("bakeries.level")||"Level",":"]}),(0,t.jsx)("span",{className:"font-medium",children:e.level})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("span",{className:"text-gray-600",children:[x("bakeries.specialization")||"Specialization",":"]}),(0,t.jsx)("span",{className:"font-medium capitalize",children:e.specialization})]}),(0,t.jsx)("div",{className:"text-xs text-blue-600",children:b(e.specialization)})]}),(0,t.jsxs)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsxs)("span",{className:"text-gray-600",children:[x("bakeries.equipment")||"Equipment",":"]}),(0,t.jsx)("span",{children:e.equipment.length})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsxs)("span",{className:"text-gray-600",children:[x("bakeries.orders")||"Active Orders",":"]}),(0,t.jsx)("span",{children:e.orders.length})]})]}),e.id!==c&&(0,t.jsx)(n.$,{variant:"primary",size:"sm",className:"w-full mt-3",onClick:s=>{s.stopPropagation(),d(e.id)},children:x("bakeries.switchTo")||"Switch To"})]},e.id))}):(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFEA"}),(0,t.jsx)("p",{children:x("bakeries.noOwned")||"You don't own any bakeries yet."})]})}),"available"===u&&(0,t.jsx)("div",{className:"space-y-4",children:p.length>0?(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:p.map((e,s)=>(0,t.jsxs)("div",{className:"p-4 rounded-lg border-2 border-gray-300 bg-white",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:v(e.specialization)}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.location})]})]}),(0,t.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["$",e.purchaseCost]})]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("span",{className:"text-gray-600",children:[x("bakeries.specialization")||"Specialization",":"]}),(0,t.jsx)("span",{className:"font-medium capitalize",children:e.specialization})]}),(0,t.jsx)("div",{className:"text-xs text-blue-600",children:b(e.specialization)})]}),(0,t.jsx)(n.$,{variant:m>=e.purchaseCost?"success":"secondary",size:"sm",className:"w-full",disabled:m<e.purchaseCost,onClick:()=>{m>=e.purchaseCost&&o({...e,id:"bakery_".concat(Date.now()),unlocked:!0})},children:m>=e.purchaseCost?x("bakeries.purchase")||"\uD83D\uDCB0 Purchase":x("bakeries.tooExpensive")||"\uD83D\uDCB8 Too Expensive"})]},s))}):(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDF89"}),(0,t.jsx)("p",{children:x("bakeries.allOwned")||"You own all available bakeries!"})]})})]}),(0,t.jsxs)("div",{className:"p-4 bg-blue-50 border-t border-gray-200",children:[(0,t.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:x("bakeries.tips")||"\uD83D\uDCA1 Bakery Tips"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,t.jsxs)("li",{children:["• ",x("bakeries.tip1")||"Each bakery specializes in different products for bonus efficiency"]}),(0,t.jsxs)("li",{children:["• ",x("bakeries.tip2")||"Switch between bakeries to manage multiple locations"]}),(0,t.jsxs)("li",{children:["• ",x("bakeries.tip3")||"Specialized bakeries attract customers looking for specific items"]}),(0,t.jsxs)("li",{children:["• ",x("bakeries.tip4")||"Upgrade each bakery independently for maximum profit"]})]})]})]})})}var S=a(913);class E{async initializeSaveDirectory(){if(this.isElectron&&window.electronAPI)try{this.saveDirectory=await window.electronAPI.getSaveDirectory(),await this.ensureSaveDirectoryExists()}catch(e){console.error("Failed to initialize save directory:",e),this.isElectron=!1}}async ensureSaveDirectoryExists(){if(this.isElectron&&window.electronAPI)try{await window.electronAPI.ensureDirectory(this.saveDirectory)}catch(e){console.error("Failed to create save directory:",e)}}async saveToFile(e,s){if(!this.isElectron||!window.electronAPI)return this.saveToLocalStorage(e,s);try{let a=s||"save_".concat(Date.now(),".json"),t="".concat(this.saveDirectory,"/").concat(a),r=JSON.stringify(e,null,2);return await window.electronAPI.writeFile(t,r),console.log("Game saved to file: ".concat(t)),!0}catch(e){return console.error("Failed to save to file:",e),!1}}async loadFromFile(e){if(!this.isElectron||!window.electronAPI)return this.loadFromLocalStorage(e);try{let s="".concat(this.saveDirectory,"/").concat(e),a=await window.electronAPI.readFile(s);if(!a)return null;let t=JSON.parse(a);return console.log("Game loaded from file: ".concat(s)),t}catch(e){return console.error("Failed to load from file:",e),null}}async getSaveFiles(){if(!this.isElectron||!window.electronAPI)return this.getLocalStorageSaves();try{let e=await window.electronAPI.listFiles(this.saveDirectory,".json"),s=[];for(let a of e)try{let e="".concat(this.saveDirectory,"/").concat(a.name),t=await window.electronAPI.readFile(e),r=JSON.parse(t);s.push({fileName:a.name,displayName:r.player.name||a.name.replace(".json",""),timestamp:r.timestamp,playerLevel:r.player.level,money:r.player.money,playTime:r.player.playTime||0,version:r.version,fileSize:a.size||0})}catch(e){console.error("Failed to read save file ".concat(a.name,":"),e)}return s.sort((e,s)=>s.timestamp-e.timestamp)}catch(e){return console.error("Failed to get save files:",e),[]}}async deleteSaveFile(e){if(!this.isElectron||!window.electronAPI)return this.deleteFromLocalStorage(e);try{let s="".concat(this.saveDirectory,"/").concat(e);return await window.electronAPI.deleteFile(s),console.log("Save file deleted: ".concat(s)),!0}catch(e){return console.error("Failed to delete save file:",e),!1}}async exportSave(e,s){if(!this.isElectron||!window.electronAPI)return this.exportToBrowser(e,s);try{let a=s||"bake-it-out-save-".concat(Date.now(),".json"),t=await window.electronAPI.showSaveDialog(a);if(!t)return!1;let r=JSON.stringify(e,null,2);return await window.electronAPI.writeFile(t,r),console.log("Save exported to: ".concat(t)),!0}catch(e){return console.error("Failed to export save:",e),!1}}async importSave(){if(!this.isElectron||!window.electronAPI)return this.importFromBrowser();try{let e=await window.electronAPI.showOpenDialog([".json"]);if(!e)return null;let s=await window.electronAPI.readFile(e),a=JSON.parse(s);return console.log("Save imported from: ".concat(e)),a}catch(e){return console.error("Failed to import save:",e),null}}async createBackup(e){if(!this.isElectron||!window.electronAPI)return!1;try{let s="".concat(this.saveDirectory,"/").concat(e),a="".concat(this.saveDirectory,"/backups/").concat(e,".backup.").concat(Date.now());return await window.electronAPI.ensureDirectory("".concat(this.saveDirectory,"/backups")),await window.electronAPI.copyFile(s,a),console.log("Backup created: ".concat(a)),!0}catch(e){return console.error("Failed to create backup:",e),!1}}saveToLocalStorage(e,s){try{let a=s||"save_".concat(Date.now());return localStorage.setItem("bakeItOut_file_".concat(a),JSON.stringify(e)),!0}catch(e){return console.error("Failed to save to localStorage:",e),!1}}loadFromLocalStorage(e){try{let s=localStorage.getItem("bakeItOut_file_".concat(e));return s?JSON.parse(s):null}catch(e){return console.error("Failed to load from localStorage:",e),null}}getLocalStorageSaves(){let e=[];for(let s=0;s<localStorage.length;s++){let a=localStorage.key(s);if(null==a?void 0:a.startsWith("bakeItOut_file_"))try{let s=localStorage.getItem(a);if(s){let t=JSON.parse(s),r=a.replace("bakeItOut_file_","");e.push({fileName:r,displayName:t.player.name||r,timestamp:t.timestamp,playerLevel:t.player.level,money:t.player.money,playTime:t.player.playTime||0,version:t.version,fileSize:s.length})}}catch(e){console.error("Failed to parse save ".concat(a,":"),e)}}return e.sort((e,s)=>s.timestamp-e.timestamp)}deleteFromLocalStorage(e){try{return localStorage.removeItem("bakeItOut_file_".concat(e)),!0}catch(e){return console.error("Failed to delete from localStorage:",e),!1}}exportToBrowser(e,s){try{let a=JSON.stringify(e,null,2),t=new Blob([a],{type:"application/json"}),r=URL.createObjectURL(t),l=document.createElement("a");return l.href=r,l.download=s||"bake-it-out-save-".concat(Date.now(),".json"),l.click(),URL.revokeObjectURL(r),!0}catch(e){return console.error("Failed to export to browser:",e),!1}}importFromBrowser(){return new Promise(e=>{let s=document.createElement("input");s.type="file",s.accept=".json",s.onchange=s=>{var a;let t=null==(a=s.target.files)?void 0:a[0];if(!t)return void e(null);let r=new FileReader;r.onload=s=>{try{var a;let t=null==(a=s.target)?void 0:a.result,r=JSON.parse(t);e(r)}catch(s){console.error("Failed to parse imported file:",s),e(null)}},r.readAsText(t)},s.click()})}constructor(){this.saveDirectory="",this.isElectron=!1,this.isElectron=void 0!==window.electronAPI,this.initializeSaveDirectory()}}let L=new E;function _(e){let{isOpen:s,onClose:a,mode:i,onModeChange:c}=e,{t:d}=(0,l.o)(),{login:o,register:m,isLoading:x,error:u,clearError:h}=(0,S.y)(),[g,p]=(0,r.useState)({username:"",email:"",password:"",confirmPassword:""}),[v,b]=(0,r.useState)({});if(!s)return null;let y=async e=>{if(e.preventDefault(),(()=>{let e={};return g.username.trim()?g.username.length<3&&(e.username=d("cloud.auth.username_too_short","Username must be at least 3 characters")):e.username=d("cloud.auth.username_required","Username is required"),"register"===i&&(g.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(g.email)||(e.email=d("cloud.auth.email_invalid","Please enter a valid email address")):e.email=d("cloud.auth.email_required","Email is required"),g.password!==g.confirmPassword&&(e.confirmPassword=d("cloud.auth.passwords_dont_match","Passwords do not match"))),g.password?g.password.length<6&&(e.password=d("cloud.auth.password_too_short","Password must be at least 6 characters")):e.password=d("cloud.auth.password_required","Password is required"),b(e),0===Object.keys(e).length})()){h();try{("login"===i?await o(g.username,g.password):await m(g.username,g.email,g.password))&&(a(),p({username:"",email:"",password:"",confirmPassword:""}),b({}))}catch(e){console.error("Auth error:",e)}}},j=(e,s)=>{p(a=>({...a,[e]:s})),v[e]&&b(s=>({...s,[e]:""}))};return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:"login"===i?d("cloud.auth.login_title","Login to Cloud Save"):d("cloud.auth.register_title","Create Cloud Save Account")}),(0,t.jsx)("button",{onClick:a,className:"text-gray-500 hover:text-gray-700 text-2xl",children:"\xd7"})]}),u&&(0,t.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:u}),(0,t.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:d("cloud.auth.username","Username")}),(0,t.jsx)("input",{type:"text",value:g.username,onChange:e=>j("username",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 ".concat(v.username?"border-red-500":"border-gray-300"),placeholder:d("cloud.auth.username_placeholder","Enter your username"),disabled:x}),v.username&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-1",children:v.username})]}),"register"===i&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:d("cloud.auth.email","Email")}),(0,t.jsx)("input",{type:"email",value:g.email,onChange:e=>j("email",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 ".concat(v.email?"border-red-500":"border-gray-300"),placeholder:d("cloud.auth.email_placeholder","Enter your email"),disabled:x}),v.email&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-1",children:v.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:d("cloud.auth.password","Password")}),(0,t.jsx)("input",{type:"password",value:g.password,onChange:e=>j("password",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 ".concat(v.password?"border-red-500":"border-gray-300"),placeholder:d("cloud.auth.password_placeholder","Enter your password"),disabled:x}),v.password&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-1",children:v.password})]}),"register"===i&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:d("cloud.auth.confirm_password","Confirm Password")}),(0,t.jsx)("input",{type:"password",value:g.confirmPassword,onChange:e=>j("confirmPassword",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 ".concat(v.confirmPassword?"border-red-500":"border-gray-300"),placeholder:d("cloud.auth.confirm_password_placeholder","Confirm your password"),disabled:x}),v.confirmPassword&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-1",children:v.confirmPassword})]}),(0,t.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,t.jsx)(n.$,{type:"submit",variant:"primary",className:"flex-1",disabled:x,children:x?d("cloud.auth.loading","Loading..."):"login"===i?d("cloud.auth.login_button","Login"):d("cloud.auth.register_button","Create Account")}),(0,t.jsx)(n.$,{type:"button",variant:"secondary",onClick:a,disabled:x,children:d("cloud.auth.cancel","Cancel")})]})]}),(0,t.jsx)("div",{className:"mt-6 text-center",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["login"===i?d("cloud.auth.no_account","Don't have an account?"):d("cloud.auth.have_account","Already have an account?"),(0,t.jsx)("button",{onClick:()=>c("login"===i?"register":"login"),className:"ml-2 text-orange-600 hover:text-orange-800 font-medium",disabled:x,children:"login"===i?d("cloud.auth.register_link","Create one"):d("cloud.auth.login_link","Login")})]})})]})})}function A(e){let{isOpen:s,onClose:a,mode:i,currentGameData:c,onLoadGame:d}=e,{t:o}=(0,l.o)(),{isAuthenticated:m,user:x,cloudSaves:u,isLoadingSaves:h,saveToCloud:g,loadFromCloud:p,deleteCloudSave:v,refreshCloudSaves:b,syncStatus:y,lastSyncTime:j,error:f,clearError:N}=(0,S.y)(),[w,k]=(0,r.useState)(!1),[D,C]=(0,r.useState)("login"),[E,L]=(0,r.useState)(""),[A,P]=(0,r.useState)(null),[F,I]=(0,r.useState)(!1);if((0,r.useEffect)(()=>{s&&m&&b()},[s,m]),(0,r.useEffect)(()=>{if(f){let e=setTimeout(()=>{N()},5e3);return()=>clearTimeout(e)}},[f,N]),!s)return null;let q=async()=>{if(E.trim()&&c){I(!0);try{await g(E,c)&&(L(""),a())}catch(e){console.error("Save failed:",e)}finally{I(!1)}}},z=async()=>{if(A&&d){I(!0);try{let e=await p(A);e&&(d(e),a())}catch(e){console.error("Load failed:",e)}finally{I(!1)}}},O=async e=>{confirm(o("cloud.save.confirm_delete","Are you sure you want to delete this save?"))&&await v(e)&&A===e&&P(null)},$=e=>new Date(e).toLocaleString();return m?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-6 w-full max-w-4xl mx-4 max-h-[90vh] overflow-hidden flex flex-col",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:"save"===i?o("cloud.save.save_title","Save to Cloud"):o("cloud.save.load_title","Load from Cloud")}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[o("cloud.save.logged_in_as","Logged in as"),": ",null==x?void 0:x.username]})]}),(0,t.jsx)("button",{onClick:a,className:"text-gray-500 hover:text-gray-700 text-2xl",children:"\xd7"})]}),f&&(0,t.jsx)("div",{className:"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4",children:f}),(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4 p-3 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("syncing"===y?"bg-yellow-500 animate-pulse":"success"===y?"bg-green-500":"error"===y?"bg-red-500":"bg-gray-400")}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:["syncing"===y&&o("cloud.save.syncing","Syncing..."),"success"===y&&o("cloud.save.sync_success","Synced"),"error"===y&&o("cloud.save.sync_error","Sync Error"),"idle"===y&&o("cloud.save.sync_idle","Ready")]})]}),j&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:[o("cloud.save.last_sync","Last sync"),": ",$(j.toISOString())]})]}),"save"===i&&(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:o("cloud.save.save_name","Save Name")}),(0,t.jsx)("input",{type:"text",value:E,onChange:e=>L(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500",placeholder:o("cloud.save.save_name_placeholder","Enter a name for your save"),maxLength:100})]}),(0,t.jsxs)("div",{className:"flex-1 overflow-hidden",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-800",children:[o("cloud.save.your_saves","Your Cloud Saves")," (",u.length,")"]}),(0,t.jsx)(n.$,{variant:"secondary",size:"sm",onClick:b,disabled:h,children:h?o("cloud.save.refreshing","Refreshing..."):o("cloud.save.refresh","Refresh")})]}),(0,t.jsx)("div",{className:"overflow-y-auto max-h-96 border border-gray-200 rounded-lg",children:h?(0,t.jsx)("div",{className:"p-8 text-center text-gray-500",children:o("cloud.save.loading_saves","Loading saves...")}):0===u.length?(0,t.jsx)("div",{className:"p-8 text-center text-gray-500",children:o("cloud.save.no_saves","No cloud saves found")}):(0,t.jsx)("div",{className:"divide-y divide-gray-200",children:u.map(e=>(0,t.jsx)("div",{className:"p-4 hover:bg-gray-50 cursor-pointer ".concat(A===e.id?"bg-orange-50 border-l-4 border-orange-500":""),onClick:()=>P(e.id),children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-800",children:e.saveName}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 mt-1",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("span",{children:[o("cloud.save.level","Level")," ",e.metadata.level]}),(0,t.jsxs)("span",{children:["$",e.metadata.money]}),(0,t.jsx)("span",{className:"px-2 py-1 rounded text-xs ".concat("manual"===e.saveType?"bg-blue-100 text-blue-800":"auto"===e.saveType?"bg-green-100 text-green-800":"bg-purple-100 text-purple-800"),children:e.saveType})]}),(0,t.jsxs)("div",{className:"mt-1 text-xs text-gray-500",children:[$(e.updatedAt)," • ",(e=>{if(0===e)return"0 B";let s=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,s)).toFixed(2))+" "+["B","KB","MB","GB"][s]})(e.size)]})]})]}),(0,t.jsx)("button",{onClick:s=>{s.stopPropagation(),O(e.id)},className:"text-red-500 hover:text-red-700 ml-4",title:o("cloud.save.delete","Delete"),children:"\uD83D\uDDD1️"})]})},e.id))})})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-3 mt-6",children:[(0,t.jsx)(n.$,{variant:"secondary",onClick:a,disabled:F,children:o("cloud.save.cancel","Cancel")}),"save"===i?(0,t.jsx)(n.$,{variant:"primary",onClick:q,disabled:!E.trim()||F,children:F?o("cloud.save.saving","Saving..."):o("cloud.save.save_button","Save to Cloud")}):(0,t.jsx)(n.$,{variant:"primary",onClick:z,disabled:!A||F,children:F?o("cloud.save.loading","Loading..."):o("cloud.save.load_button","Load Game")})]})]})}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:o("cloud.save.auth_required","Cloud Save Account Required")}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:o("cloud.save.auth_description","You need to login or create an account to use cloud saves.")}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsx)(n.$,{variant:"primary",onClick:()=>{C("login"),k(!0)},className:"flex-1",children:o("cloud.auth.login","Login")}),(0,t.jsx)(n.$,{variant:"secondary",onClick:()=>{C("register"),k(!0)},className:"flex-1",children:o("cloud.auth.register","Register")})]}),(0,t.jsx)(n.$,{variant:"secondary",onClick:a,className:"w-full mt-3",children:o("cloud.auth.cancel","Cancel")})]})})}),(0,t.jsx)(_,{isOpen:w,onClose:()=>k(!1),mode:D,onModeChange:C})]})}function P(e){var s;let{isOpen:a,onClose:c,mode:d,onSaveSuccess:o,onLoadSuccess:m}=e,{t:x}=(0,l.o)(),{player:u,saveGameState:h,loadGameState:g}=(0,i.I)(),{isAuthenticated:p}=(0,S.y)(),[v,b]=(0,r.useState)([]),[y,j]=(0,r.useState)([]),[f,N]=(0,r.useState)(null),[w,k]=(0,r.useState)(null),[D,C]=(0,r.useState)(""),[E,_]=(0,r.useState)("local"),[P,F]=(0,r.useState)(!1),[I,q]=(0,r.useState)(!1),[z,O]=(0,r.useState)(!1),[$,M]=(0,r.useState)(!1);(0,r.useEffect)(()=>{a&&(B(),T(),M(void 0!==window.electronAPI))},[a]);let T=async()=>{try{let e=await L.getSaveFiles();j(e)}catch(e){console.error("Failed to load file saves:",e)}},B=()=>{let e=[];for(let t=1;t<=8;t++){let r="bakeItOut_save_slot_".concat(t),l=localStorage.getItem(r);if(l)try{var s,a;let r=JSON.parse(l);e.push({id:t,name:r.player.name||"Save ".concat(t),timestamp:r.timestamp,playerLevel:r.player.level,money:r.player.money,bakeryName:(null==(a=r.bakeries)||null==(s=a[0])?void 0:s.name)||"Main Bakery",playTime:r.player.playTime||0,isEmpty:!1,data:r})}catch(s){console.error("Failed to load save slot ".concat(t,":"),s),e.push(R(t))}else e.push(R(t))}b(e)},R=e=>({id:e,name:"Empty Slot ".concat(e),timestamp:0,playerLevel:0,money:0,bakeryName:"",playTime:0,isEmpty:!0}),U=async e=>{if(!f)return;let s=v.find(s=>s.id===e);if(!(null==s?void 0:s.isEmpty)&&!z)return void O(!0);q(!0);try{await h(e,D||"Save ".concat(e))&&(null==o||o(),B(),O(!1),C(""))}catch(e){console.error("Save failed:",e)}finally{q(!1)}},J=async e=>{let s=v.find(s=>s.id===e);if(s&&!s.isEmpty){q(!0);try{await g(e)&&(null==m||m(),c())}catch(e){console.error("Load failed:",e)}finally{q(!1)}}};return a?(0,t.jsxs)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-500 p-6 text-white",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold",children:"save"===d?"\uD83D\uDCBE Save Game":"\uD83D\uDCC1 Load Game"}),(0,t.jsx)("p",{className:"text-blue-100 text-sm",children:"save"===d?x("saveLoad.saveDesc","Choose a slot to save your progress"):x("saveLoad.loadDesc","Select a save file to load")})]}),(0,t.jsx)(n.$,{variant:"secondary",size:"sm",className:"bg-white/20 hover:bg-white/30 text-white border-white/30",onClick:c,children:"✕"})]})}),(0,t.jsx)("div",{className:"border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsxs)("button",{className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat("local"===E?"border-blue-500 text-blue-600 bg-blue-50":"border-transparent text-gray-500 hover:text-gray-700"),onClick:()=>{_("local"),N(1),k(null)},children:["\uD83D\uDCC1 ",x("saveLoad.local_saves","Local Saves")]}),$&&(0,t.jsxs)("button",{className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat("file"===E?"border-blue-500 text-blue-600 bg-blue-50":"border-transparent text-gray-500 hover:text-gray-700"),onClick:()=>{_("file"),N(null),k(null)},children:["\uD83D\uDCBE ",x("saveLoad.file_saves","File Saves")]}),(0,t.jsxs)("button",{className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat("cloud"===E?"border-blue-500 text-blue-600 bg-blue-50":"border-transparent text-gray-500 hover:text-gray-700"),onClick:()=>{_("cloud"),N(null),k(null),F(!0)},children:["☁️ ",x("saveLoad.cloud_saves","Cloud Saves"),p&&(0,t.jsx)("span",{className:"ml-1 text-green-500",children:"●"})]})]})}),(0,t.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[70vh]",children:["save"===d&&f&&(0,t.jsxs)("div",{className:"mb-6 p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:x("saveLoad.saveName","Save Name")}),(0,t.jsx)("input",{type:"text",value:D,onChange:e=>C(e.target.value),placeholder:"Save ".concat(f),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:v.map(e=>{var s;return(0,t.jsxs)("div",{className:"border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 ".concat(f===e.id?"border-blue-500 bg-blue-50":e.isEmpty?"border-gray-200 bg-gray-50 hover:border-gray-300":"border-gray-300 bg-white hover:border-blue-300 hover:shadow-md"),onClick:()=>N(e.id),children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-orange-400 to-yellow-400 rounded-lg flex items-center justify-center text-white font-bold text-lg",children:e.id}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800",children:e.isEmpty?"Slot ".concat(e.id):e.name}),!e.isEmpty&&(0,t.jsx)("p",{className:"text-sm text-gray-500",children:(s=e.timestamp)?new Date(s).toLocaleString():""})]})]}),!e.isEmpty&&(0,t.jsx)(n.$,{variant:"secondary",size:"sm",className:"text-red-600 hover:bg-red-50",onClick:s=>{var a;s.stopPropagation(),a=e.id,localStorage.removeItem("bakeItOut_save_slot_".concat(a)),B()},children:"\uD83D\uDDD1️"})]}),e.isEmpty?(0,t.jsxs)("div",{className:"text-center py-8 text-gray-400",children:[(0,t.jsx)("div",{className:"text-3xl mb-2",children:"\uD83D\uDCC4"}),(0,t.jsx)("p",{className:"text-sm",children:x("saveLoad.emptySlot","Empty Slot")})]}):(0,t.jsx)("div",{className:"space-y-2",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-500",children:"Level:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:e.playerLevel})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-500",children:"Money:"}),(0,t.jsxs)("span",{className:"ml-2 font-medium",children:["$",e.money]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-500",children:"Bakery:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:e.bakeryName})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-500",children:"Play Time:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:(e=>{let s=Math.floor(e/3600),a=Math.floor(e%3600/60);return"".concat(s,"h ").concat(a,"m")})(e.playTime)})]})]})})]},e.id)})})]}),(0,t.jsxs)("div",{className:"bg-gray-50 px-6 py-4 flex justify-between items-center",children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:f&&(0,t.jsx)("span",{children:"save"===d?x("saveLoad.selectedSaveSlot","Selected: Slot ".concat(f)):x("saveLoad.selectedLoadSlot","Selected: Slot ".concat(f))})}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsx)(n.$,{variant:"secondary",onClick:c,children:x("common.cancel","Cancel")}),"save"===d?(0,t.jsx)(n.$,{variant:"primary",onClick:()=>f&&U(f),disabled:!f||I,className:"bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600",children:I?"\uD83D\uDCBE Saving...":"\uD83D\uDCBE Save Game"}):(0,t.jsx)(n.$,{variant:"primary",onClick:()=>f&&J(f),disabled:!f||I||(null==(s=v.find(e=>e.id===f))?void 0:s.isEmpty),className:"bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600",children:I?"\uD83D\uDCC1 Loading...":"\uD83D\uDCC1 Load Game"})]})]}),z&&(0,t.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md mx-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:x("saveLoad.confirmOverwrite","Overwrite Save?")}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:x("saveLoad.overwriteWarning","This will overwrite the existing save. This action cannot be undone.")}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsx)(n.$,{variant:"secondary",onClick:()=>O(!1),children:x("common.cancel","Cancel")}),(0,t.jsx)(n.$,{variant:"primary",className:"bg-red-500 hover:bg-red-600",onClick:()=>f&&U(f),children:x("saveLoad.overwrite","Overwrite")})]})]})})]}),(0,t.jsx)(A,{isOpen:P,onClose:()=>F(!1),mode:d,currentGameData:"save"===d?{player:u,gameState:{}}:void 0,onLoadGame:e=>{e&&m&&(m(),c())}})]}):null}function F(e){let{equipment:s,onEquipmentClick:a}=e,{t:n}=(0,l.o)(),{orders:c,player:d}=(0,i.I)(),[o,m]=(0,r.useState)([]),[x,u]=(0,r.useState)("kitchen"),h=e=>{let s=["\uD83D\uDC68‍\uD83D\uDCBC","\uD83D\uDC69‍\uD83D\uDCBC","\uD83D\uDC68‍\uD83C\uDF93","\uD83D\uDC69‍\uD83C\uDF93","\uD83D\uDC68‍\uD83C\uDF73","\uD83D\uDC69‍\uD83C\uDF73","\uD83D\uDC68‍⚕️","\uD83D\uDC69‍⚕️","\uD83D\uDC68‍\uD83C\uDFA8","\uD83D\uDC69‍\uD83C\uDFA8"];return s[e.length%s.length]};(0,r.useEffect)(()=>{m(c.map((e,s)=>({id:e.id,name:e.customerName,avatar:h(e.customerName),position:(e=>{let s=[{x:20,y:20},{x:60,y:20},{x:100,y:20},{x:20,y:60},{x:60,y:60},{x:100,y:60},{x:20,y:100},{x:60,y:100},{x:100,y:100}];return s[e%s.length]})(s),order:e.items[0],satisfaction:100-20*(e.timeLimit<180),waitTime:300-e.timeLimit,status:"pending"===e.status?"waiting":"in_progress"===e.status?"ordering":"eating"})))},[c]);let g=e=>{switch(e){case"oven":return"\uD83D\uDD25";case"mixer":return"\uD83E\uDD44";case"counter":return"\uD83C\uDFEA";case"auto_oven":return"\uD83E\uDD16";case"auto_mixer":return"⚙️";case"conveyor":return"\uD83D\uDD04";default:return"\uD83D\uDCE6"}},p=e=>e.isActive?"bg-green-100 border-green-400":e.automationLevel>0?"bg-blue-100 border-blue-400":"bg-gray-100 border-gray-300";return(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-orange-800",children:["\uD83C\uDFEA ",n("bakery.layout.title","Bakery Layout")]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)("button",{onClick:()=>u("kitchen"),className:"px-4 py-2 rounded-lg font-medium transition-colors ".concat("kitchen"===x?"bg-orange-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:["\uD83D\uDC68‍\uD83C\uDF73 ",n("bakery.kitchen","Kitchen")]}),(0,t.jsxs)("button",{onClick:()=>u("dining"),className:"px-4 py-2 rounded-lg font-medium transition-colors ".concat("dining"===x?"bg-orange-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:["\uD83C\uDF7D️ ",n("bakery.dining","Dining Area")]}),(0,t.jsxs)("button",{onClick:()=>u("counter"),className:"px-4 py-2 rounded-lg font-medium transition-colors ".concat("counter"===x?"bg-orange-500 text-white":"bg-gray-200 text-gray-700 hover:bg-gray-300"),children:["\uD83D\uDED2 ",n("bakery.counter","Service Counter")]})]})]}),(0,t.jsxs)("div",{className:"relative bg-gradient-to-br from-yellow-50 to-orange-50 rounded-lg border-2 border-orange-200 min-h-[500px] overflow-hidden",children:["kitchen"===x&&(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4 mb-6",children:[(0,t.jsxs)("div",{className:"bg-red-100 rounded-lg p-4 border-2 border-red-300",children:[(0,t.jsxs)("h3",{className:"font-semibold text-red-800 mb-3",children:["\uD83D\uDD25 ",n("bakery.baking.area","Baking Area")]}),(0,t.jsx)("div",{className:"space-y-2",children:s.filter(e=>e.type.includes("oven")).map(e=>(0,t.jsxs)("div",{onClick:()=>a(e.id,e.name),className:"p-3 rounded-lg border-2 cursor-pointer transition-all hover:scale-105 ".concat(p(e)),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-xl",children:g(e.type)}),(0,t.jsx)("span",{className:"font-medium text-sm",children:e.name})]}),e.isActive&&(0,t.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"})]}),e.currentRecipe&&(0,t.jsxs)("div",{className:"text-xs text-gray-600 mt-1",children:["Making: ",e.currentRecipe]})]},e.id))})]}),(0,t.jsxs)("div",{className:"bg-blue-100 rounded-lg p-4 border-2 border-blue-300",children:[(0,t.jsxs)("h3",{className:"font-semibold text-blue-800 mb-3",children:["\uD83E\uDD44 ",n("bakery.prep.area","Prep Area")]}),(0,t.jsx)("div",{className:"space-y-2",children:s.filter(e=>e.type.includes("mixer")).map(e=>(0,t.jsx)("div",{onClick:()=>a(e.id,e.name),className:"p-3 rounded-lg border-2 cursor-pointer transition-all hover:scale-105 ".concat(p(e)),children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-xl",children:g(e.type)}),(0,t.jsx)("span",{className:"font-medium text-sm",children:e.name})]}),e.isActive&&(0,t.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full animate-pulse"})]})},e.id))})]}),(0,t.jsxs)("div",{className:"bg-purple-100 rounded-lg p-4 border-2 border-purple-300",children:[(0,t.jsxs)("h3",{className:"font-semibold text-purple-800 mb-3",children:["⚙️ ",n("bakery.automation.area","Automation")]}),(0,t.jsx)("div",{className:"space-y-2",children:s.filter(e=>e.type.includes("conveyor")||e.automationLevel>0).map(e=>(0,t.jsx)("div",{onClick:()=>a(e.id,e.name),className:"p-3 rounded-lg border-2 cursor-pointer transition-all hover:scale-105 ".concat(p(e)),children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-xl",children:g(e.type)}),(0,t.jsx)("span",{className:"font-medium text-sm",children:e.name})]}),(0,t.jsxs)("div",{className:"text-xs bg-purple-200 px-2 py-1 rounded",children:["Auto Lv.",e.automationLevel]})]})},e.id))})]})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg p-4 border border-gray-300",children:[(0,t.jsxs)("h4",{className:"font-semibold text-gray-800 mb-2",children:["\uD83D\uDCCA ",n("bakery.kitchen.stats","Kitchen Stats")]}),(0,t.jsxs)("div",{className:"grid grid-cols-3 gap-4 text-sm",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-lg font-bold text-green-600",children:s.filter(e=>e.isActive).length}),(0,t.jsx)("div",{className:"text-gray-600",children:n("bakery.active.equipment","Active Equipment")})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-lg font-bold text-blue-600",children:s.filter(e=>e.automationLevel>0).length}),(0,t.jsx)("div",{className:"text-gray-600",children:n("bakery.automated.equipment","Automated Equipment")})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"text-lg font-bold text-purple-600",children:[Math.round(s.reduce((e,s)=>e+s.efficiency,0)/s.length*100),"%"]}),(0,t.jsx)("div",{className:"text-gray-600",children:n("bakery.efficiency","Efficiency")})]})]})]})]}),"dining"===x&&(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("div",{className:"grid grid-cols-4 gap-4 mb-6",children:[1,2,3,4,5,6,7,8].map(e=>{let s=o[e-1];return(0,t.jsxs)("div",{className:"bg-amber-800 rounded-lg p-4 border-2 border-amber-600 relative text-white",children:[(0,t.jsxs)("div",{className:"text-center mb-2",children:[(0,t.jsx)("div",{className:"text-2xl",children:"\uD83E\uDE91"}),(0,t.jsxs)("div",{className:"text-xs",children:["Table ",e]})]}),s?(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-2xl mb-1",children:s.avatar}),(0,t.jsx)("div",{className:"text-xs font-medium",children:s.name}),(0,t.jsx)("div",{className:"text-xs opacity-75",children:s.order}),(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsx)("div",{className:"w-full h-1 rounded ".concat(s.satisfaction>70?"bg-green-400":s.satisfaction>40?"bg-yellow-400":"bg-red-400"),style:{width:"".concat(s.satisfaction,"%")}})})]}):(0,t.jsx)("div",{className:"text-center text-gray-400",children:(0,t.jsx)("div",{className:"text-xs",children:"Empty"})})]},e)})}),(0,t.jsxs)("div",{className:"bg-white rounded-lg p-4 border border-gray-300",children:[(0,t.jsxs)("h4",{className:"font-semibold text-gray-800 mb-2",children:["\uD83C\uDF7D️ ",n("bakery.dining.stats","Dining Stats")]}),(0,t.jsxs)("div",{className:"grid grid-cols-4 gap-4 text-sm",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-lg font-bold text-green-600",children:o.length}),(0,t.jsx)("div",{className:"text-gray-600",children:n("bakery.current.customers","Current Customers")})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-lg font-bold text-blue-600",children:o.filter(e=>"waiting"===e.status).length}),(0,t.jsx)("div",{className:"text-gray-600",children:n("bakery.waiting.customers","Waiting")})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-lg font-bold text-purple-600",children:o.filter(e=>"eating"===e.status).length}),(0,t.jsx)("div",{className:"text-gray-600",children:n("bakery.eating.customers","Eating")})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"text-lg font-bold text-orange-600",children:[o.length>0?Math.round(o.reduce((e,s)=>e+s.satisfaction,0)/o.length):100,"%"]}),(0,t.jsx)("div",{className:"text-gray-600",children:n("bakery.avg.satisfaction","Avg Satisfaction")})]})]})]})]}),"counter"===x&&(0,t.jsx)("div",{className:"p-6",children:(0,t.jsxs)("div",{className:"bg-gradient-to-r from-yellow-100 to-orange-100 rounded-lg p-6 border-2 border-orange-300",children:[(0,t.jsxs)("h3",{className:"text-xl font-bold text-orange-800 mb-4 text-center",children:["\uD83D\uDED2 ",n("bakery.service.counter","Service Counter")]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg p-4 border-2 border-gray-300 mb-4",children:[(0,t.jsxs)("h4",{className:"font-semibold text-gray-800 mb-3",children:["\uD83E\uDDC1 ",n("bakery.display.case","Display Case")]}),(0,t.jsx)("div",{className:"grid grid-cols-4 gap-3",children:["\uD83C\uDF6A","\uD83E\uDDC1","\uD83E\uDD50","\uD83C\uDF5E","\uD83E\uDD67","\uD83C\uDF70","\uD83E\uDD68","\uD83E\uDDC7"].map((e,s)=>(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3 text-center border",children:[(0,t.jsx)("div",{className:"text-2xl mb-1",children:e}),(0,t.jsx)("div",{className:"text-xs text-gray-600",children:"Fresh"})]},s))})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg p-4 border-2 border-gray-300",children:[(0,t.jsxs)("h4",{className:"font-semibold text-gray-800 mb-3",children:["\uD83D\uDCCB ",n("bakery.order.queue","Order Queue")]}),(0,t.jsx)("div",{className:"space-y-2",children:c.slice(0,3).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-lg",children:h(e.customerName)}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium text-sm",children:e.customerName}),(0,t.jsx)("div",{className:"text-xs text-gray-600",children:e.items[0]})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("div",{className:"text-sm font-bold text-green-600",children:["$",e.reward]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[Math.floor(e.timeLimit/60),"m left"]})]})]},e.id))})]})]})})]}),(0,t.jsx)("div",{className:"mt-4 bg-gray-100 rounded-lg p-3",children:(0,t.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsxs)("span",{children:["\uD83D\uDCB0 $",d.money]}),(0,t.jsxs)("span",{children:["⭐ Level ",d.level]}),(0,t.jsxs)("span",{children:["\uD83D\uDCE6 ",c.length," Orders"]})]}),(0,t.jsxs)("div",{className:"text-gray-600",children:[n("bakery.last.updated","Last updated"),": ",new Date().toLocaleTimeString()]})]})})]})}function I(e){let{isOpen:s,onClose:a}=e,{t:n}=(0,l.o)(),{orders:c,player:d,completeOrder:o}=(0,i.I)(),[m,x]=(0,r.useState)([]),[u,h]=(0,r.useState)(null);(0,r.useEffect)(()=>{x(c.map((e,s)=>{let a=e.timeLimit,t=Math.max(0,Math.min(100,a/300*100));return{id:e.id,name:e.customerName,avatar:(e=>{let s=["\uD83D\uDC68‍\uD83D\uDCBC","\uD83D\uDC69‍\uD83D\uDCBC","\uD83D\uDC68‍\uD83C\uDF93","\uD83D\uDC69‍\uD83C\uDF93","\uD83D\uDC68‍\uD83C\uDF73","\uD83D\uDC69‍\uD83C\uDF73","\uD83D\uDC68‍⚕️","\uD83D\uDC69‍⚕️","\uD83D\uDC68‍\uD83C\uDFA8","\uD83D\uDC69‍\uD83C\uDFA8","\uD83D\uDC68‍\uD83D\uDCBB","\uD83D\uDC69‍\uD83D\uDCBB","\uD83D\uDC68‍\uD83D\uDD2C","\uD83D\uDC69‍\uD83D\uDD2C","\uD83D\uDC68‍\uD83C\uDFEB","\uD83D\uDC69‍\uD83C\uDFEB","\uD83D\uDC68‍\uD83C\uDFA4","\uD83D\uDC69‍\uD83C\uDFA4"];return s[e.length%s.length]})(e.customerName),orderItems:e.items,patience:a,maxPatience:300,satisfaction:t,status:"pending"===e.status?"waiting":"in_progress"===e.status?"ordering":"served",tableNumber:s+1,orderValue:e.reward,preferences:["sweet","savory","healthy","indulgent","traditional","exotic"].slice(0,2+e.customerName.split("").reduce((e,s)=>e+s.charCodeAt(0),0)%3),mood:t>80?"happy":t>50?"neutral":t>20?"impatient":"angry"}}))},[c]);let g=e=>{switch(e){case"happy":return"\uD83D\uDE0A";case"neutral":return"\uD83D\uDE10";case"impatient":return"\uD83D\uDE24";case"angry":return"\uD83D\uDE20"}},p=e=>{switch(e){case"happy":return"text-green-600 bg-green-100";case"neutral":return"text-yellow-600 bg-yellow-100";case"impatient":return"text-orange-600 bg-orange-100";case"angry":return"text-red-600 bg-red-100"}},v=e=>{switch(e){case"entering":return"\uD83D\uDEB6";case"waiting":return"⏰";case"ordering":return"\uD83D\uDCDD";case"served":return"\uD83C\uDF7D️";case"eating":return"\uD83D\uDE0B";case"leaving":return"\uD83D\uDC4B"}};return s?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-500 p-6 text-white",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold",children:["\uD83D\uDC65 ",n("customers.manager.title","Customer Manager")]}),(0,t.jsx)("p",{className:"text-blue-100 text-sm",children:n("customers.manager.subtitle","Monitor and serve your customers")})]}),(0,t.jsxs)("button",{onClick:a,className:"bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg transition-colors",children:["✕ ",n("common.close","Close")]})]})}),(0,t.jsxs)("div",{className:"flex h-[70vh]",children:[(0,t.jsxs)("div",{className:"w-1/2 p-6 border-r border-gray-200 overflow-y-auto",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:["\uD83D\uDCCB ",n("customers.current.list","Current Customers")," (",m.length,")"]}),(0,t.jsxs)("div",{className:"space-y-3",children:[m.map(e=>(0,t.jsxs)("div",{onClick:()=>h(e),className:"p-4 rounded-lg border-2 cursor-pointer transition-all hover:shadow-md ".concat((null==u?void 0:u.id)===e.id?"border-blue-400 bg-blue-50":"border-gray-300 bg-white hover:border-gray-400"),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("span",{className:"text-2xl",children:e.avatar}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-gray-800",children:e.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[n("customers.table","Table")," ",e.tableNumber]})]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsxs)("div",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ".concat(p(e.mood)),children:[g(e.mood)," ",e.mood]})})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-lg",children:v(e.status)}),(0,t.jsx)("span",{className:"text-sm text-gray-600 capitalize",children:e.status})]}),(0,t.jsxs)("div",{className:"text-sm font-bold text-green-600",children:["$",e.orderValue]})]}),(0,t.jsxs)("div",{className:"mt-2",children:[(0,t.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mb-1",children:[(0,t.jsx)("span",{children:n("customers.patience","Patience")}),(0,t.jsxs)("span",{children:[Math.round(e.satisfaction),"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"h-2 rounded-full transition-all duration-300 ".concat(e.satisfaction>60?"bg-green-500":e.satisfaction>30?"bg-yellow-500":"bg-red-500"),style:{width:"".concat(e.satisfaction,"%")}})})]})]},e.id)),0===m.length&&(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFEA"}),(0,t.jsx)("p",{children:n("customers.no.customers","No customers currently")}),(0,t.jsx)("p",{className:"text-sm",children:n("customers.waiting.for.orders","Waiting for new orders...")})]})]})]}),(0,t.jsx)("div",{className:"w-1/2 p-6 overflow-y-auto",children:u?(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"text-center mb-6",children:[(0,t.jsx)("div",{className:"text-6xl mb-2",children:u.avatar}),(0,t.jsx)("h3",{className:"text-xl font-bold text-gray-800",children:u.name}),(0,t.jsxs)("p",{className:"text-gray-600",children:[n("customers.table","Table")," ",u.tableNumber]})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4 mb-4",children:[(0,t.jsxs)("h4",{className:"font-semibold text-gray-800 mb-3",children:["\uD83D\uDCDD ",n("customers.order.details","Order Details")]}),(0,t.jsx)("div",{className:"space-y-2",children:u.orderItems.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-2 bg-white rounded border",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{children:"\uD83E\uDDC1"}),(0,t.jsx)("span",{className:"font-medium",children:e})]}),(0,t.jsxs)("span",{className:"text-green-600 font-bold",children:["$",Math.round(u.orderValue/u.orderItems.length)]})]},s))}),(0,t.jsx)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:(0,t.jsxs)("div",{className:"flex justify-between font-bold",children:[(0,t.jsxs)("span",{children:[n("customers.total","Total"),":"]}),(0,t.jsxs)("span",{className:"text-green-600",children:["$",u.orderValue]})]})})]}),(0,t.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 mb-4",children:[(0,t.jsxs)("h4",{className:"font-semibold text-blue-800 mb-3",children:["ℹ️ ",n("customers.info","Customer Info")]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("span",{children:[n("customers.mood","Mood"),":"]}),(0,t.jsxs)("span",{className:"font-medium ".concat(p(u.mood)),children:[g(u.mood)," ",u.mood]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("span",{children:[n("customers.status","Status"),":"]}),(0,t.jsxs)("span",{className:"font-medium capitalize",children:[v(u.status)," ",u.status]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("span",{children:[n("customers.patience","Patience"),":"]}),(0,t.jsxs)("span",{className:"font-medium",children:[Math.round(u.satisfaction),"%"]})]})]})]}),(0,t.jsxs)("div",{className:"bg-purple-50 rounded-lg p-4 mb-4",children:[(0,t.jsxs)("h4",{className:"font-semibold text-purple-800 mb-3",children:["❤️ ",n("customers.preferences","Preferences")]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:u.preferences.map((e,s)=>(0,t.jsx)("span",{className:"px-3 py-1 bg-purple-200 text-purple-800 rounded-full text-sm font-medium",children:e},s))})]}),"waiting"===u.status&&(0,t.jsx)("div",{className:"space-y-2",children:(0,t.jsxs)("button",{onClick:()=>{o(u.id),x(e=>e.map(e=>e.id===u.id?{...e,status:"served"}:e))},className:"w-full bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-4 rounded-lg transition-colors",children:["\uD83C\uDF7D️ ",n("customers.serve.order","Serve Order")]})})]}):(0,t.jsxs)("div",{className:"text-center py-12 text-gray-500",children:[(0,t.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDC46"}),(0,t.jsx)("p",{className:"text-lg",children:n("customers.select.customer","Select a customer")}),(0,t.jsx)("p",{className:"text-sm",children:n("customers.select.to.view.details","Select a customer to view details")})]})})]})]})}):null}function q(e){let{onCustomerClick:s}=e,{t:a}=(0,l.o)(),{orders:n}=(0,i.I)(),[c,d]=(0,r.useState)([]),[o,m]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{d([{id:1,position:{x:20,y:20},seats:2,isOccupied:!1},{id:2,position:{x:60,y:20},seats:4,isOccupied:!1},{id:3,position:{x:100,y:20},seats:2,isOccupied:!1},{id:4,position:{x:20,y:60},seats:2,isOccupied:!1},{id:5,position:{x:60,y:60},seats:6,isOccupied:!1},{id:6,position:{x:100,y:60},seats:2,isOccupied:!1},{id:7,position:{x:20,y:100},seats:4,isOccupied:!1},{id:8,position:{x:60,y:100},seats:2,isOccupied:!1},{id:9,position:{x:100,y:100},seats:2,isOccupied:!1}])},[]),(0,r.useEffect)(()=>{d(e=>{let s=[...e];return s.forEach(e=>{e.isOccupied=!1,e.customer=void 0}),n.forEach((e,a)=>{let t=a%s.length,r=s[t];r&&(r.isOccupied=!0,r.customer={id:e.id,name:e.customerName,avatar:(e=>{let s=["\uD83D\uDC68‍\uD83D\uDCBC","\uD83D\uDC69‍\uD83D\uDCBC","\uD83D\uDC68‍\uD83C\uDF93","\uD83D\uDC69‍\uD83C\uDF93","\uD83D\uDC68‍\uD83C\uDF73","\uD83D\uDC69‍\uD83C\uDF73","\uD83D\uDC68‍⚕️","\uD83D\uDC69‍⚕️","\uD83D\uDC68‍\uD83C\uDFA8","\uD83D\uDC69‍\uD83C\uDFA8"];return s[e.length%s.length]})(e.customerName),order:e.items[0],satisfaction:Math.max(0,Math.min(100,e.timeLimit/300*100)),timeSeated:300-e.timeLimit})}),s})},[n]),(0,t.jsxs)("div",{className:"bg-gradient-to-br from-amber-50 to-orange-50 rounded-lg p-6 min-h-[600px] relative overflow-hidden",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-orange-800",children:["\uD83C\uDF7D️ ",a("dining.room.title","Dining Room")]}),(0,t.jsx)("p",{className:"text-orange-600",children:a("dining.room.subtitle","Watch your customers enjoy their meals")})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg px-4 py-2 border border-orange-200",children:[(0,t.jsx)("div",{className:"text-sm text-gray-600",children:a("dining.occupied.tables","Occupied Tables")}),(0,t.jsxs)("div",{className:"text-xl font-bold text-orange-600",children:[c.filter(e=>e.isOccupied).length,"/",c.length]})]}),(0,t.jsx)("button",{onClick:()=>m(!o),className:"p-2 rounded-lg transition-colors ".concat(o?"bg-green-100 text-green-600":"bg-gray-100 text-gray-600"),title:a("dining.ambient.sounds","Toggle ambient sounds"),children:o?"\uD83D\uDD0A":"\uD83D\uDD07"})]})]}),(0,t.jsxs)("div",{className:"relative bg-white rounded-lg border-2 border-orange-200 h-96 overflow-hidden",children:[(0,t.jsx)("div",{className:"absolute inset-0 opacity-10",children:(0,t.jsx)("div",{className:"grid grid-cols-8 grid-rows-6 h-full",children:Array.from({length:48}).map((e,s)=>(0,t.jsx)("div",{className:"border border-gray-300"},s))})}),c.map(e=>{var a;return(0,t.jsxs)("div",{className:(e=>{let s=e.seats<=2?"w-16 h-16":e.seats<=4?"w-20 h-20":"w-24 h-24",a=e.isOccupied?"bg-orange-100 border-orange-300":"bg-gray-100 border-gray-300";return"".concat("absolute transition-all duration-300 hover:scale-105 cursor-pointer"," ").concat(s," ").concat(a," border-2 rounded-lg flex flex-col items-center justify-center")})(e),style:{left:"".concat(e.position.x,"px"),top:"".concat(e.position.y,"px")},onClick:()=>e.customer&&(null==s?void 0:s(e.customer.id)),children:[(0,t.jsx)("div",{className:"text-2xl mb-1",children:(a=e.seats)<=2?"\uD83E\uDE91":a<=4?"\uD83C\uDF7D️":"\uD83C\uDFDB️"}),(0,t.jsxs)("div",{className:"text-xs font-bold text-gray-600",children:["#",e.id]}),e.customer&&(0,t.jsxs)("div",{className:"absolute -top-8 left-1/2 transform -translate-x-1/2",children:[(0,t.jsx)("div",{className:"bg-white rounded-full p-1 border-2 border-orange-300 shadow-lg",children:(0,t.jsx)("span",{className:"text-lg",children:e.customer.avatar})}),(0,t.jsx)("div",{className:"absolute -bottom-1 -right-1",children:(0,t.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(e.customer.satisfaction>80?"bg-green-500":e.customer.satisfaction>50?"bg-yellow-500":e.customer.satisfaction>20?"bg-orange-500":"bg-red-500")})})]}),e.customer&&(0,t.jsx)("div",{className:"absolute -bottom-6 left-1/2 transform -translate-x-1/2",children:(0,t.jsx)("div",{className:"animate-bounce text-xs",children:"\uD83C\uDF7D️"})})]},e.id)}),(0,t.jsx)("div",{className:"absolute top-4 left-4 text-2xl",children:"\uD83E\uDEB4"}),(0,t.jsx)("div",{className:"absolute top-4 right-4 text-2xl",children:"\uD83E\uDEB4"}),(0,t.jsx)("div",{className:"absolute bottom-4 left-4 text-2xl",children:"\uD83D\uDD6F️"}),(0,t.jsx)("div",{className:"absolute bottom-4 right-4 text-2xl",children:"\uD83D\uDD6F️"}),(0,t.jsx)("div",{className:"absolute bottom-0 left-1/2 transform -translate-x-1/2 bg-amber-200 rounded-t-lg p-2 border-2 border-amber-300",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-lg",children:"\uD83D\uDECE️"}),(0,t.jsx)("div",{className:"text-xs font-bold",children:a("dining.service.counter","Service")})]})})]}),(0,t.jsxs)("div",{className:"mt-6 bg-white rounded-lg p-4 border border-orange-200",children:[(0,t.jsxs)("h3",{className:"font-semibold text-gray-800 mb-3",children:["\uD83D\uDC65 ",a("dining.customer.status","Customer Status")]}),c.filter(e=>e.isOccupied).length>0?(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3",children:c.filter(e=>e.isOccupied).map(e=>{var r,l,i,n,c,d,o,m,x,u,h;return(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-3 border cursor-pointer hover:bg-gray-100 transition-colors",onClick:()=>e.customer&&(null==s?void 0:s(e.customer.id)),children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-lg",children:null==(r=e.customer)?void 0:r.avatar}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium text-sm",children:null==(l=e.customer)?void 0:l.name}),(0,t.jsxs)("div",{className:"text-xs text-gray-600",children:["Table ",e.id]})]})]}),(0,t.jsx)("div",{className:"text-lg ".concat((h=(null==(i=e.customer)?void 0:i.satisfaction)||0)>80?"text-green-500":h>50?"text-yellow-500":h>20?"text-orange-500":"text-red-500"),children:(null==(n=e.customer)?void 0:n.satisfaction)&&e.customer.satisfaction>80?"\uD83D\uDE0A":(null==(c=e.customer)?void 0:c.satisfaction)&&e.customer.satisfaction>50?"\uD83D\uDE10":(null==(d=e.customer)?void 0:d.satisfaction)&&e.customer.satisfaction>20?"\uD83D\uDE24":"\uD83D\uDE20"})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-600 mb-2",children:[a("dining.enjoying","Enjoying"),": ",null==(o=e.customer)?void 0:o.order]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-1",children:(0,t.jsx)("div",{className:"h-1 rounded-full transition-all duration-300 ".concat(((null==(m=e.customer)?void 0:m.satisfaction)||0)>60?"bg-green-500":((null==(x=e.customer)?void 0:x.satisfaction)||0)>30?"bg-yellow-500":"bg-red-500"),style:{width:"".concat((null==(u=e.customer)?void 0:u.satisfaction)||0,"%")}})})]},e.id)})}):(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFEA"}),(0,t.jsx)("p",{children:a("dining.no.customers","No customers dining currently")}),(0,t.jsx)("p",{className:"text-sm",children:a("dining.waiting.for.customers","Complete orders to see customers dining")})]})]}),o&&(0,t.jsxs)("div",{className:"absolute top-2 right-2 text-xs text-gray-500 animate-pulse",children:["\uD83C\uDFB5 ",a("dining.ambient.playing","Ambient sounds playing")]})]})}function z(e){let{children:s,fallback:a=null}=e,[l,i]=(0,r.useState)(!1);return((0,r.useEffect)(()=>{i(!0)},[]),l)?(0,t.jsx)(t.Fragment,{children:s}):(0,t.jsx)(t.Fragment,{children:a})}var O=a(9509);function $(e){let{isOpen:s,onClose:a}=e,{t:i}=(0,l.o)(),{isAuthenticated:c,user:d}=(0,S.y)(),[o,m]=(0,r.useState)(!1);if(!s)return null;let x=async()=>{if(!c)return void alert(i("dashboard.login_required","Please login to access the dashboard"));m(!0);try{let e=localStorage.getItem("accessToken"),s="".concat(O.env.NEXT_PUBLIC_API_URL||"http://localhost:3001","/dashboard?token=").concat(e);window.open(s,"_blank")}catch(e){console.error("Failed to open dashboard:",e),alert(i("dashboard.open_failed","Failed to open dashboard"))}finally{m(!1)}};return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-800",children:i("dashboard.title","Server Dashboard")}),(0,t.jsx)("button",{onClick:a,className:"text-gray-500 hover:text-gray-700 text-2xl",children:"\xd7"})]}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)("i",{className:"fas fa-chart-bar text-white text-2xl"})}),(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:i("dashboard.access_title","Access Server Dashboard")}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:i("dashboard.access_description","Monitor server statistics, manage users, and view game analytics")})]}),c?(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("i",{className:"fas fa-check-circle text-green-600 mr-2"}),(0,t.jsxs)("p",{className:"text-green-800 text-sm",children:[i("dashboard.logged_in_as","Logged in as"),": ",(0,t.jsx)("strong",{children:null==d?void 0:d.username})]})]})}):(0,t.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("i",{className:"fas fa-exclamation-triangle text-yellow-600 mr-2"}),(0,t.jsx)("p",{className:"text-yellow-800 text-sm",children:i("dashboard.login_required","Please login to access the dashboard")})]})}),(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("i",{className:"fas fa-users text-blue-500 mr-2"}),(0,t.jsx)("span",{children:i("dashboard.feature.users","User Management")})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("i",{className:"fas fa-gamepad text-green-500 mr-2"}),(0,t.jsx)("span",{children:i("dashboard.feature.rooms","Game Rooms")})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("i",{className:"fas fa-cloud text-purple-500 mr-2"}),(0,t.jsx)("span",{children:i("dashboard.feature.saves","Cloud Saves")})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("i",{className:"fas fa-chart-line text-orange-500 mr-2"}),(0,t.jsx)("span",{children:i("dashboard.feature.analytics","Analytics")})]})]})}),(0,t.jsxs)("div",{className:"flex space-x-3 mt-6",children:[(0,t.jsx)(n.$,{variant:"primary",onClick:x,disabled:!c||o,className:"flex-1",children:o?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-spinner fa-spin mr-2"}),i("dashboard.opening","Opening...")]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("i",{className:"fas fa-external-link-alt mr-2"}),i("dashboard.open","Open Dashboard")]})}),(0,t.jsx)(n.$,{variant:"secondary",onClick:a,disabled:o,children:i("dashboard.cancel","Cancel")})]}),(0,t.jsx)("div",{className:"mt-4 text-xs text-gray-500",children:(0,t.jsx)("p",{children:i("dashboard.new_tab_notice","Dashboard will open in a new tab")})})]})]})})}function M(){let{t:e}=(0,l.o)(),{isAuthenticated:s}=(0,S.y)(),[a,i]=(0,r.useState)(!1);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("button",{onClick:()=>i(!0),className:"flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors ".concat(s?"bg-blue-100 text-blue-700 hover:bg-blue-200":"bg-gray-100 text-gray-500 hover:bg-gray-200"),title:e("dashboard.tooltip","Access server dashboard"),children:[(0,t.jsx)("i",{className:"fas fa-chart-bar text-sm"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:e("dashboard.button","Dashboard")}),s&&(0,t.jsx)("div",{className:"w-2 h-2 bg-green-500 rounded-full"})]}),(0,t.jsx)($,{isOpen:a,onClose:()=>i(!1)})]})}function T(e){let{player:s,onOpenMenu:a,onOpenAchievements:i,onOpenSkills:c,onOpenBakeries:d,onOpenSettings:o}=e,{t:m}=(0,l.o)(),[x,u]=(0,r.useState)(!1),h=s.experience/s.maxExperience*100;return(0,t.jsx)("div",{className:"bg-white shadow-lg border-b border-gray-200 relative",children:(0,t.jsxs)("div",{className:"px-6 py-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(n.$,{variant:"secondary",size:"sm",className:"bg-orange-100 hover:bg-orange-200 text-orange-800",onClick:a,children:["☰ ",m("toolbar.menu","Menu")]}),(0,t.jsx)("div",{className:"hidden md:block",children:(0,t.jsxs)("h1",{className:"text-xl font-bold text-orange-800",children:["\uD83E\uDD56 ",m("game.title","Bake It Out")]})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-800",children:s.name}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:[m("game.level","Level")," ",s.level]})]}),(0,t.jsxs)("div",{className:"hidden sm:block",children:[(0,t.jsx)("div",{className:"w-32 bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(h,"%")}})}),(0,t.jsxs)("div",{className:"text-xs text-center text-gray-500 mt-1",children:[s.experience,"/",s.maxExperience," ",m("game.xp","XP")]})]}),(0,t.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,t.jsxs)("span",{className:"text-green-800 font-medium",children:["$",s.money]})}),s.skillPoints>0&&(0,t.jsxs)("div",{className:"bg-yellow-100 px-3 py-1 rounded-full relative",children:[(0,t.jsxs)("span",{className:"text-yellow-800 font-medium",children:["⭐ ",s.skillPoints]}),(0,t.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(n.$,{variant:"secondary",size:"sm",className:"md:hidden",onClick:()=>u(!x),children:"⚡"}),(0,t.jsxs)("div",{className:"hidden md:flex items-center space-x-2",children:[(0,t.jsx)(n.$,{variant:"secondary",size:"sm",onClick:d,children:"\uD83C\uDFEA"}),(0,t.jsx)(n.$,{variant:"secondary",size:"sm",onClick:i,className:"relative",children:"\uD83C\uDFC6"}),(0,t.jsxs)(n.$,{variant:"secondary",size:"sm",onClick:c,className:s.skillPoints>0?"bg-yellow-100 hover:bg-yellow-200":"",children:["\uD83C\uDF1F",s.skillPoints>0&&(0,t.jsx)("span",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]}),(0,t.jsx)(n.$,{variant:"secondary",size:"sm",onClick:o,children:"⚙️"}),(0,t.jsx)(M,{})]})]})]}),x&&(0,t.jsxs)("div",{className:"md:hidden mt-4 pt-4 border-t border-gray-200",children:[(0,t.jsxs)("div",{className:"grid grid-cols-4 gap-2",children:[(0,t.jsxs)(n.$,{variant:"secondary",size:"sm",className:"flex flex-col items-center py-3",onClick:()=>{d(),u(!1)},children:[(0,t.jsx)("span",{className:"text-lg",children:"\uD83C\uDFEA"}),(0,t.jsx)("span",{className:"text-xs",children:m("toolbar.bakeries","Bakeries")})]}),(0,t.jsxs)(n.$,{variant:"secondary",size:"sm",className:"flex flex-col items-center py-3",onClick:()=>{i(),u(!1)},children:[(0,t.jsx)("span",{className:"text-lg",children:"\uD83C\uDFC6"}),(0,t.jsx)("span",{className:"text-xs",children:m("toolbar.achievements","Achievements")})]}),(0,t.jsxs)(n.$,{variant:"secondary",size:"sm",className:"flex flex-col items-center py-3 relative ".concat(s.skillPoints>0?"bg-yellow-100 hover:bg-yellow-200":""),onClick:()=>{c(),u(!1)},children:[(0,t.jsx)("span",{className:"text-lg",children:"\uD83C\uDF1F"}),(0,t.jsx)("span",{className:"text-xs",children:m("toolbar.skills","Skills")}),s.skillPoints>0&&(0,t.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]}),(0,t.jsxs)(n.$,{variant:"secondary",size:"sm",className:"flex flex-col items-center py-3",onClick:()=>{o(),u(!1)},children:[(0,t.jsx)("span",{className:"text-lg",children:"⚙️"}),(0,t.jsx)("span",{className:"text-xs",children:m("toolbar.settings","Settings")})]})]}),(0,t.jsxs)("div",{className:"mt-3",children:[(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(h,"%")}})}),(0,t.jsxs)("div",{className:"text-xs text-center text-gray-500 mt-1",children:[s.experience,"/",s.maxExperience," XP"]})]})]})]})})}function B(e){let{isOpen:s,onClose:a,onSaveGame:r,onLoadGame:i,onSettings:c,onMainMenu:d,onExit:o}=e,{t:m}=(0,l.o)();return s?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-gradient-to-r from-orange-500 to-yellow-500 p-6 text-white",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold",children:["\uD83C\uDFAE ",m("gameMenu.title","Game Menu")]}),(0,t.jsx)("p",{className:"text-orange-100 text-sm",children:m("gameMenu.subtitle","Manage your game")})]}),(0,t.jsx)(n.$,{variant:"secondary",size:"sm",className:"bg-white/20 hover:bg-white/30 text-white border-white/30",onClick:a,children:"✕"})]})}),(0,t.jsxs)("div",{className:"p-6 space-y-3",children:[(0,t.jsxs)(n.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-orange-50",onClick:()=>{a()},children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:"▶️"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold",children:m("gameMenu.resume","Resume Game")}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:m("gameMenu.resumeDesc","Continue playing")})]})]}),(0,t.jsxs)(n.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-green-50",onClick:()=>{r(),a()},children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDCBE"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold",children:m("gameMenu.save","Save Game")}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:m("gameMenu.saveDesc","Save your progress")})]})]}),(0,t.jsxs)(n.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-blue-50",onClick:()=>{i(),a()},children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDCC1"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold",children:m("gameMenu.load","Load Game")}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:m("gameMenu.loadDesc","Load saved progress")})]})]}),(0,t.jsxs)(n.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-purple-50",onClick:()=>{c(),a()},children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:"⚙️"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold",children:m("gameMenu.settings","Settings")}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:m("gameMenu.settingsDesc","Game preferences")})]})]}),(0,t.jsxs)("div",{className:"border-t pt-3 mt-4",children:[(0,t.jsxs)(n.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-yellow-50",onClick:()=>{d(),a()},children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:"\uD83C\uDFE0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold",children:m("gameMenu.mainMenu","Main Menu")}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:m("gameMenu.mainMenuDesc","Return to main menu")})]})]}),o&&(0,t.jsxs)(n.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-red-50 text-red-600",onClick:()=>{o(),a()},children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDEAA"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold",children:m("gameMenu.exit","Exit Game")}),(0,t.jsx)("div",{className:"text-sm text-red-400",children:m("gameMenu.exitDesc","Close the application")})]})]})]})]}),(0,t.jsx)("div",{className:"bg-gray-50 px-6 py-3 text-center text-sm text-gray-500",children:m("gameMenu.tip","Press ESC to open this menu anytime")})]})}):null}var R=a(2148);function U(){var e;let{t:s}=(0,l.o)(),{setGameActivity:a,setBakingActivity:o}=(0,R.l)(),{player:m,equipment:x,inventory:j,orders:N,achievements:D,skills:S,levelUpRewards:E,showLevelUp:L,updateEquipment:_,acceptOrder:A,completeOrder:O,declineOrder:$,generateNewOrder:M,upgradeSkill:U,checkAchievements:J,dismissLevelUp:G,spendMoney:Y}=(0,i.I)(),[W,V]=(0,r.useState)(!1),[Q,H]=(0,r.useState)(!1),[K,X]=(0,r.useState)(!1),[Z,ee]=(0,r.useState)(!1),[es,ea]=(0,r.useState)(!1),[et,er]=(0,r.useState)(!1),[el,ei]=(0,r.useState)(!1),[en,ec]=(0,r.useState)(!1),[ed,eo]=(0,r.useState)(!1),[em,ex]=(0,r.useState)(!1),[eu,eh]=(0,r.useState)(!1),[eg,ep]=(0,r.useState)(!1),[ev,eb]=(0,r.useState)("save"),[ey,ej]=(0,r.useState)(null),[ef,eN]=(0,r.useState)("traditional"),[ew,ek]=(0,r.useState)({language:"en",soundEnabled:!0,musicEnabled:!0,notificationsEnabled:!0,autoSaveEnabled:!0,graphicsQuality:"medium",animationSpeed:1,showTutorials:!0}),[eD,eC]=(0,r.useState)([{id:"main",name:"Downtown Delights",location:"City Center",specialization:"general",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],unlocked:!0,purchaseCost:0}]),[eS,eE]=(0,r.useState)("main"),{notifications:eL,removeNotification:e_,showSuccess:eA,showError:eP,showInfo:eF}=function(){let[e,s]=(0,r.useState)([]),a=e=>{let a=Date.now().toString()+Math.random().toString(36).substr(2,9),t={...e,id:a,duration:e.duration||5e3};s(e=>[...e,t])};return{notifications:e,addNotification:a,removeNotification:e=>{s(s=>s.filter(s=>s.id!==e))},showSuccess:(e,s)=>{a({type:"success",title:e,message:s})},showError:(e,s)=>{a({type:"error",title:e,message:s})},showWarning:(e,s)=>{a({type:"warning",title:e,message:s})},showInfo:(e,s)=>{a({type:"info",title:e,message:s})}}}(),eI=(e,s)=>{ej({id:e,name:s}),X(!0)},eq=e=>{A(e),eF("Order Accepted","You have accepted a new order!")},ez=e=>{let s=N.find(s=>s.id===e);s&&(O(e),J(),eA("Order Completed!","You earned $".concat(s.reward," and gained experience!")))},eO=e=>{$(e),eF("Order Declined","Order has been removed from your queue.")};return window.addEventListener("keydown",e=>{"Escape"===e.key&&ex(!em)}),(0,r.useEffect)(()=>{let e=D.filter(e=>e.completed),s=parseInt(localStorage.getItem("completedAchievements")||"0");e.length>s&&(e.slice(s).forEach(e=>{eA("Achievement Unlocked!","\uD83C\uDFC6 ".concat(e.name))}),localStorage.setItem("completedAchievements",e.length.toString()))},[D,eA]),(0,r.useEffect)(()=>{(async()=>{if(N&&N.length>0){var e;let s=(null==(e=N[0].items[0])?void 0:e.name)||"Unknown item";await o(m.level,s)}else await a(m.level,m.money,"Managing bakery")})()},[m.level,m.money,N,a,o]),(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-orange-50 to-yellow-50",children:[(0,t.jsx)(T,{player:m,onOpenMenu:()=>ex(!0),onOpenAchievements:()=>ee(!0),onOpenSkills:()=>ea(!0),onOpenBakeries:()=>eo(!0),onOpenSettings:()=>ec(!0)}),(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-6 pb-4",children:(0,t.jsxs)("div",{className:"flex justify-center space-x-2",children:[(0,t.jsxs)("button",{onClick:()=>eN("traditional"),className:"px-4 py-2 rounded-lg font-medium transition-colors ".concat("traditional"===ef?"bg-orange-500 text-white":"bg-white text-gray-700 hover:bg-gray-100 border border-gray-300"),children:["\uD83D\uDCCA ",s("game.view.traditional","Traditional View")]}),(0,t.jsxs)("button",{onClick:()=>eN("layout"),className:"px-4 py-2 rounded-lg font-medium transition-colors ".concat("layout"===ef?"bg-orange-500 text-white":"bg-white text-gray-700 hover:bg-gray-100 border border-gray-300"),children:["\uD83C\uDFEA ",s("game.view.layout","Bakery Layout")]}),(0,t.jsxs)("button",{onClick:()=>eN("dining"),className:"px-4 py-2 rounded-lg font-medium transition-colors ".concat("dining"===ef?"bg-orange-500 text-white":"bg-white text-gray-700 hover:bg-gray-100 border border-gray-300"),children:["\uD83C\uDF7D️ ",s("game.view.dining","Dining Room")]}),(0,t.jsxs)("button",{onClick:()=>ep(!0),className:"px-4 py-2 rounded-lg font-medium bg-blue-500 text-white hover:bg-blue-600 transition-colors",children:["\uD83D\uDC65 ",s("game.view.customers","Customer Manager")]})]})}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto p-6 grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-3 space-y-6",children:["layout"===ef&&(0,t.jsx)(z,{fallback:(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6 text-center",children:"Loading bakery layout..."}),children:(0,t.jsx)(F,{equipment:x,onEquipmentClick:eI})}),"dining"===ef&&(0,t.jsx)(z,{fallback:(0,t.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6 text-center",children:"Loading dining room..."}),children:(0,t.jsx)(q,{onCustomerClick:e=>{ep(!0)}})}),"traditional"===ef&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-orange-800",children:s("kitchen.title")}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Current: ",null==(e=eD.find(e=>e.id===eS))?void 0:e.name]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:x.map(e=>(0,t.jsx)(c.$,{equipment:e,onClick:eI},e.id))})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-orange-800 mb-4",children:s("inventory.title")}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:j.map(e=>(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsx)("div",{className:"text-2xl mb-1",children:e.icon}),(0,t.jsx)("div",{className:"font-medium text-gray-800",children:s("ingredient.".concat(e.name.toLowerCase().replace(/\s+/g,"_")),e.name)}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:[s("inventory.quantity","Quantity"),": ",e.quantity]}),(0,t.jsxs)("div",{className:"text-xs text-green-600",children:[e.cost," ",s("currency.czk","Kč")," ",s("inventory.price_per_unit","per unit")]})]},e.name))})]})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-orange-800",children:s("orders.title")}),(0,t.jsx)(n.$,{size:"sm",variant:"primary",onClick:M,children:s("orders.new_order","+ New Order")})]}),(0,t.jsx)("div",{className:"space-y-4",children:N.map(e=>(0,t.jsx)(d.p,{order:e,onAccept:eq,onDecline:eO,onComplete:ez},e.id))})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-orange-800 mb-4",children:s("quick_actions.title","Quick Actions")}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(n.$,{variant:"secondary",size:"sm",className:"w-full",onClick:()=>H(!0),children:s("quick_actions.buy_ingredients","\uD83D\uDED2 Buy Ingredients")}),(0,t.jsx)(n.$,{variant:"secondary",size:"sm",className:"w-full",onClick:()=>V(!0),children:s("quick_actions.view_recipes","\uD83D\uDCD6 View Recipes")}),(0,t.jsx)(n.$,{variant:"secondary",size:"sm",className:"w-full",onClick:()=>ei(!0),children:s("quick_actions.equipment_shop","\uD83D\uDD27 Equipment Shop")})]})]})]})]}),(0,t.jsx)(u,{isOpen:W,onClose:()=>V(!1)}),(0,t.jsx)(h,{isOpen:Q,onClose:()=>H(!1)}),(0,t.jsx)(g,{isOpen:K,onClose:()=>X(!1),equipmentId:(null==ey?void 0:ey.id)||"",equipmentName:(null==ey?void 0:ey.name)||""}),(0,t.jsx)(b,{isOpen:Z,onClose:()=>ee(!1),achievements:D}),(0,t.jsx)(y,{isOpen:es,onClose:()=>ea(!1),skills:S,skillPoints:m.skillPoints,playerLevel:m.level,onUpgradeSkill:U}),(0,t.jsx)(v,{isOpen:L,onClose:G,newLevel:m.level,rewards:E}),(0,t.jsx)(f,{isOpen:et,onClose:()=>er(!1)}),(0,t.jsx)(w,{isOpen:el,onClose:()=>ei(!1),onShowSuccess:eA}),(0,t.jsx)(k.b,{isOpen:en,onClose:()=>ec(!1),settings:ew,onSettingsChange:e=>{ek(s=>({...s,...e}))}}),(0,t.jsx)(C,{isOpen:ed,onClose:()=>eo(!1),bakeries:eD,currentBakeryId:eS,onSwitchBakery:e=>{var s;eE(e),eF("Bakery Switched","Switched to ".concat(null==(s=eD.find(s=>s.id===e))?void 0:s.name))},onPurchaseBakery:e=>{if(Y(e.purchaseCost)){let s={id:Date.now().toString(),name:e.name,location:"Downtown",specialization:"general",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],unlocked:!0,purchaseCost:e.purchaseCost};eC(e=>[...e,s]),eA("Bakery Purchased!","You now own ".concat(e.name,"!"))}},playerMoney:m.money}),(0,t.jsx)(z,{children:(0,t.jsx)(I,{isOpen:eg,onClose:()=>ep(!1)})}),(0,t.jsx)(B,{isOpen:em,onClose:()=>ex(!1),onSaveGame:()=>{eb("save"),eh(!0)},onLoadGame:()=>{eb("load"),eh(!0)},onSettings:()=>ec(!0),onMainMenu:()=>{window.location.href="/"},onExit:window.electronAPI?()=>{window.electronAPI&&window.electronAPI.quit()}:void 0}),(0,t.jsx)(P,{isOpen:eu,onClose:()=>eh(!1),mode:ev,onSaveSuccess:()=>{eA("Game Saved!","Your progress has been saved successfully."),eh(!1)},onLoadSuccess:()=>{eA("Game Loaded!","Your saved progress has been loaded."),eh(!1)}}),(0,t.jsx)(p,{notifications:eL,onRemove:e_})]})}function J(){return(0,t.jsx)(i.S,{children:(0,t.jsx)(U,{})})}},913:(e,s,a)=>{a.d(s,{CloudSaveProvider:()=>d,y:()=>o});var t=a(5155),r=a(2115),l=a(9283),i=a(9509);let n=(0,r.createContext)(void 0),c=i.env.NEXT_PUBLIC_API_URL||"http://localhost:3001/api";function d(e){let{children:s}=e,{t:a}=(0,l.o)(),[i,d]=(0,r.useState)(null),[o,m]=(0,r.useState)(!1),[x,u]=(0,r.useState)(!0),[h,g]=(0,r.useState)([]),[p,v]=(0,r.useState)(!1),[b,y]=(0,r.useState)(!0),[j,f]=(0,r.useState)(null),[N,w]=(0,r.useState)("idle"),[k,D]=(0,r.useState)(null),C=async function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let a=await fetch("".concat(c).concat(e),{...s,headers:{...(()=>{let e=localStorage.getItem("accessToken");return{"Content-Type":"application/json",...e&&{Authorization:"Bearer ".concat(e)}}})(),...s.headers}}),t=await a.json();if(!a.ok)throw Error(t.error||"API request failed");return t}catch(e){throw console.error("API call failed:",e),e}},S=async(e,s)=>{try{u(!0),D(null);let a=await C("/auth/login",{method:"POST",body:JSON.stringify({username:e,password:s})});return localStorage.setItem("accessToken",a.tokens.accessToken),localStorage.setItem("refreshToken",a.tokens.refreshToken),d(a.user),m(!0),await F(),!0}catch(e){return D(e.message),!1}finally{u(!1)}},E=async(e,s,a)=>{try{u(!0),D(null);let t=await C("/auth/register",{method:"POST",body:JSON.stringify({username:e,email:s,password:a})});return localStorage.setItem("accessToken",t.tokens.accessToken),localStorage.setItem("refreshToken",t.tokens.refreshToken),d(t.user),m(!0),!0}catch(e){return D(e.message),!1}finally{u(!1)}},L=async()=>{try{await C("/auth/logout",{method:"POST"})}catch(e){console.error("Logout API call failed:",e)}finally{localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken"),d(null),m(!1),g([]),D(null)}},_=async function(e,s){let a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"manual";try{w("syncing"),D(null);let t=await C("/saves",{method:"POST",body:JSON.stringify({saveName:e,gameData:s,saveType:a,metadata:{gameVersion:"1.0.0",platform:"web",deviceInfo:navigator.userAgent}})});return await F(),w("success"),f(new Date),t.save.id}catch(e){return D(e.message),w("error"),null}},A=async e=>{try{w("syncing"),D(null);let s=await C("/saves/".concat(e));return w("success"),f(new Date),s.save.gameData}catch(e){return D(e.message),w("error"),null}},P=async e=>{try{return D(null),await C("/saves/".concat(e),{method:"DELETE"}),g(s=>s.filter(s=>s.id!==e)),!0}catch(e){return D(e.message),!1}},F=async()=>{if(o)try{v(!0),D(null);let e=await C("/saves");g(e.saves)}catch(e){D(e.message)}finally{v(!1)}};return(0,r.useEffect)(()=>{if(!b||!o)return;let e=setInterval(()=>{F()},3e5);return()=>clearInterval(e)},[b,o]),(0,r.useEffect)(()=>{(async()=>{if(localStorage.getItem("accessToken"))try{let e=await C("/auth/verify");d(e.user),m(!0),await F()}catch(e){localStorage.removeItem("accessToken"),localStorage.removeItem("refreshToken")}u(!1)})()},[]),(0,t.jsx)(n.Provider,{value:{user:i,isAuthenticated:o,isLoading:x,login:S,register:E,logout:L,cloudSaves:h,isLoadingSaves:p,saveToCloud:_,loadFromCloud:A,deleteCloudSave:P,refreshCloudSaves:F,autoSyncEnabled:b,setAutoSyncEnabled:y,lastSyncTime:j,syncStatus:N,error:k,clearError:()=>D(null)},children:s})}function o(){let e=(0,r.useContext)(n);if(void 0===e)throw Error("useCloudSave must be used within a CloudSaveProvider");return e}}}]);