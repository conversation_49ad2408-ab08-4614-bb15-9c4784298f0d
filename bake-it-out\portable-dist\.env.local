# Firebase Configuration for Cloud Saves
NEXT_PUBLIC_FIREBASE_API_KEY=your_firebase_api_key
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your_measurement_id

# Optional: Firebase Emulator (for development)
NEXT_PUBLIC_USE_FIREBASE_EMULATOR=false

# Socket.io Configuration (for multiplayer)
NEXT_PUBLIC_SOCKET_URL=http://localhost:3001

# Game Configuration
NEXT_PUBLIC_GAME_VERSION=1.1.0
NEXT_PUBLIC_API_URL=http://localhost:3001

# Development Settings
NODE_ENV=development
NEXT_PUBLIC_DEBUG_MODE=false

# Cloud Save Settings
NEXT_PUBLIC_ENABLE_CLOUD_SAVES=true
NEXT_PUBLIC_AUTO_SYNC_INTERVAL=30000
NEXT_PUBLIC_MAX_CLOUD_SAVES=10
