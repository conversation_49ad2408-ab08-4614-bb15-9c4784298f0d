#!/bin/bash

# Bake It Out Server Deployment Script
# Automates deployment to various platforms

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Show usage
show_usage() {
    echo "Usage: $0 [PLATFORM] [OPTIONS]"
    echo
    echo "Platforms:"
    echo "  docker     - Build and run with Docker"
    echo "  heroku     - Deploy to Heroku"
    echo "  vps        - Deploy to VPS"
    echo "  local      - Local production deployment"
    echo
    echo "Options:"
    echo "  --build-only    - Only build, don't deploy"
    echo "  --skip-tests    - Skip running tests"
    echo "  --force         - Force deployment without confirmation"
    echo
    echo "Examples:"
    echo "  $0 docker"
    echo "  $0 heroku --skip-tests"
    echo "  $0 vps --build-only"
}

# Check prerequisites
check_prerequisites() {
    print_status "Checking prerequisites..."
    
    # Check if we're in the right directory
    if [ ! -f "package.json" ]; then
        print_error "package.json not found. Please run from the server directory."
        exit 1
    fi
    
    # Check Node.js
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed"
        exit 1
    fi
    
    # Check npm
    if ! command -v npm &> /dev/null; then
        print_error "npm is not installed"
        exit 1
    fi
    
    print_success "Prerequisites check passed"
}

# Run tests
run_tests() {
    if [ "$SKIP_TESTS" != "true" ]; then
        print_status "Running tests..."
        npm test
        print_success "Tests passed"
    else
        print_warning "Skipping tests"
    fi
}

# Build application
build_app() {
    print_status "Building application..."
    
    # Install production dependencies
    npm ci --only=production
    
    # Run any build scripts if they exist
    if npm run build &> /dev/null; then
        print_success "Build completed"
    else
        print_status "No build script found, skipping"
    fi
}

# Docker deployment
deploy_docker() {
    print_status "Deploying with Docker..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed"
        exit 1
    fi
    
    # Build Docker image
    print_status "Building Docker image..."
    docker build -t bake-it-out-server .
    
    if [ "$BUILD_ONLY" = "true" ]; then
        print_success "Docker image built successfully"
        return
    fi
    
    # Stop existing container if running
    if docker ps | grep -q "bake-it-out-server"; then
        print_status "Stopping existing container..."
        docker stop bake-it-out-server
        docker rm bake-it-out-server
    fi
    
    # Run new container
    print_status "Starting new container..."
    docker run -d \
        --name bake-it-out-server \
        -p 3001:3001 \
        --env-file .env \
        bake-it-out-server
    
    print_success "Docker deployment completed"
    print_status "Server is running on http://localhost:3001"
}

# Heroku deployment
deploy_heroku() {
    print_status "Deploying to Heroku..."
    
    if ! command -v heroku &> /dev/null; then
        print_error "Heroku CLI is not installed"
        exit 1
    fi
    
    # Check if logged in to Heroku
    if ! heroku auth:whoami &> /dev/null; then
        print_error "Please login to Heroku: heroku login"
        exit 1
    fi
    
    # Get app name
    if [ -z "$HEROKU_APP" ]; then
        read -p "Enter Heroku app name: " HEROKU_APP
    fi
    
    # Check if app exists
    if ! heroku apps:info $HEROKU_APP &> /dev/null; then
        print_status "Creating Heroku app: $HEROKU_APP"
        heroku create $HEROKU_APP
    fi
    
    # Set environment variables
    print_status "Setting environment variables..."
    if [ -f ".env" ]; then
        while IFS= read -r line; do
            if [[ $line =~ ^[A-Z_]+=.* ]]; then
                heroku config:set "$line" --app $HEROKU_APP
            fi
        done < .env
    fi
    
    # Add MongoDB addon if not exists
    if ! heroku addons --app $HEROKU_APP | grep -q "mongolab\|mongodb"; then
        print_status "Adding MongoDB addon..."
        heroku addons:create mongolab:sandbox --app $HEROKU_APP
    fi
    
    # Deploy
    print_status "Deploying to Heroku..."
    git push heroku main
    
    print_success "Heroku deployment completed"
    print_status "App URL: https://$HEROKU_APP.herokuapp.com"
}

# VPS deployment
deploy_vps() {
    print_status "Deploying to VPS..."
    
    if [ -z "$VPS_HOST" ]; then
        read -p "Enter VPS hostname/IP: " VPS_HOST
    fi
    
    if [ -z "$VPS_USER" ]; then
        read -p "Enter VPS username: " VPS_USER
    fi
    
    # Create deployment package
    print_status "Creating deployment package..."
    tar -czf deploy.tar.gz \
        --exclude=node_modules \
        --exclude=.git \
        --exclude=logs \
        --exclude=*.log \
        .
    
    # Upload to VPS
    print_status "Uploading to VPS..."
    scp deploy.tar.gz $VPS_USER@$VPS_HOST:~/
    
    # Deploy on VPS
    print_status "Deploying on VPS..."
    ssh $VPS_USER@$VPS_HOST << 'EOF'
        # Extract deployment package
        tar -xzf deploy.tar.gz -C ~/bake-it-out-server --strip-components=1 || mkdir -p ~/bake-it-out-server && tar -xzf deploy.tar.gz -C ~/bake-it-out-server
        cd ~/bake-it-out-server
        
        # Install dependencies
        npm ci --only=production
        
        # Stop existing server
        pm2 stop bake-it-out-server || true
        
        # Start server
        pm2 start src/index.js --name bake-it-out-server
        pm2 save
        
        # Setup startup script
        pm2 startup || true
EOF
    
    # Cleanup
    rm deploy.tar.gz
    
    print_success "VPS deployment completed"
    print_status "Server should be running on $VPS_HOST:3001"
}

# Local production deployment
deploy_local() {
    print_status "Setting up local production deployment..."
    
    # Set production environment
    export NODE_ENV=production
    
    # Build application
    build_app
    
    # Install PM2 if not present
    if ! command -v pm2 &> /dev/null; then
        print_status "Installing PM2..."
        npm install -g pm2
    fi
    
    # Stop existing server
    pm2 stop bake-it-out-server || true
    
    # Start server with PM2
    print_status "Starting server with PM2..."
    pm2 start src/index.js --name bake-it-out-server
    pm2 save
    
    # Setup startup script
    pm2 startup
    
    print_success "Local production deployment completed"
    print_status "Server is running on http://localhost:3001"
    print_status "Use 'pm2 status' to check server status"
}

# Confirmation prompt
confirm_deployment() {
    if [ "$FORCE" != "true" ]; then
        echo
        print_warning "You are about to deploy to $PLATFORM"
        read -p "Are you sure you want to continue? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            print_status "Deployment cancelled"
            exit 0
        fi
    fi
}

# Main deployment function
main() {
    PLATFORM=$1
    BUILD_ONLY=false
    SKIP_TESTS=false
    FORCE=false
    
    # Parse options
    shift
    while [[ $# -gt 0 ]]; do
        case $1 in
            --build-only)
                BUILD_ONLY=true
                shift
                ;;
            --skip-tests)
                SKIP_TESTS=true
                shift
                ;;
            --force)
                FORCE=true
                shift
                ;;
            *)
                print_error "Unknown option: $1"
                show_usage
                exit 1
                ;;
        esac
    done
    
    # Check if platform is specified
    if [ -z "$PLATFORM" ]; then
        show_usage
        exit 1
    fi
    
    echo "🚀 Bake It Out Server Deployment"
    echo "================================"
    echo "Platform: $PLATFORM"
    echo
    
    # Check prerequisites
    check_prerequisites
    
    # Run tests
    run_tests
    
    # Confirm deployment
    confirm_deployment
    
    # Deploy based on platform
    case $PLATFORM in
        docker)
            deploy_docker
            ;;
        heroku)
            deploy_heroku
            ;;
        vps)
            deploy_vps
            ;;
        local)
            deploy_local
            ;;
        *)
            print_error "Unknown platform: $PLATFORM"
            show_usage
            exit 1
            ;;
    esac
    
    echo
    print_success "🎉 Deployment completed successfully!"
}

# Run main function with all arguments
main "$@"
