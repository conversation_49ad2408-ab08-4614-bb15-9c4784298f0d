<!-- <PERSON> Header -->
<div class="flex justify-between items-center mb-6">
    <div>
        <h1 class="text-2xl font-bold text-gray-800">User Management</h1>
        <p class="text-gray-600">Manage registered users and their accounts</p>
    </div>
    <div class="flex space-x-3">
        <button class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition duration-200 flex items-center">
            <i class="fas fa-download mr-2"></i>
            Export Users
        </button>
        <button class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition duration-200 flex items-center">
            <i class="fas fa-plus mr-2"></i>
            Add User
        </button>
    </div>
</div>

<!-- Search and Filters -->
<div class="bg-white rounded-lg shadow-sm p-6 mb-6">
    <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
        <div class="flex-1 max-w-md">
            <div class="relative">
                <input 
                    type="text" 
                    placeholder="Search users..." 
                    class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                    id="user-search"
                >
                <i class="fas fa-search absolute left-3 top-3 text-gray-400"></i>
            </div>
        </div>
        <div class="flex space-x-3">
            <select class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option>All Users</option>
                <option>Active Users</option>
                <option>Inactive Users</option>
                <option>New Users (7 days)</option>
            </select>
            <select class="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option>Sort by: Created Date</option>
                <option>Sort by: Username</option>
                <option>Sort by: Last Login</option>
                <option>Sort by: Play Time</option>
            </select>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="bg-white rounded-lg shadow-sm overflow-hidden">
    <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
                <tr>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        User
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Game Stats
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Activity
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Status
                    </th>
                    <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                    </th>
                </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
                <% users.forEach(user => { %>
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <div class="h-10 w-10 rounded-full bg-gradient-to-r from-purple-400 to-pink-400 flex items-center justify-center">
                                    <span class="text-white font-medium text-sm">
                                        <%= user.profile.avatar || user.username.charAt(0).toUpperCase() %>
                                    </span>
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">
                                    <%= user.profile.displayName || user.username %>
                                </div>
                                <div class="text-sm text-gray-500">
                                    @<%= user.username %> • <%= user.email %>
                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">
                            <div class="flex items-center space-x-4">
                                <div>
                                    <span class="text-xs text-gray-500">Level</span>
                                    <div class="font-medium"><%= user.gameStats.highestLevel %></div>
                                </div>
                                <div>
                                    <span class="text-xs text-gray-500">Play Time</span>
                                    <div class="font-medium"><%= Math.floor(user.gameStats.totalPlayTime / 3600) %>h</div>
                                </div>
                                <div>
                                    <span class="text-xs text-gray-500">Achievements</span>
                                    <div class="font-medium"><%= user.gameStats.achievementsUnlocked %></div>
                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="text-sm text-gray-900">
                            <div>
                                <span class="text-xs text-gray-500">Joined</span>
                                <div class="font-medium"><%= new Date(user.createdAt).toLocaleDateString() %></div>
                            </div>
                            <div class="mt-1">
                                <span class="text-xs text-gray-500">Last Login</span>
                                <div class="text-sm text-gray-600">
                                    <%= user.lastLoginAt ? new Date(user.lastLoginAt).toLocaleDateString() : 'Never' %>
                                </div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <% if (user.isActive) { %>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <div class="w-1.5 h-1.5 bg-green-400 rounded-full mr-1"></div>
                                Active
                            </span>
                        <% } else { %>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                <div class="w-1.5 h-1.5 bg-red-400 rounded-full mr-1"></div>
                                Inactive
                            </span>
                        <% } %>
                        <div class="mt-1">
                            <span class="text-xs text-gray-500">
                                <%= user.profile.preferredLanguage === 'cs' ? '🇨🇿 Czech' : '🇺🇸 English' %>
                            </span>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div class="flex space-x-2">
                            <button class="text-blue-600 hover:text-blue-900 transition duration-200" title="View Details">
                                <i class="fas fa-eye"></i>
                            </button>
                            <button class="text-green-600 hover:text-green-900 transition duration-200" title="Edit User">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="text-orange-600 hover:text-orange-900 transition duration-200" title="View Saves">
                                <i class="fas fa-cloud"></i>
                            </button>
                            <% if (user.isActive) { %>
                                <button class="text-red-600 hover:text-red-900 transition duration-200" title="Deactivate">
                                    <i class="fas fa-ban"></i>
                                </button>
                            <% } else { %>
                                <button class="text-green-600 hover:text-green-900 transition duration-200" title="Activate">
                                    <i class="fas fa-check"></i>
                                </button>
                            <% } %>
                        </div>
                    </td>
                </tr>
                <% }); %>
            </tbody>
        </table>
    </div>
</div>

<!-- Pagination -->
<% if (pagination.total > 1) { %>
<div class="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6 mt-6 rounded-lg shadow-sm">
    <div class="flex-1 flex justify-between sm:hidden">
        <% if (pagination.hasPrev) { %>
            <a href="?page=<%= pagination.current - 1 %>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Previous
            </a>
        <% } %>
        <% if (pagination.hasNext) { %>
            <a href="?page=<%= pagination.current + 1 %>" class="ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                Next
            </a>
        <% } %>
    </div>
    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
        <div>
            <p class="text-sm text-gray-700">
                Showing page <span class="font-medium"><%= pagination.current %></span> of <span class="font-medium"><%= pagination.total %></span>
            </p>
        </div>
        <div>
            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                <% if (pagination.hasPrev) { %>
                    <a href="?page=<%= pagination.current - 1 %>" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i class="fas fa-chevron-left"></i>
                    </a>
                <% } %>
                
                <% for (let i = Math.max(1, pagination.current - 2); i <= Math.min(pagination.total, pagination.current + 2); i++) { %>
                    <% if (i === pagination.current) { %>
                        <span class="relative inline-flex items-center px-4 py-2 border border-blue-500 bg-blue-50 text-sm font-medium text-blue-600">
                            <%= i %>
                        </span>
                    <% } else { %>
                        <a href="?page=<%= i %>" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700 hover:bg-gray-50">
                            <%= i %>
                        </a>
                    <% } %>
                <% } %>
                
                <% if (pagination.hasNext) { %>
                    <a href="?page=<%= pagination.current + 1 %>" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                        <i class="fas fa-chevron-right"></i>
                    </a>
                <% } %>
            </nav>
        </div>
    </div>
</div>
<% } %>

<script>
    // Search functionality
    document.getElementById('user-search').addEventListener('input', function(e) {
        const searchTerm = e.target.value.toLowerCase();
        const rows = document.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });

    // Action buttons
    document.querySelectorAll('button[title]').forEach(button => {
        button.addEventListener('click', function() {
            const action = this.title;
            const row = this.closest('tr');
            const username = row.querySelector('.text-sm.font-medium.text-gray-900').textContent.trim();
            
            console.log(`${action} action for user: ${username}`);
            // Implement actual actions here
        });
    });
</script>
