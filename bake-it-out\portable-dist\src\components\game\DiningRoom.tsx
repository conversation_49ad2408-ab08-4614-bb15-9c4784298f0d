'use client'

import { useState, useEffect } from 'react'
import { useLanguage } from '@/contexts/LanguageContext'
import { useGame } from '@/contexts/GameContext'

interface DiningTable {
  id: number
  position: { x: number; y: number }
  seats: number
  isOccupied: boolean
  customer?: {
    id: string
    name: string
    avatar: string
    order: string
    satisfaction: number
    timeSeated: number
  }
}

interface DiningRoomProps {
  onCustomerClick?: (customerId: string) => void
}

export function DiningRoom({ onCustomerClick }: DiningRoomProps) {
  const { t } = useLanguage()
  const { orders } = useGame()
  const [tables, setTables] = useState<DiningTable[]>([])
  const [ambientSounds, setAmbientSounds] = useState(true)

  // Helper functions
  const getCustomerAvatar = (name: string) => {
    const avatars = ['👨‍💼', '👩‍💼', '👨‍🎓', '👩‍🎓', '👨‍🍳', '👩‍🍳', '👨‍⚕️', '👩‍⚕️', '👨‍🎨', '👩‍🎨']
    return avatars[name.length % avatars.length]
  }

  // Initialize dining room layout
  useEffect(() => {
    const initialTables: DiningTable[] = [
      { id: 1, position: { x: 20, y: 20 }, seats: 2, isOccupied: false },
      { id: 2, position: { x: 60, y: 20 }, seats: 4, isOccupied: false },
      { id: 3, position: { x: 100, y: 20 }, seats: 2, isOccupied: false },
      { id: 4, position: { x: 20, y: 60 }, seats: 2, isOccupied: false },
      { id: 5, position: { x: 60, y: 60 }, seats: 6, isOccupied: false },
      { id: 6, position: { x: 100, y: 60 }, seats: 2, isOccupied: false },
      { id: 7, position: { x: 20, y: 100 }, seats: 4, isOccupied: false },
      { id: 8, position: { x: 60, y: 100 }, seats: 2, isOccupied: false },
      { id: 9, position: { x: 100, y: 100 }, seats: 2, isOccupied: false },
    ]
    setTables(initialTables)
  }, [])

  // Update tables with customers from orders
  useEffect(() => {
    setTables(prevTables => {
      const updatedTables = [...prevTables]

      // Clear all tables first
      updatedTables.forEach(table => {
        table.isOccupied = false
        table.customer = undefined
      })

      // Assign customers to tables
      orders.forEach((order, index) => {
        const tableIndex = index % updatedTables.length
        const table = updatedTables[tableIndex]

        if (table) {
          table.isOccupied = true
          table.customer = {
            id: order.id,
            name: order.customerName,
            avatar: getCustomerAvatar(order.customerName),
            order: order.items[0],
            satisfaction: Math.max(0, Math.min(100, (order.timeLimit / 300) * 100)),
            timeSeated: 300 - order.timeLimit
          }
        }
      })

      return updatedTables
    })
  }, [orders])

  const getTableIcon = (seats: number) => {
    if (seats <= 2) return '🪑'
    if (seats <= 4) return '🍽️'
    return '🏛️'
  }

  const getSatisfactionColor = (satisfaction: number) => {
    if (satisfaction > 80) return 'text-green-500'
    if (satisfaction > 50) return 'text-yellow-500'
    if (satisfaction > 20) return 'text-orange-500'
    return 'text-red-500'
  }

  const getTableStyle = (table: DiningTable) => {
    const baseStyle = "absolute transition-all duration-300 hover:scale-105 cursor-pointer"
    const sizeStyle = table.seats <= 2 ? "w-16 h-16" : table.seats <= 4 ? "w-20 h-20" : "w-24 h-24"
    const colorStyle = table.isOccupied ? "bg-orange-100 border-orange-300" : "bg-gray-100 border-gray-300"
    
    return `${baseStyle} ${sizeStyle} ${colorStyle} border-2 rounded-lg flex flex-col items-center justify-center`
  }

  return (
    <div className="bg-gradient-to-br from-amber-50 to-orange-50 rounded-lg p-6 min-h-[600px] relative overflow-hidden">
      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold text-orange-800">
            🍽️ {t('dining.room.title', 'Dining Room')}
          </h2>
          <p className="text-orange-600">
            {t('dining.room.subtitle', 'Watch your customers enjoy their meals')}
          </p>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="bg-white rounded-lg px-4 py-2 border border-orange-200">
            <div className="text-sm text-gray-600">{t('dining.occupied.tables', 'Occupied Tables')}</div>
            <div className="text-xl font-bold text-orange-600">
              {tables.filter(t => t.isOccupied).length}/{tables.length}
            </div>
          </div>
          
          <button
            onClick={() => setAmbientSounds(!ambientSounds)}
            className={`p-2 rounded-lg transition-colors ${
              ambientSounds ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'
            }`}
            title={t('dining.ambient.sounds', 'Toggle ambient sounds')}
          >
            {ambientSounds ? '🔊' : '🔇'}
          </button>
        </div>
      </div>

      {/* Dining Room Floor Plan */}
      <div className="relative bg-white rounded-lg border-2 border-orange-200 h-96 overflow-hidden">
        {/* Floor Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="grid grid-cols-8 grid-rows-6 h-full">
            {Array.from({ length: 48 }).map((_, i) => (
              <div key={i} className="border border-gray-300"></div>
            ))}
          </div>
        </div>

        {/* Tables */}
        {tables.map(table => (
          <div
            key={table.id}
            className={getTableStyle(table)}
            style={{
              left: `${table.position.x}px`,
              top: `${table.position.y}px`,
            }}
            onClick={() => table.customer && onCustomerClick?.(table.customer.id)}
          >
            {/* Table Icon */}
            <div className="text-2xl mb-1">{getTableIcon(table.seats)}</div>
            
            {/* Table Number */}
            <div className="text-xs font-bold text-gray-600">#{table.id}</div>
            
            {/* Customer Info */}
            {table.customer && (
              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2">
                <div className="bg-white rounded-full p-1 border-2 border-orange-300 shadow-lg">
                  <span className="text-lg">{table.customer.avatar}</span>
                </div>
                
                {/* Satisfaction Indicator */}
                <div className="absolute -bottom-1 -right-1">
                  <div className={`w-3 h-3 rounded-full ${
                    table.customer.satisfaction > 80 ? 'bg-green-500' :
                    table.customer.satisfaction > 50 ? 'bg-yellow-500' :
                    table.customer.satisfaction > 20 ? 'bg-orange-500' : 'bg-red-500'
                  }`}></div>
                </div>
              </div>
            )}

            {/* Eating Animation */}
            {table.customer && (
              <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2">
                <div className="animate-bounce text-xs">🍽️</div>
              </div>
            )}
          </div>
        ))}

        {/* Decorative Elements */}
        <div className="absolute top-4 left-4 text-2xl">🪴</div>
        <div className="absolute top-4 right-4 text-2xl">🪴</div>
        <div className="absolute bottom-4 left-4 text-2xl">🕯️</div>
        <div className="absolute bottom-4 right-4 text-2xl">🕯️</div>
        
        {/* Service Counter */}
        <div className="absolute bottom-0 left-1/2 transform -translate-x-1/2 bg-amber-200 rounded-t-lg p-2 border-2 border-amber-300">
          <div className="text-center">
            <div className="text-lg">🛎️</div>
            <div className="text-xs font-bold">{t('dining.service.counter', 'Service')}</div>
          </div>
        </div>
      </div>

      {/* Customer Status Panel */}
      <div className="mt-6 bg-white rounded-lg p-4 border border-orange-200">
        <h3 className="font-semibold text-gray-800 mb-3">
          👥 {t('dining.customer.status', 'Customer Status')}
        </h3>
        
        {tables.filter(t => t.isOccupied).length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
            {tables.filter(t => t.isOccupied).map(table => (
              <div
                key={table.id}
                className="bg-gray-50 rounded-lg p-3 border cursor-pointer hover:bg-gray-100 transition-colors"
                onClick={() => table.customer && onCustomerClick?.(table.customer.id)}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-lg">{table.customer?.avatar}</span>
                    <div>
                      <div className="font-medium text-sm">{table.customer?.name}</div>
                      <div className="text-xs text-gray-600">Table {table.id}</div>
                    </div>
                  </div>
                  <div className={`text-lg ${getSatisfactionColor(table.customer?.satisfaction || 0)}`}>
                    {table.customer?.satisfaction && table.customer.satisfaction > 80 ? '😊' :
                     table.customer?.satisfaction && table.customer.satisfaction > 50 ? '😐' :
                     table.customer?.satisfaction && table.customer.satisfaction > 20 ? '😤' : '😠'}
                  </div>
                </div>
                
                <div className="text-xs text-gray-600 mb-2">
                  {t('dining.enjoying', 'Enjoying')}: {table.customer?.order}
                </div>
                
                <div className="w-full bg-gray-200 rounded-full h-1">
                  <div
                    className={`h-1 rounded-full transition-all duration-300 ${
                      (table.customer?.satisfaction || 0) > 60 ? 'bg-green-500' :
                      (table.customer?.satisfaction || 0) > 30 ? 'bg-yellow-500' : 'bg-red-500'
                    }`}
                    style={{ width: `${table.customer?.satisfaction || 0}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            <div className="text-4xl mb-2">🏪</div>
            <p>{t('dining.no.customers', 'No customers dining currently')}</p>
            <p className="text-sm">{t('dining.waiting.for.customers', 'Complete orders to see customers dining')}</p>
          </div>
        )}
      </div>

      {/* Ambient Effects */}
      {ambientSounds && (
        <div className="absolute top-2 right-2 text-xs text-gray-500 animate-pulse">
          🎵 {t('dining.ambient.playing', 'Ambient sounds playing')}
        </div>
      )}
    </div>
  )
}
