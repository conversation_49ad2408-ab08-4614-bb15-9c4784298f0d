// Save/Load system for Bake It Out

export interface GameSave {
  version: string
  timestamp: number
  player: {
    level: number
    experience: number
    money: number
    skillPoints: number
    totalMoneyEarned: number
    totalOrdersCompleted: number
    totalItemsBaked: number
    unlockedRecipes: string[]
    automationUpgrades: string[]
  }
  equipment: any[]
  inventory: any[]
  achievements: any[]
  skills: any[]
  automationSettings: any
  gameSettings: {
    language: string
    soundEnabled: boolean
    musicEnabled: boolean
    notificationsEnabled: boolean
    autoSaveEnabled: boolean
  }
  bakeries: BakeryLocation[]
  currentBakeryId: string
}

export interface BakeryLocation {
  id: string
  name: string
  location: string
  specialization: 'general' | 'cookies' | 'cakes' | 'bread' | 'pastries'
  level: number
  equipment: any[]
  inventory: any[]
  orders: any[]
  automationJobs: any[]
  conveyorBelts: any[]
  unlocked: boolean
  purchaseCost: number
}

export interface CloudSaveMetadata {
  id: string
  userId: string
  deviceId: string
  lastModified: number
  gameVersion: string
  bakeryCount: number
  playerLevel: number
}

const SAVE_VERSION = '1.0.0'
const LOCAL_STORAGE_KEY = 'bakeItOut_gameSave'
const AUTO_SAVE_INTERVAL = 30000 // 30 seconds

export class SaveSystem {
  private autoSaveInterval: NodeJS.Timeout | null = null
  private cloudSyncEnabled = false

  constructor() {
    this.initializeAutoSave()
  }

  // Local Storage Operations
  saveToLocal(gameData: Partial<GameSave>): boolean {
    try {
      const save: GameSave = {
        version: SAVE_VERSION,
        timestamp: Date.now(),
        ...gameData
      } as GameSave

      const saveString = JSON.stringify(save)
      localStorage.setItem(LOCAL_STORAGE_KEY, saveString)
      
      console.log('Game saved to local storage')
      return true
    } catch (error) {
      console.error('Failed to save game to local storage:', error)
      return false
    }
  }

  loadFromLocal(): GameSave | null {
    try {
      const saveString = localStorage.getItem(LOCAL_STORAGE_KEY)
      if (!saveString) return null

      const save: GameSave = JSON.parse(saveString)
      
      // Version compatibility check
      if (save.version !== SAVE_VERSION) {
        console.warn('Save version mismatch, attempting migration')
        return this.migrateSave(save)
      }

      console.log('Game loaded from local storage')
      return save
    } catch (error) {
      console.error('Failed to load game from local storage:', error)
      return null
    }
  }

  deleteLocalSave(): boolean {
    try {
      localStorage.removeItem(LOCAL_STORAGE_KEY)
      console.log('Local save deleted')
      return true
    } catch (error) {
      console.error('Failed to delete local save:', error)
      return false
    }
  }

  // Auto-save functionality
  initializeAutoSave() {
    if (typeof window !== 'undefined') {
      this.autoSaveInterval = setInterval(() => {
        this.triggerAutoSave()
      }, AUTO_SAVE_INTERVAL)
    }
  }

  private triggerAutoSave() {
    // This would be called by the game context to auto-save
    const event = new CustomEvent('autoSave')
    window.dispatchEvent(event)
  }

  stopAutoSave() {
    if (this.autoSaveInterval) {
      clearInterval(this.autoSaveInterval)
      this.autoSaveInterval = null
    }
  }

  // Cloud Save Operations (integrated with CloudSaveContext)
  async saveToCloud(gameData: GameSave, userId: string, cloudSaveContext?: any): Promise<boolean> {
    try {
      if (!cloudSaveContext) {
        console.warn('Cloud save context not available')
        return false
      }

      const saveName = `Auto Save - ${new Date().toLocaleString()}`
      const saveId = await cloudSaveContext.saveToCloud(saveName, gameData, 'auto')

      return saveId !== null
    } catch (error) {
      console.error('Failed to save to cloud:', error)
      return false
    }
  }

  async loadFromCloud(saveId: string, cloudSaveContext?: any): Promise<GameSave | null> {
    try {
      if (!cloudSaveContext) {
        console.warn('Cloud save context not available')
        return null
      }

      const gameData = await cloudSaveContext.loadFromCloud(saveId)
      return gameData
    } catch (error) {
      console.error('Failed to load from cloud:', error)
      return null
    }
  }

  async syncWithCloud(localSave: GameSave, cloudSaveContext?: any): Promise<GameSave> {
    try {
      if (!cloudSaveContext || !cloudSaveContext.isAuthenticated) {
        console.log('Not authenticated, using local save')
        return localSave
      }

      // Get the most recent cloud save
      await cloudSaveContext.refreshCloudSaves()
      const cloudSaves = cloudSaveContext.cloudSaves

      if (!cloudSaves || cloudSaves.length === 0) {
        // No cloud save exists, upload local save
        console.log('No cloud saves found, uploading local save')
        await this.saveToCloud(localSave, cloudSaveContext.user?.id || '', cloudSaveContext)
        return localSave
      }

      // Get the most recent cloud save
      const mostRecentCloudSave = cloudSaves[0]
      const cloudSaveData = await cloudSaveContext.loadFromCloud(mostRecentCloudSave.id)

      if (!cloudSaveData) {
        console.log('Failed to load cloud save, using local save')
        return localSave
      }

      // Compare timestamps and merge
      if (cloudSaveData.timestamp > localSave.timestamp) {
        console.log('Cloud save is newer, using cloud data')
        return cloudSaveData
      } else {
        console.log('Local save is newer, uploading to cloud')
        await this.saveToCloud(localSave, cloudSaveContext.user?.id || '', cloudSaveContext)
        return localSave
      }
    } catch (error) {
      console.error('Failed to sync with cloud:', error)
      return localSave
    }
  }

  // Save migration for version compatibility
  private migrateSave(oldSave: any): GameSave | null {
    try {
      // Handle migration from older save versions
      const migratedSave: GameSave = {
        version: SAVE_VERSION,
        timestamp: oldSave.timestamp || Date.now(),
        player: {
          level: oldSave.player?.level || 1,
          experience: oldSave.player?.experience || 0,
          money: oldSave.player?.money || 100,
          skillPoints: oldSave.player?.skillPoints || 0,
          totalMoneyEarned: oldSave.player?.totalMoneyEarned || 0,
          totalOrdersCompleted: oldSave.player?.totalOrdersCompleted || 0,
          totalItemsBaked: oldSave.player?.totalItemsBaked || 0,
          unlockedRecipes: oldSave.player?.unlockedRecipes || ['chocolate_chip_cookies', 'vanilla_muffins'],
          automationUpgrades: oldSave.player?.automationUpgrades || []
        },
        equipment: oldSave.equipment || [],
        inventory: oldSave.inventory || [],
        achievements: oldSave.achievements || [],
        skills: oldSave.skills || [],
        automationSettings: oldSave.automationSettings || {},
        gameSettings: {
          language: oldSave.gameSettings?.language || 'en',
          soundEnabled: oldSave.gameSettings?.soundEnabled ?? true,
          musicEnabled: oldSave.gameSettings?.musicEnabled ?? true,
          notificationsEnabled: oldSave.gameSettings?.notificationsEnabled ?? true,
          autoSaveEnabled: oldSave.gameSettings?.autoSaveEnabled ?? true
        },
        bakeries: oldSave.bakeries || [],
        currentBakeryId: oldSave.currentBakeryId || 'main'
      }

      console.log('Save migrated successfully')
      return migratedSave
    } catch (error) {
      console.error('Failed to migrate save:', error)
      return null
    }
  }

  // Utility functions
  private getDeviceId(): string {
    let deviceId = localStorage.getItem('deviceId')
    if (!deviceId) {
      deviceId = 'device_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9)
      localStorage.setItem('deviceId', deviceId)
    }
    return deviceId
  }

  exportSave(gameData: GameSave): string {
    return JSON.stringify(gameData, null, 2)
  }

  importSave(saveString: string): GameSave | null {
    try {
      const save = JSON.parse(saveString)
      return this.migrateSave(save)
    } catch (error) {
      console.error('Failed to import save:', error)
      return null
    }
  }

  // Backup management
  createBackup(gameData: GameSave): boolean {
    try {
      const backupKey = `${LOCAL_STORAGE_KEY}_backup_${Date.now()}`
      localStorage.setItem(backupKey, JSON.stringify(gameData))
      
      // Keep only the 5 most recent backups
      this.cleanupOldBackups()
      return true
    } catch (error) {
      console.error('Failed to create backup:', error)
      return false
    }
  }

  private cleanupOldBackups() {
    const backupKeys = Object.keys(localStorage)
      .filter(key => key.startsWith(`${LOCAL_STORAGE_KEY}_backup_`))
      .sort()

    while (backupKeys.length > 5) {
      const oldestKey = backupKeys.shift()
      if (oldestKey) {
        localStorage.removeItem(oldestKey)
      }
    }
  }

  getBackups(): Array<{ key: string; timestamp: number; data: GameSave }> {
    const backups: Array<{ key: string; timestamp: number; data: GameSave }> = []
    
    Object.keys(localStorage).forEach(key => {
      if (key.startsWith(`${LOCAL_STORAGE_KEY}_backup_`)) {
        try {
          const data = JSON.parse(localStorage.getItem(key) || '{}')
          const timestamp = parseInt(key.split('_').pop() || '0')
          backups.push({ key, timestamp, data })
        } catch (error) {
          console.error('Failed to parse backup:', error)
        }
      }
    })

    return backups.sort((a, b) => b.timestamp - a.timestamp)
  }
}

export const saveSystem = new SaveSystem()
