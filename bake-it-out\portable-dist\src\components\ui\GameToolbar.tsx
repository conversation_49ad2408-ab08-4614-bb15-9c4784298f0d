'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { useLanguage } from '@/contexts/LanguageContext'
import { DashboardButton } from '@/components/dashboard/DashboardAccess'

interface Player {
  name: string
  level: number
  experience: number
  maxExperience: number
  money: number
  skillPoints: number
}

interface GameToolbarProps {
  player: Player
  onOpenMenu: () => void
  onOpenAchievements: () => void
  onOpenSkills: () => void
  onOpenBakeries: () => void
  onOpenSettings: () => void
}

export function GameToolbar({ 
  player, 
  onOpenMenu, 
  onOpenAchievements, 
  onOpenSkills, 
  onOpenBakeries,
  onOpenSettings 
}: GameToolbarProps) {
  const { t } = useLanguage()
  const [showQuickMenu, setShowQuickMenu] = useState(false)

  const experiencePercentage = (player.experience / player.maxExperience) * 100

  return (
    <div className="bg-white shadow-lg border-b border-gray-200 relative">
      <div className="px-6 py-4">
        <div className="flex items-center justify-between">
          {/* Left Side - Game Title & Menu */}
          <div className="flex items-center space-x-4">
            <Button
              variant="secondary"
              size="sm"
              className="bg-orange-100 hover:bg-orange-200 text-orange-800"
              onClick={onOpenMenu}
            >
              ☰ {t('toolbar.menu', 'Menu')}
            </Button>
            
            <div className="hidden md:block">
              <h1 className="text-xl font-bold text-orange-800">🥖 {t('game.title', 'Bake It Out')}</h1>
            </div>
          </div>

          {/* Center - Player Stats */}
          <div className="flex items-center space-x-4">
            {/* Player Name & Level */}
            <div className="text-center">
              <div className="text-sm font-medium text-gray-800">{player.name}</div>
              <div className="text-xs text-gray-500">{t('game.level', 'Level')} {player.level}</div>
            </div>

            {/* Experience Bar */}
            <div className="hidden sm:block">
              <div className="w-32 bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${experiencePercentage}%` }}
                />
              </div>
              <div className="text-xs text-center text-gray-500 mt-1">
                {player.experience}/{player.maxExperience} {t('game.xp', 'XP')}
              </div>
            </div>

            {/* Money */}
            <div className="bg-green-100 px-3 py-1 rounded-full">
              <span className="text-green-800 font-medium">${player.money}</span>
            </div>

            {/* Skill Points */}
            {player.skillPoints > 0 && (
              <div className="bg-yellow-100 px-3 py-1 rounded-full relative">
                <span className="text-yellow-800 font-medium">⭐ {player.skillPoints}</span>
                <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse" />
              </div>
            )}
          </div>

          {/* Right Side - Quick Actions */}
          <div className="flex items-center space-x-2">
            {/* Quick Menu Toggle */}
            <Button
              variant="secondary"
              size="sm"
              className="md:hidden"
              onClick={() => setShowQuickMenu(!showQuickMenu)}
            >
              ⚡
            </Button>

            {/* Desktop Quick Actions */}
            <div className="hidden md:flex items-center space-x-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={onOpenBakeries}
              >
                🏪
              </Button>
              
              <Button
                variant="secondary"
                size="sm"
                onClick={onOpenAchievements}
                className="relative"
              >
                🏆
              </Button>
              
              <Button
                variant="secondary"
                size="sm"
                onClick={onOpenSkills}
                className={player.skillPoints > 0 ? 'bg-yellow-100 hover:bg-yellow-200' : ''}
              >
                🌟
                {player.skillPoints > 0 && (
                  <span className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse" />
                )}
              </Button>
              
              <Button
                variant="secondary"
                size="sm"
                onClick={onOpenSettings}
              >
                ⚙️
              </Button>

              <DashboardButton />
            </div>
          </div>
        </div>

        {/* Mobile Quick Menu */}
        {showQuickMenu && (
          <div className="md:hidden mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-4 gap-2">
              <Button
                variant="secondary"
                size="sm"
                className="flex flex-col items-center py-3"
                onClick={() => {
                  onOpenBakeries()
                  setShowQuickMenu(false)
                }}
              >
                <span className="text-lg">🏪</span>
                <span className="text-xs">{t('toolbar.bakeries', 'Bakeries')}</span>
              </Button>
              
              <Button
                variant="secondary"
                size="sm"
                className="flex flex-col items-center py-3"
                onClick={() => {
                  onOpenAchievements()
                  setShowQuickMenu(false)
                }}
              >
                <span className="text-lg">🏆</span>
                <span className="text-xs">{t('toolbar.achievements', 'Achievements')}</span>
              </Button>
              
              <Button
                variant="secondary"
                size="sm"
                className={`flex flex-col items-center py-3 relative ${
                  player.skillPoints > 0 ? 'bg-yellow-100 hover:bg-yellow-200' : ''
                }`}
                onClick={() => {
                  onOpenSkills()
                  setShowQuickMenu(false)
                }}
              >
                <span className="text-lg">🌟</span>
                <span className="text-xs">{t('toolbar.skills', 'Skills')}</span>
                {player.skillPoints > 0 && (
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse" />
                )}
              </Button>
              
              <Button
                variant="secondary"
                size="sm"
                className="flex flex-col items-center py-3"
                onClick={() => {
                  onOpenSettings()
                  setShowQuickMenu(false)
                }}
              >
                <span className="text-lg">⚙️</span>
                <span className="text-xs">{t('toolbar.settings', 'Settings')}</span>
              </Button>
            </div>

            {/* Mobile Experience Bar */}
            <div className="mt-3">
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${experiencePercentage}%` }}
                />
              </div>
              <div className="text-xs text-center text-gray-500 mt-1">
                {player.experience}/{player.maxExperience} XP
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
