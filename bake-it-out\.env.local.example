# Firebase Configuration for Cloud Saves
NEXT_PUBLIC_FIREBASE_API_KEY=AIzaSyCGKM4l0nn58idkFMrRqMwsXv88hJjXxco
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=bake-it-out.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=bake-it-out
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=bake-it-out.firebasestorage.app
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=1042256325512
NEXT_PUBLIC_FIREBASE_APP_ID=1:1042256325512:web:63b126b736fb4da53f683d
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=G-3VCE8QCJLN

# Optional: Firebase Emulator (for development)
NEXT_PUBLIC_USE_FIREBASE_EMULATOR=false

# Socket.io Configuration (for multiplayer)
NEXT_PUBLIC_SOCKET_URL=http://localhost:3001

# Game Configuration
NEXT_PUBLIC_GAME_VERSION=1.1.0
NEXT_PUBLIC_API_URL=http://localhost:3001

# Development Settings
NODE_ENV=development
NEXT_PUBLIC_DEBUG_MODE=false

# Cloud Save Settings
NEXT_PUBLIC_ENABLE_CLOUD_SAVES=true
NEXT_PUBLIC_AUTO_SYNC_INTERVAL=30000
NEXT_PUBLIC_MAX_CLOUD_SAVES=10
