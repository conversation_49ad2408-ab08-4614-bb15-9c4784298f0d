# Supabase Configuration for Cloud Saves
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Optional: Supabase Service Role Key (for server-side operations)
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here

# Socket.io Configuration (for multiplayer)
NEXT_PUBLIC_SOCKET_URL=http://localhost:3001

# Game Configuration
NEXT_PUBLIC_GAME_VERSION=1.1.0
NEXT_PUBLIC_API_URL=http://localhost:3001

# Development Settings
NODE_ENV=development
NEXT_PUBLIC_DEBUG_MODE=false

# Cloud Save Settings
NEXT_PUBLIC_ENABLE_CLOUD_SAVES=true
NEXT_PUBLIC_AUTO_SYNC_INTERVAL=30000
NEXT_PUBLIC_MAX_CLOUD_SAVES=10
