{"name": "bake-it-out-server", "version": "1.0.0", "description": "Cloud save server for Bake It Out game", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "build": "echo 'No build step required'", "setup-easy": "node scripts/easy-setup.js", "quick-setup": "node scripts/quick-setup.js", "validate": "node scripts/validate-setup.js", "test": "jest", "docker:build": "docker build -t bake-it-out-server .", "docker:run": "docker run -p 3001:3001 bake-it-out-server"}, "keywords": ["game", "cloud-save", "bakery", "multiplayer"], "author": "Bake It Out Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "express-rate-limit": "^7.1.5", "express-validator": "^7.0.1", "compression": "^1.7.4", "morgan": "^1.10.0", "dotenv": "^16.3.1", "uuid": "^9.0.1", "multer": "^1.4.5-lts.1", "socket.io": "^4.7.4", "ejs": "^3.1.9", "express-session": "^1.17.3", "connect-mongo": "^5.1.0", "chart.js": "^4.4.0", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}