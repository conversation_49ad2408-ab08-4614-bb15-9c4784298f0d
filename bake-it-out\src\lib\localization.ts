import { Recipe, Ingredient } from './gameLogic'

// Helper function to get localized recipe name
export function getLocalizedRecipeName(recipe: Recipe, t: (key: string, fallback?: string) => string): string {
  // If the recipe name is a translation key, use it
  if (recipe.name.startsWith('recipe.')) {
    return t(recipe.name, recipe.name.replace('recipe.', '').replace(/_/g, ' '))
  }
  // Otherwise, return the name as-is (for backward compatibility)
  return recipe.name
}

// Helper function to get localized ingredient name
export function getLocalizedIngredientName(ingredient: Ingredient, t: (key: string, fallback?: string) => string): string {
  // If the ingredient name is a translation key, use it
  if (ingredient.name.startsWith('ingredient.')) {
    return t(ingredient.name, ingredient.name.replace('ingredient.', '').replace(/_/g, ' '))
  }
  // Otherwise, return the name as-is (for backward compatibility)
  return ingredient.name
}

// Helper function to get localized category name
export function getLocalizedCategoryName(category: string, t: (key: string, fallback?: string) => string): string {
  // If the category is a translation key, use it
  if (category.startsWith('category.')) {
    return t(category, category.replace('category.', '').replace(/_/g, ' '))
  }
  // Otherwise, return the category as-is (for backward compatibility)
  return category
}

// Helper function to get localized equipment name
export function getLocalizedEquipmentName(equipment: string, t: (key: string, fallback?: string) => string): string {
  // If the equipment name is a translation key, use it
  if (equipment.startsWith('equipment.')) {
    return t(equipment, equipment.replace('equipment.', '').replace(/_/g, ' '))
  }
  // Otherwise, return the name as-is (for backward compatibility)
  return equipment
}

// Helper to format ingredient with quantity
export function formatIngredientWithQuantity(
  ingredient: Ingredient, 
  t: (key: string, fallback?: string) => string
): string {
  const name = getLocalizedIngredientName(ingredient, t)
  return `${name} (${ingredient.quantity})`
}

// Helper to format recipe ingredients list
export function formatRecipeIngredients(
  recipe: Recipe, 
  t: (key: string, fallback?: string) => string
): string {
  return recipe.ingredients
    .map(ingredient => formatIngredientWithQuantity(ingredient, t))
    .join(', ')
}
