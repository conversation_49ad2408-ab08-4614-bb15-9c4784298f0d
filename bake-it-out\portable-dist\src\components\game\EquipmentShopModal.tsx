'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/Button'
import { useGame } from '@/contexts/GameContext'

interface EquipmentItem {
  id: string
  name: string
  type: string
  description: string
  cost: number
  unlockLevel: number
  automationLevel: number
  efficiency: number
  icon: string
  category: 'basic' | 'automated' | 'advanced'
}

const EQUIPMENT_SHOP: EquipmentItem[] = [
  {
    id: 'professional_oven',
    name: 'Professional Oven',
    type: 'oven',
    description: 'Faster and more efficient than basic oven',
    cost: 500,
    unlockLevel: 3,
    automationLevel: 0,
    efficiency: 1.3,
    icon: '🔥',
    category: 'basic'
  },
  {
    id: 'auto_oven',
    name: 'Automated Oven',
    type: 'auto_oven',
    description: 'Fully automated oven that can run without supervision',
    cost: 1500,
    unlockLevel: 5,
    automationLevel: 2,
    efficiency: 1.5,
    icon: '🤖',
    category: 'automated'
  },
  {
    id: 'industrial_mixer',
    name: 'Industrial Mixer',
    type: 'mixer',
    description: 'High-capacity mixer for large batches',
    cost: 750,
    unlockLevel: 4,
    automationLevel: 0,
    efficiency: 1.4,
    icon: '🥄',
    category: 'basic'
  },
  {
    id: 'auto_mixer',
    name: 'Automated Mixer',
    type: 'auto_mixer',
    description: 'Self-operating mixer with ingredient dispensing',
    cost: 2000,
    unlockLevel: 6,
    automationLevel: 2,
    efficiency: 1.6,
    icon: '🤖',
    category: 'automated'
  },
  {
    id: 'conveyor_belt_basic',
    name: 'Basic Conveyor Belt',
    type: 'conveyor',
    description: 'Moves items between equipment automatically',
    cost: 1000,
    unlockLevel: 7,
    automationLevel: 1,
    efficiency: 1.2,
    icon: '🔄',
    category: 'automated'
  },
  {
    id: 'smart_conveyor',
    name: 'Smart Conveyor System',
    type: 'conveyor',
    description: 'Intelligent conveyor with sorting and routing',
    cost: 3000,
    unlockLevel: 10,
    automationLevel: 3,
    efficiency: 1.8,
    icon: '🧠',
    category: 'advanced'
  },
  {
    id: 'master_oven',
    name: 'Master Oven',
    type: 'oven',
    description: 'The ultimate baking machine with AI assistance',
    cost: 5000,
    unlockLevel: 12,
    automationLevel: 3,
    efficiency: 2.0,
    icon: '👑',
    category: 'advanced'
  }
]

interface EquipmentShopModalProps {
  isOpen: boolean
  onClose: () => void
  onShowSuccess?: (title: string, message: string) => void
}

export function EquipmentShopModal({ isOpen, onClose, onShowSuccess }: EquipmentShopModalProps) {
  const { player, equipment, spendMoney, addEquipment } = useGame()
  const [selectedCategory, setSelectedCategory] = useState<string>('all')

  if (!isOpen) return null

  const categories = [
    { id: 'all', name: 'All', icon: '🏪' },
    { id: 'basic', name: 'Basic', icon: '🔧' },
    { id: 'automated', name: 'Automated', icon: '🤖' },
    { id: 'advanced', name: 'Advanced', icon: '⚡' }
  ]

  const availableEquipment = EQUIPMENT_SHOP.filter(item => 
    player.level >= item.unlockLevel &&
    (selectedCategory === 'all' || item.category === selectedCategory)
  )

  const purchaseEquipment = (item: EquipmentItem) => {
    if (player.money < item.cost) return

    if (spendMoney(item.cost)) {
      const newEquipment = {
        name: item.name,
        type: item.type as 'oven' | 'mixer' | 'counter' | 'display' | 'storage',
        isActive: false,
        level: 1,
        efficiency: item.efficiency,
        automationLevel: item.automationLevel
      }

      addEquipment(newEquipment)
      if (onShowSuccess) {
        onShowSuccess('Equipment Purchased!', `You bought ${item.name}!`)
      }
    }
  }

  const getAutomationBadge = (level: number) => {
    if (level === 0) return null
    return (
      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
        🤖 Auto Level {level}
      </span>
    )
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'basic': return 'border-gray-300 bg-gray-50'
      case 'automated': return 'border-blue-300 bg-blue-50'
      case 'advanced': return 'border-purple-300 bg-purple-50'
      default: return 'border-gray-300 bg-white'
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold text-orange-800">🏪 Equipment Shop</h2>
              <p className="text-gray-600">
                Upgrade your bakery with professional equipment
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="bg-green-100 px-3 py-1 rounded-full">
                <span className="text-green-800 font-medium">${player.money}</span>
              </div>
              <Button variant="secondary" onClick={onClose}>
                ✕ Close
              </Button>
            </div>
          </div>
        </div>

        <div className="p-6">
          {/* Category Filter */}
          <div className="flex flex-wrap gap-2 mb-6">
            {categories.map(category => (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? 'primary' : 'secondary'}
                size="sm"
                onClick={() => setSelectedCategory(category.id)}
              >
                {category.icon} {category.name}
              </Button>
            ))}
          </div>

          {/* Equipment Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto">
            {availableEquipment.map(item => (
              <div
                key={item.id}
                className={`p-4 rounded-lg border-2 ${getCategoryColor(item.category)}`}
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    <span className="text-2xl">{item.icon}</span>
                    <div>
                      <h3 className="font-semibold text-gray-800">{item.name}</h3>
                      <p className="text-xs text-gray-500 uppercase tracking-wide">
                        {item.category}
                      </p>
                    </div>
                  </div>
                  <span className="text-lg font-bold text-green-600">${item.cost}</span>
                </div>

                <p className="text-sm text-gray-600 mb-3">{item.description}</p>

                <div className="space-y-2 mb-4">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Efficiency:</span>
                    <span className="font-medium">{item.efficiency}x</span>
                  </div>
                  {item.automationLevel > 0 && (
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-600">Automation:</span>
                      {getAutomationBadge(item.automationLevel)}
                    </div>
                  )}
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Unlock Level:</span>
                    <span className="font-medium">{item.unlockLevel}</span>
                  </div>
                </div>

                <Button
                  variant={player.money >= item.cost ? 'success' : 'secondary'}
                  size="sm"
                  className="w-full"
                  disabled={player.money < item.cost}
                  onClick={() => purchaseEquipment(item)}
                >
                  {player.money >= item.cost ? '💰 Purchase' : '💸 Too Expensive'}
                </Button>
              </div>
            ))}
          </div>

          {availableEquipment.length === 0 && (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">🏪</div>
              <p>No equipment available in this category.</p>
              <p className="text-sm">Level up to unlock more equipment!</p>
            </div>
          )}

          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <h3 className="font-medium text-blue-800 mb-2">💡 Equipment Tips</h3>
            <ul className="text-sm text-blue-700 space-y-1">
              <li>• Automated equipment can run without your supervision</li>
              <li>• Higher efficiency means faster production and better quality</li>
              <li>• Conveyor belts connect equipment for seamless workflow</li>
              <li>• Advanced equipment unlocks at higher levels</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
