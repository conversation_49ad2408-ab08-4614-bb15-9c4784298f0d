'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/Button'
import { useLanguage } from '@/contexts/LanguageContext'
import { useMultiplayer } from '@/contexts/MultiplayerContext'
import { Equipment } from '@/components/game/Equipment'
import { Order } from '@/components/game/Order'

interface MultiplayerGameProps {
  isOpen: boolean
  onClose: () => void
}

export function MultiplayerGame({ isOpen, onClose }: MultiplayerGameProps) {
  const { t } = useLanguage()
  const {
    currentRoom,
    currentPlayer,
    players,
    gameState,
    sharedGameState,
    sendPlayerAction,
    leaveRoom
  } = useMultiplayer()

  const [selectedTab, setSelectedTab] = useState<'game' | 'players' | 'chat'>('game')

  if (!isOpen || !currentRoom || gameState !== 'playing') return null

  const handleEquipmentClick = (equipmentId: string, equipmentName: string) => {
    sendPlayerAction({
      type: 'use_equipment',
      data: {
        equipmentId,
        equipmentName,
        playerId: currentPlayer?.id
      }
    })
  }

  const handleOrderAction = (orderId: string, action: 'accept' | 'decline' | 'complete') => {
    sendPlayerAction({
      type: 'order_action',
      data: {
        orderId,
        action,
        playerId: currentPlayer?.id
      }
    })
  }

  const handleLeaveGame = () => {
    leaveRoom()
    onClose()
  }

  const tabs = [
    { id: 'game', name: t('multiplayer.game.tabs.game'), icon: '🎮' },
    { id: 'players', name: t('multiplayer.game.tabs.players'), icon: '👥' },
    { id: 'chat', name: t('multiplayer.game.tabs.chat'), icon: '💬' }
  ]

  // Mock shared game data (in real implementation, this would come from sharedGameState)
  const sharedEquipment = [
    { id: 'oven1', name: 'Shared Oven', type: 'oven' as const, isActive: false, level: 1, efficiency: 1.0, automationLevel: 0 },
    { id: 'mixer1', name: 'Shared Mixer', type: 'mixer' as const, isActive: false, level: 1, efficiency: 1.0, automationLevel: 0 },
    { id: 'counter1', name: 'Shared Counter', type: 'counter' as const, isActive: false, level: 1, efficiency: 1.0, automationLevel: 0 }
  ]

  const sharedOrders = [
    {
      id: '1',
      customerName: 'Shared Customer',
      items: ['Chocolate Chip Cookies'],
      timeLimit: 300,
      reward: 50,
      status: 'pending' as const,
      difficulty: 1
    }
  ]

  const sharedInventory = [
    { name: 'Flour', quantity: 20, cost: 2 },
    { name: 'Sugar', quantity: 15, cost: 3 },
    { name: 'Eggs', quantity: 12, cost: 4 }
  ]

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-2xl font-bold text-orange-800">
                {t('multiplayer.game.title', { roomName: currentRoom.name })}
              </h2>
              <p className="text-gray-600">
                {t('multiplayer.game.mode', { mode: currentRoom.mode })} • {t('multiplayer.game.playersCount', { count: players.length.toString() })}
              </p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="bg-green-100 px-3 py-1 rounded-full">
                <span className="text-green-800 text-sm">{t('multiplayer.game.playing')}</span>
              </div>
              <Button variant="secondary" onClick={handleLeaveGame}>
                {t('multiplayer.game.leaveGame')}
              </Button>
            </div>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <div className="flex space-x-0">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setSelectedTab(tab.id as any)}
                className={`px-6 py-3 font-medium text-sm border-b-2 transition-colors ${
                  selectedTab === tab.id
                    ? 'border-orange-500 text-orange-600 bg-orange-50'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50'
                }`}
              >
                {tab.icon} {tab.name}
              </button>
            ))}
          </div>
        </div>

        <div className="p-6 max-h-[70vh] overflow-y-auto">
          {/* Game Tab */}
          {selectedTab === 'game' && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Shared Kitchen */}
              <div className="lg:col-span-2">
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h3 className="text-xl font-semibold text-orange-800 mb-4">
                    {t('multiplayer.sharedKitchen')}
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    {sharedEquipment.map(equipment => (
                      <Equipment
                        key={equipment.id}
                        equipment={equipment}
                        onClick={handleEquipmentClick}
                      />
                    ))}
                  </div>
                </div>

                {/* Shared Orders */}
                <div className="bg-white rounded-lg shadow-md p-6 mt-6">
                  <h3 className="text-xl font-semibold text-orange-800 mb-4">
                    {t('multiplayer.sharedOrders')}
                  </h3>
                  <div className="space-y-4">
                    {sharedOrders.map(order => (
                      <Order
                        key={order.id}
                        order={order}
                        onAccept={(id) => handleOrderAction(id, 'accept')}
                        onDecline={(id) => handleOrderAction(id, 'decline')}
                        onComplete={(id) => handleOrderAction(id, 'complete')}
                      />
                    ))}
                  </div>
                </div>
              </div>

              {/* Shared Resources */}
              <div className="space-y-6">
                {/* Shared Inventory */}
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h3 className="text-xl font-semibold text-orange-800 mb-4">
                    {t('multiplayer.sharedInventory')}
                  </h3>
                  <div className="space-y-3">
                    {sharedInventory.map((ingredient, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <div>
                          <div className="font-medium text-gray-800">{ingredient.name}</div>
                          <div className="text-sm text-gray-600">Qty: {ingredient.quantity}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Game Stats */}
                <div className="bg-white rounded-lg shadow-md p-6">
                  <h3 className="text-xl font-semibold text-orange-800 mb-4">
                    {t('multiplayer.teamStats')}
                  </h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>{t('multiplayer.ordersCompleted')}</span>
                      <span className="font-medium">0</span>
                    </div>
                    <div className="flex justify-between">
                      <span>{t('multiplayer.totalRevenue')}</span>
                      <span className="font-medium">$0</span>
                    </div>
                    <div className="flex justify-between">
                      <span>{t('multiplayer.teamExperience')}</span>
                      <span className="font-medium">0 XP</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Players Tab */}
          {selectedTab === 'players' && (
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-orange-800">
                {t('multiplayer.players', { count: players.length.toString() })}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {players.map(player => (
                  <div
                    key={player.id}
                    className={`p-4 rounded-lg border-2 ${
                      player.id === currentPlayer?.id
                        ? 'border-orange-400 bg-orange-50'
                        : 'border-gray-300 bg-white'
                    }`}
                  >
                    <div className="flex items-center space-x-3 mb-3">
                      <span className="text-3xl">{player.avatar}</span>
                      <div>
                        <h4 className="font-semibold text-gray-800">
                          {player.name}
                          {player.id === currentPlayer?.id && <span className="ml-2 text-sm text-orange-600">{t('multiplayer.you')}</span>}
                        </h4>
                        <div className="text-sm text-gray-600">
                          {t('multiplayer.level', { level: player.level.toString() })}
                          {player.isHost && <span className="ml-2 bg-yellow-100 text-yellow-800 px-2 py-1 rounded text-xs">{t('multiplayer.host')}</span>}
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>{t('multiplayer.status')}</span>
                        <span className="text-green-600">{t('multiplayer.online')}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>{t('multiplayer.contribution')}</span>
                        <span className="font-medium">0 orders</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Chat Tab */}
          {selectedTab === 'chat' && (
            <div className="space-y-4">
              <h3 className="text-xl font-semibold text-orange-800">{t('multiplayer.teamChat')}</h3>
              <div className="bg-gray-50 p-4 rounded-lg h-64 overflow-y-auto">
                <div className="text-sm text-gray-500 text-center">
                  {t('multiplayer.chatPlaceholder')}
                </div>
              </div>
              <div className="flex space-x-2">
                <input
                  type="text"
                  placeholder={t('multiplayer.typeMessage')}
                  className="flex-1 p-3 border rounded-lg"
                />
                <Button>{t('common.send')}</Button>
              </div>
            </div>
          )}
        </div>

        {/* Game Mode Info */}
        <div className="p-4 bg-blue-50 border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-blue-700">
              {currentRoom.mode === 'cooperative' ?
                t('multiplayer.mode.cooperative.description') :
                t('multiplayer.mode.competitive.description')
              }
            </div>
            <div className="text-sm text-blue-600">
              {t('multiplayer.gameTime', { time: '00:00' })}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
