"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[511],{1511:(e,s,a)=>{a.r(s),a.d(s,{default:()=>A});var t=a(5155),l=a(2115),i=a(9283),r=a(2517),n=a(3741),c=a(9419),o=a(2163),d=a(4983);function m(e){let{isOpen:s,onClose:a}=e,{player:c,inventory:o}=(0,r.I)(),{t:m}=(0,i.o)(),[x,u]=(0,l.useState)("all");if(!s)return null;let h=(0,d.x0)(c.level),p="all"===x?h:h.filter(e=>e.category===x),v=e=>e.ingredients.every(e=>{let s=o.find(s=>s.name===e.name);return s&&s.quantity>=e.quantity}),g=[{id:"all",name:m("recipes.all"),icon:"\uD83C\uDF7D️"},{id:"cookies",name:m("recipes.cookies"),icon:"\uD83C\uDF6A"},{id:"cakes",name:m("recipes.cakes"),icon:"\uD83E\uDDC1"},{id:"bread",name:m("recipes.bread"),icon:"\uD83C\uDF5E"},{id:"pastries",name:m("recipes.pastries"),icon:"\uD83E\uDD50"}];return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:m("modal.recipes.title")}),(0,t.jsx)(n.$,{variant:"secondary",onClick:a,children:m("game.close")})]})}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:g.map(e=>(0,t.jsxs)(n.$,{variant:x===e.id?"primary":"secondary",size:"sm",onClick:()=>u(e.id),children:[e.icon," ",e.name]},e.id))}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto",children:p.map(e=>{let s;return(0,t.jsxs)("div",{className:"p-4 rounded-lg border-2 ".concat(v(e)?"border-green-300 bg-green-50":"border-gray-300 bg-gray-50"),children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name}),(0,t.jsxs)("span",{className:"text-sm text-green-600",children:["$",e.basePrice]})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mb-2",children:[(s=e.difficulty,"⭐".repeat(s)+"☆".repeat(5-s))," • ⏱️ ",(e=>{let s=Math.floor(e/60);return"".concat(s,":").concat((e%60).toString().padStart(2,"0"))})(e.bakingTime)]}),(0,t.jsxs)("div",{className:"space-y-1 mb-3",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-700",children:m("recipes.ingredients")}),e.ingredients.map((e,s)=>{let a=o.find(s=>s.name===e.name),l=a&&a.quantity>=e.quantity;return(0,t.jsxs)("div",{className:"text-xs flex justify-between ".concat(l?"text-green-600":"text-red-600"),children:[(0,t.jsx)("span",{children:e.name}),(0,t.jsxs)("span",{children:[e.quantity,a&&(0,t.jsxs)("span",{className:"ml-1",children:["(",a.quantity," available)"]})]})]},s)})]}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:m("recipes.unlockLevel",{level:e.unlockLevel.toString()})}),v(e)&&(0,t.jsx)("div",{className:"mt-2",children:(0,t.jsx)(n.$,{size:"sm",variant:"success",className:"w-full",children:m("recipes.canCraft")})})]},e.id)})}),0===p.length&&(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)("div",{className:"text-4xl mb-2",children:"\uD83D\uDCDD"}),(0,t.jsx)("p",{children:m("recipes.noRecipes")}),(0,t.jsx)("p",{className:"text-sm",children:m("recipes.levelUpToUnlock")})]})]})]})})}function x(e){let{isOpen:s,onClose:a}=e,{player:c,inventory:o,spendMoney:d,addIngredient:m}=(0,r.I)(),{t:x}=(0,i.o)(),[u,h]=(0,l.useState)({});if(!s)return null;let p=(e,s)=>{h(a=>({...a,[e]:Math.max(0,s)}))},v=(e,s)=>s*(u[e]||1),g=(e,s)=>c.money>=v(e,s);return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:x("modal.shop.title")}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,t.jsx)("span",{className:"text-green-800 font-medium",children:x("ui.money",{amount:c.money.toString()})})}),(0,t.jsx)(n.$,{variant:"secondary",onClick:a,children:x("game.close")})]})]})}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("div",{className:"space-y-4 max-h-[60vh] overflow-y-auto",children:o.map(e=>{let s=u[e.name]||1,a=v(e.name,e.cost),l=g(e.name,e.cost);return(0,t.jsxs)("div",{className:"flex items-center justify-between p-4 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"text-2xl",children:e.icon}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-medium text-gray-800",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:x("shop.currentStock",{quantity:e.quantity.toString()})}),(0,t.jsx)("p",{className:"text-sm text-green-600",children:x("inventory.cost",{cost:e.cost.toString()})})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(n.$,{size:"sm",variant:"secondary",onClick:()=>p(e.name,s-1),disabled:s<=1,children:"-"}),(0,t.jsx)("span",{className:"w-12 text-center font-mono",children:s}),(0,t.jsx)(n.$,{size:"sm",variant:"secondary",onClick:()=>p(e.name,s+1),disabled:!g(e.name,e.cost)&&s>=1,children:"+"})]}),(0,t.jsxs)("div",{className:"text-right min-w-[80px]",children:[(0,t.jsxs)("div",{className:"font-medium ".concat(l?"text-green-600":"text-red-600"),children:["$",a]}),(0,t.jsx)(n.$,{size:"sm",variant:l?"success":"secondary",onClick:()=>((e,s)=>{let a=u[e]||1;d(s*a)&&(m(e,a),h(s=>({...s,[e]:0})))})(e.name,e.cost),disabled:!l,className:"mt-1",children:l?x("shop.buy"):x("shop.tooExpensive")})]})]})]},e.name)})}),(0,t.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:x("shop.tips.title")}),(0,t.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,t.jsx)("li",{children:x("shop.tips.bulk")}),(0,t.jsx)("li",{children:x("shop.tips.stock")}),(0,t.jsx)("li",{children:x("shop.tips.rare")}),(0,t.jsx)("li",{children:x("shop.tips.prices")})]})]})]})]})})}function u(e){let{isOpen:s,onClose:a,equipmentId:i,equipmentName:c}=e,{player:o,inventory:m,updateEquipment:x,useIngredient:u}=(0,r.I)(),[h,p]=(0,l.useState)(null);if(!s)return null;let v=(0,d.x0)(o.level).filter(e=>(0,d.hF)(e,m)),g=e=>{let s=Math.floor(e/60);return"".concat(s,":").concat((e%60).toString().padStart(2,"0"))},y=e=>"⭐".repeat(e)+"☆".repeat(5-e);return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-orange-800",children:["\uD83D\uDD25 ",c," - Select Recipe"]}),(0,t.jsx)(n.$,{variant:"secondary",onClick:a,children:"✕ Close"})]})}),(0,t.jsxs)("div",{className:"p-6",children:[0===v.length?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"text-4xl mb-4",children:"\uD83D\uDE14"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-800 mb-2",children:"No recipes available"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"You don't have enough ingredients to craft any recipes."}),(0,t.jsx)(n.$,{variant:"primary",onClick:a,children:"Buy Ingredients"})]}):(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[60vh] overflow-y-auto",children:v.map(e=>(0,t.jsxs)("div",{className:"p-4 rounded-lg border-2 cursor-pointer transition-all ".concat((null==h?void 0:h.id)===e.id?"border-orange-400 bg-orange-50":"border-gray-300 bg-gray-50 hover:border-orange-300"),onClick:()=>p(e),children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-2",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:(e=>{switch(e){case"cookies":return"\uD83C\uDF6A";case"cakes":return"\uD83E\uDDC1";case"bread":return"\uD83C\uDF5E";case"pastries":return"\uD83E\uDD50";default:return"\uD83C\uDF7D️"}})(e.category)}),(0,t.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name})]}),(0,t.jsxs)("span",{className:"text-sm text-green-600",children:["$",e.basePrice]})]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 mb-2",children:[y(e.difficulty)," • ⏱️ ",g(e.bakingTime)]}),(0,t.jsxs)("div",{className:"space-y-1 mb-3",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-700",children:"Ingredients:"}),e.ingredients.map((e,s)=>{let a=m.find(s=>s.name===e.name);return(0,t.jsxs)("div",{className:"text-xs flex justify-between text-green-600",children:[(0,t.jsx)("span",{children:e.name}),(0,t.jsxs)("span",{children:[e.quantity,(0,t.jsxs)("span",{className:"ml-1",children:["(",(null==a?void 0:a.quantity)||0," available)"]})]})]},s)})]}),(null==h?void 0:h.id)===e.id&&(0,t.jsx)(n.$,{variant:"success",size:"sm",className:"w-full",onClick:()=>{var s;return s=e,void((0,d.hF)(s,m)&&s.ingredients.every(e=>{let s=m.find(s=>s.name===e.name);return s&&s.quantity>=e.quantity})&&(s.ingredients.forEach(e=>{u(e.name,e.quantity)}),x(i,{isActive:!0,timeRemaining:s.bakingTime,currentRecipe:s.name}),a()))},children:"\uD83D\uDD25 Start Baking"})]},e.id))}),h&&v.length>0&&(0,t.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,t.jsxs)("h3",{className:"font-medium text-blue-800 mb-2",children:["\uD83D\uDCCB Baking Instructions for ",h.name]}),(0,t.jsxs)("div",{className:"text-sm text-blue-700 space-y-1",children:[(0,t.jsxs)("p",{children:["• Baking time: ",g(h.bakingTime)]}),(0,t.jsxs)("p",{children:["• Difficulty: ",y(h.difficulty)]}),(0,t.jsxs)("p",{children:["• Expected reward: $",h.basePrice]}),(0,t.jsx)("p",{children:"• Make sure you have all ingredients before starting!"})]})]})]})]})})}function h(e){let{notifications:s,onRemove:a}=e;return((0,l.useEffect)(()=>{s.forEach(e=>{if(e.duration){let s=setTimeout(()=>{a(e.id)},e.duration);return()=>clearTimeout(s)}})},[s,a]),0===s.length)?null:(0,t.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2 max-w-sm",children:s.map(e=>(0,t.jsx)("div",{className:"p-4 rounded-lg border-l-4 shadow-lg transition-all duration-300 ".concat((e=>{switch(e){case"success":return"bg-green-100 border-green-400 text-green-800";case"error":return"bg-red-100 border-red-400 text-red-800";case"warning":return"bg-yellow-100 border-yellow-400 text-yellow-800";case"info":return"bg-blue-100 border-blue-400 text-blue-800";default:return"bg-gray-100 border-gray-400 text-gray-800"}})(e.type)),children:(0,t.jsxs)("div",{className:"flex items-start justify-between",children:[(0,t.jsxs)("div",{className:"flex items-start space-x-2",children:[(0,t.jsx)("span",{className:"text-lg",children:(e=>{switch(e){case"success":return"✅";case"error":return"❌";case"warning":return"⚠️";case"info":return"ℹ️";default:return"\uD83D\uDCE2"}})(e.type)}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium",children:e.title}),(0,t.jsx)("p",{className:"text-sm opacity-90",children:e.message})]})]}),(0,t.jsx)("button",{onClick:()=>a(e.id),className:"text-lg opacity-60 hover:opacity-100 transition-opacity",children:"\xd7"})]})},e.id))})}function p(e){let{isOpen:s,onClose:a,newLevel:l,rewards:i}=e;return s?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full overflow-hidden",children:[(0,t.jsxs)("div",{className:"bg-gradient-to-r from-yellow-400 to-orange-500 p-6 text-center",children:[(0,t.jsx)("div",{className:"text-6xl mb-2",children:"\uD83C\uDF89"}),(0,t.jsx)("h2",{className:"text-3xl font-bold text-white mb-2",children:"Level Up!"}),(0,t.jsxs)("p",{className:"text-xl text-yellow-100",children:["You reached Level ",l,"!"]})]}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:"\uD83C\uDF81 Level Rewards"}),(0,t.jsx)("div",{className:"space-y-3 mb-6",children:i.map((e,s)=>(0,t.jsx)("div",{className:"p-3 rounded-lg border ".concat((e=>{switch(e){case"recipe":return"bg-blue-50 border-blue-300 text-blue-800";case"equipment":return"bg-purple-50 border-purple-300 text-purple-800";case"money":return"bg-green-50 border-green-300 text-green-800";case"skill_point":return"bg-yellow-50 border-yellow-300 text-yellow-800";case"achievement":return"bg-orange-50 border-orange-300 text-orange-800";default:return"bg-gray-50 border-gray-300 text-gray-800"}})(e.type)),children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("span",{className:"text-2xl",children:(e=>{switch(e){case"recipe":return"\uD83D\uDCD6";case"equipment":return"⚙️";case"money":return"\uD83D\uDCB0";case"skill_point":return"⭐";case"achievement":return"\uD83C\uDFC6";default:return"\uD83C\uDF81"}})(e.type)}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"font-medium",children:e.name}),(0,t.jsx)("p",{className:"text-sm opacity-80",children:e.description}),e.value&&(0,t.jsx)("p",{className:"text-sm font-semibold",children:"money"===e.type?"$".concat(e.value):"+".concat(e.value)})]})]})},s))}),(0,t.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg mb-6",children:[(0,t.jsx)("h4",{className:"font-medium text-blue-800 mb-2",children:"\uD83D\uDCA1 What's Next?"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,t.jsx)("li",{children:"• Check out new recipes in your recipe book"}),(0,t.jsx)("li",{children:"• Visit the shop for new equipment"}),(0,t.jsx)("li",{children:"• Take on more challenging orders"}),(0,t.jsx)("li",{children:"• Invest in skill upgrades"})]})]}),(0,t.jsx)(n.$,{variant:"primary",size:"lg",className:"w-full",onClick:a,children:"\uD83D\uDE80 Continue Playing"})]})]})}):null}function v(e){let{isOpen:s,onClose:a,achievements:i}=e,[r,c]=(0,l.useState)("all");if(!s)return null;let o="all"===r?i:i.filter(e=>e.category===r),d=i.filter(e=>e.completed).length,m=i.length;return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:"\uD83C\uDFC6 Achievements"}),(0,t.jsxs)("p",{className:"text-gray-600",children:[d," of ",m," achievements completed"]})]}),(0,t.jsx)(n.$,{variant:"secondary",onClick:a,children:"✕ Close"})]})}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsxs)("div",{className:"mb-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,t.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Overall Progress"}),(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:[Math.round(d/m*100),"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3",children:(0,t.jsx)("div",{className:"bg-gradient-to-r from-yellow-400 to-orange-500 h-3 rounded-full transition-all duration-500",style:{width:"".concat(d/m*100,"%")}})})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:[{id:"all",name:"All",icon:"\uD83C\uDFC6"},{id:"baking",name:"Baking",icon:"\uD83D\uDC68‍\uD83C\uDF73"},{id:"business",name:"Business",icon:"\uD83D\uDCBC"},{id:"efficiency",name:"Efficiency",icon:"⚡"},{id:"collection",name:"Collection",icon:"\uD83D\uDCDA"},{id:"special",name:"Special",icon:"⭐"}].map(e=>(0,t.jsxs)(n.$,{variant:r===e.id?"primary":"secondary",size:"sm",onClick:()=>c(e.id),children:[e.icon," ",e.name]},e.id))}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 max-h-[50vh] overflow-y-auto",children:o.map(e=>{let s=e.completed?100:Math.min(...e.requirements.map(e=>e.current?Math.min(100,e.current/e.target*100):0)),a=e.completed,l=e.unlocked;return(0,t.jsx)("div",{className:"p-4 rounded-lg border-2 ".concat(a?"border-green-400 bg-green-50":l?"border-gray-300 bg-white":"border-gray-200 bg-gray-50 opacity-60"),children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("div",{className:"text-3xl ".concat(a?"grayscale-0":"grayscale"),children:e.icon}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("h3",{className:"font-semibold ".concat(a?"text-green-800":"text-gray-800"),children:[e.name,a&&(0,t.jsx)("span",{className:"ml-2",children:"✅"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-2",children:e.description}),l&&!a&&(0,t.jsxs)("div",{className:"mb-2",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-1",children:[(0,t.jsx)("span",{className:"text-xs text-gray-500",children:"Progress"}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:(e=>{if(e.completed)return"Completed!";let s=e.requirements[0];return"".concat(s.current||0," / ").concat(s.target)})(e)})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(s,"%")}})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 text-sm",children:[(0,t.jsx)("span",{className:"text-gray-500",children:"Reward:"}),(0,t.jsx)("span",{className:"text-lg",children:(e=>{switch(e){case"recipe":return"\uD83D\uDCD6";case"equipment":return"⚙️";case"money":return"\uD83D\uDCB0";case"skill_point":return"⭐";default:return"\uD83C\uDF81"}})(e.reward.type)}),(0,t.jsx)("span",{className:"text-gray-700",children:e.reward.name}),e.reward.value&&(0,t.jsx)("span",{className:"text-green-600 font-medium",children:"money"===e.reward.type?"$".concat(e.reward.value):"+".concat(e.reward.value)})]})]})]})},e.id)})}),0===o.length&&(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFC6"}),(0,t.jsx)("p",{children:"No achievements in this category."})]})]})]})})}function g(e){let{isOpen:s,onClose:a,skills:i,skillPoints:r,playerLevel:c,onUpgradeSkill:o}=e,[d,m]=(0,l.useState)("all");if(!s)return null;let x="all"===d?i:i.filter(e=>e.category===d),u=e=>!(e.level>=e.maxLevel)&&!(r<e.cost)&&(!e.requirements.playerLevel||!(c<e.requirements.playerLevel))&&(!e.requirements.skills||e.requirements.skills.every(e=>{let s=i.find(s=>s.id===e);return s&&s.level>0}));return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-5xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:"\uD83C\uDF1F Skill Tree"}),(0,t.jsxs)("p",{className:"text-gray-600",children:["Available Skill Points: ",(0,t.jsx)("span",{className:"font-semibold text-blue-600",children:r})]})]}),(0,t.jsx)(n.$,{variant:"secondary",onClick:a,children:"✕ Close"})]})}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:[{id:"all",name:"All",icon:"\uD83C\uDF1F"},{id:"efficiency",name:"Efficiency",icon:"⚡"},{id:"automation",name:"Automation",icon:"\uD83E\uDD16"},{id:"quality",name:"Quality",icon:"\uD83D\uDC8E"},{id:"business",name:"Business",icon:"\uD83D\uDCBC"}].map(e=>(0,t.jsxs)(n.$,{variant:d===e.id?"primary":"secondary",size:"sm",onClick:()=>m(e.id),children:[e.icon," ",e.name]},e.id))}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto",children:x.map(e=>{let s=e.level>=e.maxLevel?"maxed":u(e)?"available":"locked",a=u(e);return(0,t.jsxs)("div",{className:"p-4 rounded-lg border-2 ".concat((e=>{switch(e){case"maxed":return"border-green-400 bg-green-50";case"available":return"border-blue-400 bg-blue-50";default:return"border-gray-300 bg-gray-50"}})(s)),children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:e.icon}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name}),(0,t.jsx)("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:e.category})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("div",{className:"text-sm font-medium text-gray-700",children:["Level ",e.level,"/",e.maxLevel]}),"maxed"!==s&&(0,t.jsxs)("div",{className:"text-xs text-blue-600",children:["Cost: ",e.cost," SP"]})]})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,t.jsxs)("div",{className:"mb-3",children:[(0,t.jsx)("h4",{className:"text-xs font-medium text-gray-700 mb-1",children:"Effects:"}),e.effects.map((e,s)=>(0,t.jsxs)("div",{className:"text-xs text-green-600",children:["• ",(e=>{let s=Math.round(100*e.value);switch(e.type){case"baking_speed":return"+".concat(s,"% baking speed");case"money_multiplier":return"+".concat(s,"% money earned");case"xp_multiplier":return"+".concat(s,"% experience gained");case"ingredient_efficiency":return"".concat(s,"% less ingredients used");case"automation_unlock":return"Unlock automation features";default:return"+".concat(s,"% bonus")}})(e)]},s))]}),e.requirements.playerLevel&&c<e.requirements.playerLevel&&(0,t.jsx)("div",{className:"mb-3",children:(0,t.jsxs)("div",{className:"text-xs text-red-600",children:["Requires Level ",e.requirements.playerLevel]})}),e.requirements.skills&&(0,t.jsx)("div",{className:"mb-3",children:(0,t.jsxs)("div",{className:"text-xs text-gray-600",children:["Requires: ",e.requirements.skills.map(e=>{let s=i.find(s=>s.id===e);return null==s?void 0:s.name}).join(", ")]})}),(0,t.jsx)("div",{className:"mb-3",children:(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(e.level/e.maxLevel*100,"%")}})})}),"maxed"===s?(0,t.jsx)(n.$,{variant:"success",size:"sm",className:"w-full",disabled:!0,children:"✅ Maxed"}):a?(0,t.jsxs)(n.$,{variant:"primary",size:"sm",className:"w-full",onClick:()=>o(e.id),children:["⬆️ Upgrade (",e.cost," SP)"]}):(0,t.jsx)(n.$,{variant:"secondary",size:"sm",className:"w-full",disabled:!0,children:"\uD83D\uDD12 Locked"})]},e.id)})}),0===x.length&&(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDF1F"}),(0,t.jsx)("p",{children:"No skills in this category."})]}),(0,t.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"\uD83D\uDCA1 Skill Tips"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,t.jsx)("li",{children:"• Earn skill points by leveling up (1 point every 2 levels)"}),(0,t.jsx)("li",{children:"• Some skills require other skills to be unlocked first"}),(0,t.jsx)("li",{children:"• Focus on skills that match your playstyle"}),(0,t.jsx)("li",{children:"• Efficiency skills help with resource management"})]})]})]})]})})}var y=a(7871);function j(e){var s;let{isOpen:a,onClose:i}=e,{player:c,equipment:o,automationSettings:d,updateAutomationSettings:m,purchaseAutomationUpgrade:x}=(0,r.I)(),[u,h]=(0,l.useState)("settings");if(!a)return null;let p=o.filter(e=>e.automationLevel>0),v=y.sA.filter(e=>{var s;return c.level>=e.unlockLevel&&!(null==(s=c.automationUpgrades)?void 0:s.includes(e.id))}),g=(e,s)=>{m({[e]:s})};return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:"\uD83E\uDD16 Automation Control"}),(0,t.jsx)(n.$,{variant:"secondary",onClick:i,children:"✕ Close"})]})}),(0,t.jsx)("div",{className:"border-b border-gray-200",children:(0,t.jsx)("div",{className:"flex space-x-0",children:[{id:"settings",name:"Settings",icon:"⚙️"},{id:"upgrades",name:"Upgrades",icon:"\uD83D\uDD27"},{id:"status",name:"Status",icon:"\uD83D\uDCCA"}].map(e=>(0,t.jsxs)("button",{onClick:()=>h(e.id),className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat(u===e.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[e.icon," ",e.name]},e.id))})}),(0,t.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["settings"===u&&(0,t.jsx)("div",{className:"space-y-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,t.jsx)("h3",{className:"font-semibold text-blue-800 mb-3",children:"\uD83C\uDF9B️ Master Control"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"checkbox",checked:(null==d?void 0:d.enabled)||!1,onChange:e=>g("enabled",e.target.checked),className:"rounded"}),(0,t.jsx)("span",{className:"text-sm",children:"Enable Automation"})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"checkbox",checked:(null==d?void 0:d.autoStart)||!1,onChange:e=>g("autoStart",e.target.checked),className:"rounded"}),(0,t.jsx)("span",{className:"text-sm",children:"Auto-start Equipment"})]})]})]}),(0,t.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,t.jsx)("h3",{className:"font-semibold text-green-800 mb-3",children:"\uD83C\uDFAF Priority Mode"}),(0,t.jsxs)("select",{value:(null==d?void 0:d.priorityMode)||"efficiency",onChange:e=>g("priorityMode",e.target.value),className:"w-full p-2 border rounded-lg",children:[(0,t.jsx)("option",{value:"efficiency",children:"Efficiency (Orders First)"}),(0,t.jsx)("option",{value:"profit",children:"Profit (Highest Value)"}),(0,t.jsx)("option",{value:"speed",children:"Speed (Fastest Recipes)"})]}),(0,t.jsx)("p",{className:"text-xs text-green-600 mt-1",children:"How automation chooses what to bake"})]}),(0,t.jsxs)("div",{className:"bg-purple-50 p-4 rounded-lg",children:[(0,t.jsx)("h3",{className:"font-semibold text-purple-800 mb-3",children:"⚡ Performance"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"block text-sm",children:["Max Concurrent Jobs: ",(null==d?void 0:d.maxConcurrentJobs)||2]}),(0,t.jsx)("input",{type:"range",min:"1",max:"5",value:(null==d?void 0:d.maxConcurrentJobs)||2,onChange:e=>g("maxConcurrentJobs",parseInt(e.target.value)),className:"w-full"})]})]}),(0,t.jsxs)("div",{className:"bg-yellow-50 p-4 rounded-lg",children:[(0,t.jsx)("h3",{className:"font-semibold text-yellow-800 mb-3",children:"\uD83D\uDEE1️ Safety"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("label",{className:"block text-sm",children:["Stop when ingredients below: ",(null==d?void 0:d.ingredientThreshold)||5]}),(0,t.jsx)("input",{type:"range",min:"0",max:"20",value:(null==d?void 0:d.ingredientThreshold)||5,onChange:e=>g("ingredientThreshold",parseInt(e.target.value)),className:"w-full"})]})]})]})}),"upgrades"===u&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg mb-4",children:[(0,t.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"\uD83D\uDCA1 Automation Upgrades"}),(0,t.jsx)("p",{className:"text-sm text-blue-700",children:"Improve your automation efficiency, speed, and intelligence with these upgrades."})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:v.map(e=>(0,t.jsxs)("div",{className:"p-4 border rounded-lg bg-white hover:bg-gray-50 transition-colors",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-2",children:[(0,t.jsx)("h4",{className:"font-semibold text-gray-800",children:e.name}),(0,t.jsxs)("span",{className:"text-sm text-green-600 font-medium",children:["$",e.cost]})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-xs text-gray-500 uppercase tracking-wide",children:e.type}),(0,t.jsx)(n.$,{size:"sm",variant:c.money>=e.cost?"primary":"secondary",disabled:c.money<e.cost,onClick:()=>x(e.id),children:c.money>=e.cost?"Purchase":"Too Expensive"})]})]},e.id))}),0===v.length&&(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)("div",{className:"text-4xl mb-2",children:"\uD83D\uDD27"}),(0,t.jsx)("p",{children:"No upgrades available at your current level."}),(0,t.jsx)("p",{className:"text-sm",children:"Level up to unlock more automation upgrades!"})]})]}),"status"===u&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg text-center",children:[(0,t.jsx)("div",{className:"text-2xl text-green-600 mb-1",children:p.length}),(0,t.jsx)("div",{className:"text-sm text-green-800",children:"Automated Equipment"})]}),(0,t.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg text-center",children:[(0,t.jsx)("div",{className:"text-2xl text-blue-600 mb-1",children:(null==d?void 0:d.enabled)?"✅":"❌"}),(0,t.jsx)("div",{className:"text-sm text-blue-800",children:"Automation Status"})]}),(0,t.jsxs)("div",{className:"bg-purple-50 p-4 rounded-lg text-center",children:[(0,t.jsx)("div",{className:"text-2xl text-purple-600 mb-1",children:(null==(s=c.automationUpgrades)?void 0:s.length)||0}),(0,t.jsx)("div",{className:"text-sm text-purple-800",children:"Active Upgrades"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800",children:"\uD83C\uDFED Equipment Status"}),p.length>0?(0,t.jsx)("div",{className:"space-y-2",children:p.map(e=>(0,t.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:e.name}),(0,t.jsxs)("span",{className:"text-sm text-gray-500 ml-2",children:["Level ",e.automationLevel," • ",e.efficiency,"x efficiency"]})]}),(0,t.jsx)("div",{className:"px-2 py-1 rounded text-xs ".concat(e.isActive?"bg-green-100 text-green-800":"bg-gray-100 text-gray-600"),children:e.isActive?"Running":"Idle"})]},e.id))}):(0,t.jsxs)("div",{className:"text-center py-4 text-gray-500",children:[(0,t.jsx)("p",{children:"No automated equipment available."}),(0,t.jsx)("p",{className:"text-sm",children:"Purchase auto-equipment from the shop to get started!"})]})]})]})]})]})})}let b=[{id:"professional_oven",name:"Professional Oven",type:"oven",description:"Faster and more efficient than basic oven",cost:500,unlockLevel:3,automationLevel:0,efficiency:1.3,icon:"\uD83D\uDD25",category:"basic"},{id:"auto_oven",name:"Automated Oven",type:"auto_oven",description:"Fully automated oven that can run without supervision",cost:1500,unlockLevel:5,automationLevel:2,efficiency:1.5,icon:"\uD83E\uDD16",category:"automated"},{id:"industrial_mixer",name:"Industrial Mixer",type:"mixer",description:"High-capacity mixer for large batches",cost:750,unlockLevel:4,automationLevel:0,efficiency:1.4,icon:"\uD83E\uDD44",category:"basic"},{id:"auto_mixer",name:"Automated Mixer",type:"auto_mixer",description:"Self-operating mixer with ingredient dispensing",cost:2e3,unlockLevel:6,automationLevel:2,efficiency:1.6,icon:"\uD83E\uDD16",category:"automated"},{id:"conveyor_belt_basic",name:"Basic Conveyor Belt",type:"conveyor",description:"Moves items between equipment automatically",cost:1e3,unlockLevel:7,automationLevel:1,efficiency:1.2,icon:"\uD83D\uDD04",category:"automated"},{id:"smart_conveyor",name:"Smart Conveyor System",type:"conveyor",description:"Intelligent conveyor with sorting and routing",cost:3e3,unlockLevel:10,automationLevel:3,efficiency:1.8,icon:"\uD83E\uDDE0",category:"advanced"},{id:"master_oven",name:"Master Oven",type:"oven",description:"The ultimate baking machine with AI assistance",cost:5e3,unlockLevel:12,automationLevel:3,efficiency:2,icon:"\uD83D\uDC51",category:"advanced"}];function f(e){let{isOpen:s,onClose:a,onShowSuccess:i}=e,{player:c,equipment:o,spendMoney:d,addEquipment:m}=(0,r.I)(),[x,u]=(0,l.useState)("all");if(!s)return null;let h=b.filter(e=>c.level>=e.unlockLevel&&("all"===x||e.category===x));return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:"\uD83C\uDFEA Equipment Shop"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Upgrade your bakery with professional equipment"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,t.jsxs)("span",{className:"text-green-800 font-medium",children:["$",c.money]})}),(0,t.jsx)(n.$,{variant:"secondary",onClick:a,children:"✕ Close"})]})]})}),(0,t.jsxs)("div",{className:"p-6",children:[(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mb-6",children:[{id:"all",name:"All",icon:"\uD83C\uDFEA"},{id:"basic",name:"Basic",icon:"\uD83D\uDD27"},{id:"automated",name:"Automated",icon:"\uD83E\uDD16"},{id:"advanced",name:"Advanced",icon:"⚡"}].map(e=>(0,t.jsxs)(n.$,{variant:x===e.id?"primary":"secondary",size:"sm",onClick:()=>u(e.id),children:[e.icon," ",e.name]},e.id))}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-h-[60vh] overflow-y-auto",children:h.map(e=>{var s;return(0,t.jsxs)("div",{className:"p-4 rounded-lg border-2 ".concat((e=>{switch(e){case"basic":return"border-gray-300 bg-gray-50";case"automated":return"border-blue-300 bg-blue-50";case"advanced":return"border-purple-300 bg-purple-50";default:return"border-gray-300 bg-white"}})(e.category)),children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:e.icon}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name}),(0,t.jsx)("p",{className:"text-xs text-gray-500 uppercase tracking-wide",children:e.category})]})]}),(0,t.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["$",e.cost]})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,t.jsxs)("div",{className:"space-y-2 mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Efficiency:"}),(0,t.jsxs)("span",{className:"font-medium",children:[e.efficiency,"x"]})]}),e.automationLevel>0&&(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Automation:"}),0===(s=e.automationLevel)?null:(0,t.jsxs)("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800",children:["\uD83E\uDD16 Auto Level ",s]})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Unlock Level:"}),(0,t.jsx)("span",{className:"font-medium",children:e.unlockLevel})]})]}),(0,t.jsx)(n.$,{variant:c.money>=e.cost?"success":"secondary",size:"sm",className:"w-full",disabled:c.money<e.cost,onClick:()=>{!(c.money<e.cost)&&d(e.cost)&&(m({name:e.name,type:e.type,isActive:!1,level:1,efficiency:e.efficiency,automationLevel:e.automationLevel}),i&&i("Equipment Purchased!","You bought ".concat(e.name,"!")))},children:c.money>=e.cost?"\uD83D\uDCB0 Purchase":"\uD83D\uDCB8 Too Expensive"})]},e.id)})}),0===h.length&&(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFEA"}),(0,t.jsx)("p",{children:"No equipment available in this category."}),(0,t.jsx)("p",{className:"text-sm",children:"Level up to unlock more equipment!"})]}),(0,t.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:"\uD83D\uDCA1 Equipment Tips"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,t.jsx)("li",{children:"• Automated equipment can run without your supervision"}),(0,t.jsx)("li",{children:"• Higher efficiency means faster production and better quality"}),(0,t.jsx)("li",{children:"• Conveyor belts connect equipment for seamless workflow"}),(0,t.jsx)("li",{children:"• Advanced equipment unlocks at higher levels"})]})]})]})]})})}var N=a(2785);let w=[{name:"Downtown Delights",location:"City Center",specialization:"general",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:0},{name:"Cookie Corner",location:"Shopping Mall",specialization:"cookies",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:2500},{name:"Cake Castle",location:"Wedding District",specialization:"cakes",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:3500},{name:"Bread Basket",location:"Farmers Market",specialization:"bread",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:3e3},{name:"Pastry Palace",location:"French Quarter",specialization:"pastries",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],purchaseCost:4e3}];function k(e){let{isOpen:s,onClose:a,bakeries:r,currentBakeryId:c,onSwitchBakery:o,onPurchaseBakery:d,playerMoney:m}=e,{t:x}=(0,i.o)(),[u,h]=(0,l.useState)("owned");if(!s)return null;let p=r.filter(e=>e.unlocked),v=w.filter(e=>!r.some(s=>s.name===e.name&&s.unlocked)),g=e=>{switch(e){case"cookies":return"\uD83C\uDF6A";case"cakes":return"\uD83E\uDDC1";case"bread":return"\uD83C\uDF5E";case"pastries":return"\uD83E\uDD50";default:return"\uD83C\uDFEA"}},y=e=>{switch(e){case"cookies":return"+20% Cookie Production Speed";case"cakes":return"+25% Cake Profit Margin";case"bread":return"+15% Bread Ingredient Efficiency";case"pastries":return"+30% Pastry Experience Gain";default:return"Balanced Production"}},j=[{id:"owned",name:x("bakeries.owned")||"My Bakeries",icon:"\uD83C\uDFEA"},{id:"available",name:x("bakeries.available")||"Available",icon:"\uD83D\uDED2"}];return(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"p-6 border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-orange-800",children:x("bakeries.title")||"\uD83C\uDFEA Bakery Manager"}),(0,t.jsx)("p",{className:"text-gray-600",children:x("bakeries.subtitle")||"Manage your bakery empire"})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,t.jsxs)("span",{className:"text-green-800 font-medium",children:["$",m]})}),(0,t.jsx)(n.$,{variant:"secondary",onClick:a,children:x("game.close")||"✕ Close"})]})]})}),(0,t.jsx)("div",{className:"border-b border-gray-200",children:(0,t.jsx)("div",{className:"flex space-x-0",children:j.map(e=>(0,t.jsxs)("button",{onClick:()=>h(e.id),className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat(u===e.id?"border-orange-500 text-orange-600 bg-orange-50":"border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"),children:[e.icon," ",e.name]},e.id))})}),(0,t.jsxs)("div",{className:"p-6 max-h-[60vh] overflow-y-auto",children:["owned"===u&&(0,t.jsx)("div",{className:"space-y-4",children:p.length>0?(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:p.map(e=>(0,t.jsxs)("div",{className:"p-4 rounded-lg border-2 cursor-pointer transition-all ".concat(e.id===c?"border-orange-400 bg-orange-50":"border-gray-300 bg-white hover:border-orange-300"),onClick:()=>o(e.id),children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:g(e.specialization)}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.location})]})]}),e.id===c&&(0,t.jsx)("span",{className:"bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full",children:x("bakeries.current")||"Current"})]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("span",{className:"text-gray-600",children:[x("bakeries.level")||"Level",":"]}),(0,t.jsx)("span",{className:"font-medium",children:e.level})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("span",{className:"text-gray-600",children:[x("bakeries.specialization")||"Specialization",":"]}),(0,t.jsx)("span",{className:"font-medium capitalize",children:e.specialization})]}),(0,t.jsx)("div",{className:"text-xs text-blue-600",children:y(e.specialization)})]}),(0,t.jsxs)("div",{className:"mt-3 pt-3 border-t border-gray-200",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsxs)("span",{className:"text-gray-600",children:[x("bakeries.equipment")||"Equipment",":"]}),(0,t.jsx)("span",{children:e.equipment.length})]}),(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsxs)("span",{className:"text-gray-600",children:[x("bakeries.orders")||"Active Orders",":"]}),(0,t.jsx)("span",{children:e.orders.length})]})]}),e.id!==c&&(0,t.jsx)(n.$,{variant:"primary",size:"sm",className:"w-full mt-3",onClick:s=>{s.stopPropagation(),o(e.id)},children:x("bakeries.switchTo")||"Switch To"})]},e.id))}):(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDFEA"}),(0,t.jsx)("p",{children:x("bakeries.noOwned")||"You don't own any bakeries yet."})]})}),"available"===u&&(0,t.jsx)("div",{className:"space-y-4",children:v.length>0?(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:v.map((e,s)=>(0,t.jsxs)("div",{className:"p-4 rounded-lg border-2 border-gray-300 bg-white",children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-2xl",children:g(e.specialization)}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.location})]})]}),(0,t.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["$",e.purchaseCost]})]}),(0,t.jsxs)("div",{className:"space-y-2 text-sm mb-4",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsxs)("span",{className:"text-gray-600",children:[x("bakeries.specialization")||"Specialization",":"]}),(0,t.jsx)("span",{className:"font-medium capitalize",children:e.specialization})]}),(0,t.jsx)("div",{className:"text-xs text-blue-600",children:y(e.specialization)})]}),(0,t.jsx)(n.$,{variant:m>=e.purchaseCost?"success":"secondary",size:"sm",className:"w-full",disabled:m<e.purchaseCost,onClick:()=>{m>=e.purchaseCost&&d({...e,id:"bakery_".concat(Date.now()),unlocked:!0})},children:m>=e.purchaseCost?x("bakeries.purchase")||"\uD83D\uDCB0 Purchase":x("bakeries.tooExpensive")||"\uD83D\uDCB8 Too Expensive"})]},s))}):(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDF89"}),(0,t.jsx)("p",{children:x("bakeries.allOwned")||"You own all available bakeries!"})]})})]}),(0,t.jsxs)("div",{className:"p-4 bg-blue-50 border-t border-gray-200",children:[(0,t.jsx)("h3",{className:"font-medium text-blue-800 mb-2",children:x("bakeries.tips")||"\uD83D\uDCA1 Bakery Tips"}),(0,t.jsxs)("ul",{className:"text-sm text-blue-700 space-y-1",children:[(0,t.jsxs)("li",{children:["• ",x("bakeries.tip1")||"Each bakery specializes in different products for bonus efficiency"]}),(0,t.jsxs)("li",{children:["• ",x("bakeries.tip2")||"Switch between bakeries to manage multiple locations"]}),(0,t.jsxs)("li",{children:["• ",x("bakeries.tip3")||"Specialized bakeries attract customers looking for specific items"]}),(0,t.jsxs)("li",{children:["• ",x("bakeries.tip4")||"Upgrade each bakery independently for maximum profit"]})]})]})]})})}class C{async initializeSaveDirectory(){if(this.isElectron&&window.electronAPI)try{this.saveDirectory=await window.electronAPI.getSaveDirectory(),await this.ensureSaveDirectoryExists()}catch(e){console.error("Failed to initialize save directory:",e),this.isElectron=!1}}async ensureSaveDirectoryExists(){if(this.isElectron&&window.electronAPI)try{await window.electronAPI.ensureDirectory(this.saveDirectory)}catch(e){console.error("Failed to create save directory:",e)}}async saveToFile(e,s){if(!this.isElectron||!window.electronAPI)return this.saveToLocalStorage(e,s);try{let a=s||"save_".concat(Date.now(),".json"),t="".concat(this.saveDirectory,"/").concat(a),l=JSON.stringify(e,null,2);return await window.electronAPI.writeFile(t,l),console.log("Game saved to file: ".concat(t)),!0}catch(e){return console.error("Failed to save to file:",e),!1}}async loadFromFile(e){if(!this.isElectron||!window.electronAPI)return this.loadFromLocalStorage(e);try{let s="".concat(this.saveDirectory,"/").concat(e),a=await window.electronAPI.readFile(s);if(!a)return null;let t=JSON.parse(a);return console.log("Game loaded from file: ".concat(s)),t}catch(e){return console.error("Failed to load from file:",e),null}}async getSaveFiles(){if(!this.isElectron||!window.electronAPI)return this.getLocalStorageSaves();try{let e=await window.electronAPI.listFiles(this.saveDirectory,".json"),s=[];for(let a of e)try{let e="".concat(this.saveDirectory,"/").concat(a.name),t=await window.electronAPI.readFile(e),l=JSON.parse(t);s.push({fileName:a.name,displayName:l.player.name||a.name.replace(".json",""),timestamp:l.timestamp,playerLevel:l.player.level,money:l.player.money,playTime:l.player.playTime||0,version:l.version,fileSize:a.size||0})}catch(e){console.error("Failed to read save file ".concat(a.name,":"),e)}return s.sort((e,s)=>s.timestamp-e.timestamp)}catch(e){return console.error("Failed to get save files:",e),[]}}async deleteSaveFile(e){if(!this.isElectron||!window.electronAPI)return this.deleteFromLocalStorage(e);try{let s="".concat(this.saveDirectory,"/").concat(e);return await window.electronAPI.deleteFile(s),console.log("Save file deleted: ".concat(s)),!0}catch(e){return console.error("Failed to delete save file:",e),!1}}async exportSave(e,s){if(!this.isElectron||!window.electronAPI)return this.exportToBrowser(e,s);try{let a=s||"bake-it-out-save-".concat(Date.now(),".json"),t=await window.electronAPI.showSaveDialog(a);if(!t)return!1;let l=JSON.stringify(e,null,2);return await window.electronAPI.writeFile(t,l),console.log("Save exported to: ".concat(t)),!0}catch(e){return console.error("Failed to export save:",e),!1}}async importSave(){if(!this.isElectron||!window.electronAPI)return this.importFromBrowser();try{let e=await window.electronAPI.showOpenDialog([".json"]);if(!e)return null;let s=await window.electronAPI.readFile(e),a=JSON.parse(s);return console.log("Save imported from: ".concat(e)),a}catch(e){return console.error("Failed to import save:",e),null}}async createBackup(e){if(!this.isElectron||!window.electronAPI)return!1;try{let s="".concat(this.saveDirectory,"/").concat(e),a="".concat(this.saveDirectory,"/backups/").concat(e,".backup.").concat(Date.now());return await window.electronAPI.ensureDirectory("".concat(this.saveDirectory,"/backups")),await window.electronAPI.copyFile(s,a),console.log("Backup created: ".concat(a)),!0}catch(e){return console.error("Failed to create backup:",e),!1}}saveToLocalStorage(e,s){try{let a=s||"save_".concat(Date.now());return localStorage.setItem("bakeItOut_file_".concat(a),JSON.stringify(e)),!0}catch(e){return console.error("Failed to save to localStorage:",e),!1}}loadFromLocalStorage(e){try{let s=localStorage.getItem("bakeItOut_file_".concat(e));return s?JSON.parse(s):null}catch(e){return console.error("Failed to load from localStorage:",e),null}}getLocalStorageSaves(){let e=[];for(let s=0;s<localStorage.length;s++){let a=localStorage.key(s);if(null==a?void 0:a.startsWith("bakeItOut_file_"))try{let s=localStorage.getItem(a);if(s){let t=JSON.parse(s),l=a.replace("bakeItOut_file_","");e.push({fileName:l,displayName:t.player.name||l,timestamp:t.timestamp,playerLevel:t.player.level,money:t.player.money,playTime:t.player.playTime||0,version:t.version,fileSize:s.length})}}catch(e){console.error("Failed to parse save ".concat(a,":"),e)}}return e.sort((e,s)=>s.timestamp-e.timestamp)}deleteFromLocalStorage(e){try{return localStorage.removeItem("bakeItOut_file_".concat(e)),!0}catch(e){return console.error("Failed to delete from localStorage:",e),!1}}exportToBrowser(e,s){try{let a=JSON.stringify(e,null,2),t=new Blob([a],{type:"application/json"}),l=URL.createObjectURL(t),i=document.createElement("a");return i.href=l,i.download=s||"bake-it-out-save-".concat(Date.now(),".json"),i.click(),URL.revokeObjectURL(l),!0}catch(e){return console.error("Failed to export to browser:",e),!1}}importFromBrowser(){return new Promise(e=>{let s=document.createElement("input");s.type="file",s.accept=".json",s.onchange=s=>{var a;let t=null==(a=s.target.files)?void 0:a[0];if(!t)return void e(null);let l=new FileReader;l.onload=s=>{try{var a;let t=null==(a=s.target)?void 0:a.result,l=JSON.parse(t);e(l)}catch(s){console.error("Failed to parse imported file:",s),e(null)}},l.readAsText(t)},s.click()})}constructor(){this.saveDirectory="",this.isElectron=!1,this.isElectron=void 0!==window.electronAPI,this.initializeSaveDirectory()}}let S=new C;function D(e){var s;let{isOpen:a,onClose:c,mode:o,onSaveSuccess:d,onLoadSuccess:m}=e,{t:x}=(0,i.o)(),{player:u,saveGameState:h,loadGameState:p}=(0,r.I)(),[v,g]=(0,l.useState)([]),[y,j]=(0,l.useState)([]),[b,f]=(0,l.useState)(null),[N,w]=(0,l.useState)(null),[k,C]=(0,l.useState)(""),[D,E]=(0,l.useState)(!1),[L,z]=(0,l.useState)(!1),[F,A]=(0,l.useState)(!1);(0,l.useEffect)(()=>{a&&(P(),$(),A(void 0!==window.electronAPI))},[a]);let $=async()=>{try{let e=await S.getSaveFiles();j(e)}catch(e){console.error("Failed to load file saves:",e)}},P=()=>{let e=[];for(let t=1;t<=8;t++){let l="bakeItOut_save_slot_".concat(t),i=localStorage.getItem(l);if(i)try{var s,a;let l=JSON.parse(i);e.push({id:t,name:l.player.name||"Save ".concat(t),timestamp:l.timestamp,playerLevel:l.player.level,money:l.player.money,bakeryName:(null==(a=l.bakeries)||null==(s=a[0])?void 0:s.name)||"Main Bakery",playTime:l.player.playTime||0,isEmpty:!1,data:l})}catch(s){console.error("Failed to load save slot ".concat(t,":"),s),e.push(q(t))}else e.push(q(t))}g(e)},q=e=>({id:e,name:"Empty Slot ".concat(e),timestamp:0,playerLevel:0,money:0,bakeryName:"",playTime:0,isEmpty:!0}),I=async e=>{if(!b)return;let s=v.find(s=>s.id===e);if(!(null==s?void 0:s.isEmpty)&&!L)return void z(!0);E(!0);try{await h(e,k||"Save ".concat(e))&&(null==d||d(),P(),z(!1),C(""))}catch(e){console.error("Save failed:",e)}finally{E(!1)}},M=async e=>{let s=v.find(s=>s.id===e);if(s&&!s.isEmpty){E(!0);try{await p(e)&&(null==m||m(),c())}catch(e){console.error("Load failed:",e)}finally{E(!1)}}};return a?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-gradient-to-r from-blue-500 to-purple-500 p-6 text-white",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold",children:"save"===o?"\uD83D\uDCBE Save Game":"\uD83D\uDCC1 Load Game"}),(0,t.jsx)("p",{className:"text-blue-100 text-sm",children:"save"===o?x("saveLoad.saveDesc","Choose a slot to save your progress"):x("saveLoad.loadDesc","Select a save file to load")})]}),(0,t.jsx)(n.$,{variant:"secondary",size:"sm",className:"bg-white/20 hover:bg-white/30 text-white border-white/30",onClick:c,children:"✕"})]})}),F&&(0,t.jsx)("div",{className:"border-b border-gray-200",children:(0,t.jsxs)("div",{className:"flex",children:[(0,t.jsx)("button",{className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat(F&&null===b?"border-transparent text-gray-500 hover:text-gray-700":"border-blue-500 text-blue-600 bg-blue-50"),onClick:()=>{f(1),w(null)},children:"\uD83D\uDCC1 Save Slots"}),(0,t.jsx)("button",{className:"px-6 py-3 font-medium text-sm border-b-2 transition-colors ".concat(null!==N?"border-blue-500 text-blue-600 bg-blue-50":"border-transparent text-gray-500 hover:text-gray-700"),onClick:()=>{f(null),w("")},children:"\uD83D\uDCBE File System"})]})}),(0,t.jsxs)("div",{className:"p-6 overflow-y-auto max-h-[70vh]",children:["save"===o&&b&&(0,t.jsxs)("div",{className:"mb-6 p-4 bg-blue-50 rounded-lg",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:x("saveLoad.saveName","Save Name")}),(0,t.jsx)("input",{type:"text",value:k,onChange:e=>C(e.target.value),placeholder:"Save ".concat(b),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:v.map(e=>{var s;return(0,t.jsxs)("div",{className:"border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 ".concat(b===e.id?"border-blue-500 bg-blue-50":e.isEmpty?"border-gray-200 bg-gray-50 hover:border-gray-300":"border-gray-300 bg-white hover:border-blue-300 hover:shadow-md"),onClick:()=>f(e.id),children:[(0,t.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-orange-400 to-yellow-400 rounded-lg flex items-center justify-center text-white font-bold text-lg",children:e.id}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-800",children:e.isEmpty?"Slot ".concat(e.id):e.name}),!e.isEmpty&&(0,t.jsx)("p",{className:"text-sm text-gray-500",children:(s=e.timestamp)?new Date(s).toLocaleString():""})]})]}),!e.isEmpty&&(0,t.jsx)(n.$,{variant:"secondary",size:"sm",className:"text-red-600 hover:bg-red-50",onClick:s=>{var a;s.stopPropagation(),a=e.id,localStorage.removeItem("bakeItOut_save_slot_".concat(a)),P()},children:"\uD83D\uDDD1️"})]}),e.isEmpty?(0,t.jsxs)("div",{className:"text-center py-8 text-gray-400",children:[(0,t.jsx)("div",{className:"text-3xl mb-2",children:"\uD83D\uDCC4"}),(0,t.jsx)("p",{className:"text-sm",children:x("saveLoad.emptySlot","Empty Slot")})]}):(0,t.jsx)("div",{className:"space-y-2",children:(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-500",children:"Level:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:e.playerLevel})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-500",children:"Money:"}),(0,t.jsxs)("span",{className:"ml-2 font-medium",children:["$",e.money]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-500",children:"Bakery:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:e.bakeryName})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"text-gray-500",children:"Play Time:"}),(0,t.jsx)("span",{className:"ml-2 font-medium",children:(e=>{let s=Math.floor(e/3600),a=Math.floor(e%3600/60);return"".concat(s,"h ").concat(a,"m")})(e.playTime)})]})]})})]},e.id)})})]}),(0,t.jsxs)("div",{className:"bg-gray-50 px-6 py-4 flex justify-between items-center",children:[(0,t.jsx)("div",{className:"text-sm text-gray-500",children:b&&(0,t.jsx)("span",{children:"save"===o?x("saveLoad.selectedSaveSlot","Selected: Slot ".concat(b)):x("saveLoad.selectedLoadSlot","Selected: Slot ".concat(b))})}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsx)(n.$,{variant:"secondary",onClick:c,children:x("common.cancel","Cancel")}),"save"===o?(0,t.jsx)(n.$,{variant:"primary",onClick:()=>b&&I(b),disabled:!b||D,className:"bg-gradient-to-r from-green-500 to-blue-500 hover:from-green-600 hover:to-blue-600",children:D?"\uD83D\uDCBE Saving...":"\uD83D\uDCBE Save Game"}):(0,t.jsx)(n.$,{variant:"primary",onClick:()=>b&&M(b),disabled:!b||D||(null==(s=v.find(e=>e.id===b))?void 0:s.isEmpty),className:"bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600",children:D?"\uD83D\uDCC1 Loading...":"\uD83D\uDCC1 Load Game"})]})]}),L&&(0,t.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg p-6 max-w-md mx-4",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-4",children:x("saveLoad.confirmOverwrite","Overwrite Save?")}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:x("saveLoad.overwriteWarning","This will overwrite the existing save. This action cannot be undone.")}),(0,t.jsxs)("div",{className:"flex space-x-3",children:[(0,t.jsx)(n.$,{variant:"secondary",onClick:()=>z(!1),children:x("common.cancel","Cancel")}),(0,t.jsx)(n.$,{variant:"primary",className:"bg-red-500 hover:bg-red-600",onClick:()=>b&&I(b),children:x("saveLoad.overwrite","Overwrite")})]})]})})]})}):null}function E(e){let{player:s,onOpenMenu:a,onOpenAchievements:r,onOpenSkills:c,onOpenBakeries:o,onOpenSettings:d}=e,{t:m}=(0,i.o)(),[x,u]=(0,l.useState)(!1),h=s.experience/s.maxExperience*100;return(0,t.jsx)("div",{className:"bg-white shadow-lg border-b border-gray-200 relative",children:(0,t.jsxs)("div",{className:"px-6 py-4",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(n.$,{variant:"secondary",size:"sm",className:"bg-orange-100 hover:bg-orange-200 text-orange-800",onClick:a,children:["☰ ",m("toolbar.menu","Menu")]}),(0,t.jsx)("div",{className:"hidden md:block",children:(0,t.jsx)("h1",{className:"text-xl font-bold text-orange-800",children:"\uD83E\uDD56 Bake It Out"})})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-800",children:s.name}),(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["Level ",s.level]})]}),(0,t.jsxs)("div",{className:"hidden sm:block",children:[(0,t.jsx)("div",{className:"w-32 bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(h,"%")}})}),(0,t.jsxs)("div",{className:"text-xs text-center text-gray-500 mt-1",children:[s.experience,"/",s.maxExperience," XP"]})]}),(0,t.jsx)("div",{className:"bg-green-100 px-3 py-1 rounded-full",children:(0,t.jsxs)("span",{className:"text-green-800 font-medium",children:["$",s.money]})}),s.skillPoints>0&&(0,t.jsxs)("div",{className:"bg-yellow-100 px-3 py-1 rounded-full relative",children:[(0,t.jsxs)("span",{className:"text-yellow-800 font-medium",children:["⭐ ",s.skillPoints]}),(0,t.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(n.$,{variant:"secondary",size:"sm",className:"md:hidden",onClick:()=>u(!x),children:"⚡"}),(0,t.jsxs)("div",{className:"hidden md:flex items-center space-x-2",children:[(0,t.jsx)(n.$,{variant:"secondary",size:"sm",onClick:o,children:"\uD83C\uDFEA"}),(0,t.jsx)(n.$,{variant:"secondary",size:"sm",onClick:r,className:"relative",children:"\uD83C\uDFC6"}),(0,t.jsxs)(n.$,{variant:"secondary",size:"sm",onClick:c,className:s.skillPoints>0?"bg-yellow-100 hover:bg-yellow-200":"",children:["\uD83C\uDF1F",s.skillPoints>0&&(0,t.jsx)("span",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]}),(0,t.jsx)(n.$,{variant:"secondary",size:"sm",onClick:d,children:"⚙️"})]})]})]}),x&&(0,t.jsxs)("div",{className:"md:hidden mt-4 pt-4 border-t border-gray-200",children:[(0,t.jsxs)("div",{className:"grid grid-cols-4 gap-2",children:[(0,t.jsxs)(n.$,{variant:"secondary",size:"sm",className:"flex flex-col items-center py-3",onClick:()=>{o(),u(!1)},children:[(0,t.jsx)("span",{className:"text-lg",children:"\uD83C\uDFEA"}),(0,t.jsx)("span",{className:"text-xs",children:m("toolbar.bakeries","Bakeries")})]}),(0,t.jsxs)(n.$,{variant:"secondary",size:"sm",className:"flex flex-col items-center py-3",onClick:()=>{r(),u(!1)},children:[(0,t.jsx)("span",{className:"text-lg",children:"\uD83C\uDFC6"}),(0,t.jsx)("span",{className:"text-xs",children:m("toolbar.achievements","Achievements")})]}),(0,t.jsxs)(n.$,{variant:"secondary",size:"sm",className:"flex flex-col items-center py-3 relative ".concat(s.skillPoints>0?"bg-yellow-100 hover:bg-yellow-200":""),onClick:()=>{c(),u(!1)},children:[(0,t.jsx)("span",{className:"text-lg",children:"\uD83C\uDF1F"}),(0,t.jsx)("span",{className:"text-xs",children:m("toolbar.skills","Skills")}),s.skillPoints>0&&(0,t.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse"})]}),(0,t.jsxs)(n.$,{variant:"secondary",size:"sm",className:"flex flex-col items-center py-3",onClick:()=>{d(),u(!1)},children:[(0,t.jsx)("span",{className:"text-lg",children:"⚙️"}),(0,t.jsx)("span",{className:"text-xs",children:m("toolbar.settings","Settings")})]})]}),(0,t.jsxs)("div",{className:"mt-3",children:[(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-blue-500 h-2 rounded-full transition-all duration-300",style:{width:"".concat(h,"%")}})}),(0,t.jsxs)("div",{className:"text-xs text-center text-gray-500 mt-1",children:[s.experience,"/",s.maxExperience," XP"]})]})]})]})})}function L(e){let{isOpen:s,onClose:a,onSaveGame:l,onLoadGame:r,onSettings:c,onMainMenu:o,onExit:d}=e,{t:m}=(0,i.o)();return s?(0,t.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden",children:[(0,t.jsx)("div",{className:"bg-gradient-to-r from-orange-500 to-yellow-500 p-6 text-white",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold",children:["\uD83C\uDFAE ",m("gameMenu.title","Game Menu")]}),(0,t.jsx)("p",{className:"text-orange-100 text-sm",children:m("gameMenu.subtitle","Manage your game")})]}),(0,t.jsx)(n.$,{variant:"secondary",size:"sm",className:"bg-white/20 hover:bg-white/30 text-white border-white/30",onClick:a,children:"✕"})]})}),(0,t.jsxs)("div",{className:"p-6 space-y-3",children:[(0,t.jsxs)(n.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-orange-50",onClick:()=>{a()},children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:"▶️"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold",children:m("gameMenu.resume","Resume Game")}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:m("gameMenu.resumeDesc","Continue playing")})]})]}),(0,t.jsxs)(n.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-green-50",onClick:()=>{l(),a()},children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDCBE"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold",children:m("gameMenu.save","Save Game")}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:m("gameMenu.saveDesc","Save your progress")})]})]}),(0,t.jsxs)(n.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-blue-50",onClick:()=>{r(),a()},children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDCC1"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold",children:m("gameMenu.load","Load Game")}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:m("gameMenu.loadDesc","Load saved progress")})]})]}),(0,t.jsxs)(n.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-purple-50",onClick:()=>{c(),a()},children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:"⚙️"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold",children:m("gameMenu.settings","Settings")}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:m("gameMenu.settingsDesc","Game preferences")})]})]}),(0,t.jsxs)("div",{className:"border-t pt-3 mt-4",children:[(0,t.jsxs)(n.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-yellow-50",onClick:()=>{o(),a()},children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:"\uD83C\uDFE0"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold",children:m("gameMenu.mainMenu","Main Menu")}),(0,t.jsx)("div",{className:"text-sm text-gray-500",children:m("gameMenu.mainMenuDesc","Return to main menu")})]})]}),d&&(0,t.jsxs)(n.$,{variant:"secondary",size:"lg",className:"w-full justify-start text-left py-4 hover:bg-red-50 text-red-600",onClick:()=>{d(),a()},children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDEAA"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-semibold",children:m("gameMenu.exit","Exit Game")}),(0,t.jsx)("div",{className:"text-sm text-red-400",children:m("gameMenu.exitDesc","Close the application")})]})]})]})]}),(0,t.jsx)("div",{className:"bg-gray-50 px-6 py-3 text-center text-sm text-gray-500",children:m("gameMenu.tip","Press ESC to open this menu anytime")})]})}):null}var z=a(2148);function F(){var e;let{t:s}=(0,i.o)(),{setGameActivity:a,setBakingActivity:d}=(0,z.l)(),{player:y,equipment:b,inventory:w,orders:C,achievements:S,skills:F,levelUpRewards:A,showLevelUp:$,updateEquipment:P,acceptOrder:q,completeOrder:I,declineOrder:M,generateNewOrder:O,upgradeSkill:B,checkAchievements:_,dismissLevelUp:T,spendMoney:J}=(0,r.I)(),[R,U]=(0,l.useState)(!1),[G,Y]=(0,l.useState)(!1),[W,H]=(0,l.useState)(!1),[Q,V]=(0,l.useState)(!1),[X,K]=(0,l.useState)(!1),[Z,ee]=(0,l.useState)(!1),[es,ea]=(0,l.useState)(!1),[et,el]=(0,l.useState)(!1),[ei,er]=(0,l.useState)(!1),[en,ec]=(0,l.useState)(!1),[eo,ed]=(0,l.useState)(!1),[em,ex]=(0,l.useState)("save"),[eu,eh]=(0,l.useState)(null),[ep,ev]=(0,l.useState)({language:"en",soundEnabled:!0,musicEnabled:!0,notificationsEnabled:!0,autoSaveEnabled:!0,graphicsQuality:"medium",animationSpeed:1,showTutorials:!0}),[eg,ey]=(0,l.useState)([{id:"main",name:"Downtown Delights",location:"City Center",specialization:"general",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],unlocked:!0,purchaseCost:0}]),[ej,eb]=(0,l.useState)("main"),{notifications:ef,removeNotification:eN,showSuccess:ew,showError:ek,showInfo:eC}=function(){let[e,s]=(0,l.useState)([]),a=e=>{let a=Date.now().toString()+Math.random().toString(36).substr(2,9),t={...e,id:a,duration:e.duration||5e3};s(e=>[...e,t])};return{notifications:e,addNotification:a,removeNotification:e=>{s(s=>s.filter(s=>s.id!==e))},showSuccess:(e,s)=>{a({type:"success",title:e,message:s})},showError:(e,s)=>{a({type:"error",title:e,message:s})},showWarning:(e,s)=>{a({type:"warning",title:e,message:s})},showInfo:(e,s)=>{a({type:"info",title:e,message:s})}}}(),eS=(e,s)=>{eh({id:e,name:s}),H(!0)},eD=e=>{q(e),eC("Order Accepted","You have accepted a new order!")},eE=e=>{let s=C.find(s=>s.id===e);s&&(I(e),_(),ew("Order Completed!","You earned $".concat(s.reward," and gained experience!")))},eL=e=>{M(e),eC("Order Declined","Order has been removed from your queue.")};return window.addEventListener("keydown",e=>{"Escape"===e.key&&ec(!en)}),(0,l.useEffect)(()=>{(async()=>{if(C&&C.length>0){var e;let s=(null==(e=C[0].items[0])?void 0:e.name)||"Unknown item";await d(y.level,s)}else await a(y.level,y.money,"Managing bakery")})()},[y.level,y.money,C,a,d]),(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-orange-50 to-yellow-50",children:[(0,t.jsx)(E,{player:y,onOpenMenu:()=>ec(!0),onOpenAchievements:()=>V(!0),onOpenSkills:()=>K(!0),onOpenBakeries:()=>er(!0),onOpenSettings:()=>el(!0)}),(0,t.jsxs)("div",{className:"max-w-7xl mx-auto p-6 grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-3 space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-orange-800",children:s("kitchen.title")}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Current: ",null==(e=eg.find(e=>e.id===ej))?void 0:e.name]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:b.map(e=>(0,t.jsx)(c.$,{equipment:e,onClick:eS},e.id))})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-orange-800 mb-4",children:s("inventory.title")}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:w.map(e=>(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg text-center",children:[(0,t.jsx)("div",{className:"text-2xl mb-1",children:e.icon}),(0,t.jsx)("div",{className:"font-medium text-gray-800",children:e.name}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:s("inventory.quantity",{qty:e.quantity.toString()})}),(0,t.jsx)("div",{className:"text-xs text-green-600",children:s("inventory.cost",{cost:e.cost.toString()})})]},e.name))})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-orange-800",children:s("orders.title")}),(0,t.jsx)(n.$,{size:"sm",variant:"primary",onClick:O,children:s("orders.newOrder")})]}),(0,t.jsx)("div",{className:"space-y-4",children:C.map(e=>(0,t.jsx)(o.p,{order:e,onAccept:eD,onDecline:eL,onComplete:eE},e.id))})]}),(0,t.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6",children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-orange-800 mb-4",children:s("actions.title")}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(n.$,{variant:"secondary",size:"sm",className:"w-full",onClick:()=>Y(!0),children:s("actions.buyIngredients")}),(0,t.jsx)(n.$,{variant:"secondary",size:"sm",className:"w-full",onClick:()=>U(!0),children:s("actions.viewRecipes")}),(0,t.jsx)(n.$,{variant:"secondary",size:"sm",className:"w-full",onClick:()=>ea(!0),children:s("actions.equipmentShop")})]})]})]})]}),(0,t.jsx)(m,{isOpen:R,onClose:()=>U(!1)}),(0,t.jsx)(x,{isOpen:G,onClose:()=>Y(!1)}),(0,t.jsx)(u,{isOpen:W,onClose:()=>H(!1),equipmentId:(null==eu?void 0:eu.id)||"",equipmentName:(null==eu?void 0:eu.name)||""}),(0,t.jsx)(v,{isOpen:Q,onClose:()=>V(!1),achievements:S}),(0,t.jsx)(g,{isOpen:X,onClose:()=>K(!1),skills:F,skillPoints:y.skillPoints,playerLevel:y.level,onUpgradeSkill:B}),(0,t.jsx)(p,{isOpen:$,onClose:T,newLevel:y.level,rewards:A}),(0,t.jsx)(j,{isOpen:Z,onClose:()=>ee(!1)}),(0,t.jsx)(f,{isOpen:es,onClose:()=>ea(!1),onShowSuccess:ew}),(0,t.jsx)(N.b,{isOpen:et,onClose:()=>el(!1),settings:ep,onSettingsChange:e=>{ev(s=>({...s,...e}))}}),(0,t.jsx)(k,{isOpen:ei,onClose:()=>er(!1),bakeries:eg,currentBakeryId:ej,onSwitchBakery:e=>{var s;eb(e),eC("Bakery Switched","Switched to ".concat(null==(s=eg.find(s=>s.id===e))?void 0:s.name))},onPurchaseBakery:e=>{if(J(e.purchaseCost)){let s={id:Date.now().toString(),name:e.name,location:"Downtown",specialization:"general",level:1,equipment:[],inventory:[],orders:[],automationJobs:[],conveyorBelts:[],unlocked:!0,purchaseCost:e.purchaseCost};ey(e=>[...e,s]),ew("Bakery Purchased!","You now own ".concat(e.name,"!"))}},playerMoney:y.money}),(0,t.jsx)(L,{isOpen:en,onClose:()=>ec(!1),onSaveGame:()=>{ex("save"),ed(!0)},onLoadGame:()=>{ex("load"),ed(!0)},onSettings:()=>el(!0),onMainMenu:()=>{window.location.href="/"},onExit:window.electronAPI?()=>{window.electronAPI&&window.electronAPI.quit()}:void 0}),(0,t.jsx)(D,{isOpen:eo,onClose:()=>ed(!1),mode:em,onSaveSuccess:()=>{ew("Game Saved!","Your progress has been saved successfully."),ed(!1)},onLoadSuccess:()=>{ew("Game Loaded!","Your saved progress has been loaded."),ed(!1)}}),(0,t.jsx)(h,{notifications:ef,onRemove:eN})]})}function A(){return(0,t.jsx)(r.S,{children:(0,t.jsx)(F,{})})}}}]);