# 🧁 Bake It Out - Complete Setup Guide

**The easiest way to get your bakery management game running!**

## ⚡ **Super Quick Start (30 seconds)**

### **Option 1: One-Click Everything**
```bash
# Windows - Sets up EVERYTHING automatically
EASY-SETUP.bat
```
**What it does:**
- ✅ Builds the game client
- ✅ Sets up the server with database
- ✅ Creates portable distribution
- ✅ Tests everything works
- ✅ Gives you clear next steps

### **Option 2: Server Only**
```bash
# Just the server with dashboard
cd server
setup-easy.bat
# or
npm run setup-easy
```

### **Option 3: Quick Manual**
```bash
# Game client
npm install && npm run build

# Server
cd server && npm run quick-setup && npm start
```

---

## 🎯 **What You Get After Setup**

### **🎮 Game Client**
- **URL**: http://localhost:3000 (or 3002)
- **Features**: Complete bakery management game
- **Languages**: English and Czech
- **Saves**: Local and cloud saves available

### **🌐 Unified Server**
- **URL**: http://localhost:3001
- **Features**: Cloud saves, multiplayer, user accounts
- **Dashboard**: http://localhost:3001/dashboard
- **Health Check**: http://localhost:3001/health

### **📊 Admin Dashboard**
- **User Management**: View and manage players
- **Game Rooms**: Monitor multiplayer sessions
- **Cloud Saves**: Manage save files
- **Analytics**: Real-time statistics and charts
- **Server Monitoring**: Live performance metrics

---

## 🛠️ **Setup Scripts Explained**

### **🚀 EASY-SETUP.bat** (Root Directory)
**The ultimate one-click installer**
- Checks Node.js installation
- Builds game client
- Sets up server with database
- Creates portable distribution
- Tests everything works
- Provides clear instructions

### **⚡ setup-easy.bat** (Server Directory)
**Automatic server setup**
- Installs dependencies
- Creates configuration files
- Sets up MongoDB (Docker or local)
- Generates secure keys
- Tests server startup
- Beautiful progress display

### **📦 npm run quick-setup** (Server Directory)
**Simple configuration setup**
- Creates .env file
- Creates necessary directories
- Generates secure JWT secrets
- Basic validation

### **🔍 npm run validate** (Server Directory)
**Comprehensive setup validation**
- Checks Node.js version
- Validates dependencies
- Verifies configuration
- Tests database connection
- Provides detailed feedback

### **🧪 test-setup.bat** (Server Directory)
**Complete setup testing**
- Runs validation checks
- Tests server startup
- Validates all endpoints
- Checks for errors
- Provides troubleshooting tips

---

## 📋 **Requirements**

### **Minimum Requirements**
- **Node.js 18+** ([Download](https://nodejs.org/))
- **4GB RAM** (recommended)
- **1GB disk space**
- **Windows/Mac/Linux**

### **Optional (Auto-installed)**
- **MongoDB** (auto-setup with Docker)
- **Docker** (for easy MongoDB)

---

## 🎮 **Usage After Setup**

### **Start Everything**
```bash
# Start server (in one terminal)
cd server
npm start

# Start game client (in another terminal)
npx serve out -p 3002
```

### **Or Use Portable Version**
```bash
# From portable-dist folder
start-server.bat    # Starts the server
start-game.bat      # Starts the game client
```

### **Access Points**
- 🎮 **Game**: http://localhost:3002
- 📊 **Dashboard**: http://localhost:3001/dashboard
- 🔍 **API**: http://localhost:3001/health

---

## 🔧 **Configuration Options**

### **Database Options**
```bash
# Option 1: Docker (Automatic)
# The setup scripts handle this automatically

# Option 2: Local MongoDB
# Download from: https://www.mongodb.com/try/download/community

# Option 3: MongoDB Atlas (Cloud)
# 1. Create account at: https://cloud.mongodb.com/
# 2. Get connection string
# 3. Update MONGODB_URI in server/.env
```

### **Environment Variables**
The setup scripts create a `.env` file with secure defaults:
```env
NODE_ENV=development
PORT=3001
MONGODB_URI=mongodb://localhost:27017/bake-it-out
JWT_SECRET=<randomly-generated-secure-key>
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3002
```

---

## 🆘 **Troubleshooting**

### **Common Issues**

#### **"Node.js not found"**
```bash
# Install Node.js 18+ from: https://nodejs.org/
# Restart your terminal after installation
```

#### **"Port 3001 already in use"**
```bash
# Find what's using the port
netstat -ano | findstr :3001

# Kill the process (Windows)
taskkill /PID <process_id> /F

# Or change port in server/.env
PORT=3002
```

#### **"MongoDB connection failed"**
```bash
# For Docker setup
docker ps | grep mongo
docker start bake-it-out-mongo

# For local MongoDB
# Make sure MongoDB service is running

# For cloud MongoDB
# Check your connection string in .env
```

#### **"Dependencies failed to install"**
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install
```

### **Validation Commands**
```bash
# Check if everything is set up correctly
cd server
npm run validate

# Test server startup
test-setup.bat

# Check server health
curl http://localhost:3001/health
```

### **Debug Mode**
```bash
# Start server with detailed logging
cd server
DEBUG=* npm start
```

---

## 📊 **Features Overview**

### **Game Features**
- ✅ Complete bakery management simulation
- ✅ Recipe system with unlockable content
- ✅ Equipment upgrades and automation
- ✅ Order management and customer satisfaction
- ✅ Achievement system with rewards
- ✅ Skill trees and character progression
- ✅ Multilingual support (English/Czech)

### **Server Features**
- ✅ Cloud save synchronization
- ✅ Real-time multiplayer gaming
- ✅ User account management
- ✅ Professional web dashboard
- ✅ Real-time analytics and monitoring
- ✅ Secure authentication system
- ✅ Docker deployment support

### **Dashboard Features**
- ✅ User management interface
- ✅ Game room monitoring
- ✅ Cloud save administration
- ✅ Real-time server statistics
- ✅ Analytics charts and graphs
- ✅ Performance monitoring
- ✅ System health checks

---

## 🎉 **Success Indicators**

### **Your setup is working when:**
✅ **Server starts** without errors  
✅ **Health check** returns "OK" at http://localhost:3001/health  
✅ **Dashboard loads** at http://localhost:3001/dashboard  
✅ **Game loads** at http://localhost:3002  
✅ **Can create account** and login  
✅ **Cloud saves work** - can save and load from cloud  
✅ **Multiplayer works** - can create and join rooms  

---

## 📞 **Getting Help**

### **Self-Help Tools**
```bash
# Validate your setup
npm run validate

# Test everything
test-setup.bat

# Check server logs
tail -f server/logs/combined.log
```

### **Documentation**
- 📖 **Server Guide**: [server/README.md](server/README.md)
- 🌐 **Unified System**: [UNIFIED-SERVER.md](UNIFIED-SERVER.md)
- 🐳 **Docker Guide**: [docker-compose.yml](docker-compose.yml)

### **Quick Fixes**
- **Reset everything**: Delete `node_modules`, `.env`, run setup again
- **Database issues**: `docker run -d -p 27017:27017 mongo:6.0`
- **Port conflicts**: Change `PORT` in `server/.env`
- **Permission issues**: Run as administrator (Windows)

---

## 🚀 **Ready to Start?**

**Choose your setup method:**

1. **🎯 Complete Setup**: Run `EASY-SETUP.bat` (everything in one go)
2. **⚡ Server Only**: Run `cd server && setup-easy.bat`
3. **📦 Quick Setup**: Run `cd server && npm run quick-setup`
4. **🔍 Validate Setup**: Run `cd server && npm run validate`

**After setup, start playing:**
1. Start server: `cd server && npm start`
2. Start game: `npx serve out -p 3002`
3. Play: http://localhost:3002
4. Manage: http://localhost:3001/dashboard

**Happy baking! 🧁👨‍🍳**
