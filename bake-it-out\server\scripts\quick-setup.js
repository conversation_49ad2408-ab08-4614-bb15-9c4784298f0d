#!/usr/bin/env node

const fs = require('fs');
const crypto = require('crypto');

console.log('🧁 Bake It Out - Quick Setup');
console.log('============================');
console.log('');

// Create .env file if it doesn't exist
if (!fs.existsSync('.env')) {
  console.log('📝 Creating environment configuration...');
  
  const envContent = `NODE_ENV=development
PORT=3001
MONGODB_URI=mongodb://localhost:27017/bake-it-out
JWT_SECRET=${crypto.randomBytes(32).toString('hex')}
JWT_EXPIRES_IN=7d
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:3002
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
AUTH_RATE_LIMIT_MAX=10
`;
  
  fs.writeFileSync('.env', envContent);
  console.log('✅ Environment file created!');
} else {
  console.log('✅ Environment file already exists!');
}

// Create directories
const dirs = ['logs', 'uploads', 'backups'];
console.log('📁 Creating directories...');

dirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`  ✅ Created ${dir}/`);
  } else {
    console.log(`  ✅ ${dir}/ already exists`);
  }
});

console.log('');
console.log('🎉 Quick setup complete!');
console.log('');
console.log('Next steps:');
console.log('1. Make sure MongoDB is running (port 27017)');
console.log('2. Start the server: npm start');
console.log('3. Open dashboard: http://localhost:3001/dashboard');
console.log('');
console.log('💡 For automatic MongoDB setup, use: npm run setup-easy');
console.log('');
