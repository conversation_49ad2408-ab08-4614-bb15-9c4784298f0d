const express = require('express');
const { body, validationResult } = require('express-validator');
const { authenticateToken } = require('../middleware/auth');
const GameRoom = require('../models/GameRoom');
const User = require('../models/User');

const router = express.Router();

// All routes require authentication
router.use(authenticateToken);

// Get all active game rooms
router.get('/rooms', async (req, res) => {
  try {
    const rooms = await GameRoom.find({ isActive: true })
      .populate('host', 'username profile.displayName')
      .populate('players', 'username profile.displayName')
      .sort({ createdAt: -1 });

    res.json({
      rooms: rooms.map(room => ({
        id: room._id,
        name: room.name,
        host: room.host,
        players: room.players,
        maxPlayers: room.maxPlayers,
        isPrivate: room.isPrivate,
        gameMode: room.gameMode,
        status: room.status,
        createdAt: room.createdAt
      }))
    });
  } catch (error) {
    console.error('Get rooms error:', error);
    res.status(500).json({
      error: 'Failed to get rooms',
      code: 'GET_ROOMS_ERROR'
    });
  }
});

// Create a new game room
router.post('/rooms', [
  body('name')
    .isLength({ min: 1, max: 50 })
    .trim()
    .withMessage('Room name must be 1-50 characters'),
  body('maxPlayers')
    .isInt({ min: 2, max: 8 })
    .withMessage('Max players must be between 2 and 8'),
  body('gameMode')
    .isIn(['cooperative', 'competitive', 'sandbox'])
    .withMessage('Invalid game mode')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { name, maxPlayers, isPrivate = false, gameMode = 'cooperative' } = req.body;

    // Check if user already has an active room
    const existingRoom = await GameRoom.findOne({
      host: req.userId,
      isActive: true
    });

    if (existingRoom) {
      return res.status(409).json({
        error: 'You already have an active room',
        code: 'ROOM_EXISTS'
      });
    }

    const room = new GameRoom({
      name,
      host: req.userId,
      players: [req.userId],
      maxPlayers,
      isPrivate,
      gameMode,
      roomCode: generateRoomCode()
    });

    await room.save();
    await room.populate('host', 'username profile.displayName');
    await room.populate('players', 'username profile.displayName');

    res.status(201).json({
      message: 'Room created successfully',
      room: {
        id: room._id,
        name: room.name,
        host: room.host,
        players: room.players,
        maxPlayers: room.maxPlayers,
        isPrivate: room.isPrivate,
        gameMode: room.gameMode,
        roomCode: room.roomCode,
        status: room.status
      }
    });
  } catch (error) {
    console.error('Create room error:', error);
    res.status(500).json({
      error: 'Failed to create room',
      code: 'CREATE_ROOM_ERROR'
    });
  }
});

// Join a game room
router.post('/rooms/:roomId/join', async (req, res) => {
  try {
    const { roomId } = req.params;
    const { roomCode } = req.body;

    const room = await GameRoom.findById(roomId);
    if (!room || !room.isActive) {
      return res.status(404).json({
        error: 'Room not found',
        code: 'ROOM_NOT_FOUND'
      });
    }

    // Check room code for private rooms
    if (room.isPrivate && room.roomCode !== roomCode) {
      return res.status(401).json({
        error: 'Invalid room code',
        code: 'INVALID_ROOM_CODE'
      });
    }

    // Check if room is full
    if (room.players.length >= room.maxPlayers) {
      return res.status(409).json({
        error: 'Room is full',
        code: 'ROOM_FULL'
      });
    }

    // Check if player is already in room
    if (room.players.includes(req.userId)) {
      return res.status(409).json({
        error: 'Already in room',
        code: 'ALREADY_IN_ROOM'
      });
    }

    // Add player to room
    room.players.push(req.userId);
    await room.save();
    await room.populate('host', 'username profile.displayName');
    await room.populate('players', 'username profile.displayName');

    res.json({
      message: 'Joined room successfully',
      room: {
        id: room._id,
        name: room.name,
        host: room.host,
        players: room.players,
        maxPlayers: room.maxPlayers,
        gameMode: room.gameMode,
        status: room.status
      }
    });
  } catch (error) {
    console.error('Join room error:', error);
    res.status(500).json({
      error: 'Failed to join room',
      code: 'JOIN_ROOM_ERROR'
    });
  }
});

// Leave a game room
router.post('/rooms/:roomId/leave', async (req, res) => {
  try {
    const { roomId } = req.params;

    const room = await GameRoom.findById(roomId);
    if (!room || !room.isActive) {
      return res.status(404).json({
        error: 'Room not found',
        code: 'ROOM_NOT_FOUND'
      });
    }

    // Remove player from room
    room.players = room.players.filter(playerId => !playerId.equals(req.userId));

    // If host leaves, transfer host to another player or close room
    if (room.host.equals(req.userId)) {
      if (room.players.length > 0) {
        room.host = room.players[0];
      } else {
        room.isActive = false;
      }
    }

    await room.save();

    res.json({
      message: 'Left room successfully'
    });
  } catch (error) {
    console.error('Leave room error:', error);
    res.status(500).json({
      error: 'Failed to leave room',
      code: 'LEAVE_ROOM_ERROR'
    });
  }
});

// Get room details
router.get('/rooms/:roomId', async (req, res) => {
  try {
    const { roomId } = req.params;

    const room = await GameRoom.findById(roomId)
      .populate('host', 'username profile.displayName')
      .populate('players', 'username profile.displayName');

    if (!room || !room.isActive) {
      return res.status(404).json({
        error: 'Room not found',
        code: 'ROOM_NOT_FOUND'
      });
    }

    res.json({
      room: {
        id: room._id,
        name: room.name,
        host: room.host,
        players: room.players,
        maxPlayers: room.maxPlayers,
        isPrivate: room.isPrivate,
        gameMode: room.gameMode,
        status: room.status,
        gameState: room.gameState,
        createdAt: room.createdAt
      }
    });
  } catch (error) {
    console.error('Get room error:', error);
    res.status(500).json({
      error: 'Failed to get room',
      code: 'GET_ROOM_ERROR'
    });
  }
});

// Update room game state
router.put('/rooms/:roomId/state', async (req, res) => {
  try {
    const { roomId } = req.params;
    const { gameState } = req.body;

    const room = await GameRoom.findById(roomId);
    if (!room || !room.isActive) {
      return res.status(404).json({
        error: 'Room not found',
        code: 'ROOM_NOT_FOUND'
      });
    }

    // Only host can update game state
    if (!room.host.equals(req.userId)) {
      return res.status(403).json({
        error: 'Only host can update game state',
        code: 'NOT_HOST'
      });
    }

    room.gameState = gameState;
    room.lastActivity = new Date();
    await room.save();

    res.json({
      message: 'Game state updated successfully'
    });
  } catch (error) {
    console.error('Update game state error:', error);
    res.status(500).json({
      error: 'Failed to update game state',
      code: 'UPDATE_STATE_ERROR'
    });
  }
});

// Generate random room code
function generateRoomCode() {
  return Math.random().toString(36).substring(2, 8).toUpperCase();
}

module.exports = router;
